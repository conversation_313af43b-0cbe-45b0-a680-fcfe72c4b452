# 
# <https://github.com/KFERMercer/OpenWrt-CI>
#
# Copyright (C) 2019 P3TERX
#
# Copyright (C) 2020 KFERMercer
#
name: OpenWrt-CI

on:
  schedule:
    - cron: 0 20 * * *

permissions:
  contents: read

jobs:

  build_openwrt:

    name: Build OpenWrt Firmware

    runs-on: ubuntu-22.04

    steps:
      - name: Space cleanup and Initialization environment
        env:
          DEBIAN_FRONTEND: noninteractive
        run: |
          docker rmi `docker images -q`
          sudo -E rm -rf /usr/share/dotnet /etc/mysql /etc/php /etc/apt/sources.list.d /usr/local/lib/android
          sudo -E apt-mark hold grub-efi-amd64-signed
          sudo -E apt update
          sudo -E apt -y purge azure-cli* docker* ghc* zulu* llvm* firefox google* dotnet* powershell* openjdk* mysql* php* mongodb* dotnet* snap*
          sudo -E apt -y full-upgrade
          sudo -E apt -y install ack antlr3 asciidoc autoconf automake autopoint binutils bison build-essential bzip2 ccache clang cmake cpio curl device-tree-compiler flex gawk gcc-multilib g++-multilib gettext genisoimage git gperf haveged help2man intltool libc6-dev-i386 libelf-dev libfuse-dev libglib2.0-dev libgmp3-dev libltdl-dev libmpc-dev libmpfr-dev libncurses5-dev libncursesw5-dev libpython3-dev libreadline-dev libssl-dev libtool llvm lrzsz msmtp ninja-build p7zip p7zip-full patch pkgconf python3 python3-pyelftools python3-setuptools qemu-utils rsync scons squashfs-tools subversion swig texinfo uglifyjs upx-ucl unzip vim wget xmlto xxd zlib1g-dev
          sudo -E systemctl daemon-reload
          sudo -E apt -y autoremove --purge
          sudo -E apt clean
          sudo -E timedatectl set-timezone "Asia/Shanghai"

      - name: Checkout OpenWrt
        uses: actions/checkout@v4

      - name: Free Disk Space (Ubuntu)
        uses: jlumbroso/free-disk-space@main
        with:
          # this might remove tools that are actually needed,
          # if set to "true" but frees about 6 GB
          tool-cache: true
          # all of these default to true, but feel free to set to
          # "false" if necessary for your workflow
          android: true
          dotnet: true
          haskell: true
          large-packages: true
          swap-storage: true

      - name: Update feeds
        run: |
          sed -i 's/#src-git helloworld/src-git helloworld/g' ./feeds.conf.default
          ./scripts/feeds update -a
          ./scripts/feeds install -a

      - name: Generate configuration file
        run: make defconfig

      - name: Download packages
        run: make download -j16

      - name: Compile firmware
        run: |
          make -j$(nproc) || make -j1 V=s
          echo "======================="
          echo "Space usage:"
          echo "======================="
          df -h
          echo "======================="
          du -h --max-depth=1 ./ --exclude=build_dir --exclude=bin
          du -h --max-depth=1 ./build_dir
          du -h --max-depth=1 ./bin

      - name: Prepare artifact
        run: |
          mkdir -p ./artifact/package
          mkdir -p ./artifact/buildinfo
          rm -rf $(find ./bin/targets/ -type d -name "packages")
          cp -rf $(find ./bin/packages/ -type f -name "*.ipk") ./artifact/package/
          cp -rf $(find ./bin/targets/ -type f -name "*.buildinfo" -o -name "*.manifest") ./artifact/buildinfo/

      - name: Upload buildinfo
        uses: actions/upload-artifact@v4
        with:
          name: OpenWrt_buildinfo
          path: ./artifact/buildinfo/

      - name: Upload package
        uses: actions/upload-artifact@v4
        with:
          name: OpenWrt_package
          path: ./artifact/package/

      - name: Upload firmware
        uses: actions/upload-artifact@v4
        with:
          name: OpenWrt_firmware
          path: ./bin/targets/
