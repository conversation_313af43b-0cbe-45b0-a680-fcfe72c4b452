# SPDX-License-Identifier: GPL-2.0-only
#
# Copyright (C) 2006-2014 OpenWrt.org

config KERNEL_BUILD_USER
	string "Custom Kernel Build User Name"
	default "builder" if BUILDBOT
	default ""
	help
	  Sets the Kernel build user string, which for example will be returned
	  by 'uname -a' on running systems.
	  If not set, uses system user at build time.

config KERNEL_BUILD_DOMAIN
	string "Custom Kernel Build Domain Name"
	default "buildhost" if BUILDBOT
	default ""
	help
	  Sets the Kernel build domain string, which for example will be
	  returned by 'uname -a' on running systems.
	  If not set, uses system hostname at build time.

config KERNEL_PRINTK
	bool "Enable support for printk"
	default y

config KERNEL_SWAP
	bool "Support for paging of anonymous memory (swap)"
	default y if !SMALL_FLASH

config KERNEL_PROC_STRIPPED
	bool "Strip non-essential /proc functionality to reduce code size"
	default y if SMALL_FLASH

config KERNEL_DEBUG_FS
	bool "Compile the kernel with debug filesystem enabled"
	default y
	help
	  debugfs is a virtual file system that kernel developers use to put
	  debugging files into. Enable this option to be able to read and
	  write to these files. Many common debugging facilities, such as
	  ftrace, require the existence of debugfs.

config KERNEL_MIPS_FP_SUPPORT
	bool
	default y if TARGET_pistachio

config KERNEL_ARM_PMU
	bool
	default y if TARGET_armsr_armv8
	depends on (arm || aarch64)

config KERNEL_ARM_PMUV3
	bool
	default y if TARGET_armsr_armv8
	depends on (arm_v7 || aarch64) && LINUX_6_6

config KERNEL_RISCV_PMU
	bool
	select KERNEL_RISCV_PMU_SBI
	depends on riscv64

config KERNEL_RISCV_PMU_SBI
	bool
	depends on riscv64

config KERNEL_X86_VSYSCALL_EMULATION
	bool "Enable vsyscall emulation"
	depends on x86_64
	help
	  This enables emulation of the legacy vsyscall page.  Disabling
	  it is roughly equivalent to booting with vsyscall=none, except
	  that it will also disable the helpful warning if a program
	  tries to use a vsyscall.  With this option set to N, offending
	  programs will just segfault, citing addresses of the form
	  0xffffffffff600?00.

	  This option is required by many programs built before 2013, and
	  care should be used even with newer programs if set to N.

	  Disabling this option saves about 7K of kernel size and
	  possibly 4K of additional runtime pagetable memory.

config KERNEL_PERF_EVENTS
	bool "Compile the kernel with performance events and counters"
	select KERNEL_ARM_PMU if (arm || aarch64)
	select KERNEL_ARM_PMUV3 if (arm_v7 || aarch64) && LINUX_6_6
	select KERNEL_RISCV_PMU if riscv64

config KERNEL_PROFILING
	bool "Compile the kernel with profiling enabled"
	select KERNEL_PERF_EVENTS
	help
	  Enable the extended profiling support mechanisms used by profilers such
	  as OProfile.

config KERNEL_RPI_AXIPERF
	bool "Compile the kernel with RaspberryPi AXI Performance monitors"
	default y
	depends on KERNEL_PERF_EVENTS && TARGET_bcm27xx

config KERNEL_UBSAN
	bool "Compile the kernel with undefined behaviour sanity checker"
	help
	  This option enables undefined behaviour sanity checker
	  Compile-time instrumentation is used to detect various undefined
	  behaviours in runtime. Various types of checks may be enabled
	  via boot parameter ubsan_handle
	  (see: Documentation/dev-tools/ubsan.rst).

config KERNEL_UBSAN_SANITIZE_ALL
	bool "Enable instrumentation for the entire kernel"
	depends on KERNEL_UBSAN
	default y
	help
	  This option activates instrumentation for the entire kernel.
	  If you don't enable this option, you have to explicitly specify
	  UBSAN_SANITIZE := y for the files/directories you want to check for UB.
	  Enabling this option will get kernel image size increased
	  significantly.

config KERNEL_UBSAN_ALIGNMENT
	bool "Enable checking of pointers alignment"
	depends on KERNEL_UBSAN
	help
	  This option enables detection of unaligned memory accesses.
	  Enabling this option on architectures that support unaligned
	  accesses may produce a lot of false positives.

config KERNEL_UBSAN_BOUNDS
	bool "Perform array index bounds checking"
	depends on KERNEL_UBSAN
	help
	  This option enables detection of directly indexed out of bounds array
	  accesses, where the array size is known at compile time. Note that
	  this does not protect array overflows via bad calls to the
	  {str,mem}*cpy() family of functions (that is addressed by
	  FORTIFY_SOURCE).

config KERNEL_UBSAN_NULL
	bool "Enable checking of null pointers"
	depends on KERNEL_UBSAN
	help
	  This option enables detection of memory accesses via a
	  null pointer.

config KERNEL_UBSAN_TRAP
	bool "On Sanitizer warnings, abort the running kernel code"
	depends on KERNEL_UBSAN
	help
	  Building kernels with Sanitizer features enabled tends to grow the
	  kernel size by around 5%, due to adding all the debugging text on
	  failure paths. To avoid this, Sanitizer instrumentation can just
	  issue a trap. This reduces the kernel size overhead but turns all
	  warnings (including potentially harmless conditions) into full
	  exceptions that abort the running kernel code (regardless of context,
	  locks held, etc), which may destabilize the system. For some system
	  builders this is an acceptable trade-off.

config KERNEL_KASAN
	bool "Compile the kernel with KASan: runtime memory debugger"
	select KERNEL_SLUB_DEBUG
	depends on (x86_64 || aarch64 || arm || powerpc || riscv64)
	help
	  Enables kernel address sanitizer - runtime memory debugger,
	  designed to find out-of-bounds accesses and use-after-free bugs.
	  This is strictly a debugging feature and it requires a gcc version
	  of 4.9.2 or later. Detection of out of bounds accesses to stack or
	  global variables requires gcc 5.0 or later.
	  This feature consumes about 1/8 of available memory and brings about
	  ~x3 performance slowdown.
	  For better error detection enable CONFIG_STACKTRACE.
	  Currently CONFIG_KASAN doesn't work with CONFIG_DEBUG_SLAB
	  (the resulting kernel does not boot).

config KERNEL_KASAN_VMALLOC
	bool "Back mappings in vmalloc space with real shadow memory"
	depends on KERNEL_KASAN
	help
	  By default, the shadow region for vmalloc space is the read-only
	  zero page. This means that KASAN cannot detect errors involving
	  vmalloc space.

	  Enabling this option will hook in to vmap/vmalloc and back those
	  mappings with real shadow memory allocated on demand. This allows
	  for KASAN to detect more sorts of errors (and to support vmapped
	  stacks), but at the cost of higher memory usage.

	  This option depends on HAVE_ARCH_KASAN_VMALLOC, but we can't
	  depend on that in here, so it is possible that enabling this
	  will have no effect.

if KERNEL_KASAN
choice
	prompt "KASAN mode"
	depends on KERNEL_KASAN
	default KERNEL_KASAN_GENERIC
	help
	  KASAN has three modes:

	  1. Generic KASAN (supported by many architectures, enabled with
	     CONFIG_KASAN_GENERIC, similar to userspace ASan),
	  2. Software Tag-Based KASAN (arm64 only, based on software memory
	     tagging, enabled with CONFIG_KASAN_SW_TAGS, similar to userspace
	     HWASan), and
	  3. Hardware Tag-Based KASAN (arm64 only, based on hardware memory
	     tagging, enabled with CONFIG_KASAN_HW_TAGS).

config KERNEL_KASAN_GENERIC
	bool "Generic KASAN"
	select KERNEL_SLUB_DEBUG
	help
	  Enables Generic KASAN.

	  Consumes about 1/8th of available memory at kernel start and adds an
	  overhead of ~50% for dynamic allocations.
	  The performance slowdown is ~x3.

config KERNEL_KASAN_SW_TAGS
	bool "Software Tag-Based KASAN"
	depends on aarch64
	select KERNEL_SLUB_DEBUG
	help
	  Enables Software Tag-Based KASAN.

	  Supported only on arm64 CPUs and relies on Top Byte Ignore.

	  Consumes about 1/16th of available memory at kernel start and
	  add an overhead of ~20% for dynamic allocations.

	  May potentially introduce problems related to pointer casting and
	  comparison, as it embeds a tag into the top byte of each pointer.

config KERNEL_KASAN_HW_TAGS
	bool "Hardware Tag-Based KASAN"
	depends on aarch64
	select KERNEL_SLUB_DEBUG
	select KERNEL_ARM64_MTE
	help
	  Enables Hardware Tag-Based KASAN.

	  Supported only on arm64 CPUs starting from ARMv8.5 and relies on
	  Memory Tagging Extension and Top Byte Ignore.

	  Consumes about 1/32nd of available memory.

	  May potentially introduce problems related to pointer casting and
	  comparison, as it embeds a tag into the top byte of each pointer.

endchoice

	config KERNEL_ARM64_MTE
		def_bool n

endif

choice
	prompt "Instrumentation type"
	depends on KERNEL_KASAN
	depends on !KERNEL_KASAN_HW_TAGS
	default KERNEL_KASAN_OUTLINE

config KERNEL_KASAN_OUTLINE
	bool "Outline instrumentation"
	help
	  Before every memory access compiler insert function call
	  __asan_load*/__asan_store*. These functions performs check
	  of shadow memory. This is slower than inline instrumentation,
	  however it doesn't bloat size of kernel's .text section so
	  much as inline does.

config KERNEL_KASAN_INLINE
	bool "Inline instrumentation"
	help
	  Compiler directly inserts code checking shadow memory before
	  memory accesses. This is faster than outline (in some workloads
	  it gives about x2 boost over outline instrumentation), but
	  make kernel's .text size much bigger.
	  This requires a gcc version of 5.0 or later.

endchoice

config KERNEL_KCOV
	bool "Compile the kernel with code coverage for fuzzing"
	select KERNEL_DEBUG_FS
	help
	  KCOV exposes kernel code coverage information in a form suitable
	  for coverage-guided fuzzing (randomized testing).

	  If RANDOMIZE_BASE is enabled, PC values will not be stable across
	  different machines and across reboots. If you need stable PC values,
	  disable RANDOMIZE_BASE.

	  For more details, see Documentation/kcov.txt.

config KERNEL_KCOV_ENABLE_COMPARISONS
	bool "Enable comparison operands collection by KCOV"
	depends on KERNEL_KCOV
	help
	  KCOV also exposes operands of every comparison in the instrumented
	  code along with operand sizes and PCs of the comparison instructions.
	  These operands can be used by fuzzing engines to improve the quality
	  of fuzzing coverage.

config KERNEL_KCOV_INSTRUMENT_ALL
	bool "Instrument all code by default"
	depends on KERNEL_KCOV
	default y if KERNEL_KCOV
	help
	  If you are doing generic system call fuzzing (like e.g. syzkaller),
	  then you will want to instrument the whole kernel and you should
	  say y here. If you are doing more targeted fuzzing (like e.g.
	  filesystem fuzzing with AFL) then you will want to enable coverage
	  for more specific subsets of files, and should say n here.

config KERNEL_TASKSTATS
	bool "Compile the kernel with task resource/io statistics and accounting"
	help
	  Enable the collection and publishing of task/io statistics and
	  accounting.  Enable this option to enable i/o monitoring in system
	  monitors.

if KERNEL_TASKSTATS

	config KERNEL_TASK_DELAY_ACCT
		def_bool y

	config KERNEL_TASK_IO_ACCOUNTING
		def_bool y

	config KERNEL_TASK_XACCT
		def_bool y

endif

config KERNEL_PSI
	bool "Compile the kernel with pressure stall information tracking"
	help
	  Collect metrics that indicate how overcommitted the CPU, memory,
	  and IO capacity are in the system.

	  If you say Y here, the kernel will create /proc/pressure/ with the
	  pressure statistics files cpu, memory, and io. These will indicate
	  the share of walltime in which some or all tasks in the system are
	  delayed due to contention of the respective resource.

	  In kernels with cgroup support, cgroups (cgroup2 only) will
	  have cpu.pressure, memory.pressure, and io.pressure files,
	  which aggregate pressure stalls for the grouped tasks only.

	  For more details see Documentation/accounting/psi.rst.

	  Say N if unsure.

config KERNEL_KALLSYMS
	bool "Compile the kernel with symbol table information"
	default y if !SMALL_FLASH
	help
	  This will give you more information in stack traces from kernel oopses.

config KERNEL_FTRACE
	bool "Compile the kernel with tracing support"
	depends on !TARGET_uml

config KERNEL_FTRACE_SYSCALLS
	bool "Trace system calls"
	depends on KERNEL_FTRACE

config KERNEL_ENABLE_DEFAULT_TRACERS
	bool "Trace process context switches and events"
	depends on KERNEL_FTRACE

config KERNEL_FUNCTION_TRACER
	bool "Function tracer"
	depends on KERNEL_FTRACE

config KERNEL_FUNCTION_GRAPH_TRACER
	bool "Function graph tracer"
	depends on KERNEL_FUNCTION_TRACER

config KERNEL_DYNAMIC_FTRACE
	bool "Enable/disable function tracing dynamically"
	depends on KERNEL_FUNCTION_TRACER

config KERNEL_FUNCTION_PROFILER
	bool "Function profiler"
	depends on KERNEL_FUNCTION_TRACER

config KERNEL_IRQSOFF_TRACER
	bool "Interrupts-off Latency Tracer"
	depends on KERNEL_FTRACE
	help
	  This option measures the time spent in irqs-off critical
	  sections, with microsecond accuracy.

	  The default measurement method is a maximum search, which is
	  disabled by default and can be runtime (re-)started
	  via:

	      echo 0 > /sys/kernel/debug/tracing/tracing_max_latency

	  (Note that kernel size and overhead increase with this option
	  enabled. This option and the preempt-off timing option can be
	  used together or separately.)

config KERNEL_PREEMPT_TRACER
	bool "Preemption-off Latency Tracer"
	depends on KERNEL_FTRACE
	help
	  This option measures the time spent in preemption-off critical
	  sections, with microsecond accuracy.

	  The default measurement method is a maximum search, which is
	  disabled by default and can be runtime (re-)started
	  via:

	      echo 0 > /sys/kernel/debug/tracing/tracing_max_latency

	  (Note that kernel size and overhead increase with this option
	  enabled. This option and the irqs-off timing option can be
	  used together or separately.)

config KERNEL_HIST_TRIGGERS
	bool "Histogram triggers"
	depends on KERNEL_FTRACE
	help
	  Hist triggers allow one or more arbitrary trace event fields to be
	  aggregated into hash tables and dumped to stdout by reading a
	  debugfs/tracefs file. They're useful for gathering quick and dirty
	  (though precise) summaries of event activity as an initial guide for
	  further investigation using more advanced tools.

	  Inter-event tracing of quantities such as latencies is also
	  supported using hist triggers under this option.

config KERNEL_DEBUG_KERNEL
	bool

config KERNEL_DEBUG_INFO
	bool "Compile the kernel with debug information"
	default y if !SMALL_FLASH
	select KERNEL_DEBUG_KERNEL
	help
	  This will compile your kernel and modules with debug information.

config KERNEL_DEBUG_INFO_BTF
	bool "Enable additional BTF type information"
	depends on !HOST_OS_MACOS
	depends on KERNEL_DEBUG_INFO && !KERNEL_DEBUG_INFO_REDUCED
	select DWARVES
	help
	  Generate BPF Type Format (BTF) information from DWARF debug info.
	  Turning this on expects presence of pahole tool, which will convert
	  DWARF type info into equivalent deduplicated BTF type info.

	  Required to run BPF CO-RE applications.

config KERNEL_DEBUG_INFO_BTF_MODULES
	def_bool y
	depends on KERNEL_DEBUG_INFO_BTF

config KERNEL_MODULE_ALLOW_BTF_MISMATCH
	bool "Allow loading modules with non-matching BTF type info"
	depends on KERNEL_DEBUG_INFO_BTF_MODULES
	help
	  For modules whose split BTF does not match vmlinux, load without
	  BTF rather than refusing to load. The default behavior with
	  module BTF enabled is to reject modules with such mismatches;
	  this option will still load module BTF where possible but ignore
	  it when a mismatch is found.

config KERNEL_DEBUG_INFO_REDUCED
	bool "Reduce debugging information"
	default y
	depends on KERNEL_DEBUG_INFO
	help
	  If you say Y here gcc is instructed to generate less debugging
	  information for structure types. This means that tools that
	  need full debugging information (like kgdb or systemtap) won't
	  be happy. But if you merely need debugging information to
	  resolve line numbers there is no loss. Advantage is that
	  build directory object sizes shrink dramatically over a full
	  DEBUG_INFO build and compile times are reduced too.
	  Only works with newer gcc versions.

config KERNEL_FRAME_WARN
	int
	range 0 8192
	default 1280 if KERNEL_KASAN && !ARCH_64BIT
	default 1024 if !ARCH_64BIT
	default 2048 if ARCH_64BIT
	help
	  Tell the compiler to warn at build time for stack frames larger than this.
	  Setting this too low will cause a lot of warnings.
	  Setting it to 0 disables the warning.

# KERNEL_DEBUG_LL symbols must have the default value set as otherwise
# KConfig wont evaluate them unless KERNEL_EARLY_PRINTK is selected
# which means that buildroot wont override the DEBUG_LL symbols in target
# kernel configurations and lead to devices that dont have working console
config KERNEL_DEBUG_LL_UART_NONE
	bool
	default n
	depends on arm

config KERNEL_DEBUG_LL
	bool
	default n
	depends on arm
	select KERNEL_DEBUG_LL_UART_NONE
	help
	  ARM low level debugging.

config KERNEL_DEBUG_VIRTUAL
	bool "Compile the kernel with VM translations debugging"
	select KERNEL_DEBUG_KERNEL
	help
	  Enable checks sanity checks to catch invalid uses of
	  virt_to_phys()/phys_to_virt() against the non-linear address space.

config KERNEL_DYNAMIC_DEBUG
	bool "Compile the kernel with dynamic printk"
	select KERNEL_DEBUG_FS
	help
	  Compiles debug level messages into the kernel, which would not
	  otherwise be available at runtime. These messages can then be
	  enabled/disabled based on various levels of scope - per source file,
	  function, module, format string, and line number. This mechanism
	  implicitly compiles in all pr_debug() and dev_dbg() calls, which
	  enlarges the kernel text size by about 2%.

config KERNEL_EARLY_PRINTK
	bool "Compile the kernel with early printk"
	default y if TARGET_bcm53xx
	depends on arm
	select KERNEL_DEBUG_KERNEL
	select KERNEL_DEBUG_LL if arm
	help
	  Compile the kernel with early printk support.  This is only useful for
	  debugging purposes to send messages over the serial console in early boot.
	  Enable this to debug early boot problems.

config KERNEL_KPROBES
	bool "Compile the kernel with kprobes support"
	select KERNEL_FTRACE
	select KERNEL_PERF_EVENTS
	help
	  Compiles the kernel with KPROBES support, which allows you to trap
	  at almost any kernel address and execute a callback function.
	  register_kprobe() establishes a probepoint and specifies the
	  callback. Kprobes is useful for kernel debugging, non-intrusive
	  instrumentation and testing.
	  If in doubt, say "N".

config KERNEL_KPROBE_EVENTS
	bool
	default y if KERNEL_KPROBES

config KERNEL_BPF_EVENTS
	bool "Compile the kernel with BPF event support"
	select KERNEL_KPROBES
	help
	  Allows to attach BPF programs to kprobe, uprobe and tracepoint events.
	  This is required to use BPF maps of type BPF_MAP_TYPE_PERF_EVENT_ARRAY
	  for sending data from BPF programs to user-space for post-processing
	  or logging.

config KERNEL_PROBE_EVENTS_BTF_ARGS
	bool "Support BTF function arguments for probe events"
	depends on KERNEL_DEBUG_INFO_BTF && KERNEL_KPROBE_EVENTS && LINUX_6_6

config KERNEL_BPF_KPROBE_OVERRIDE
	bool
	depends on KERNEL_KPROBES
	default n

config KERNEL_AIO
	bool "Compile the kernel with asynchronous IO support"
	default y if !SMALL_FLASH

config KERNEL_IO_URING
	bool "Compile the kernel with io_uring support"
	depends on !SMALL_FLASH
	default y if (x86_64 || aarch64)

config KERNEL_FHANDLE
	bool "Compile the kernel with support for fhandle syscalls"
	default y if !SMALL_FLASH

config KERNEL_FANOTIFY
	bool "Compile the kernel with modern file notification support"
	default y if !SMALL_FLASH

config KERNEL_BLK_DEV_BSG
	bool "Compile the kernel with SCSI generic v4 support for any block device"

config KERNEL_TRANSPARENT_HUGEPAGE
	bool

choice
	prompt "Transparent Hugepage Support sysfs defaults"
	depends on KERNEL_TRANSPARENT_HUGEPAGE
	default KERNEL_TRANSPARENT_HUGEPAGE_ALWAYS

	config KERNEL_TRANSPARENT_HUGEPAGE_ALWAYS
		bool "always"

	config KERNEL_TRANSPARENT_HUGEPAGE_MADVISE
		bool "madvise"
endchoice

config KERNEL_ARM64_CONTPTE
	bool "Compile the kernel with Contiguous PTE mappings for user memory"
	depends on KERNEL_ARM64
	depends on KERNEL_TRANSPARENT_HUGEPAGE
	depends on LINUX_6_12
	default y

config KERNEL_HUGETLBFS
	bool

config KERNEL_HUGETLB_PAGE
	bool "Compile the kernel with HugeTLB support"
	select KERNEL_TRANSPARENT_HUGEPAGE
	select KERNEL_HUGETLBFS

config KERNEL_MAGIC_SYSRQ
	bool "Compile the kernel with SysRq support"
	default y

config KERNEL_DEBUG_PINCTRL
	bool "Compile the kernel with pinctrl debugging"
	select KERNEL_DEBUG_KERNEL

config KERNEL_DEBUG_GPIO
	bool "Compile the kernel with gpio debugging"
	select KERNEL_DEBUG_KERNEL

config KERNEL_COREDUMP
	bool

config KERNEL_ELF_CORE
	bool "Enable process core dump support"
	select KERNEL_COREDUMP
	default y if !SMALL_FLASH

config KERNEL_PROVE_LOCKING
	bool "Enable kernel lock checking"
	select KERNEL_DEBUG_KERNEL

config KERNEL_SOFTLOCKUP_DETECTOR
	bool "Compile the kernel with detect Soft Lockups"
	depends on KERNEL_DEBUG_KERNEL
	help
	  Say Y here to enable the kernel to act as a watchdog to detect
	  soft lockups.

	  Softlockups are bugs that cause the kernel to loop in kernel
	  mode for more than 20 seconds, without giving other tasks a
	  chance to run.  The current stack trace is displayed upon
	  detection and the system will stay locked up.

config KERNEL_HARDLOCKUP_DETECTOR
	bool "Compile the kernel with detect Hard Lockups"
	depends on KERNEL_DEBUG_KERNEL
	help
	  Say Y here to enable the kernel to act as a watchdog to detect
	  hard lockups.

	  Hardlockups are bugs that cause the CPU to loop in kernel mode
	  for more than 10 seconds, without letting other interrupts have a
	  chance to run.  The current stack trace is displayed upon detection
	  and the system will stay locked up.

config KERNEL_DETECT_HUNG_TASK
	bool "Compile the kernel with detect Hung Tasks"
	depends on KERNEL_DEBUG_KERNEL
	default KERNEL_SOFTLOCKUP_DETECTOR
	help
	  Say Y here to enable the kernel to detect "hung tasks",
	  which are bugs that cause the task to be stuck in
	  uninterruptible "D" state indefinitely.

	  When a hung task is detected, the kernel will print the
	  current stack trace (which you should report), but the
	  task will stay in uninterruptible state. If lockdep is
	  enabled then all held locks will also be reported. This
	  feature has negligible overhead.

config KERNEL_WQ_WATCHDOG
	bool "Compile the kernel with detect Workqueue Stalls"
	depends on KERNEL_DEBUG_KERNEL
	help
	  Say Y here to enable stall detection on workqueues.  If a
	  worker pool doesn't make forward progress on a pending work
	  item for over a given amount of time, 30s by default, a
	  warning message is printed along with dump of workqueue
	  state.  This can be configured through kernel parameter
	  "workqueue.watchdog_thresh" and its sysfs counterpart.

config KERNEL_DEBUG_ATOMIC_SLEEP
	bool "Compile the kernel with sleep inside atomic section checking"
	depends on KERNEL_DEBUG_KERNEL
	help
	  If you say Y here, various routines which may sleep will become very
	  noisy if they are called inside atomic sections: when a spinlock is
	  held, inside an rcu read side critical section, inside preempt disabled
	  sections, inside an interrupt, etc...

config KERNEL_DEBUG_VM
	bool "Compile the kernel with debug VM"
	depends on KERNEL_DEBUG_KERNEL
	help
	  Enable this to turn on extended checks in the virtual-memory system
          that may impact performance.

	  If unsure, say N.

config KERNEL_PRINTK_TIME
	bool "Enable printk timestamps"
	default y

config KERNEL_SLUB_DEBUG
	bool "Enable SLUB debugging support"
	help
	  This enables various debugging features:
	    - Accepts "slub_debug" kernel parameter
	    - Provides caches debugging options (e.g. tracing, validating)
	    - Adds /sys/kernel/slab/ attrs for reading amounts of *objects*
	    - Enables /proc/slabinfo support
	    - Prints info when running out of memory

	  Enabling this can result in a significant increase of code size.

config KERNEL_SLUB_DEBUG_ON
	depends on KERNEL_SLUB_DEBUG
	bool "Boot kernel with basic caches debugging enabled"
	help
	  This enables by default sanity_checks, red_zone, poison and store_user
	  debugging options for all caches.

config KERNEL_SLABINFO
	select KERNEL_SLUB_DEBUG
	select KERNEL_SLUB_DEBUG_ON
	bool "Enable /proc slab debug info"

config KERNEL_PROC_PAGE_MONITOR
	bool "Enable /proc page monitoring"

config KERNEL_RELAY
	bool

config KERNEL_KEXEC
	bool "Enable kexec support"

config KERNEL_PROC_VMCORE
	bool

config KERNEL_PROC_KCORE
	bool

config KERNEL_CRASH_DUMP
	depends on i386 || x86_64 || arm || armeb
	select KERNEL_KEXEC
	select KERNEL_PROC_VMCORE
	select KERNEL_PROC_KCORE
	bool "Enable support for kexec crashdump"
	default y

config USE_RFKILL
	bool "Enable rfkill support"
	default RFKILL_SUPPORT

config USE_SPARSE
	bool "Enable sparse check during kernel build"

config KERNEL_DEVTMPFS
	bool "Compile the kernel with device tmpfs enabled"
	help
	  devtmpfs is a simple, kernel-managed /dev filesystem. The kernel creates
	  devices nodes for all registered devices to simplify boot, but leaves more
	  complex tasks to userspace (e.g. udev).

if KERNEL_DEVTMPFS

	config KERNEL_DEVTMPFS_MOUNT
		bool "Automatically mount devtmpfs after root filesystem is mounted"

endif

config KERNEL_KEYS
	bool "Enable kernel access key retention support"
	default !SMALL_FLASH

config KERNEL_PERSISTENT_KEYRINGS
	bool "Enable kernel persistent keyrings"
	depends on KERNEL_KEYS

config KERNEL_KEYS_REQUEST_CACHE
	bool "Enable temporary caching of the last request_key() result"
	depends on KERNEL_KEYS

config KERNEL_BIG_KEYS
	bool "Enable large payload keys on kernel keyrings"
	depends on KERNEL_KEYS

#
# CGROUP support symbols
#

config KERNEL_CGROUPS
	bool "Enable kernel cgroups"
	default y if !SMALL_FLASH

if KERNEL_CGROUPS

	config KERNEL_CGROUP_DEBUG
		bool "Example debug cgroup subsystem"
		help
		  This option enables a simple cgroup subsystem that
		  exports useful debugging information about the cgroups
		  framework.

	config KERNEL_FREEZER
		bool

	config KERNEL_CGROUP_FREEZER
		bool "legacy Freezer cgroup subsystem"
		select KERNEL_FREEZER
		help
		  Provides a way to freeze and unfreeze all tasks in a
		  cgroup.
		  (legacy cgroup1-only controller, in cgroup2 freezer
		  is integrated in the Memory controller)

	config KERNEL_CGROUP_DEVICE
		bool "legacy Device controller for cgroups"
		help
		  Provides a cgroup implementing whitelists for devices which
		  a process in the cgroup can mknod or open.
		  (legacy cgroup1-only controller)

	config KERNEL_CGROUP_HUGETLB
		bool "HugeTLB controller"
		select KERNEL_HUGETLB_PAGE

	config KERNEL_CGROUP_PIDS
		bool "PIDs cgroup subsystem"
		default y
		help
		  Provides enforcement of process number limits in the scope of a
		  cgroup.

	config KERNEL_CGROUP_RDMA
		bool "RDMA controller for cgroups"
		default y

	config KERNEL_CGROUP_BPF
		bool "Support for eBPF programs attached to cgroups"
		default y

	config KERNEL_CPUSETS
		bool "Cpuset support"
		default y
		help
		  This option will let you create and manage CPUSETs which
		  allow dynamically partitioning a system into sets of CPUs and
		  Memory Nodes and assigning tasks to run only within those sets.
		  This is primarily useful on large SMP or NUMA systems.

	config KERNEL_CPUSETS_V1
		bool "Legacy cgroup v1 cpusets controller"
		depends on KERNEL_CPUSETS
		default n
		help
		  Legacy cgroup v1 cpusets controller which has been deprecated by
		  cgroup v2 implementation. The v1 is there for legacy applications
		  which haven't migrated to the new cgroup v2 interface yet. If you
		  do not have any such application then you are completely fine leaving
		  this option disabled.

	config KERNEL_PROC_PID_CPUSET
		bool "Include legacy /proc/<pid>/cpuset file"
		depends on KERNEL_CPUSETS

	config KERNEL_CGROUP_CPUACCT
		bool "Simple CPU accounting cgroup subsystem"
		default y
		help
		  Provides a simple Resource Controller for monitoring the
		  total CPU consumed by the tasks in a cgroup.

	config KERNEL_RESOURCE_COUNTERS
		bool "Resource counters"
		default y
		help
		  This option enables controller independent resource accounting
		  infrastructure that works with cgroups.

	config KERNEL_MM_OWNER
		bool
		default y if KERNEL_MEMCG

	config KERNEL_MEMCG
		bool "Memory Resource Controller for Control Groups"
		default y
		select KERNEL_FREEZER
		depends on KERNEL_RESOURCE_COUNTERS
		help
		  Provides a memory resource controller that manages both anonymous
		  memory and page cache. (See Documentation/cgroups/memory.txt)

		  Note that setting this option increases fixed memory overhead
		  associated with each page of memory in the system. By this,
		  20(40)bytes/PAGE_SIZE on 32(64)bit system will be occupied by memory
		  usage tracking struct at boot. Total amount of this is printed out
		  at boot.

		  Only enable when you're ok with these tradeoffs and really
		  sure you need the memory resource controller. Even when you enable
		  this, you can set "cgroup_disable=memory" at your boot option to
		  disable memory resource controller and you can avoid overheads
		  (but lose benefits of memory resource controller).

		  This config option also selects MM_OWNER config option, which
		  could in turn add some fork/exit overhead.

	config KERNEL_MEMCG_SWAP
		bool "Memory Resource Controller Swap Extension"
		default y
		depends on KERNEL_MEMCG
		help
		  Add swap management feature to memory resource controller. When you
		  enable this, you can limit mem+swap usage per cgroup. In other words,
		  when you disable this, memory resource controller has no cares to
		  usage of swap...a process can exhaust all of the swap. This extension
		  is useful when you want to avoid exhaustion swap but this itself
		  adds more overheads and consumes memory for remembering information.
		  Especially if you use 32bit system or small memory system, please
		  be careful about enabling this. When memory resource controller
		  is disabled by boot option, this will be automatically disabled and
		  there will be no overhead from this. Even when you set this config=y,
		  if boot option "swapaccount=0" is set, swap will not be accounted.
		  Now, memory usage of swap_cgroup is 2 bytes per entry. If swap page
		  size is 4096bytes, 512k per 1Gbytes of swap.

	config KERNEL_MEMCG_SWAP_ENABLED
		bool "Memory Resource Controller Swap Extension enabled by default"
		depends on KERNEL_MEMCG_SWAP
		help
		  Memory Resource Controller Swap Extension comes with its price in
		  a bigger memory consumption. General purpose distribution kernels
		  which want to enable the feature but keep it disabled by default
		  and let the user enable it by swapaccount boot command line
		  parameter should have this option unselected.

		  Those who want to have the feature enabled by default should
		  select this option (if, for some reason, they need to disable it,
		  then swapaccount=0 does the trick).


	config KERNEL_MEMCG_KMEM
		bool "Memory Resource Controller Kernel Memory accounting (EXPERIMENTAL)"
		default y
		depends on KERNEL_MEMCG
		help
		  The Kernel Memory extension for Memory Resource Controller can limit
		  the amount of memory used by kernel objects in the system. Those are
		  fundamentally different from the entities handled by the standard
		  Memory Controller, which are page-based, and can be swapped. Users of
		  the kmem extension can use it to guarantee that no group of processes
		  will ever exhaust kernel resources alone.

	config KERNEL_MEMCG_V1
		bool "Legacy cgroup v1 memory controller"
		default n
		depends on KERNEL_MEMCG
		help
		  Legacy cgroup v1 memory controller which has been deprecated by
		  cgroup v2 implementation. The v1 is there for legacy applications
		  which haven't migrated to the new cgroup v2 interface yet. If you
		  do not have any such application then you are completely fine leaving
		  this option disabled.

		  Please note that feature set of the legacy memory controller is likely
		  going to shrink due to deprecation process. New deployments with v1
		  controller are highly discouraged.

	config KERNEL_CGROUP_PERF
		bool "Enable perf_event per-cpu per-container group (cgroup) monitoring"
		select KERNEL_PERF_EVENTS
		help
		  This option extends the per-cpu mode to restrict monitoring to
		  threads which belong to the cgroup specified and run on the
		  designated cpu.

	menuconfig KERNEL_CGROUP_SCHED
		bool "Group CPU scheduler"
		default y
		help
		  This feature lets CPU scheduler recognize task groups and control CPU
		  bandwidth allocation to such task groups. It uses cgroups to group
		  tasks.

	if KERNEL_CGROUP_SCHED

		config KERNEL_FAIR_GROUP_SCHED
			bool "Group scheduling for SCHED_OTHER"
			default y

		config KERNEL_CFS_BANDWIDTH
			bool "CPU bandwidth provisioning for FAIR_GROUP_SCHED"
			default y
			depends on KERNEL_FAIR_GROUP_SCHED
			help
			  This option allows users to define CPU bandwidth rates (limits) for
			  tasks running within the fair group scheduler.  Groups with no limit
			  set are considered to be unconstrained and will run with no
			  restriction.
			  See tip/Documentation/scheduler/sched-bwc.txt for more information.

		config KERNEL_RT_GROUP_SCHED
			bool "Group scheduling for SCHED_RR/FIFO"
			default y
			help
			  This feature lets you explicitly allocate real CPU bandwidth
			  to task groups. If enabled, it will also make it impossible to
			  schedule realtime tasks for non-root users until you allocate
			  realtime bandwidth for them.

	endif

	config KERNEL_BLK_CGROUP
		bool "Block IO controller"
		default y
		help
		  Generic block IO controller cgroup interface. This is the common
		  cgroup interface which should be used by various IO controlling
		  policies.

		  Currently, CFQ IO scheduler uses it to recognize task groups and
		  control disk bandwidth allocation (proportional time slice allocation)
		  to such task groups. It is also used by bio throttling logic in
		  block layer to implement upper limit in IO rates on a device.

		  This option only enables generic Block IO controller infrastructure.
		  One needs to also enable actual IO controlling logic/policy. For
		  enabling proportional weight division of disk bandwidth in CFQ, set
		  CONFIG_CFQ_GROUP_IOSCHED=y; for enabling throttling policy, set
		  CONFIG_BLK_DEV_THROTTLING=y.

	if KERNEL_BLK_CGROUP

		config KERNEL_CFQ_GROUP_IOSCHED
			bool "Proportional weight of disk bandwidth in CFQ"

		config KERNEL_BLK_DEV_THROTTLING
			bool "Enable throttling policy"
			default y

		config KERNEL_BLK_DEV_THROTTLING_LOW
			bool "Block throttling .low limit interface support (EXPERIMENTAL)"
			depends on KERNEL_BLK_DEV_THROTTLING
	endif

	config KERNEL_DEBUG_BLK_CGROUP
		bool "Enable Block IO controller debugging"
		depends on KERNEL_BLK_CGROUP
		help
		  Enable some debugging help. Currently it exports additional stat
		  files in a cgroup which can be useful for debugging.

	config KERNEL_NET_CLS_CGROUP
		bool "legacy Control Group Classifier"

	config KERNEL_CGROUP_NET_CLASSID
		bool "legacy Network classid cgroup"

	config KERNEL_CGROUP_NET_PRIO
		bool "legacy Network priority cgroup"

endif

#
# Namespace support symbols
#

config KERNEL_NAMESPACES
	bool "Enable kernel namespaces"
	default y if !SMALL_FLASH

if KERNEL_NAMESPACES

	config KERNEL_UTS_NS
		bool "UTS namespace"
		default y
		help
		  In this namespace, tasks see different info provided
		  with the uname() system call.

	config KERNEL_IPC_NS
		bool "IPC namespace"
		default y
		help
		  In this namespace, tasks work with IPC ids which correspond to
		  different IPC objects in different namespaces.

	config KERNEL_USER_NS
		bool "User namespace (EXPERIMENTAL)"
		default y
		help
		  This allows containers, i.e. vservers, to use user namespaces
		  to provide different user info for different servers.

	config KERNEL_PID_NS
		bool "PID Namespaces"
		default y
		help
		  Support process id namespaces. This allows having multiple
		  processes with the same pid as long as they are in different
		  pid namespaces. This is a building block of containers.

	config KERNEL_NET_NS
		bool "Network namespace"
		default y
		help
		  Allow user space to create what appear to be multiple instances
		  of the network stack.

endif

config KERNEL_DEVPTS_MULTIPLE_INSTANCES
	bool "Support multiple instances of devpts"
	default y if !SMALL_FLASH
	help
	  Enable support for multiple instances of devpts filesystem.
	  If you want to have isolated PTY namespaces (eg: in containers),
	  say Y here. Otherwise, say N. If enabled, each mount of devpts
	  filesystem with the '-o newinstance' option will create an
	  independent PTY namespace.

config KERNEL_POSIX_MQUEUE
	bool "POSIX Message Queues"
	default y if !SMALL_FLASH
	help
	  POSIX variant of message queues is a part of IPC. In POSIX message
	  queues every message has a priority which decides about succession
	  of receiving it by a process. If you want to compile and run
	  programs written e.g. for Solaris with use of its POSIX message
	  queues (functions mq_*) say Y here.

	  POSIX message queues are visible as a filesystem called 'mqueue'
	  and can be mounted somewhere if you want to do filesystem
	  operations on message queues.


config KERNEL_SECCOMP_FILTER
	bool
	default y if !SMALL_FLASH

config KERNEL_SECCOMP
	bool "Enable seccomp support"
		depends on !(TARGET_uml)
		select KERNEL_SECCOMP_FILTER
		default y if !SMALL_FLASH
		help
		  Build kernel with support for seccomp.

#
# IPv4 configuration
#

config KERNEL_IP_MROUTE
	bool "Enable IPv4 multicast routing"
	default y
	help
	  Multicast routing requires a multicast routing daemon in
	  addition to kernel support.

if KERNEL_IP_MROUTE

	config KERNEL_IP_MROUTE_MULTIPLE_TABLES
		def_bool y

	config KERNEL_IP_PIMSM_V1
		def_bool y

	config KERNEL_IP_PIMSM_V2
		def_bool y

endif

#
# IPv6 configuration
#

config KERNEL_IPV6
	def_bool IPV6

if KERNEL_IPV6

	config KERNEL_IPV6_MULTIPLE_TABLES
		def_bool y

	config KERNEL_IPV6_SUBTREES
		def_bool y

	config KERNEL_IPV6_MROUTE
		bool "Enable IPv6 multicast routing"
		default y
		help
		  Multicast routing requires a multicast routing daemon in
		  addition to kernel support.

	if KERNEL_IPV6_MROUTE

		config KERNEL_IPV6_MROUTE_MULTIPLE_TABLES
			def_bool y

		config KERNEL_IPV6_PIMSM_V2
			def_bool y

	endif

	config KERNEL_IPV6_SEG6_LWTUNNEL
		bool "Enable support for lightweight tunnels"
		default y if !SMALL_FLASH
		help
		  Using lwtunnel (needed for IPv6 segment routing) requires ip-full package.

	config KERNEL_LWTUNNEL_BPF
		def_bool n

endif

#
# Miscellaneous network configuration
#

config KERNEL_NET_L3_MASTER_DEV
	bool "L3 Master device support"
	default y if !SMALL_FLASH
	help
	  This module provides glue between core networking code and device
	  drivers to support L3 master devices like VRF.
  	  Increases the compressed kernel size by ~4kB (as of Linux 6.6).

config KERNEL_XDP_SOCKETS
	bool "XDP sockets support"
	help
	  XDP sockets allows a channel between XDP programs and
	  userspace applications.

config KERNEL_PAGE_POOL
	def_bool n

config KERNEL_PAGE_POOL_STATS
	bool "Page pool stats support"
	depends on KERNEL_PAGE_POOL

config KERNEL_MPTCP
	bool "Multi-Path TCP support"
	default y if !SMALL_FLASH
	help
	   Select this option to enable support for Multi-Path TCP.
	   Increases the compressed kernel size by ~214kB (as of Linux 6.6).

if KERNEL_IPV6
config KERNEL_MPTCP_IPV6
	bool "IPv6 support for Multipath TCP"
	depends on KERNEL_MPTCP
	default KERNEL_MPTCP
endif

#
# NFS related symbols
#
config KERNEL_IP_PNP
	bool "Compile the kernel with rootfs on NFS"
	help
	   If you want to make your kernel boot off a NFS server as root
	   filesystem, select Y here.

if KERNEL_IP_PNP

	config KERNEL_IP_PNP_DHCP
		def_bool y

	config KERNEL_IP_PNP_BOOTP
		def_bool n

	config KERNEL_IP_PNP_RARP
		def_bool n

	config KERNEL_NFS_FS
		def_bool y

	config KERNEL_NFS_V2
		def_bool y

	config KERNEL_NFS_V3
		def_bool y

	config KERNEL_ROOT_NFS
		def_bool y

endif

config KERNEL_BTRFS_FS
	bool "Compile the kernel with built-in BTRFS support"
	help
	  Say Y here if you want to make the kernel to be able to boot off a
	  BTRFS partition.

config KERNEL_EROFS_FS
	bool "Compile the kernel with built-in EROFS support"
	help
	  Say Y here if you want to make the kernel to be able to boot off a
	  EROFS partition.

config KERNEL_EROFS_FS_XATTR
	bool "EROFS XATTR support"

config KERNEL_EROFS_FS_ZIP
	bool
	default y if KERNEL_EROFS_FS

config KERNEL_EROFS_FS_ZIP_LZMA
	bool
	default y if KERNEL_EROFS_FS

menu "Filesystem ACL and attr support options"
	config USE_FS_ACL_ATTR
		bool "Use filesystem ACL and attr support by default"
		default y if !SMALL_FLASH
		help
		  Make using ACLs (e.g. POSIX ACL, NFSv4 ACL) the default
		  for kernel and packages, except old NFS.
		  Also enable userspace extended attribute support
		  by default.  (OpenWrt already has an expection it will be
		  present in the kernel).

	config KERNEL_FS_POSIX_ACL
		bool "Enable POSIX ACL support"
		default y if USE_FS_ACL_ATTR

	config KERNEL_BTRFS_FS_POSIX_ACL
		bool "Enable POSIX ACL for BtrFS Filesystems"
		select KERNEL_FS_POSIX_ACL
		default y if USE_FS_ACL_ATTR

	config KERNEL_EROFS_FS_POSIX_ACL
		bool "Enable POSIX ACL for EROFS Filesystems"
		select KERNEL_FS_POSIX_ACL
		default y if USE_FS_ACL_ATTR

	config KERNEL_EXT4_FS_POSIX_ACL
		bool "Enable POSIX ACL for Ext4 Filesystems"
		select KERNEL_FS_POSIX_ACL
		default y if USE_FS_ACL_ATTR

	config KERNEL_F2FS_FS_POSIX_ACL
		bool "Enable POSIX ACL for F2FS Filesystems"
		select KERNEL_FS_POSIX_ACL
		default y if USE_FS_ACL_ATTR

	config KERNEL_JFFS2_FS_POSIX_ACL
		bool "Enable POSIX ACL for JFFS2 Filesystems"
		select KERNEL_FS_POSIX_ACL
		default y if USE_FS_ACL_ATTR

	config KERNEL_TMPFS_POSIX_ACL
		bool "Enable POSIX ACL for TMPFS Filesystems"
		select KERNEL_FS_POSIX_ACL
		default y if USE_FS_ACL_ATTR

	config KERNEL_CIFS_ACL
		bool "Enable CIFS ACLs"
		select KERNEL_FS_POSIX_ACL
		default y if USE_FS_ACL_ATTR

	config KERNEL_HFS_FS_POSIX_ACL
		bool "Enable POSIX ACL for HFS Filesystems"
		select KERNEL_FS_POSIX_ACL
		default y if USE_FS_ACL_ATTR

	config KERNEL_HFSPLUS_FS_POSIX_ACL
		bool "Enable POSIX ACL for HFS+ Filesystems"
		select KERNEL_FS_POSIX_ACL
		default y if USE_FS_ACL_ATTR

	config KERNEL_NFS_ACL_SUPPORT
		bool "Enable ACLs for NFS"
		default y if USE_FS_ACL_ATTR

	config KERNEL_NFS_V3_ACL_SUPPORT
		bool "Enable ACLs for NFSv3"

	config KERNEL_NFSD_V2_ACL_SUPPORT
		bool "Enable ACLs for NFSDv2"

	config KERNEL_NFSD_V3_ACL_SUPPORT
		bool "Enable ACLs for NFSDv3"

	config KERNEL_REISER_FS_POSIX_ACL
		bool "Enable POSIX ACLs for ReiserFS"
		select KERNEL_FS_POSIX_ACL
		default y if USE_FS_ACL_ATTR

	config KERNEL_XFS_POSIX_ACL
		bool "Enable POSIX ACLs for XFS"
		select KERNEL_FS_POSIX_ACL
		default y if USE_FS_ACL_ATTR

	config KERNEL_JFS_POSIX_ACL
		bool "Enable POSIX ACLs for JFS"
		select KERNEL_FS_POSIX_ACL
		default y if USE_FS_ACL_ATTR

endmenu

config KERNEL_DEVMEM
	bool "/dev/mem virtual device support"
	help
	  Say Y here if you want to support the /dev/mem device.
	  The /dev/mem device is used to access areas of physical
	  memory.

config KERNEL_DEVKMEM
	bool "/dev/kmem virtual device support"
	help
	  Say Y here if you want to support the /dev/kmem device. The
	  /dev/kmem device is rarely used, but can be used for certain
	  kind of kernel debugging operations.

config KERNEL_SQUASHFS_FRAGMENT_CACHE_SIZE
	int "Number of squashfs fragments cached"
	default 2 if (SMALL_FLASH && !LOW_MEMORY_FOOTPRINT)
	default 3

config KERNEL_SQUASHFS_XATTR
	bool "Squashfs XATTR support"

#
# compile optimization setting
#
choice
	prompt "Compiler optimization level"
	default KERNEL_CC_OPTIMIZE_FOR_SIZE if SMALL_FLASH

config KERNEL_CC_OPTIMIZE_FOR_PERFORMANCE
	bool "Optimize for performance"
	help
	  This is the default optimization level for the kernel, building
	  with the "-O2" compiler flag for best performance and most
	  helpful compile-time warnings.

config KERNEL_CC_OPTIMIZE_FOR_SIZE
	bool "Optimize for size"
	help
	  Enabling this option will pass "-Os" instead of "-O2" to
	  your compiler resulting in a smaller kernel.

endchoice

config KERNEL_AUDIT
	bool "Auditing support"

config KERNEL_SECURITY
	bool "Enable different security models"

config KERNEL_SECURITY_NETWORK
	bool "Socket and Networking Security Hooks"
	select KERNEL_SECURITY

config KERNEL_SECURITY_SELINUX
	bool "NSA SELinux Support"
	select KERNEL_SECURITY_NETWORK
	select KERNEL_AUDIT

config KERNEL_SECURITY_SELINUX_BOOTPARAM
	bool "NSA SELinux boot parameter"
	depends on KERNEL_SECURITY_SELINUX
	default y

config KERNEL_SECURITY_SELINUX_DISABLE
	bool "NSA SELinux runtime disable"
	depends on KERNEL_SECURITY_SELINUX

config KERNEL_SECURITY_SELINUX_DEVELOP
	bool "NSA SELinux Development Support"
	depends on KERNEL_SECURITY_SELINUX
	default y

config KERNEL_SECURITY_SELINUX_SIDTAB_HASH_BITS
	int
	depends on KERNEL_SECURITY_SELINUX
	default 9

config KERNEL_SECURITY_SELINUX_SID2STR_CACHE_SIZE
	int
	depends on KERNEL_SECURITY_SELINUX
	default 256

config KERNEL_LSM
	string
	default "lockdown,yama,loadpin,safesetid,integrity,selinux"
	depends on KERNEL_SECURITY_SELINUX

config KERNEL_EROFS_FS_SECURITY
	bool "EROFS Security Labels"
	default y if !SMALL_FLASH
	select KERNEL_EROFS_FS_XATTR

config KERNEL_EXT4_FS_SECURITY
	bool "Ext4 Security Labels"
	default y if !SMALL_FLASH

config KERNEL_F2FS_FS_SECURITY
	bool "F2FS Security Labels"
	default y if !SMALL_FLASH

config KERNEL_UBIFS_FS_SECURITY
	bool "UBIFS Security Labels"
	default y if !SMALL_FLASH

config KERNEL_JFFS2_FS_SECURITY
	bool "JFFS2 Security Labels"
	default y if !SMALL_FLASH

config KERNEL_WERROR
	bool "Compile the kernel with warnings as errors"
	help
	  A kernel build should not cause any compiler warnings, and this
	  enables the '-Werror' (for C) and '-Dwarnings' (for Rust) flags
	  to enforce that rule by default. Certain warnings from other tools
	  such as the linker may be upgraded to errors with this option as
	  well.

	  However, if you have a new (or very old) compiler or linker with odd
	  and unusual warnings, or you have some architecture with problems,
	  you may need to disable this config option in order to
	  successfully build the kernel.
