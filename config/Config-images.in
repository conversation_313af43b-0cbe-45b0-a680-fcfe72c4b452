# SPDX-License-Identifier: GPL-2.0-only
#
# Copyright (C) 2006-2013 OpenWrt.org

menu "Target Images"

	menuconfig TARGET_ROOTFS_INITRAMFS
		bool "ramdisk"
		default y if USES_INITRAMFS
		help
		  Embed the root filesystem into the kernel (initramfs).

		choice
			prompt "Compression"
			default TARGET_INITRAMFS_COMPRESSION_LZMA if TARGET_apm821xx
			default TARGET_INITRAMFS_COMPRESSION_LZMA if TARGET_ath79_mikrotik
			default TARGET_INITRAMFS_COMPRESSION_LZMA if TARGET_lantiq
			default TARGET_INITRAMFS_COMPRESSION_LZMA if TARGET_mpc85xx
			default TARGET_INITRAMFS_COMPRESSION_LZMA if TARGET_ramips
			default TARGET_INITRAMFS_COMPRESSION_ZSTD if TARGET_qualcommax
			default TARGET_INITRAMFS_COMPRESSION_XZ if USES_SEPARATE_INITRAMFS
			default TARGET_INITRAMFS_COMPRESSION_NONE
			depends on TARGET_ROOTFS_INITRAMFS
			help
			  Select ramdisk compression.

			config TARGET_INITRAMFS_COMPRESSION_NONE
				bool "none"

			config TARGET_INITRAMFS_COMPRESSION_GZIP
				bool "gzip"

			config TARGET_INITRAMFS_COMPRESSION_BZIP2
				bool "bzip2"

			config TARGET_INITRAMFS_COMPRESSION_LZMA
				bool "lzma"

			config TARGET_INITRAMFS_COMPRESSION_LZO
				bool "lzo"

			config TARGET_INITRAMFS_COMPRESSION_LZ4
				bool "lz4"

			config TARGET_INITRAMFS_COMPRESSION_XZ
				bool "xz"

			config TARGET_INITRAMFS_COMPRESSION_ZSTD
				depends on !LINUX_5_4
				bool "zstd"
		endchoice

		config EXTERNAL_CPIO
			string
			prompt "Use external cpio" if TARGET_ROOTFS_INITRAMFS
			default ""
			help
			  Kernel uses specified external cpio as INITRAMFS_SOURCE.

		config TARGET_INITRAMFS_FORCE
			bool "Force"
			depends on TARGET_ROOTFS_INITRAMFS
			default n
			help
			  Ignore the initramfs passed by the bootloader.

		config TARGET_ROOTFS_INITRAMFS_SEPARATE
			bool "separate ramdisk"
			depends on USES_SEPARATE_INITRAMFS && TARGET_ROOTFS_INITRAMFS && !TARGET_INITRAMFS_FORCE
			default y if USES_SEPARATE_INITRAMFS
			help
			  Generate separate initrd.cpio instead of embedding it.
			  This is useful for generating images with a dedicated
			  ramdisk e.g. in U-Boot's uImage and uImage.FIT formats.

	comment "Root filesystem archives"

	config TARGET_ROOTFS_CPIOGZ
		bool "cpio.gz"
		default y if USES_CPIOGZ
		help
		  Build a compressed cpio archive of the root filesystem.

	config TARGET_ROOTFS_TARGZ
		bool "tar.gz"
		default y if USES_TARGZ
		help
		  Build a compressed tar archive of the root filesystem.

	comment "Root filesystem images"

	menuconfig TARGET_ROOTFS_EROFS
		bool "erofs"
		default y if USES_EROFS
		select KERNEL_EROFS_FS
		help
		  Build a EROFS root filesystem.

		config TARGET_EROFS_PCLUSTER_SIZE
			int "physical cluster size (in KiB)"
			depends on TARGET_ROOTFS_EROFS
			default 64 if LOW_MEMORY_FOOTPRINT
			default 1024 if (SMALL_FLASH && !LOW_MEMORY_FOOTPRINT)
			default 256
			help
			  Specify the EROFS physical cluster size (must be equal
			  to or a multiple of the filesystem block size).

	menuconfig TARGET_ROOTFS_EXT4FS
		bool "ext4"
		default y if USES_EXT4
		help
		  Build an ext4 root filesystem.

		config TARGET_EXT4_RESERVED_PCT
			int "Percentage of reserved blocks in root filesystem"
			depends on TARGET_ROOTFS_EXT4FS
			default 0
			help
			  Select the percentage of reserved blocks in the root filesystem.

		choice
			prompt "Root filesystem block size"
			default TARGET_EXT4_BLOCKSIZE_4K
			depends on TARGET_ROOTFS_EXT4FS
			help
			  Select the block size of the root filesystem.

			config TARGET_EXT4_BLOCKSIZE_4K
				bool "4k"

			config TARGET_EXT4_BLOCKSIZE_2K
				bool "2k"

			config TARGET_EXT4_BLOCKSIZE_1K
				bool "1k"
		endchoice

		config TARGET_EXT4_BLOCKSIZE
			int
			default 4096 if TARGET_EXT4_BLOCKSIZE_4K
			default 2048 if TARGET_EXT4_BLOCKSIZE_2K
			default 1024 if TARGET_EXT4_BLOCKSIZE_1K
			depends on TARGET_ROOTFS_EXT4FS

		config TARGET_EXT4_JOURNAL
			bool "Create a journaling filesystem"
			depends on TARGET_ROOTFS_EXT4FS
			default n
			help
			  Create an ext4 filesystem with a journal.

	config TARGET_ROOTFS_JFFS2
		bool "jffs2"
		depends on USES_JFFS2
		help
		  Build a JFFS2 root filesystem.

	config TARGET_ROOTFS_JFFS2_NAND
		bool "jffs2 for NAND"
		default y if USES_JFFS2_NAND
		depends on USES_JFFS2_NAND
		help
		  Build a JFFS2 root filesystem for NAND flash.

	menuconfig TARGET_ROOTFS_SQUASHFS
		bool "squashfs"
		default y if USES_SQUASHFS
		help
		  Build a squashfs root filesystem.

		config TARGET_SQUASHFS_BLOCK_SIZE
			int "Block size (in KiB)"
			depends on TARGET_ROOTFS_SQUASHFS
			default 64 if LOW_MEMORY_FOOTPRINT
			default 1024 if (SMALL_FLASH && !LOW_MEMORY_FOOTPRINT)
			default 256
			help
			  Select squashfs block size, must be one of:
			    4, 8, 16, 32, 64, 128, 256, 512, 1024

	menuconfig TARGET_ROOTFS_UBIFS
		bool "ubifs"
		default y if USES_UBIFS
		depends on USES_UBIFS
		help
		  Build a UBIFS root filesystem.

		choice
			prompt "compression"
			default TARGET_UBIFS_COMPRESSION_ZLIB
			depends on TARGET_ROOTFS_UBIFS
			help
			  Select compression type

			config TARGET_UBIFS_COMPRESSION_NONE
				bool "none"

			config TARGET_UBIFS_COMPRESSION_LZO
				bool "lzo"

			config TARGET_UBIFS_COMPRESSION_ZLIB
				bool "zlib"
		endchoice

		config TARGET_UBIFS_FREE_SPACE_FIXUP
			bool "free space fixup" if TARGET_ROOTFS_UBIFS
			default y
			help
			  The filesystem free space has to be fixed up on first mount.

		config TARGET_UBIFS_JOURNAL_SIZE
			string
			prompt "journal size" if TARGET_ROOTFS_UBIFS
			default ""

	config GRUB_IMAGES
		bool "Build GRUB images"
		depends on TARGET_x86
		depends on TARGET_ROOTFS_EROFS || TARGET_ROOTFS_EXT4FS || TARGET_ROOTFS_JFFS2 || TARGET_ROOTFS_SQUASHFS
		select PACKAGE_grub2
		select PACKAGE_grub2-bios-setup
		default n

	config GRUB_EFI_IMAGES
		bool "Build GRUB EFI images"
		depends on TARGET_x86 || TARGET_armsr || TARGET_loongarch64 || TARGET_phytium
		depends on TARGET_ROOTFS_EROFS || TARGET_ROOTFS_EXT4FS || TARGET_ROOTFS_JFFS2 || TARGET_ROOTFS_SQUASHFS
		select PACKAGE_grub2 if TARGET_x86
		select PACKAGE_grub2-efi if TARGET_x86
		select PACKAGE_grub2-bios-setup if TARGET_x86
		select PACKAGE_grub2-efi-arm if TARGET_armsr
		select PACKAGE_grub2-efi-arm if TARGET_phytium_armv8
		select PACKAGE_grub2-efi-loongarch64 if TARGET_loongarch64
		select PACKAGE_kmod-fs-vfat
		default y

	config GRUB_CONSOLE
		bool "Use Console Terminal (in addition to Serial)"
		depends on GRUB_IMAGES || GRUB_EFI_IMAGES
		default y

	config GRUB_BAUDRATE
		int "Serial port baud rate"
		depends on GRUB_IMAGES || GRUB_EFI_IMAGES
		default 38400 if TARGET_x86_generic
		default 115200

	config GRUB_FLOWCONTROL
		bool "Use RTE/CTS on serial console"
		depends on GRUB_IMAGES || GRUB_EFI_IMAGES
		depends on TARGET_SERIAL != ""

	config GRUB_BOOTOPTS
		string "Extra kernel boot options"
		depends on GRUB_IMAGES || GRUB_EFI_IMAGES
		help
		  If you don't know, just leave it blank.

	config GRUB_TIMEOUT
		string "Seconds to wait before booting the default entry"
		depends on GRUB_IMAGES || GRUB_EFI_IMAGES
		default "0"
		help
		  If you don't know, 5 seconds is a reasonable default.

	config GRUB_TITLE
		string "Title for the menu entry in GRUB"
		depends on GRUB_IMAGES || GRUB_EFI_IMAGES
		default "LEDE"
		help
		  This is the title of the GRUB menu entry.
		  If unspecified, it defaults to LEDE.

	config ISO_IMAGES
		bool "Build LiveCD image (ISO)"
		depends on TARGET_x86
		depends on GRUB_IMAGES || GRUB_EFI_IMAGES

	config QCOW2_IMAGES
		bool "Build PVE/KVM image files (QCOW2)"
		depends on TARGET_x86
		depends on GRUB_IMAGES || GRUB_EFI_IMAGES
		select PACKAGE_kmod-e1000

	config VDI_IMAGES
		bool "Build VirtualBox image files (VDI)"
		depends on TARGET_x86
		depends on GRUB_IMAGES || GRUB_EFI_IMAGES
		select PACKAGE_kmod-e1000

	config VMDK_IMAGES
		bool "Build VMware image files (VMDK)"
		depends on TARGET_x86 || TARGET_armsr
		depends on GRUB_IMAGES || GRUB_EFI_IMAGES
		select PACKAGE_kmod-e1000

	config VHDX_IMAGES
		bool "Build Hyper-V image files (VHDX)"
		depends on TARGET_x86
		depends on GRUB_IMAGES || GRUB_EFI_IMAGES
		select PACKAGE_kmod-e1000

	config TARGET_SERIAL
		string "Serial port device"
		depends on TARGET_x86 || TARGET_armsr || TARGET_loongarch64 || TARGET_phytium
		default "ttyS0"

	config TARGET_IMAGES_GZIP
		bool "GZip images"
		depends on TARGET_ROOTFS_EXT4FS || TARGET_x86 || TARGET_armsr || TARGET_malta || TARGET_loongarch64
		default y

	comment "Image Options"

	source "target/linux/*/image/Config.in"
	source "target/linux/*/*/image/Config.in"

	config TARGET_KERNEL_PARTSIZE
		int "Kernel partition size (in MiB)"
		depends on USES_BOOT_PART
		default 8 if TARGET_apm821xx_sata
		default 128 if TARGET_armsr
		default 64 if TARGET_bcm27xx
		default 32 if TARGET_rockchip
		default 16

	config TARGET_ROOTFS_PARTSIZE
		int "Root filesystem partition size (in MiB)"
		depends on USES_ROOTFS_PART || TARGET_ROOTFS_EXT4FS
		default 232 if TARGET_loongarch64
		default 448 if TARGET_mediatek || TARGET_x86
		default 160
		help
		  Select the root filesystem partition size.

	config TARGET_ROOTFS_PARTNAME
		string "Root partition on target device"
		depends on GRUB_IMAGES || GRUB_EFI_IMAGES
		help
		  Override the root partition on the final device. If left empty,
		  it will be mounted by PARTUUID which makes the kernel find the
		  appropriate disk automatically.

	config TARGET_ROOTFS_PERSIST_VAR
		bool "Make /var persistent"
		default n
		help
		  Do not symlink /var to /tmp, so that its content will persist
		  across reboots. When enabled, /var/run will still be linked
		  to /tmp/run.

endmenu
