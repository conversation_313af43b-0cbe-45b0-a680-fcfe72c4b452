From 79c9d7aabae1d1da9eea97d83b61e1517a8a2221 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Fri, 22 Jun 2018 18:59:44 +0200
Subject: [PATCH] ath10k: use tpt LED trigger by default

Use the tpt LED trigger for each created phy led. Ths way LEDs attached
to the ath10k GPIO pins are indicating the phy status and blink on
traffic.

Signed-off-by: <PERSON> <<EMAIL>>
---
 ath10k-5.15/core.h | 4 ++++
 ath10k-5.15/leds.c | 4 +---
 ath10k-5.15/mac.c  | 2 +-
 3 files changed, 6 insertions(+), 4 deletions(-)

--- a/ath10k-5.15/core.h
+++ b/ath10k-5.15/core.h
@@ -1692,6 +1692,10 @@ struct ath10k {
 	u8 csi_data[4096];
 	u16 csi_data_len;
 
+#ifdef CPTCFG_MAC80211_LEDS
+	const char *led_default_trigger;
+#endif
+
 	/* must be last */
 	u8 drv_priv[] __aligned(sizeof(void *));
 };
--- a/ath10k-5.15/leds.c
+++ b/ath10k-5.15/leds.c
@@ -81,9 +81,7 @@ int ath10k_leds_register(struct ath10k *
 
 	ar->leds.cdev.name = ar->leds.label;
 	ar->leds.cdev.brightness_set_blocking = ath10k_leds_set_brightness_blocking;
-
-	/* FIXME: this assignment doesn't make sense as it's NULL, remove it? */
-	ar->leds.cdev.default_trigger = ar->leds.wifi_led.default_trigger;
+	ar->leds.cdev.default_trigger = ar->led_default_trigger;
 
 	ret = led_classdev_register(wiphy_dev(ar->hw->wiphy), &ar->leds.cdev);
 	if (ret)
--- a/ath10k-5.15/mac.c
+++ b/ath10k-5.15/mac.c
@@ -11551,7 +11551,7 @@ int ath10k_mac_register(struct ath10k *a
 	ar->hw->weight_multiplier = ATH10K_AIRTIME_WEIGHT_MULTIPLIER;
 
 #ifdef CPTCFG_MAC80211_LEDS
-	ieee80211_create_tpt_led_trigger(ar->hw,
+	ar->led_default_trigger = ieee80211_create_tpt_led_trigger(ar->hw,
 		IEEE80211_TPT_LEDTRIG_FL_RADIO, ath10k_tpt_blink,
 		ARRAY_SIZE(ath10k_tpt_blink));
 #endif
