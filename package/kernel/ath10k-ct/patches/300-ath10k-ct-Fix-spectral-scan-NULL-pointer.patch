From 0d2e335d780bda1432a9ba719c8200f796d27854 Mon Sep 17 00:00:00 2001
From: <PERSON> <rob<PERSON><PERSON><PERSON>@gmail.com>
Date: Mon, 29 Nov 2021 12:27:12 +0100
Subject: [PATCH] ath10k-ct: Fix spectral scan NULL pointer

If spectral scan support is enabled then ath10k-ct will cause a NULL
pointer due to relay_open() being called with a const callback struct
which is only supported in kernel 5.11 and later.

So, simply check the kernel version and if 5.11 and newer use the const
callback struct, otherwise use the regular struct.

Fixes: 553a3ac ("ath10k-ct: use 5.15 version")
Signed-off-by: <PERSON> <<EMAIL>>
---
 ath10k-5.15/spectral.c | 4 ++++
 1 file changed, 4 insertions(+)

--- a/ath10k-5.15/spectral.c
+++ b/ath10k-5.15/spectral.c
@@ -497,7 +497,11 @@ static int remove_buf_file_handler(struc
 	return 0;
 }
 
+#if LINUX_VERSION_IS_GEQ(5,11,0)
 static const struct rchan_callbacks rfs_spec_scan_cb = {
+#else
+static struct rchan_callbacks rfs_spec_scan_cb = {
+#endif
 	.create_buf_file = create_buf_file_handler,
 	.remove_buf_file = remove_buf_file_handler,
 };
