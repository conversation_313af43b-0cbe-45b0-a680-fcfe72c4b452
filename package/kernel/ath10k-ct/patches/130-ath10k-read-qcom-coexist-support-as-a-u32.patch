From 630df9786fdaeb78c21f1e28c9b70ac83a1b482c Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Sat, 31 Dec 2022 09:24:00 -0500
Subject: [PATCH] ath10k: read qcom,coexist-support as a u32

Read qcom,coexist-support as a u32 instead of a u8

When we set the property to <1> in the DT (as specified in the doc),
"of_property_read_u8" read 0 instead of 1. This is because of the data format. 

By default <1> is written with 32 bits. 
The problem is that the driver is trying to read a u8.

The difference can be visualized using hexdump in a running device:
Default 32 bits output:
=======================
0000000 0000 0100
0000004

8 bits output:
==============
0000000 0001
0000001

By changing "of_property_read_u8" by "of_property_read_u32", the driver
is aligned with the documentation and is able to read the value without
modifying the DT.

The other solution would be to force the value in the DT to be saved as
an 8 bits value (qcom,coexist-support = /bits/ 8 <1>), 
which is against the doc and less intuitive.

Validation:
===========
The patch was tested on a real device and we can see in the debug logs
that the feature is properly initialized:

[  109.102097] ath10k_ahb a000000.wifi: boot coex_support 1 coex_gpio_pin 52

Signed-off-by: Vincent Tremblay <<EMAIL>>

--- a/ath10k-5.15/core.c
+++ b/ath10k-5.15/core.c
@@ -2798,14 +2798,14 @@ done:
 static void ath10k_core_fetch_btcoex_dt(struct ath10k *ar)
 {
 	struct device_node *node;
-	u8 coex_support = 0;
+	u32 coex_support = 0;
 	int ret;
 
 	node = ar->dev->of_node;
 	if (!node)
 		goto out;
 
-	ret = of_property_read_u8(node, "qcom,coexist-support", &coex_support);
+	ret = of_property_read_u32(node, "qcom,coexist-support", &coex_support);
 	if (ret) {
 		ar->coex_support = true;
 		goto out;
