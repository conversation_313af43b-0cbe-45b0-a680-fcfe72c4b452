From f7d6edafe4358e3880a26775cfde4cd5c71ba063 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Wed, 5 Jul 2023 01:30:29 +0200
Subject: [PATCH] ath10k: always use mac80211 loss detection

ath10k does not report excessive loss in case of broken block-ack
sessions. The loss is communicated to the host-os, but ath10k does not
trigger a low-ack events by itself.

The mac80211 framework for loss detection however detects this
circumstance well in case of ath10k. So use it regardless of ath10k's
own loss detection mechanism.

Signed-off-by: <PERSON> <<EMAIL>>
---
 ath10k-5.15/mac.c | 1 -
 1 file changed, 1 deletion(-)

--- a/ath10k-5.15/mac.c
+++ b/ath10k-5.15/mac.c
@@ -11246,7 +11246,6 @@ int ath10k_mac_register(struct ath10k *a
 	ieee80211_hw_set(ar->hw, CHANCTX_STA_CSA);
 	ieee80211_hw_set(ar->hw, QUEUE_CONTROL);
 	ieee80211_hw_set(ar->hw, SUPPORTS_TX_FRAG);
-	ieee80211_hw_set(ar->hw, REPORTS_LOW_ACK);
 
 	if (!test_bit(ATH10K_FLAG_RAW_MODE, &ar->dev_flags))
 		ieee80211_hw_set(ar->hw, SW_CRYPTO_CONTROL);
