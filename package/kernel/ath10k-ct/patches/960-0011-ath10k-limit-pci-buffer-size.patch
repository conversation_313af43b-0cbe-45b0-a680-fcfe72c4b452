--- a/ath10k-5.15/pci.c
+++ b/ath10k-5.15/pci.c
@@ -131,7 +131,11 @@ static const struct ce_attr pci_host_ce_
 		.flags = CE_ATTR_FLAGS,
 		.src_nentries = 0,
 		.src_sz_max = 2048,
+#ifndef CONFIG_ATH10K_SMALLBUFFERS
 		.dest_nentries = 512,
+#else
+		.dest_nentries = 128,
+#endif
 		.recv_cb = ath10k_pci_htt_htc_rx_cb,
 	},
 
@@ -140,7 +144,11 @@ static const struct ce_attr pci_host_ce_
 		.flags = CE_ATTR_FLAGS,
 		.src_nentries = 0,
 		.src_sz_max = 2048,
+#ifndef CONFIG_ATH10K_SMALLBUFFERS
 		.dest_nentries = 128,
+#else
+		.dest_nentries = 64,
+#endif
 		.recv_cb = ath10k_pci_htc_rx_cb,
 	},
 
@@ -167,7 +175,11 @@ static const struct ce_attr pci_host_ce_
 		.flags = CE_ATTR_FLAGS,
 		.src_nentries = 0,
 		.src_sz_max = 512,
+#ifndef CONFIG_ATH10K_SMALLBUFFERS
 		.dest_nentries = 512,
+#else
+		.dest_nentries = 128,
+#endif
 		.recv_cb = ath10k_pci_htt_rx_cb,
 	},
 
@@ -192,7 +204,11 @@ static const struct ce_attr pci_host_ce_
 		.flags = CE_ATTR_FLAGS,
 		.src_nentries = 0,
 		.src_sz_max = 2048,
+#ifndef CONFIG_ATH10K_SMALLBUFFERS
 		.dest_nentries = 128,
+#else
+		.dest_nentries = 96,
+#endif
 		.recv_cb = ath10k_pci_pktlog_rx_cb,
 	},
 
