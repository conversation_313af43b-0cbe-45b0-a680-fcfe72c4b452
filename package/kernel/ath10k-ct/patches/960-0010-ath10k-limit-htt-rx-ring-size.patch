--- a/ath10k-5.15/htt.h
+++ b/ath10k-5.15/htt.h
@@ -237,7 +237,11 @@ enum htt_rx_ring_flags {
 };
 
 #define HTT_RX_RING_SIZE_MIN 128
+#ifndef CONFIG_ATH10K_SMALLBUFFERS
 #define HTT_RX_RING_SIZE_MAX 2048
+#else
+#define HTT_RX_RING_SIZE_MAX 512
+#endif
 #define HTT_RX_RING_SIZE HTT_RX_RING_SIZE_MAX
 #define HTT_RX_RING_FILL_LEVEL (((HTT_RX_RING_SIZE) / 2) - 1)
 #define HTT_RX_RING_FILL_LEVEL_DUAL_MAC (HTT_RX_RING_SIZE - 1)
