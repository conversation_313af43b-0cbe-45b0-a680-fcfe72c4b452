#!/bin/sh /etc/rc.common
# Copyright (C) 2014 OpenWrt.org

START=10
USE_PROCD=1

validate_system_section() {
	uci_load_validate system system "$1" "$2" \
		'hostname:string:LEDE' \
		'conloglevel:uinteger' \
		'buffersize:uinteger' \
		'timezone:string:UTC' \
		'zonename:string'
}

system_config() {
	[ "$2" = 0 ] || {
		echo "validation failed"
		return 1
	}

	echo "$hostname" > /proc/sys/kernel/hostname
	[ -z "$conloglevel" -a -z "$buffersize" ] || dmesg ${conloglevel:+-n $conloglevel} ${buffersize:+-s $buffersize}
	echo "$timezone" > /tmp/TZ
	[ -n "$zonename" ] && [ -f "/usr/share/zoneinfo/${zonename// /_}" ] \
		&& ln -sf "/usr/share/zoneinfo/${zonename// /_}" /tmp/localtime \
		&& rm -f /tmp/TZ

	# apply timezone to kernel
	hwclock -u --systz
}

reload_service() {
	config_load system
	config_foreach validate_system_section system system_config
}

service_triggers() {
	procd_add_reload_trigger "system"
	procd_add_validation validate_system_section
}

start_service() {
	reload_service
}
