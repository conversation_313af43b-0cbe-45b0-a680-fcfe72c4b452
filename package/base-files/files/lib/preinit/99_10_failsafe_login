# Copyright (C) 2006-2015 OpenWrt.org
# Copyright (C) 2010 Vertical Communications

failsafe_shell() {
	local consoles="$(sed -e 's/ /\n/g' /proc/cmdline | grep '^console=' | sed -e 's/^console=//' -e 's/,.*//')"
	[ -n "$consoles" ] || consoles=console
	for console in $consoles; do
		[ -c "/dev/$console" ] && while true; do
			ash --login <"/dev/$console" >"/dev/$console" 2>"/dev/$console"
			sleep 1
		done &
	done
}

boot_hook_add failsafe failsafe_shell
