# Copyright (C) 2006 OpenWrt.org
# Copyright (C) 2010 Vertical Communications

missing_lines() {
	local file1 file2 line
	file1="$1"
	file2="$2"
	oIFS="$IFS"
	IFS=":"
	while read line; do
		set -- $line
		grep -q "^$1:" "$file2" || echo "$line"
	done < "$file1"
	IFS="$oIFS"
}

do_mount_root() {
	mount_root
	boot_run_hook preinit_mount_root
	[ ! -f /etc/bench.log ] && touch /etc/bench.log
	[ -f /sysupgrade.tgz -o -f /tmp/sysupgrade.tar ] && {
		echo "- config restore -"
		cp /etc/passwd /etc/group /etc/shadow /tmp
		cd /
		[ -f /sysupgrade.tgz ] && tar xzf /sysupgrade.tgz
		[ -f /tmp/sysupgrade.tar ] && tar xf /tmp/sysupgrade.tar
		missing_lines /tmp/passwd /etc/passwd >> /etc/passwd
		missing_lines /tmp/group /etc/group >> /etc/group
		missing_lines /tmp/shadow /etc/shadow >> /etc/shadow
		rm /tmp/passwd /tmp/group /tmp/shadow
		# Prevent configuration corruption on a power loss
		sync
	}
}

[ "$INITRAMFS" = "1" ] || boot_hook_add preinit_main do_mount_root
