From 786d501c32b9341d381b3371234213f92d948b66 Mon Sep 17 00:00:00 2001
From: Yangbo Lu <<EMAIL>>
Date: Fri, 13 Mar 2020 18:15:36 +0800
Subject: [PATCH] Remove tclsh checking

The tclsh is no longer needed for byte swapping.

Signed-off-by: Yangbo Lu <<EMAIL>>
---
 Makefile | 6 ------
 1 file changed, 6 deletions(-)

diff --git a/Makefile b/Makefile
index 9183e19..2832ab2 100644
--- a/Makefile
+++ b/Makefile
@@ -12,15 +12,9 @@ BOARDS = b4420qds b4860qds \
 	 t1024qds t1023rdb t1024rdb t1040rdb t1042rdb t1042rdb_pi t1040qds \
 	 t2080rdb t2080qds t2081qds t4240qds t4240rdb t1040d4rdb t1042d4rdb
 
-TCLSH := $(shell command -v tclsh 2> /dev/null)
-
 VER = $(shell git describe --tags)
 
 all install clean:
-ifndef TCLSH
-	$(error "tclsh is not available. please  install it.")
-	exit 1
-endif
 	@for board in $(BOARDS); do \
 		$(MAKE) -C $$board $@ DESTDIR=$(DESTDIR)/$$board; \
 	done
-- 
2.7.4

