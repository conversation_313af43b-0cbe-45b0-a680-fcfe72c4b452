# AP6275S_NVRAM_V1.8_20210824
# AP6275S v00 WLBGA reference board, iPA version.

# SSID generated using <PERSON>'s boardssid.py script:
# ********************SUMMARY********************
# Board Name: AP6275S_V00
#SSID: 0x086d
#macmid: 0x02df
# Successfully made SSID entry in sromdefs.tcl.
# Successfully made macmid entry in sromdefs.tcl.
# Successfully made SSID entry in tblssid.py.
# *************************************************
# $ Copyright Broadcom $
#
#
# <<Broadcom-WL-IPTag/Proprietary:>>
NVRAMRev=$Rev: 844050 $
sromrev=11
boardrev=0x1213
boardtype=0x08ed
boardflags=0x00400201
boardflags2=0xc0800000
boardflags3=0x40002180
#boardnum=57410
macaddr=00:90:4c:12:d0:01
jtag_irw=38

#Regulatory specific
ccode=0
regrev=0

# Board specific
vendid=0x14e4
devid=0x449d
manfid=0x2d0
antswitch=0
pdgain5g=0
pdgain2g=0
aa2g=3
aa5g=3
agbg0=2
agbg1=2
aga0=2
aga1=2
extpagain2g=2
extpagain5g=2
rxgains2gelnagaina0=0
rxgains2gtrisoa0=0
rxgains2gtrelnabypa0=0
rxgains5gelnagaina0=0
rxgains5gtrisoa0=0
rxgains5gtrelnabypa0=0
rxgains5gmelnagaina0=0
rxgains5gmtrisoa0=0
rxgains5gmtrelnabypa0=0
rxgains5ghelnagaina0=0
rxgains5ghtrisoa0=0
rxgains5ghtrelnabypa0=0
rxgains2gelnagaina1=0
rxgains2gtrisoa1=0
rxgains2gtrelnabypa1=0
rxgains5gelnagaina1=0
rxgains5gtrisoa1=0
rxgains5gtrelnabypa1=0
rxgains5gmelnagaina1=0
rxgains5gmtrisoa1=0
rxgains5gmtrelnabypa1=0
rxgains5ghelnagaina1=0
rxgains5ghtrisoa1=0
rxgains5ghtrelnabypa1=0

#RSSI related
# 2G
rssicorrnorm_c0=4,4
rssicorrnorm_c1=4,4
# 5G
rssicorrnorm5g_c0=5,5,5,5,5,5,5,5,5,5,5,5
rssicorrnorm5g_c1=4,4,4,4,4,4,4,4,4,4,4,4


#Two range TSSI
tworangetssi2g=0
tworangetssi5g=0
lowpowerrange2g=0
lowpowerrange5g=0
low_adc_rate_en=1
# NOTE :================================================================================
# To run TPC with Two Range TSSI ,set  tworangetssi2g = 1 and lowpowerrange2g = 0
# To run TPC with Single Range TSSI, set tworangetssi2g = 0
# To run TPC please READ instructions near pa2ga0 and pa2ga1 as well
# To generate PA params for Low Range set  tworangetssi2g = 0 and lowpowerrange2g to 1
# To generate PA params for High Range set  tworangetssi2g = 1 and lowpowerrange2g to 1
# ======================================================================================

#Related to FW Download. Host may use this
nocrc=1

otpimagesize=502

xtalfreq=37400

txchain=3
rxchain=3

cckdigfilttype=2

#bit mask for slice capability bit 0:2G bit 1:5G
bandcap=3

#TXBF Related
rpcal2g=0
rpcal5gb0=0
rpcal5gb1=0
rpcal5gb2=0
rpcal5gb3=0


#FDSS Related
fdss_level_2g=4,4
fdss_interp_en=1
fdss_level_5g=4,4
fdss_level_11ax_2g=3,3
fdss_level_11ax_2g_ch1=3,3
fdss_level_11ax_2g_ch11=3,3
fdss_level_11ax_5g=3,3

#Tempsense Related
tempthresh=255
tempoffset=40
rawtempsense=0x1ff
phycal_tempdelta=15
temps_period=15
temps_hysteresis=15

#------------- TSSI Related -------------

tssipos2g=1
tssipos5g=1
AvVmid_c0=2,127,4,92,4,91,4,91,4,94
AvVmid_c1=2,127,4,93,4,93,4,95,3,110
# CCK in case of multi mode 2
pa2gccka0=-107,8241,-929
pa2gccka1=-86,8682,-961
# OFDM in case of multi_mode 2
pa2ga0=-92,7647,-831
pa2ga1=-115,7023,-766
pa5ga0=-175,5735,-699,-194,5574,-689,-160,6066,-727,-176,5834,-714
pa5ga1=-163,5928,-712,-193,5622,-695,-188,5523,-688,-170,6097,-749

# Max power and offsets
maxp2ga0=94
maxp2ga1=86
maxp5ga0=69,68,68,69
maxp5ga1=69,68,68,69
subband5gver=0x4
paparambwver=3
cckpwroffset0=0
cckpwroffset1=0
pdoffset40ma0=0x0000
pdoffset80ma0=0x0100
pdoffset40ma1=0x1111
pdoffset80ma1=0x1010
cckbw202gpo=0x1111
cckbw20ul2gpo=0x0000
mcsbw202gpo=0x77544331
mcsbw402gpo=0x00000000
dot11agofdmhrbw202gpo=0x4433
ofdmlrbw202gpo=0x1111
mcsbw205glpo=0x66200000
mcsbw405glpo=0x94100000
mcsbw805glpo=0x99221111
mcsbw1605glpo=0
mcsbw205gmpo=0x66200000
mcsbw405gmpo=0x94100000
mcsbw805gmpo=0x99221111
mcsbw1605gmpo=0
mcsbw205ghpo=0x66200000
mcsbw405ghpo=0xA5211111
mcsbw805ghpo=0xBB221111
powoffs2gtna0=0,0,0,0,0,0,0,0,0,0,0,0,0,0
powoffs2gtna1=-1,-1,-1,0,0,0,0,0,0,0,0,0,0,0
mcs1024qam2gpo=0xCCAA
mcs1024qam5glpo=0xDDDDAA
mcs1024qam5gmpo=0xDDDDAA
mcs1024qam5ghpo=0xDDFFCC
mcs1024qam5gx1po=0xEECCCC
mcs1024qam5gx2po=0xEECCCC
mcs8poexp=0
mcs9poexp=0
mcs10poexp=0

# 5G power offset per channel for band edge channel
powoffs5g20mtna0=0,0,0,0,0,0,0
powoffs5g20mtna1=0,0,0,0,0,0,0
powoffs5g40mtna0=0,0,0,0,0
powoffs5g40mtna1=0,0,0,0,0
powoffs5g80mtna0=0,0,0,0,0
powoffs5g80mtna1=0,0,0,0,0
mcs11poexp=0

#LTE Coex Related
ltecxmux=0
ltecxpadnum=0x0504
ltecxfnsel=0x44
ltecxgcigpio=0x04
#OOB params
#device_wake_opt=1
host_wake_opt=0

# SWCTRL Related

swctrlmap_2g=0x10101010,0x06030401,0x04011010,0x000000,0x3FF
swctrlmapext_2g=0x00000000,0x00000000,0x00000000,0x000000,0x000
swctrlmap_5g=0x80408040,0x21240120,0x01208040,0x000000,0x3FF
swctrlmapext_5g=0x00000000,0x00000000,0x00000000,0x000000,0x000
clb2gslice0core0=0x01b
clb2gslice1core0=0x000
clb5gslice0core0=0x064
clb5gslice1core0=0x000
clb2gslice0core1=0x056
clb2gslice1core1=0x000
clb5gslice0core1=0x0a1
clb5gslice1core1=0x000
btc_prisel_ant_mask=0x2
clb_swctrl_smask_ant0=0x27f
clb_swctrl_smask_ant1=0x2f7


#BT Coex 1:TDM
btc_mode=1

# --- PAPD Cal related params ----
txwbpapden=0 # 0:NBPAPD 1:WBPAPD
# NB PAPD Cal params
nb_eps_offset=470,470
nb_bbmult=66,66
nb_papdcalidx=6,6
nb_txattn=2,2
nb_rxattn=1,1
nb_eps_stopidx=63
epsilonoff_5g20_c0=2,2,2,0
epsilonoff_5g40_c0=2,2,2,0
epsilonoff_5g80_c0=2,2,2,0
epsilonoff_5g20_c1=0,0,-2,-2
epsilonoff_5g40_c1=0,0,0,-2
epsilonoff_5g80_c1=0,0,-2,-2
epsilonoff_2g20_c0=0
epsilonoff_2g20_c1=0

# energy detect threshold
ed_thresh2g=-67
ed_thresh5g=-67
# energy detect threshold for EU
eu_edthresh2g=-67
eu_edthresh5g=-67

#rpcal coef for imptxbf
rpcal5gb0=238
rpcal5gb1=228
rpcal5gb2=222
rpcal5gb3=229
rpcal2g=15
ocl=0
bt_coex_chdep_div=1

# OLPC Related
disable_olpc=0
olpc_thresh5g=32
olpc_anchor5g=40
olpc_thresh2g=32
olpc_anchor2g=40

#PAPR related
paprdis=0
paprrmcsgamma2g=500,550,550,-1,-1,-1,-1,-1,-1,-1,-1,-1
paprrmcsgain2g=128,128,128,0,0,0,0,0,0,0,0,0
paprrmcsgamma2g_ch13=500,550,550,-1,-1,-1,-1,-1,-1,-1,-1,-1
paprrmcsgain2g_ch13=128,128,128,0,0,0,0,0,0,0,0,0
paprrmcsgamma2g_ch1=500,550,550,-1,-1,-1,-1,-1,-1,-1,-1,-1
paprrmcsgain2g_ch1=128,128,128,0,0,0,0,0,0,0,0,0
paprrmcsgamma5g20=500,500,500,-1,-1,-1,-1,-1,-1,-1,-1,-1
paprrmcsgain5g20=128,128,128,0,0,0,0,0,0,0,0,0
paprrmcsgamma5g40=600,600,600,-1,-1,-1,-1,-1,-1,-1,-1,-1
paprrmcsgain5g40=128,128,128,0,0,0,0,0,0,0,0,0
paprrmcsgamma5g80=-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1
paprrmcsgain5g80=0,0,0,0,0,0,0,0,0,0,0,0

# Enable papd for cck when target pwr ge 16dBm
cckpapd_pwrthresh=64

## ULOFDMA Board limit PPRs for 2G20 ##
ruppr2g20bpska0=0x8421
ruppr2g20bpska1=0x8421
ruppr2g20qpska0=0x8421
ruppr2g20qpska1=0x8421
ruppr2g20qam16a0=0x10842
ruppr2g20qam16a1=0x10842
ruppr2g20qam64a0=0x18C63
ruppr2g20qam64a1=0x18C63
ruppr2g20qam256a0=0x21084
ruppr2g20qam256a1=0x21084
ruppr2g20qam1024a0=0x5294A
ruppr2g20qam1024a1=0x5294A
## ULOFDMA Board limit PPRs for 5G20 ##
ruppr5g20bpska0=0x18000
ruppr5g20bpska1=0x18000
ruppr5g20qpska0=0x18000
ruppr5g20qpska1=0x18000
ruppr5g20qam16a0=0x18000
ruppr5g20qam16a1=0x18000
ruppr5g20qam64a0=0x18842
ruppr5g20qam64a1=0x18842
ruppr5g20qam256a0=0x318C6
ruppr5g20qam256a1=0x318C6
ruppr5g20qam1024a0=0x6318C
ruppr5g20qam1024a1=0x6318C
## ULOFDMA Board limit PPRs for 5G40 ##
ruppr5g40bpska0=0x308421
ruppr5g40bpska1=0x308421
ruppr5g40qpska0=0x308421
ruppr5g40qpska1=0x308421
ruppr5g40qam16a0=0x308421
ruppr5g40qam16a1=0x308421
ruppr5g40qam64a0=0x308421
ruppr5g40qam64a1=0x308421
ruppr5g40qam256a0=0x739CE7
ruppr5g40qam256a1=0x739CE7
ruppr5g40qam1024a0=0xD6B5AD
ruppr5g40qam1024a1=0xD6B5AD
## ULOFDMA Board limit PPRs for 5G80 ##
ruppr5g80bpska0=0x4108421
ruppr5g80bpska1=0x4108421
ruppr5g80qpska0=0x4108421
ruppr5g80qpska1=0x4108421
ruppr5g80qam16a0=0x4108421
ruppr5g80qam16a1=0x4108421
ruppr5g80qam64a0=0x8108421
ruppr5g80qam64a1=0x8108421
ruppr5g80qam256a0=0x1C7398C6
ruppr5g80qam256a1=0x1C7398C6
ruppr5g80qam1024a0=0x38C6318C
ruppr5g80qam1024a1=0x38C6318C

muxenab=0x10

# ###########  BTC Dynctl profile params  ############
# flags:bit0 - dynctl enabled, bit1 dynamic desense, bit2 dynamic mode, bit 3 TX power control
#btcdyn_flags=1

#btcdyn_default_btc_mode=5
#btcdyn_msw_rows=0
#btcdyn_dsns_rows=0
#btc_params1007=100
#btc_params1017=4
