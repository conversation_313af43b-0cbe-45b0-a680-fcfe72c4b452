--- a/Makefile	2019-06-24 17:38:56.000000000 +0200
+++ b/Makefile	2019-06-24 17:39:04.000000000 +0200
@@ -1,6 +1,5 @@
 
-CC = gcc
-CFLAGS = -I. -fPIC -O -g -Wall -Werror -Wno-unused-variable -Wno-unused-parameter
+CFLAGS += -I.
 
 COMMON_PROG_OBJS = \
 	call_qcsapi.o	\
@@ -49,24 +48,25 @@
 LIB_LDNAME = lib$(LIB_NAME).so
 LIB_SONAME = $(LIB_LDNAME).1
 LIB_REALNAME = $(LIB_LDNAME).1.0.1
+LDFLAGS += -L. -l$(LIB_NAME)
 
 c_rpc_qcsapi_sample: ${SOCKET_C_SAMPLE_OBJS:%=build/%} $(LIB_REALNAME)
-	${CC} $(filter %.o, $^) -L. -l$(LIB_NAME) -o $@
+	${CC} $(filter %.o, $^) $(LDFLAGS) -o $@
 
 qcsapi_pcie: ${PCIE_PROG_OBJS:%=build/%} $(LIB_REALNAME)
-	${CC} $(filter %.o, $^) -L. -l$(LIB_NAME) -o $@
+	${CC} $(filter %.o, $^) $(LDFLAGS) -o $@
 
 qcsapi_pcie_static: ${PCIE_PROG_OBJS:%=build/%} ${LIB_OBJS}
 	${CC} $(filter %.o, $^) -o $@
 
 qcsapi_sockrpc: ${SOCKET_PROG_OBJS:%=build/%} $(LIB_REALNAME)
-	${CC} $(filter %.o, $^) -L. -l$(LIB_NAME) -o $@
+	${CC} $(filter %.o, $^) $(LDFLAGS) -o $@
 
 qcsapi_sockrpc_static: ${SOCKET_PROG_OBJS:%=build/%} ${LIB_OBJS}
 	${CC} $(filter %.o, $^) -o $@
 
 qcsapi_sockraw: ${SOCKET_RAW_PROG_OBJS:%=build/%} $(LIB_REALNAME)
-	${CC} $(filter %.o, $^) -L. -l$(LIB_NAME) -o $@
+	${CC} $(filter %.o, $^) $(LDFLAGS) -o $@
 
 qcsapi_sockraw_static: ${SOCKET_RAW_PROG_OBJS:%=build/%} ${LIB_OBJS}
 	${CC} $(filter %.o, $^) -o $@
