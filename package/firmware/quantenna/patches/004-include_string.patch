--- a/qcsapi_rpc_common/common/rpc_raw.c	2015-03-31 07:49:58.000000000 +0200
+++ b/qcsapi_rpc_common/common/rpc_raw.c	2016-04-14 00:25:02.000000000 +0200
@@ -12,6 +12,7 @@
 #include <errno.h>
 #include <stdio.h>
 #include <qcsapi_rpc_common/common/rpc_raw.h>
+#include <string.h>
 
 int qrpc_set_prot_filter(const int sock, const short prot)
 {
--- a/qcsapi_rpc/client/socket/qcsapi_socket_rpc_client.c	2015-03-31 07:49:58.000000000 +0200
+++ b/qcsapi_rpc/client/socket/qcsapi_socket_rpc_client.c	2016-04-14 00:21:38.000000000 +0200
@@ -48,6 +48,7 @@
 #include <qcsapi_rpc_common/client/find_host_addr.h>
 #include <qcsapi_rpc/client/qcsapi_rpc_client.h>
 #include <qcsapi_rpc/generated/qcsapi_rpc.h>
+#include <string.h>
 
 static int client_qcsapi_get_udp_retry_timeout(int *argc, char ***argv)
 {
