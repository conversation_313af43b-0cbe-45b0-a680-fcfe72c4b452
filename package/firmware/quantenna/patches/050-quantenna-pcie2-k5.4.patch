diff -Naur quantenna_gpl_v37.3.2.44/drivers/pcie2/host/common/qdpc_init.c quantenna_gpl_v37.3.2.44-mod/drivers/pcie2/host/common/qdpc_init.c
--- quantenna_gpl_v37.3.2.44/drivers/pcie2/host/common/qdpc_init.c	2018-03-16 15:08:37.000000000 +0200
+++ quantenna_gpl_v37.3.2.44-mod/drivers/pcie2/host/common/qdpc_init.c	2020-08-02 08:47:22.352332685 +0300
@@ -691,7 +691,7 @@
 
 				/* add code to reboot the while QCA system here*/
 				printk("%s: Attempting to reboot QCA system.\n", __func__);
-				machine_restart(NULL);
+				emergency_restart();
 				break;
 			}
 			msleep(500);
diff -Naur quantenna_gpl_v37.3.2.44/drivers/pcie2/host/common/qdpc_init.h quantenna_gpl_v37.3.2.44-mod/drivers/pcie2/host/common/qdpc_init.h
--- quantenna_gpl_v37.3.2.44/drivers/pcie2/host/common/qdpc_init.h	2018-03-16 15:08:37.000000000 +0200
+++ quantenna_gpl_v37.3.2.44-mod/drivers/pcie2/host/common/qdpc_init.h	2020-08-02 07:38:28.814212107 +0300
@@ -42,6 +42,8 @@
 #define	QDPC_ROW_INCR_OFFSET	0x04
 #undef	QDPC_CS_DEBUG
 
+#define mmiowb()		do { } while (0)
+
 extern unsigned int (*qdpc_pci_readl)(void *addr);
 extern void (*qdpc_pci_writel)(unsigned int val, void *addr);
 
diff -Naur quantenna_gpl_v37.3.2.44/drivers/pcie2/host/common/qdpc_pcie.c quantenna_gpl_v37.3.2.44-mod/drivers/pcie2/host/common/qdpc_pcie.c
--- quantenna_gpl_v37.3.2.44/drivers/pcie2/host/common/qdpc_pcie.c	2018-03-16 15:08:37.000000000 +0200
+++ quantenna_gpl_v37.3.2.44-mod/drivers/pcie2/host/common/qdpc_pcie.c	2020-08-02 07:42:31.174353547 +0300
@@ -29,7 +29,7 @@
 #include <asm/byteorder.h>
 #include <linux/pci.h>
 #include <linux/moduleparam.h>
-#include <asm-generic/pci-dma-compat.h>
+#include <linux/pci-dma-compat.h>
 #include <linux/module.h>
 
 #include "qdpc_config.h"
diff -Naur quantenna_gpl_v37.3.2.44/drivers/pcie2/host/common/topaz_vnet.c quantenna_gpl_v37.3.2.44-mod/drivers/pcie2/host/common/topaz_vnet.c
--- quantenna_gpl_v37.3.2.44/drivers/pcie2/host/common/topaz_vnet.c	2018-03-16 15:08:37.000000000 +0200
+++ quantenna_gpl_v37.3.2.44-mod/drivers/pcie2/host/common/topaz_vnet.c	2020-08-05 10:40:48.405527180 +0300
@@ -21,6 +21,8 @@
 #define EXPORT_SYMTAB
 #endif
 
+#define NET_NAME_USER		3	/* provided by user-space */
+
 #include <linux/module.h>
 #include <linux/kernel.h>
 #include <linux/string.h>
@@ -50,13 +52,13 @@
 #include "qdpc_init.h"
 #include "qdpc_debug.h"
 #include "qdpc_regs.h"
-#include "nss_api_if.h"
+//#include "nss_api_if.h"
 #include "qdpc_version.h"
 
 #define DRV_NAME	"qdpc-host"
 
 #ifndef DRV_VERSION
-#define DRV_VERSION	"1.0"
+#define DRV_VERSION	"1.0-k5.4-openwrt-nonss-tc"
 #endif
 
 #define DRV_AUTHOR	"Quantenna Communications Inc."
@@ -73,8 +75,6 @@
 static int __sram_text skb2rbd_attach(struct net_device *ndev, uint16_t i, uint32_t wrap);
 static irqreturn_t vmac_interrupt(int irq, void *dev_id);
 static void vmac_tx_timeout(struct net_device *ndev);
-static int vmac_get_settings(struct net_device *ndev, struct ethtool_cmd *cmd);
-static int vmac_set_settings(struct net_device *ndev, struct ethtool_cmd *cmd);
 static void vmac_get_drvinfo(struct net_device *ndev, struct ethtool_drvinfo *info);
 static void free_tx_skbs(struct vmac_priv *vmp);
 static void init_tx_bd(struct vmac_priv *vmp);
@@ -824,7 +824,7 @@
 
 		dump_rx_bd(vmp);
 
-		ndev->last_rx = jiffies;
+        	netif_trans_update(ndev);
 
 		/*
 		 * We are done with the current buffer attached to this descriptor, so attach a new
@@ -1180,7 +1180,7 @@
 inline static void vmac_tx_timeout(struct net_device *ndev)
 {
 	printk(KERN_ERR "%s: vmac_tx_timeout: ndev=%p\n", ndev->name, ndev);
-	ndev->trans_start = jiffies;
+	netif_trans_update(ndev);
 }
 
 #ifdef RC_TXDONE_TIMER
@@ -1202,21 +1202,6 @@
 }
 #endif
 
-/* ethtools support */
-static int vmac_get_settings(struct net_device *ndev, struct ethtool_cmd *cmd)
-{
-	return -EINVAL;
-}
-
-static int vmac_set_settings(struct net_device *ndev, struct ethtool_cmd *cmd)
-{
-
-	if (!capable(CAP_NET_ADMIN))
-		return -EPERM;
-
-	return -EINVAL;
-}
-
 static int vmac_ioctl(struct net_device *ndev, struct ifreq *rq, int cmd)
 {
 	return -EINVAL;
@@ -1233,13 +1218,6 @@
 	info->regdump_len = 0;
 }
 
-static const struct ethtool_ops vmac_ethtool_ops = {
-	.get_settings = vmac_get_settings,
-	.set_settings = vmac_set_settings,
-	.get_drvinfo = vmac_get_drvinfo,
-	.get_link = ethtool_op_get_link,
-};
-
 static const struct net_device_ops vmac_device_ops = {
 	.ndo_open = vmac_open,
 	.ndo_stop = vmac_close,
@@ -1254,7 +1232,7 @@
 	struct net_device * ndev;
 
         /* Allocate device structure */
-	ndev = alloc_netdev(sizeof(struct vmac_priv), vmaccfg.ifname, ether_setup);
+	ndev = alloc_netdev(sizeof(struct vmac_priv), vmaccfg.ifname, NET_NAME_USER ,ether_setup);
 	if(!ndev)
 		printk(KERN_ERR "%s: alloc_etherdev failed\n", vmaccfg.ifname);
 
@@ -1297,7 +1275,6 @@
 
 	ndev->netdev_ops = &vmac_device_ops;
 	ndev->tx_queue_len = QTN_GLOBAL_INIT_EMAC_TX_QUEUE_LEN;
-	SET_ETHTOOL_OPS(ndev, &vmac_ethtool_ops);
 
 	/* Initialize private data */
 	vmp = netdev_priv(ndev);
