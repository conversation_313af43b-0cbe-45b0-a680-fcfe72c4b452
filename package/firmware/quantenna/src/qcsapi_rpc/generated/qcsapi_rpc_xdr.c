/*
 * Please do not edit this file.
 * It was generated using rpcgen.
 */

#include "qcsapi_rpc.h"

bool_t
xdr_str (XDR *xdrs, str *objp)
{
	register int32_t *buf;

	 if (!xdr_string (xdrs, objp, ~0))
		 return FALSE;
	return TRUE;
}

bool_t
xdr___rpc_string (XDR *xdrs, __rpc_string *objp)
{
	register int32_t *buf;

	 if (!xdr_string (xdrs, &objp->data, ~0))
		 return FALSE;
	return TRUE;
}

bool_t
xdr___rpc_string_p (XDR *xdrs, __rpc_string_p *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)objp, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	return TRUE;
}

bool_t
xdr___rpc_qcsapi_mac_addr (XDR *xdrs, __rpc_qcsapi_mac_addr *objp)
{
	register int32_t *buf;

	int i;
	 if (!xdr_vector (xdrs, (char *)objp->data, 6,
		sizeof (u_char), (xdrproc_t) xdr_u_char))
		 return FALSE;
	return TRUE;
}

bool_t
xdr___rpc_qcsapi_mac_addr_p (XDR *xdrs, __rpc_qcsapi_mac_addr_p *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)objp, sizeof (__rpc_qcsapi_mac_addr), (xdrproc_t) xdr___rpc_qcsapi_mac_addr))
		 return FALSE;
	return TRUE;
}

bool_t
xdr___rpc_qcsapi_int_a32 (XDR *xdrs, __rpc_qcsapi_int_a32 *objp)
{
	register int32_t *buf;

	int i;

	if (xdrs->x_op == XDR_ENCODE) {
		buf = XDR_INLINE (xdrs, ( 32 ) * BYTES_PER_XDR_UNIT);
		if (buf == NULL) {
			 if (!xdr_vector (xdrs, (char *)objp->data, 32,
				sizeof (int), (xdrproc_t) xdr_int))
				 return FALSE;
		} else {
			{
				register int *genp;

				for (i = 0, genp = objp->data;
					i < 32; ++i) {
					IXDR_PUT_LONG(buf, *genp++);
				}
			}
		}
		return TRUE;
	} else if (xdrs->x_op == XDR_DECODE) {
		buf = XDR_INLINE (xdrs, ( 32 ) * BYTES_PER_XDR_UNIT);
		if (buf == NULL) {
			 if (!xdr_vector (xdrs, (char *)objp->data, 32,
				sizeof (int), (xdrproc_t) xdr_int))
				 return FALSE;
		} else {
			{
				register int *genp;

				for (i = 0, genp = objp->data;
					i < 32; ++i) {
					*genp++ = IXDR_GET_LONG(buf);
				}
			}
		}
	 return TRUE;
	}

	 if (!xdr_vector (xdrs, (char *)objp->data, 32,
		sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	return TRUE;
}

bool_t
xdr___rpc_qcsapi_int_a32_p (XDR *xdrs, __rpc_qcsapi_int_a32_p *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)objp, sizeof (__rpc_qcsapi_int_a32), (xdrproc_t) xdr___rpc_qcsapi_int_a32))
		 return FALSE;
	return TRUE;
}

bool_t
xdr___rpc_qcsapi_SSID (XDR *xdrs, __rpc_qcsapi_SSID *objp)
{
	register int32_t *buf;

	int i;
	 if (!xdr_vector (xdrs, (char *)objp->data, 33,
		sizeof (u_char), (xdrproc_t) xdr_u_char))
		 return FALSE;
	return TRUE;
}

bool_t
xdr___rpc_qcsapi_scs_ranking_rpt (XDR *xdrs, __rpc_qcsapi_scs_ranking_rpt *objp)
{
	register int32_t *buf;

	int i;
	 if (!xdr_uint8_t (xdrs, &objp->num))
		 return FALSE;
	 if (!xdr_vector (xdrs, (char *)objp->chan, 32,
		sizeof (uint8_t), (xdrproc_t) xdr_uint8_t))
		 return FALSE;
	 if (!xdr_vector (xdrs, (char *)objp->dfs, 32,
		sizeof (uint8_t), (xdrproc_t) xdr_uint8_t))
		 return FALSE;
	 if (!xdr_vector (xdrs, (char *)objp->txpwr, 32,
		sizeof (uint8_t), (xdrproc_t) xdr_uint8_t))
		 return FALSE;
	 if (!xdr_vector (xdrs, (char *)objp->metric, 32,
		sizeof (int32_t), (xdrproc_t) xdr_int32_t))
		 return FALSE;
	 if (!xdr_vector (xdrs, (char *)objp->metric_age, 32,
		sizeof (uint32_t), (xdrproc_t) xdr_uint32_t))
		 return FALSE;
	 if (!xdr_vector (xdrs, (char *)objp->cca_intf, 32,
		sizeof (uint16_t), (xdrproc_t) xdr_uint16_t))
		 return FALSE;
	 if (!xdr_vector (xdrs, (char *)objp->pmbl_ap, 32,
		sizeof (uint32_t), (xdrproc_t) xdr_uint32_t))
		 return FALSE;
	 if (!xdr_vector (xdrs, (char *)objp->pmbl_sta, 32,
		sizeof (uint32_t), (xdrproc_t) xdr_uint32_t))
		 return FALSE;
	 if (!xdr_vector (xdrs, (char *)objp->duration, 32,
		sizeof (uint32_t), (xdrproc_t) xdr_uint32_t))
		 return FALSE;
	 if (!xdr_vector (xdrs, (char *)objp->times, 32,
		sizeof (uint32_t), (xdrproc_t) xdr_uint32_t))
		 return FALSE;
	return TRUE;
}

bool_t
xdr___rpc_qcsapi_scs_score_rpt (XDR *xdrs, __rpc_qcsapi_scs_score_rpt *objp)
{
	register int32_t *buf;

	int i;
	 if (!xdr_uint8_t (xdrs, &objp->num))
		 return FALSE;
	 if (!xdr_vector (xdrs, (char *)objp->chan, 32,
		sizeof (uint8_t), (xdrproc_t) xdr_uint8_t))
		 return FALSE;
	 if (!xdr_vector (xdrs, (char *)objp->score, 32,
		sizeof (uint8_t), (xdrproc_t) xdr_uint8_t))
		 return FALSE;
	return TRUE;
}

bool_t
xdr___rpc_qcsapi_scs_currchan_rpt (XDR *xdrs, __rpc_qcsapi_scs_currchan_rpt *objp)
{
	register int32_t *buf;

	 if (!xdr_uint8_t (xdrs, &objp->chan))
		 return FALSE;
	 if (!xdr_uint16_t (xdrs, &objp->cca_try))
		 return FALSE;
	 if (!xdr_uint16_t (xdrs, &objp->cca_idle))
		 return FALSE;
	 if (!xdr_uint16_t (xdrs, &objp->cca_busy))
		 return FALSE;
	 if (!xdr_uint16_t (xdrs, &objp->cca_intf))
		 return FALSE;
	 if (!xdr_uint16_t (xdrs, &objp->cca_tx))
		 return FALSE;
	 if (!xdr_uint16_t (xdrs, &objp->tx_ms))
		 return FALSE;
	 if (!xdr_uint16_t (xdrs, &objp->rx_ms))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->pmbl))
		 return FALSE;
	return TRUE;
}

bool_t
xdr___rpc_qcsapi_autochan_rpt (XDR *xdrs, __rpc_qcsapi_autochan_rpt *objp)
{
	register int32_t *buf;

	int i;
	 if (!xdr_uint8_t (xdrs, &objp->num))
		 return FALSE;
	 if (!xdr_vector (xdrs, (char *)objp->chan, 32,
		sizeof (uint8_t), (xdrproc_t) xdr_uint8_t))
		 return FALSE;
	 if (!xdr_vector (xdrs, (char *)objp->dfs, 32,
		sizeof (uint8_t), (xdrproc_t) xdr_uint8_t))
		 return FALSE;
	 if (!xdr_vector (xdrs, (char *)objp->txpwr, 32,
		sizeof (uint8_t), (xdrproc_t) xdr_uint8_t))
		 return FALSE;
	 if (!xdr_vector (xdrs, (char *)objp->metric, 32,
		sizeof (int32_t), (xdrproc_t) xdr_int32_t))
		 return FALSE;
	 if (!xdr_vector (xdrs, (char *)objp->numbeacons, 32,
		sizeof (uint32_t), (xdrproc_t) xdr_uint32_t))
		 return FALSE;
	 if (!xdr_vector (xdrs, (char *)objp->cci, 32,
		sizeof (uint32_t), (xdrproc_t) xdr_uint32_t))
		 return FALSE;
	 if (!xdr_vector (xdrs, (char *)objp->aci, 32,
		sizeof (uint32_t), (xdrproc_t) xdr_uint32_t))
		 return FALSE;
	return TRUE;
}

bool_t
xdr___rpc_qcsapi_scs_param_rpt (XDR *xdrs, __rpc_qcsapi_scs_param_rpt *objp)
{
	register int32_t *buf;

	 if (!xdr_uint32_t (xdrs, &objp->scs_cfg_param))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->scs_signed_param_flag))
		 return FALSE;
	return TRUE;
}

bool_t
xdr___rpc_qcsapi_data_512bytes (XDR *xdrs, __rpc_qcsapi_data_512bytes *objp)
{
	register int32_t *buf;

	int i;
	 if (!xdr_vector (xdrs, (char *)objp->data, 512,
		sizeof (uint8_t), (xdrproc_t) xdr_uint8_t))
		 return FALSE;
	return TRUE;
}

bool_t
xdr___rpc_qcsapi_data_256bytes (XDR *xdrs, __rpc_qcsapi_data_256bytes *objp)
{
	register int32_t *buf;

	int i;
	 if (!xdr_vector (xdrs, (char *)objp->data, 256,
		sizeof (uint8_t), (xdrproc_t) xdr_uint8_t))
		 return FALSE;
	return TRUE;
}

bool_t
xdr___rpc_qcsapi_disconn_info (XDR *xdrs, __rpc_qcsapi_disconn_info *objp)
{
	register int32_t *buf;

	 if (!xdr_uint32_t (xdrs, &objp->asso_sta_count))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->disconn_count))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->sequence))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->up_time))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->resetflag))
		 return FALSE;
	return TRUE;
}

bool_t
xdr___rpc_qcsapi_data_64bytes (XDR *xdrs, __rpc_qcsapi_data_64bytes *objp)
{
	register int32_t *buf;

	int i;
	 if (!xdr_vector (xdrs, (char *)objp->data, 64,
		sizeof (uint8_t), (xdrproc_t) xdr_uint8_t))
		 return FALSE;
	return TRUE;
}

bool_t
xdr___rpc_qcsapi_channel_power_table (XDR *xdrs, __rpc_qcsapi_channel_power_table *objp)
{
	register int32_t *buf;

	int i;

	if (xdrs->x_op == XDR_ENCODE) {
		 if (!xdr_uint8_t (xdrs, &objp->channel))
			 return FALSE;
		buf = XDR_INLINE (xdrs, ( 8  + 8  + 8 ) * BYTES_PER_XDR_UNIT);
		if (buf == NULL) {
			 if (!xdr_vector (xdrs, (char *)objp->power_20M, 8,
				sizeof (int), (xdrproc_t) xdr_int))
				 return FALSE;
			 if (!xdr_vector (xdrs, (char *)objp->power_40M, 8,
				sizeof (int), (xdrproc_t) xdr_int))
				 return FALSE;
			 if (!xdr_vector (xdrs, (char *)objp->power_80M, 8,
				sizeof (int), (xdrproc_t) xdr_int))
				 return FALSE;
		} else {
			{
				register int *genp;

				for (i = 0, genp = objp->power_20M;
					i < 8; ++i) {
					IXDR_PUT_LONG(buf, *genp++);
				}
			}
			{
				register int *genp;

				for (i = 0, genp = objp->power_40M;
					i < 8; ++i) {
					IXDR_PUT_LONG(buf, *genp++);
				}
			}
			{
				register int *genp;

				for (i = 0, genp = objp->power_80M;
					i < 8; ++i) {
					IXDR_PUT_LONG(buf, *genp++);
				}
			}
		}
		return TRUE;
	} else if (xdrs->x_op == XDR_DECODE) {
		 if (!xdr_uint8_t (xdrs, &objp->channel))
			 return FALSE;
		buf = XDR_INLINE (xdrs, ( 8  + 8  + 8 ) * BYTES_PER_XDR_UNIT);
		if (buf == NULL) {
			 if (!xdr_vector (xdrs, (char *)objp->power_20M, 8,
				sizeof (int), (xdrproc_t) xdr_int))
				 return FALSE;
			 if (!xdr_vector (xdrs, (char *)objp->power_40M, 8,
				sizeof (int), (xdrproc_t) xdr_int))
				 return FALSE;
			 if (!xdr_vector (xdrs, (char *)objp->power_80M, 8,
				sizeof (int), (xdrproc_t) xdr_int))
				 return FALSE;
		} else {
			{
				register int *genp;

				for (i = 0, genp = objp->power_20M;
					i < 8; ++i) {
					*genp++ = IXDR_GET_LONG(buf);
				}
			}
			{
				register int *genp;

				for (i = 0, genp = objp->power_40M;
					i < 8; ++i) {
					*genp++ = IXDR_GET_LONG(buf);
				}
			}
			{
				register int *genp;

				for (i = 0, genp = objp->power_80M;
					i < 8; ++i) {
					*genp++ = IXDR_GET_LONG(buf);
				}
			}
		}
	 return TRUE;
	}

	 if (!xdr_uint8_t (xdrs, &objp->channel))
		 return FALSE;
	 if (!xdr_vector (xdrs, (char *)objp->power_20M, 8,
		sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_vector (xdrs, (char *)objp->power_40M, 8,
		sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_vector (xdrs, (char *)objp->power_80M, 8,
		sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	return TRUE;
}

bool_t
xdr___rpc_qcsapi_assoc_records (XDR *xdrs, __rpc_qcsapi_assoc_records *objp)
{
	register int32_t *buf;

	int i;
	 if (!xdr_vector (xdrs, (char *)objp->addr, 32,
		sizeof (__rpc_qcsapi_mac_addr), (xdrproc_t) xdr___rpc_qcsapi_mac_addr))
		 return FALSE;
	 if (!xdr_vector (xdrs, (char *)objp->timestamp, 32,
		sizeof (uint32_t), (xdrproc_t) xdr_uint32_t))
		 return FALSE;
	return TRUE;
}

bool_t
xdr___rpc_ieee8011req_sta_tput_caps (XDR *xdrs, __rpc_ieee8011req_sta_tput_caps *objp)
{
	register int32_t *buf;

	int i;
	 if (!xdr_vector (xdrs, (char *)objp->macaddr, 6,
		sizeof (uint8_t), (xdrproc_t) xdr_uint8_t))
		 return FALSE;
	 if (!xdr_uint8_t (xdrs, &objp->mode))
		 return FALSE;
	 if (!xdr_vector (xdrs, (char *)objp->htcap_ie, 28,
		sizeof (uint8_t), (xdrproc_t) xdr_uint8_t))
		 return FALSE;
	 if (!xdr_vector (xdrs, (char *)objp->vhtcap_ie, 14,
		sizeof (uint8_t), (xdrproc_t) xdr_uint8_t))
		 return FALSE;
	return TRUE;
}

bool_t
xdr___rpc_qcsapi_measure_report_result (XDR *xdrs, __rpc_qcsapi_measure_report_result *objp)
{
	register int32_t *buf;

	int i;

	if (xdrs->x_op == XDR_ENCODE) {
		buf = XDR_INLINE (xdrs, ( 16 ) * BYTES_PER_XDR_UNIT);
		if (buf == NULL) {
			 if (!xdr_vector (xdrs, (char *)objp->common, 16,
				sizeof (int), (xdrproc_t) xdr_int))
				 return FALSE;

		} else {
		{
			register int *genp;

			for (i = 0, genp = objp->common;
				i < 16; ++i) {
				IXDR_PUT_LONG(buf, *genp++);
			}
		}
		}
		 if (!xdr_uint8_t (xdrs, &objp->basic))
			 return FALSE;
		 if (!xdr_uint8_t (xdrs, &objp->cca))
			 return FALSE;
		 if (!xdr_vector (xdrs, (char *)objp->rpi, 8,
			sizeof (uint8_t), (xdrproc_t) xdr_uint8_t))
			 return FALSE;
		 if (!xdr_uint8_t (xdrs, &objp->channel_load))
			 return FALSE;
		return TRUE;
	} else if (xdrs->x_op == XDR_DECODE) {
		buf = XDR_INLINE (xdrs, ( 16 ) * BYTES_PER_XDR_UNIT);
		if (buf == NULL) {
			 if (!xdr_vector (xdrs, (char *)objp->common, 16,
				sizeof (int), (xdrproc_t) xdr_int))
				 return FALSE;

		} else {
		{
			register int *genp;

			for (i = 0, genp = objp->common;
				i < 16; ++i) {
				*genp++ = IXDR_GET_LONG(buf);
			}
		}
		}
		 if (!xdr_uint8_t (xdrs, &objp->basic))
			 return FALSE;
		 if (!xdr_uint8_t (xdrs, &objp->cca))
			 return FALSE;
		 if (!xdr_vector (xdrs, (char *)objp->rpi, 8,
			sizeof (uint8_t), (xdrproc_t) xdr_uint8_t))
			 return FALSE;
		 if (!xdr_uint8_t (xdrs, &objp->channel_load))
			 return FALSE;
	 return TRUE;
	}

	 if (!xdr_vector (xdrs, (char *)objp->common, 16,
		sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_uint8_t (xdrs, &objp->basic))
		 return FALSE;
	 if (!xdr_uint8_t (xdrs, &objp->cca))
		 return FALSE;
	 if (!xdr_vector (xdrs, (char *)objp->rpi, 8,
		sizeof (uint8_t), (xdrproc_t) xdr_uint8_t))
		 return FALSE;
	 if (!xdr_uint8_t (xdrs, &objp->channel_load))
		 return FALSE;
	return TRUE;
}

bool_t
xdr___rpc_qcsapi_node_stats (XDR *xdrs, __rpc_qcsapi_node_stats *objp)
{
	register int32_t *buf;

	 if (!xdr_uint64_t (xdrs, &objp->tx_bytes))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->tx_pkts))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->tx_discard))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->tx_err))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->tx_unicast))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->tx_multicast))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->tx_broadcast))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->tx_phy_rate))
		 return FALSE;
	 if (!xdr_uint64_t (xdrs, &objp->rx_bytes))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->rx_pkts))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->rx_discard))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->rx_err))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->rx_unicast))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->rx_multicast))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->rx_broadcast))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->rx_unknown))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->rx_phy_rate))
		 return FALSE;
	 if (!xdr___rpc_qcsapi_mac_addr (xdrs, &objp->mac_addr))
		 return FALSE;
	 if (!xdr_int32_t (xdrs, &objp->hw_noise))
		 return FALSE;
	 if (!xdr_int32_t (xdrs, &objp->snr))
		 return FALSE;
	 if (!xdr_int32_t (xdrs, &objp->rssi))
		 return FALSE;
	 if (!xdr_int32_t (xdrs, &objp->bw))
		 return FALSE;
	return TRUE;
}

bool_t
xdr___rpc_qcsapi_mlme_stats (XDR *xdrs, __rpc_qcsapi_mlme_stats *objp)
{
	register int32_t *buf;


	if (xdrs->x_op == XDR_ENCODE) {
		buf = XDR_INLINE (xdrs, 6 * BYTES_PER_XDR_UNIT);
		if (buf == NULL) {
			 if (!xdr_u_int (xdrs, &objp->auth))
				 return FALSE;
			 if (!xdr_u_int (xdrs, &objp->auth_fails))
				 return FALSE;
			 if (!xdr_u_int (xdrs, &objp->assoc))
				 return FALSE;
			 if (!xdr_u_int (xdrs, &objp->assoc_fails))
				 return FALSE;
			 if (!xdr_u_int (xdrs, &objp->deauth))
				 return FALSE;
			 if (!xdr_u_int (xdrs, &objp->diassoc))
				 return FALSE;
		} else {
			IXDR_PUT_U_LONG(buf, objp->auth);
			IXDR_PUT_U_LONG(buf, objp->auth_fails);
			IXDR_PUT_U_LONG(buf, objp->assoc);
			IXDR_PUT_U_LONG(buf, objp->assoc_fails);
			IXDR_PUT_U_LONG(buf, objp->deauth);
			IXDR_PUT_U_LONG(buf, objp->diassoc);
		}
		return TRUE;
	} else if (xdrs->x_op == XDR_DECODE) {
		buf = XDR_INLINE (xdrs, 6 * BYTES_PER_XDR_UNIT);
		if (buf == NULL) {
			 if (!xdr_u_int (xdrs, &objp->auth))
				 return FALSE;
			 if (!xdr_u_int (xdrs, &objp->auth_fails))
				 return FALSE;
			 if (!xdr_u_int (xdrs, &objp->assoc))
				 return FALSE;
			 if (!xdr_u_int (xdrs, &objp->assoc_fails))
				 return FALSE;
			 if (!xdr_u_int (xdrs, &objp->deauth))
				 return FALSE;
			 if (!xdr_u_int (xdrs, &objp->diassoc))
				 return FALSE;
		} else {
			objp->auth = IXDR_GET_U_LONG(buf);
			objp->auth_fails = IXDR_GET_U_LONG(buf);
			objp->assoc = IXDR_GET_U_LONG(buf);
			objp->assoc_fails = IXDR_GET_U_LONG(buf);
			objp->deauth = IXDR_GET_U_LONG(buf);
			objp->diassoc = IXDR_GET_U_LONG(buf);
		}
	 return TRUE;
	}

	 if (!xdr_u_int (xdrs, &objp->auth))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->auth_fails))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->assoc))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->assoc_fails))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->deauth))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->diassoc))
		 return FALSE;
	return TRUE;
}

bool_t
xdr___rpc_qcsapi_mlme_stats_macs (XDR *xdrs, __rpc_qcsapi_mlme_stats_macs *objp)
{
	register int32_t *buf;

	int i;
	 if (!xdr_vector (xdrs, (char *)objp->addr, 128,
		sizeof (__rpc_qcsapi_mac_addr), (xdrproc_t) xdr___rpc_qcsapi_mac_addr))
		 return FALSE;
	return TRUE;
}

bool_t
xdr___rpc_qcsapi_csw_record (XDR *xdrs, __rpc_qcsapi_csw_record *objp)
{
	register int32_t *buf;

	int i;
	 if (!xdr_uint32_t (xdrs, &objp->cnt))
		 return FALSE;
	 if (!xdr_int32_t (xdrs, &objp->index))
		 return FALSE;
	 if (!xdr_vector (xdrs, (char *)objp->channel, 32,
		sizeof (uint32_t), (xdrproc_t) xdr_uint32_t))
		 return FALSE;
	 if (!xdr_vector (xdrs, (char *)objp->timestamp, 32,
		sizeof (uint32_t), (xdrproc_t) xdr_uint32_t))
		 return FALSE;
	 if (!xdr_vector (xdrs, (char *)objp->reason, 32,
		sizeof (uint32_t), (xdrproc_t) xdr_uint32_t))
		 return FALSE;
	return TRUE;
}

bool_t
xdr___rpc_qcsapi_radar_status (XDR *xdrs, __rpc_qcsapi_radar_status *objp)
{
	register int32_t *buf;

	 if (!xdr_uint32_t (xdrs, &objp->channel))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->flags))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->ic_radardetected))
		 return FALSE;
	return TRUE;
}

bool_t
xdr___rpc_qcsapi_ap_properties (XDR *xdrs, __rpc_qcsapi_ap_properties *objp)
{
	register int32_t *buf;


	if (xdrs->x_op == XDR_ENCODE) {
		 if (!xdr___rpc_qcsapi_SSID (xdrs, &objp->ap_name_SSID))
			 return FALSE;
		 if (!xdr___rpc_qcsapi_mac_addr (xdrs, &objp->ap_mac_addr))
			 return FALSE;
		buf = XDR_INLINE (xdrs, 10 * BYTES_PER_XDR_UNIT);
		if (buf == NULL) {
			 if (!xdr_u_int (xdrs, &objp->ap_flags))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->ap_channel))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->ap_RSSI))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->ap_protocol))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->ap_encryption_modes))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->ap_authentication_mode))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->ap_best_data_rate))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->ap_wps))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->ap_80211_proto))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->ap_qhop_role))
				 return FALSE;
		} else {
			IXDR_PUT_U_LONG(buf, objp->ap_flags);
			IXDR_PUT_LONG(buf, objp->ap_channel);
			IXDR_PUT_LONG(buf, objp->ap_RSSI);
			IXDR_PUT_LONG(buf, objp->ap_protocol);
			IXDR_PUT_LONG(buf, objp->ap_encryption_modes);
			IXDR_PUT_LONG(buf, objp->ap_authentication_mode);
			IXDR_PUT_LONG(buf, objp->ap_best_data_rate);
			IXDR_PUT_LONG(buf, objp->ap_wps);
			IXDR_PUT_LONG(buf, objp->ap_80211_proto);
			IXDR_PUT_LONG(buf, objp->ap_qhop_role);
		}
		return TRUE;
	} else if (xdrs->x_op == XDR_DECODE) {
		 if (!xdr___rpc_qcsapi_SSID (xdrs, &objp->ap_name_SSID))
			 return FALSE;
		 if (!xdr___rpc_qcsapi_mac_addr (xdrs, &objp->ap_mac_addr))
			 return FALSE;
		buf = XDR_INLINE (xdrs, 10 * BYTES_PER_XDR_UNIT);
		if (buf == NULL) {
			 if (!xdr_u_int (xdrs, &objp->ap_flags))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->ap_channel))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->ap_RSSI))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->ap_protocol))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->ap_encryption_modes))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->ap_authentication_mode))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->ap_best_data_rate))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->ap_wps))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->ap_80211_proto))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->ap_qhop_role))
				 return FALSE;
		} else {
			objp->ap_flags = IXDR_GET_U_LONG(buf);
			objp->ap_channel = IXDR_GET_LONG(buf);
			objp->ap_RSSI = IXDR_GET_LONG(buf);
			objp->ap_protocol = IXDR_GET_LONG(buf);
			objp->ap_encryption_modes = IXDR_GET_LONG(buf);
			objp->ap_authentication_mode = IXDR_GET_LONG(buf);
			objp->ap_best_data_rate = IXDR_GET_LONG(buf);
			objp->ap_wps = IXDR_GET_LONG(buf);
			objp->ap_80211_proto = IXDR_GET_LONG(buf);
			objp->ap_qhop_role = IXDR_GET_LONG(buf);
		}
	 return TRUE;
	}

	 if (!xdr___rpc_qcsapi_SSID (xdrs, &objp->ap_name_SSID))
		 return FALSE;
	 if (!xdr___rpc_qcsapi_mac_addr (xdrs, &objp->ap_mac_addr))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->ap_flags))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->ap_channel))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->ap_RSSI))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->ap_protocol))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->ap_encryption_modes))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->ap_authentication_mode))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->ap_best_data_rate))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->ap_wps))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->ap_80211_proto))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->ap_qhop_role))
		 return FALSE;
	return TRUE;
}

bool_t
xdr___rpc_qcsapi_interface_stats (XDR *xdrs, __rpc_qcsapi_interface_stats *objp)
{
	register int32_t *buf;

	 if (!xdr_uint64_t (xdrs, &objp->tx_bytes))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->tx_pkts))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->tx_discard))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->tx_err))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->tx_unicast))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->tx_multicast))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->tx_broadcast))
		 return FALSE;
	 if (!xdr_uint64_t (xdrs, &objp->rx_bytes))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->rx_pkts))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->rx_discard))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->rx_err))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->rx_unicast))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->rx_multicast))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->rx_broadcast))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->rx_unknown))
		 return FALSE;
	return TRUE;
}

bool_t
xdr___rpc_qcsapi_phy_stats (XDR *xdrs, __rpc_qcsapi_phy_stats *objp)
{
	register int32_t *buf;

	int i;
	 if (!xdr_uint32_t (xdrs, &objp->tstamp))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->assoc))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->channel))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->atten))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->cca_total))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->cca_tx))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->cca_rx))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->cca_int))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->cca_idle))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->rx_pkts))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->rx_gain))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->rx_cnt_crc))
		 return FALSE;
	 if (!xdr_float (xdrs, &objp->rx_noise))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->tx_pkts))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->tx_defers))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->tx_touts))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->tx_retries))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->cnt_sp_fail))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->cnt_lp_fail))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->last_rx_mcs))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->last_tx_mcs))
		 return FALSE;
	 if (!xdr_float (xdrs, &objp->last_rssi))
		 return FALSE;
	 if (!xdr_vector (xdrs, (char *)objp->last_rssi_array, 4,
		sizeof (float), (xdrproc_t) xdr_float))
		 return FALSE;
	 if (!xdr_float (xdrs, &objp->last_rcpi))
		 return FALSE;
	 if (!xdr_float (xdrs, &objp->last_evm))
		 return FALSE;
	 if (!xdr_vector (xdrs, (char *)objp->last_evm_array, 4,
		sizeof (float), (xdrproc_t) xdr_float))
		 return FALSE;
	return TRUE;
}

bool_t
xdr___rpc_early_flash_config (XDR *xdrs, __rpc_early_flash_config *objp)
{
	register int32_t *buf;

	int i;
	 if (!xdr_uint32_t (xdrs, &objp->method))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->ipaddr))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->serverip))
		 return FALSE;
	 if (!xdr_vector (xdrs, (char *)objp->built_time_utc_sec, 11,
		sizeof (uint8_t), (xdrproc_t) xdr_uint8_t))
		 return FALSE;
	 if (!xdr_uint8_t (xdrs, &objp->uboot_type))
		 return FALSE;
	return TRUE;
}

bool_t
xdr___rpc_qcsapi_data_128bytes (XDR *xdrs, __rpc_qcsapi_data_128bytes *objp)
{
	register int32_t *buf;

	int i;
	 if (!xdr_vector (xdrs, (char *)objp->data, 128,
		sizeof (uint8_t), (xdrproc_t) xdr_uint8_t))
		 return FALSE;
	return TRUE;
}

bool_t
xdr___rpc_qcsapi_data_1Kbytes (XDR *xdrs, __rpc_qcsapi_data_1Kbytes *objp)
{
	register int32_t *buf;

	int i;
	 if (!xdr_vector (xdrs, (char *)objp->data, 1024,
		sizeof (uint8_t), (xdrproc_t) xdr_uint8_t))
		 return FALSE;
	return TRUE;
}

bool_t
xdr___rpc_qcsapi_data_3Kbytes (XDR *xdrs, __rpc_qcsapi_data_3Kbytes *objp)
{
	register int32_t *buf;

	int i;
	 if (!xdr_vector (xdrs, (char *)objp->data, 3072,
		sizeof (uint8_t), (xdrproc_t) xdr_uint8_t))
		 return FALSE;
	return TRUE;
}

bool_t
xdr___rpc_qcsapi_data_4Kbytes (XDR *xdrs, __rpc_qcsapi_data_4Kbytes *objp)
{
	register int32_t *buf;

	int i;
	 if (!xdr_vector (xdrs, (char *)objp->data, 4096,
		sizeof (uint8_t), (xdrproc_t) xdr_uint8_t))
		 return FALSE;
	return TRUE;
}

bool_t
xdr___rpc_qcsapi_calcmd_tx_power_rsp (XDR *xdrs, __rpc_qcsapi_calcmd_tx_power_rsp *objp)
{
	register int32_t *buf;

	int i;
	 if (!xdr_vector (xdrs, (char *)objp->value, 4,
		sizeof (uint32_t), (xdrproc_t) xdr_uint32_t))
		 return FALSE;
	return TRUE;
}

bool_t
xdr___rpc_qcsapi_calcmd_rssi_rsp (XDR *xdrs, __rpc_qcsapi_calcmd_rssi_rsp *objp)
{
	register int32_t *buf;

	int i;
	 if (!xdr_vector (xdrs, (char *)objp->value, 4,
		sizeof (int32_t), (xdrproc_t) xdr_int32_t))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_bootcfg_get_parameter_rpcdata (XDR *xdrs, qcsapi_bootcfg_get_parameter_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->param_name, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->max_param_len))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->param_value, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_bootcfg_update_parameter_rpcdata (XDR *xdrs, qcsapi_bootcfg_update_parameter_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->param_name, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->param_value, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_bootcfg_commit_rpcdata (XDR *xdrs, qcsapi_bootcfg_commit_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_telnet_enable_rpcdata (XDR *xdrs, qcsapi_telnet_enable_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_u_int (xdrs, &objp->onoff))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_get_service_name_enum_rpcdata (XDR *xdrs, qcsapi_get_service_name_enum_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->lookup_service, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->serv_name, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_get_service_action_enum_rpcdata (XDR *xdrs, qcsapi_get_service_action_enum_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->lookup_action, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->serv_action, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_service_control_rpcdata (XDR *xdrs, qcsapi_service_control_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_int (xdrs, &objp->service))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->action))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wfa_cert_mode_enable_rpcdata (XDR *xdrs, qcsapi_wfa_cert_mode_enable_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_uint16_t (xdrs, &objp->enable))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_scs_cce_channels_rpcdata (XDR *xdrs, qcsapi_wifi_get_scs_cce_channels_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_prev_channel, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_cur_channel, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_scs_enable_rpcdata (XDR *xdrs, qcsapi_wifi_scs_enable_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_uint16_t (xdrs, &objp->enable_val))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_scs_switch_channel_rpcdata (XDR *xdrs, qcsapi_wifi_scs_switch_channel_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_scs_verbose_rpcdata (XDR *xdrs, qcsapi_wifi_set_scs_verbose_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_uint16_t (xdrs, &objp->enable_val))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_scs_status_rpcdata (XDR *xdrs, qcsapi_wifi_get_scs_status_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_scs_status, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_scs_smpl_enable_rpcdata (XDR *xdrs, qcsapi_wifi_set_scs_smpl_enable_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_uint16_t (xdrs, &objp->enable_val))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_scs_smpl_dwell_time_rpcdata (XDR *xdrs, qcsapi_wifi_set_scs_smpl_dwell_time_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_uint16_t (xdrs, &objp->scs_sample_time))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_scs_sample_intv_rpcdata (XDR *xdrs, qcsapi_wifi_set_scs_sample_intv_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_uint16_t (xdrs, &objp->scs_sample_intv))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_scs_intf_detect_intv_rpcdata (XDR *xdrs, qcsapi_wifi_set_scs_intf_detect_intv_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_uint16_t (xdrs, &objp->scs_intf_detect_intv))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_scs_thrshld_rpcdata (XDR *xdrs, qcsapi_wifi_set_scs_thrshld_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->scs_param_name, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_uint16_t (xdrs, &objp->scs_threshold))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_scs_report_only_rpcdata (XDR *xdrs, qcsapi_wifi_set_scs_report_only_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_uint16_t (xdrs, &objp->scs_report_only))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_scs_stat_report_rpcdata (XDR *xdrs, qcsapi_wifi_get_scs_stat_report_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->scs_rpt, sizeof (__rpc_qcsapi_scs_ranking_rpt), (xdrproc_t) xdr___rpc_qcsapi_scs_ranking_rpt))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_scs_score_report_rpcdata (XDR *xdrs, qcsapi_wifi_get_scs_score_report_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->scs_rpt, sizeof (__rpc_qcsapi_scs_score_rpt), (xdrproc_t) xdr___rpc_qcsapi_scs_score_rpt))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_scs_currchan_report_rpcdata (XDR *xdrs, qcsapi_wifi_get_scs_currchan_report_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->scs_currchan_rpt, sizeof (__rpc_qcsapi_scs_currchan_rpt), (xdrproc_t) xdr___rpc_qcsapi_scs_currchan_rpt))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_scs_stats_rpcdata (XDR *xdrs, qcsapi_wifi_set_scs_stats_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_uint16_t (xdrs, &objp->start))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_autochan_report_rpcdata (XDR *xdrs, qcsapi_wifi_get_autochan_report_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->autochan_rpt, sizeof (__rpc_qcsapi_autochan_rpt), (xdrproc_t) xdr___rpc_qcsapi_autochan_rpt))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_scs_cca_intf_smth_fctr_rpcdata (XDR *xdrs, qcsapi_wifi_set_scs_cca_intf_smth_fctr_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_uint8_t (xdrs, &objp->smth_fctr_noxp))
		 return FALSE;
	 if (!xdr_uint8_t (xdrs, &objp->smth_fctr_xped))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_scs_chan_mtrc_mrgn_rpcdata (XDR *xdrs, qcsapi_wifi_set_scs_chan_mtrc_mrgn_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_uint8_t (xdrs, &objp->chan_mtrc_mrgn))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_scs_cca_intf_rpcdata (XDR *xdrs, qcsapi_wifi_get_scs_cca_intf_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->the_channel))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_cca_intf, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_scs_param_report_rpcdata (XDR *xdrs, qcsapi_wifi_get_scs_param_report_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->param_num))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_scs_param_rpt, sizeof (__rpc_qcsapi_scs_param_rpt), (xdrproc_t) xdr___rpc_qcsapi_scs_param_rpt))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_scs_dfs_reentry_request_rpcdata (XDR *xdrs, qcsapi_wifi_get_scs_dfs_reentry_request_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_scs_dfs_reentry_request, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_start_ocac_rpcdata (XDR *xdrs, qcsapi_wifi_start_ocac_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_uint16_t (xdrs, &objp->channel))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_stop_ocac_rpcdata (XDR *xdrs, qcsapi_wifi_stop_ocac_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_ocac_status_rpcdata (XDR *xdrs, qcsapi_wifi_get_ocac_status_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->status, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_ocac_dwell_time_rpcdata (XDR *xdrs, qcsapi_wifi_set_ocac_dwell_time_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_uint16_t (xdrs, &objp->dwell_time))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_ocac_duration_rpcdata (XDR *xdrs, qcsapi_wifi_set_ocac_duration_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_uint16_t (xdrs, &objp->duration))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_ocac_cac_time_rpcdata (XDR *xdrs, qcsapi_wifi_set_ocac_cac_time_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_uint16_t (xdrs, &objp->cac_time))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_ocac_report_only_rpcdata (XDR *xdrs, qcsapi_wifi_set_ocac_report_only_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_uint16_t (xdrs, &objp->enable))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_ocac_thrshld_rpcdata (XDR *xdrs, qcsapi_wifi_set_ocac_thrshld_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->param_name, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_uint16_t (xdrs, &objp->threshold))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_start_dfs_s_radio_rpcdata (XDR *xdrs, qcsapi_wifi_start_dfs_s_radio_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_uint16_t (xdrs, &objp->channel))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_stop_dfs_s_radio_rpcdata (XDR *xdrs, qcsapi_wifi_stop_dfs_s_radio_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_dfs_s_radio_status_rpcdata (XDR *xdrs, qcsapi_wifi_get_dfs_s_radio_status_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->status, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_dfs_s_radio_availability_rpcdata (XDR *xdrs, qcsapi_wifi_get_dfs_s_radio_availability_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->available, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_dfs_s_radio_dwell_time_rpcdata (XDR *xdrs, qcsapi_wifi_set_dfs_s_radio_dwell_time_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_uint16_t (xdrs, &objp->dwell_time))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_dfs_s_radio_duration_rpcdata (XDR *xdrs, qcsapi_wifi_set_dfs_s_radio_duration_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_uint16_t (xdrs, &objp->duration))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_dfs_s_radio_wea_duration_rpcdata (XDR *xdrs, qcsapi_wifi_set_dfs_s_radio_wea_duration_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->duration))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_dfs_s_radio_cac_time_rpcdata (XDR *xdrs, qcsapi_wifi_set_dfs_s_radio_cac_time_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_uint16_t (xdrs, &objp->cac_time))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_dfs_s_radio_wea_cac_time_rpcdata (XDR *xdrs, qcsapi_wifi_set_dfs_s_radio_wea_cac_time_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->cac_time))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_dfs_s_radio_report_only_rpcdata (XDR *xdrs, qcsapi_wifi_set_dfs_s_radio_report_only_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_uint16_t (xdrs, &objp->enable))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_dfs_s_radio_thrshld_rpcdata (XDR *xdrs, qcsapi_wifi_set_dfs_s_radio_thrshld_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->param_name, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_uint16_t (xdrs, &objp->threshold))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_init_rpcdata (XDR *xdrs, qcsapi_init_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_console_disconnect_rpcdata (XDR *xdrs, qcsapi_console_disconnect_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_startprod_rpcdata (XDR *xdrs, qcsapi_wifi_startprod_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_is_startprod_done_rpcdata (XDR *xdrs, qcsapi_is_startprod_done_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->p_status, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_system_get_time_since_start_rpcdata (XDR *xdrs, qcsapi_system_get_time_since_start_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->p_elapsed_time, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_get_system_status_rpcdata (XDR *xdrs, qcsapi_get_system_status_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->p_status, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_get_random_seed_rpcdata (XDR *xdrs, qcsapi_get_random_seed_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->random_buf, sizeof (__rpc_qcsapi_data_512bytes), (xdrproc_t) xdr___rpc_qcsapi_data_512bytes))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_set_random_seed_rpcdata (XDR *xdrs, qcsapi_set_random_seed_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->random_buf, sizeof (__rpc_qcsapi_data_512bytes), (xdrproc_t) xdr___rpc_qcsapi_data_512bytes))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->entropy))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_get_carrier_id_rpcdata (XDR *xdrs, qcsapi_get_carrier_id_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->p_carrier_id, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_set_carrier_id_rpcdata (XDR *xdrs, qcsapi_set_carrier_id_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_uint32_t (xdrs, &objp->carrier_id))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->update_uboot))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_spinor_jedecid_rpcdata (XDR *xdrs, qcsapi_wifi_get_spinor_jedecid_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_jedecid, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_bb_param_rpcdata (XDR *xdrs, qcsapi_wifi_get_bb_param_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_jedecid, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_bb_param_rpcdata (XDR *xdrs, qcsapi_wifi_set_bb_param_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->p_jedecid))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_optim_stats_rpcdata (XDR *xdrs, qcsapi_wifi_set_optim_stats_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->p_jedecid))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_sys_time_rpcdata (XDR *xdrs, qcsapi_wifi_set_sys_time_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_uint32_t (xdrs, &objp->timestamp))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_sys_time_rpcdata (XDR *xdrs, qcsapi_wifi_get_sys_time_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->timestamp, sizeof (uint32_t), (xdrproc_t) xdr_uint32_t))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_set_soc_mac_addr_rpcdata (XDR *xdrs, qcsapi_set_soc_mac_addr_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr___rpc_qcsapi_mac_addr_p (xdrs, &objp->soc_mac_addr))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_get_custom_value_rpcdata (XDR *xdrs, qcsapi_get_custom_value_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->custom_key, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->custom_value, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_config_get_parameter_rpcdata (XDR *xdrs, qcsapi_config_get_parameter_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->param_name, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->max_param_len))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->param_value, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_config_update_parameter_rpcdata (XDR *xdrs, qcsapi_config_update_parameter_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->param_name, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->param_value, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_config_get_ssid_parameter_rpcdata (XDR *xdrs, qcsapi_config_get_ssid_parameter_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->param_name, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->max_param_len))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->param_value, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_config_update_ssid_parameter_rpcdata (XDR *xdrs, qcsapi_config_update_ssid_parameter_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->param_name, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->param_value, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_file_path_get_config_rpcdata (XDR *xdrs, qcsapi_file_path_get_config_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_int (xdrs, &objp->e_file_path))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->path_size))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->file_path, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_file_path_set_config_rpcdata (XDR *xdrs, qcsapi_file_path_set_config_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_int (xdrs, &objp->e_file_path))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->new_path, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_restore_default_config_rpcdata (XDR *xdrs, qcsapi_restore_default_config_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_int (xdrs, &objp->flag))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_store_ipaddr_rpcdata (XDR *xdrs, qcsapi_store_ipaddr_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_u_int (xdrs, &objp->ipaddr))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->netmask))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_interface_enable_rpcdata (XDR *xdrs, qcsapi_interface_enable_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->enable_flag))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_interface_get_status_rpcdata (XDR *xdrs, qcsapi_interface_get_status_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->interface_status, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_interface_set_ip4_rpcdata (XDR *xdrs, qcsapi_interface_set_ip4_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->if_param, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->if_param_val))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_interface_get_ip4_rpcdata (XDR *xdrs, qcsapi_interface_get_ip4_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->if_param, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->if_param_val, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_interface_get_counter_rpcdata (XDR *xdrs, qcsapi_interface_get_counter_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->qcsapi_counter))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_counter_value, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_interface_get_counter64_rpcdata (XDR *xdrs, qcsapi_interface_get_counter64_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->qcsapi_counter))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_counter_value, sizeof (uint64_t), (xdrproc_t) xdr_uint64_t))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_interface_get_mac_addr_rpcdata (XDR *xdrs, qcsapi_interface_get_mac_addr_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr___rpc_qcsapi_mac_addr_p (xdrs, &objp->current_mac_addr))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_interface_set_mac_addr_rpcdata (XDR *xdrs, qcsapi_interface_set_mac_addr_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr___rpc_qcsapi_mac_addr_p (xdrs, &objp->interface_mac_addr))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_pm_get_counter_rpcdata (XDR *xdrs, qcsapi_pm_get_counter_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->qcsapi_counter))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->pm_interval, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_counter_value, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_set_aspm_l1_rpcdata (XDR *xdrs, qcsapi_set_aspm_l1_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_int (xdrs, &objp->enable))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->latency))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_set_l1_rpcdata (XDR *xdrs, qcsapi_set_l1_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_int (xdrs, &objp->enter))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_pm_get_elapsed_time_rpcdata (XDR *xdrs, qcsapi_pm_get_elapsed_time_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->pm_interval, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_elapsed_time, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_eth_phy_power_control_rpcdata (XDR *xdrs, qcsapi_eth_phy_power_control_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_int (xdrs, &objp->on_off))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->interface, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_get_emac_switch_rpcdata (XDR *xdrs, qcsapi_get_emac_switch_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->buf, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_set_emac_switch_rpcdata (XDR *xdrs, qcsapi_set_emac_switch_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_int (xdrs, &objp->value))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_eth_dscp_map_rpcdata (XDR *xdrs, qcsapi_eth_dscp_map_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_int (xdrs, &objp->oper))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->eth_type, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->level, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->value, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->size))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->buf, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_get_eth_info_rpcdata (XDR *xdrs, qcsapi_get_eth_info_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->eth_info_type))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_mode_rpcdata (XDR *xdrs, qcsapi_wifi_get_mode_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_wifi_mode, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_mode_rpcdata (XDR *xdrs, qcsapi_wifi_set_mode_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->new_wifi_mode))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_phy_mode_rpcdata (XDR *xdrs, qcsapi_wifi_get_phy_mode_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_wifi_phy_mode, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_phy_mode_rpcdata (XDR *xdrs, qcsapi_wifi_set_phy_mode_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->new_phy_mode, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_reload_in_mode_rpcdata (XDR *xdrs, qcsapi_wifi_reload_in_mode_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->new_wifi_mode))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_rfenable_rpcdata (XDR *xdrs, qcsapi_wifi_rfenable_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_u_int (xdrs, &objp->onoff))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_rfstatus_rpcdata (XDR *xdrs, qcsapi_wifi_rfstatus_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->rfstatus, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_bw_rpcdata (XDR *xdrs, qcsapi_wifi_get_bw_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_bw, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_bw_rpcdata (XDR *xdrs, qcsapi_wifi_set_bw_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->bw))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_vht_rpcdata (XDR *xdrs, qcsapi_wifi_set_vht_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->the_vht))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_vht_rpcdata (XDR *xdrs, qcsapi_wifi_get_vht_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->vht, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_channel_rpcdata (XDR *xdrs, qcsapi_wifi_get_channel_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_current_channel, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_channel_rpcdata (XDR *xdrs, qcsapi_wifi_set_channel_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->new_channel))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_chan_pri_inactive_rpcdata (XDR *xdrs, qcsapi_wifi_set_chan_pri_inactive_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->channel))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->inactive))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_chan_control_rpcdata (XDR *xdrs, qcsapi_wifi_chan_control_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->chans, sizeof (__rpc_qcsapi_data_256bytes), (xdrproc_t) xdr___rpc_qcsapi_data_256bytes))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->cnt))
		 return FALSE;
	 if (!xdr_uint8_t (xdrs, &objp->flag))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_chan_disabled_rpcdata (XDR *xdrs, qcsapi_wifi_get_chan_disabled_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_chans, sizeof (__rpc_qcsapi_data_256bytes), (xdrproc_t) xdr___rpc_qcsapi_data_256bytes))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_cnt, sizeof (uint8_t), (xdrproc_t) xdr_uint8_t))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_beacon_interval_rpcdata (XDR *xdrs, qcsapi_wifi_get_beacon_interval_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_current_intval, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_beacon_interval_rpcdata (XDR *xdrs, qcsapi_wifi_set_beacon_interval_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->new_intval))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_dtim_rpcdata (XDR *xdrs, qcsapi_wifi_get_dtim_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_dtim, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_dtim_rpcdata (XDR *xdrs, qcsapi_wifi_set_dtim_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->new_dtim))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_assoc_limit_rpcdata (XDR *xdrs, qcsapi_wifi_get_assoc_limit_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_assoc_limit, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_bss_assoc_limit_rpcdata (XDR *xdrs, qcsapi_wifi_get_bss_assoc_limit_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_bss_lim_pri, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_assoc_limit_rpcdata (XDR *xdrs, qcsapi_wifi_set_assoc_limit_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->new_assoc_limit))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_bss_assoc_limit_rpcdata (XDR *xdrs, qcsapi_wifi_set_bss_assoc_limit_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->bss_lim_pri))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_BSSID_rpcdata (XDR *xdrs, qcsapi_wifi_get_BSSID_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr___rpc_qcsapi_mac_addr_p (xdrs, &objp->current_BSSID))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_config_BSSID_rpcdata (XDR *xdrs, qcsapi_wifi_get_config_BSSID_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr___rpc_qcsapi_mac_addr_p (xdrs, &objp->config_BSSID))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_ssid_get_bssid_rpcdata (XDR *xdrs, qcsapi_wifi_ssid_get_bssid_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->ssid_str, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr___rpc_qcsapi_mac_addr_p (xdrs, &objp->bssid))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_ssid_set_bssid_rpcdata (XDR *xdrs, qcsapi_wifi_ssid_set_bssid_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->ssid_str, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr___rpc_qcsapi_mac_addr_p (xdrs, &objp->bssid))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_SSID_rpcdata (XDR *xdrs, qcsapi_wifi_get_SSID_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->SSID_str, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_SSID_rpcdata (XDR *xdrs, qcsapi_wifi_set_SSID_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->SSID_str, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_IEEE_802_11_standard_rpcdata (XDR *xdrs, qcsapi_wifi_get_IEEE_802_11_standard_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->IEEE_802_11_standard, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_list_channels_rpcdata (XDR *xdrs, qcsapi_wifi_get_list_channels_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->list_of_channels, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_mode_switch_rpcdata (XDR *xdrs, qcsapi_wifi_get_mode_switch_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->p_wifi_mode_switch_setting, sizeof (uint8_t), (xdrproc_t) xdr_uint8_t))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_disassociate_rpcdata (XDR *xdrs, qcsapi_wifi_disassociate_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_disassociate_sta_rpcdata (XDR *xdrs, qcsapi_wifi_disassociate_sta_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr___rpc_qcsapi_mac_addr_p (xdrs, &objp->mac))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_reassociate_rpcdata (XDR *xdrs, qcsapi_wifi_reassociate_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_disconn_info_rpcdata (XDR *xdrs, qcsapi_wifi_get_disconn_info_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->disconn_info, sizeof (__rpc_qcsapi_disconn_info), (xdrproc_t) xdr___rpc_qcsapi_disconn_info))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_disable_wps_rpcdata (XDR *xdrs, qcsapi_wifi_disable_wps_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->disable_wps))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_associate_rpcdata (XDR *xdrs, qcsapi_wifi_associate_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->join_ssid, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_start_cca_rpcdata (XDR *xdrs, qcsapi_wifi_start_cca_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->channel))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->duration))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_noise_rpcdata (XDR *xdrs, qcsapi_wifi_get_noise_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_noise, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_rssi_by_chain_rpcdata (XDR *xdrs, qcsapi_wifi_get_rssi_by_chain_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->rf_chain))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_rssi, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_avg_snr_rpcdata (XDR *xdrs, qcsapi_wifi_get_avg_snr_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_snr, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_get_primary_interface_rpcdata (XDR *xdrs, qcsapi_get_primary_interface_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_uint32_t (xdrs, &objp->maxlen))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_get_interface_by_index_rpcdata (XDR *xdrs, qcsapi_get_interface_by_index_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_u_int (xdrs, &objp->if_index))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->maxlen))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_wifi_macaddr_rpcdata (XDR *xdrs, qcsapi_wifi_set_wifi_macaddr_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr___rpc_qcsapi_mac_addr_p (xdrs, &objp->new_mac_addr))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_interface_get_BSSID_rpcdata (XDR *xdrs, qcsapi_interface_get_BSSID_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr___rpc_qcsapi_mac_addr_p (xdrs, &objp->current_BSSID))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_rates_rpcdata (XDR *xdrs, qcsapi_wifi_get_rates_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->rate_type))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->supported_rates, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_rates_rpcdata (XDR *xdrs, qcsapi_wifi_set_rates_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->rate_type))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->current_rates, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->num_rates))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_get_max_bitrate_rpcdata (XDR *xdrs, qcsapi_get_max_bitrate_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->max_str_len))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->max_bitrate, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_set_max_bitrate_rpcdata (XDR *xdrs, qcsapi_set_max_bitrate_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->max_bitrate, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_qos_get_param_rpcdata (XDR *xdrs, qcsapi_wifi_qos_get_param_rpcdata *objp)
{
	register int32_t *buf;


	if (xdrs->x_op == XDR_ENCODE) {
		 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
			 return FALSE;
		buf = XDR_INLINE (xdrs, 3 * BYTES_PER_XDR_UNIT);
		if (buf == NULL) {
			 if (!xdr_int (xdrs, &objp->the_queue))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->the_param))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->ap_bss_flag))
				 return FALSE;

		} else {
		IXDR_PUT_LONG(buf, objp->the_queue);
		IXDR_PUT_LONG(buf, objp->the_param);
		IXDR_PUT_LONG(buf, objp->ap_bss_flag);
		}
		 if (!xdr_pointer (xdrs, (char **)&objp->p_value, sizeof (int), (xdrproc_t) xdr_int))
			 return FALSE;
		 if (!xdr_int (xdrs, &objp->return_code))
			 return FALSE;
		return TRUE;
	} else if (xdrs->x_op == XDR_DECODE) {
		 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
			 return FALSE;
		buf = XDR_INLINE (xdrs, 3 * BYTES_PER_XDR_UNIT);
		if (buf == NULL) {
			 if (!xdr_int (xdrs, &objp->the_queue))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->the_param))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->ap_bss_flag))
				 return FALSE;

		} else {
		objp->the_queue = IXDR_GET_LONG(buf);
		objp->the_param = IXDR_GET_LONG(buf);
		objp->ap_bss_flag = IXDR_GET_LONG(buf);
		}
		 if (!xdr_pointer (xdrs, (char **)&objp->p_value, sizeof (int), (xdrproc_t) xdr_int))
			 return FALSE;
		 if (!xdr_int (xdrs, &objp->return_code))
			 return FALSE;
	 return TRUE;
	}

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->the_queue))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->the_param))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->ap_bss_flag))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_value, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_qos_set_param_rpcdata (XDR *xdrs, qcsapi_wifi_qos_set_param_rpcdata *objp)
{
	register int32_t *buf;


	if (xdrs->x_op == XDR_ENCODE) {
		 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
			 return FALSE;
		buf = XDR_INLINE (xdrs, 5 * BYTES_PER_XDR_UNIT);
		if (buf == NULL) {
			 if (!xdr_int (xdrs, &objp->the_queue))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->the_param))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->ap_bss_flag))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->value))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->return_code))
				 return FALSE;
		} else {
			IXDR_PUT_LONG(buf, objp->the_queue);
			IXDR_PUT_LONG(buf, objp->the_param);
			IXDR_PUT_LONG(buf, objp->ap_bss_flag);
			IXDR_PUT_LONG(buf, objp->value);
			IXDR_PUT_LONG(buf, objp->return_code);
		}
		return TRUE;
	} else if (xdrs->x_op == XDR_DECODE) {
		 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
			 return FALSE;
		buf = XDR_INLINE (xdrs, 5 * BYTES_PER_XDR_UNIT);
		if (buf == NULL) {
			 if (!xdr_int (xdrs, &objp->the_queue))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->the_param))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->ap_bss_flag))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->value))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->return_code))
				 return FALSE;
		} else {
			objp->the_queue = IXDR_GET_LONG(buf);
			objp->the_param = IXDR_GET_LONG(buf);
			objp->ap_bss_flag = IXDR_GET_LONG(buf);
			objp->value = IXDR_GET_LONG(buf);
			objp->return_code = IXDR_GET_LONG(buf);
		}
	 return TRUE;
	}

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->the_queue))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->the_param))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->ap_bss_flag))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->value))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_wmm_ac_map_rpcdata (XDR *xdrs, qcsapi_wifi_get_wmm_ac_map_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->mapping_table, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_wmm_ac_map_rpcdata (XDR *xdrs, qcsapi_wifi_set_wmm_ac_map_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->user_prio))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->ac_index))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_dscp_8021p_map_rpcdata (XDR *xdrs, qcsapi_wifi_get_dscp_8021p_map_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->mapping_table, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_dscp_ac_map_rpcdata (XDR *xdrs, qcsapi_wifi_get_dscp_ac_map_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->mapping_table, sizeof (__rpc_qcsapi_data_64bytes), (xdrproc_t) xdr___rpc_qcsapi_data_64bytes))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_dscp_8021p_map_rpcdata (XDR *xdrs, qcsapi_wifi_set_dscp_8021p_map_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->ip_dscp_list, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_uint8_t (xdrs, &objp->dot1p_up))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_dscp_ac_map_rpcdata (XDR *xdrs, qcsapi_wifi_set_dscp_ac_map_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->dscp_list, sizeof (__rpc_qcsapi_data_64bytes), (xdrproc_t) xdr___rpc_qcsapi_data_64bytes))
		 return FALSE;
	 if (!xdr_uint8_t (xdrs, &objp->dscp_list_len))
		 return FALSE;
	 if (!xdr_uint8_t (xdrs, &objp->ac))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_priority_rpcdata (XDR *xdrs, qcsapi_wifi_get_priority_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_priority, sizeof (uint8_t), (xdrproc_t) xdr_uint8_t))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_priority_rpcdata (XDR *xdrs, qcsapi_wifi_set_priority_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_uint8_t (xdrs, &objp->priority))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_airfair_rpcdata (XDR *xdrs, qcsapi_wifi_get_airfair_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_airfair, sizeof (uint8_t), (xdrproc_t) xdr_uint8_t))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_airfair_rpcdata (XDR *xdrs, qcsapi_wifi_set_airfair_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_uint8_t (xdrs, &objp->airfair))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_tx_power_rpcdata (XDR *xdrs, qcsapi_wifi_get_tx_power_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->the_channel))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_tx_power, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_tx_power_rpcdata (XDR *xdrs, qcsapi_wifi_set_tx_power_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->the_channel))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->tx_power))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_bw_power_rpcdata (XDR *xdrs, qcsapi_wifi_get_bw_power_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->the_channel))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_power_20M, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_power_40M, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_power_80M, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_bw_power_rpcdata (XDR *xdrs, qcsapi_wifi_set_bw_power_rpcdata *objp)
{
	register int32_t *buf;


	if (xdrs->x_op == XDR_ENCODE) {
		 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
			 return FALSE;
		buf = XDR_INLINE (xdrs, 5 * BYTES_PER_XDR_UNIT);
		if (buf == NULL) {
			 if (!xdr_u_int (xdrs, &objp->the_channel))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->power_20M))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->power_40M))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->power_80M))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->return_code))
				 return FALSE;
		} else {
			IXDR_PUT_U_LONG(buf, objp->the_channel);
			IXDR_PUT_LONG(buf, objp->power_20M);
			IXDR_PUT_LONG(buf, objp->power_40M);
			IXDR_PUT_LONG(buf, objp->power_80M);
			IXDR_PUT_LONG(buf, objp->return_code);
		}
		return TRUE;
	} else if (xdrs->x_op == XDR_DECODE) {
		 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
			 return FALSE;
		buf = XDR_INLINE (xdrs, 5 * BYTES_PER_XDR_UNIT);
		if (buf == NULL) {
			 if (!xdr_u_int (xdrs, &objp->the_channel))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->power_20M))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->power_40M))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->power_80M))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->return_code))
				 return FALSE;
		} else {
			objp->the_channel = IXDR_GET_U_LONG(buf);
			objp->power_20M = IXDR_GET_LONG(buf);
			objp->power_40M = IXDR_GET_LONG(buf);
			objp->power_80M = IXDR_GET_LONG(buf);
			objp->return_code = IXDR_GET_LONG(buf);
		}
	 return TRUE;
	}

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->the_channel))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->power_20M))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->power_40M))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->power_80M))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_bf_power_rpcdata (XDR *xdrs, qcsapi_wifi_get_bf_power_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->the_channel))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->number_ss))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_power_20M, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_power_40M, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_power_80M, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_bf_power_rpcdata (XDR *xdrs, qcsapi_wifi_set_bf_power_rpcdata *objp)
{
	register int32_t *buf;


	if (xdrs->x_op == XDR_ENCODE) {
		 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
			 return FALSE;
		buf = XDR_INLINE (xdrs, 6 * BYTES_PER_XDR_UNIT);
		if (buf == NULL) {
			 if (!xdr_u_int (xdrs, &objp->the_channel))
				 return FALSE;
			 if (!xdr_u_int (xdrs, &objp->number_ss))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->power_20M))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->power_40M))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->power_80M))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->return_code))
				 return FALSE;
		} else {
			IXDR_PUT_U_LONG(buf, objp->the_channel);
			IXDR_PUT_U_LONG(buf, objp->number_ss);
			IXDR_PUT_LONG(buf, objp->power_20M);
			IXDR_PUT_LONG(buf, objp->power_40M);
			IXDR_PUT_LONG(buf, objp->power_80M);
			IXDR_PUT_LONG(buf, objp->return_code);
		}
		return TRUE;
	} else if (xdrs->x_op == XDR_DECODE) {
		 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
			 return FALSE;
		buf = XDR_INLINE (xdrs, 6 * BYTES_PER_XDR_UNIT);
		if (buf == NULL) {
			 if (!xdr_u_int (xdrs, &objp->the_channel))
				 return FALSE;
			 if (!xdr_u_int (xdrs, &objp->number_ss))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->power_20M))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->power_40M))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->power_80M))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->return_code))
				 return FALSE;
		} else {
			objp->the_channel = IXDR_GET_U_LONG(buf);
			objp->number_ss = IXDR_GET_U_LONG(buf);
			objp->power_20M = IXDR_GET_LONG(buf);
			objp->power_40M = IXDR_GET_LONG(buf);
			objp->power_80M = IXDR_GET_LONG(buf);
			objp->return_code = IXDR_GET_LONG(buf);
		}
	 return TRUE;
	}

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->the_channel))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->number_ss))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->power_20M))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->power_40M))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->power_80M))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_tx_power_ext_rpcdata (XDR *xdrs, qcsapi_wifi_get_tx_power_ext_rpcdata *objp)
{
	register int32_t *buf;


	if (xdrs->x_op == XDR_ENCODE) {
		 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
			 return FALSE;
		buf = XDR_INLINE (xdrs, 3 * BYTES_PER_XDR_UNIT);
		if (buf == NULL) {
			 if (!xdr_u_int (xdrs, &objp->the_channel))
				 return FALSE;
			 if (!xdr_u_int (xdrs, &objp->bf_on))
				 return FALSE;
			 if (!xdr_u_int (xdrs, &objp->number_ss))
				 return FALSE;

		} else {
		IXDR_PUT_U_LONG(buf, objp->the_channel);
		IXDR_PUT_U_LONG(buf, objp->bf_on);
		IXDR_PUT_U_LONG(buf, objp->number_ss);
		}
		 if (!xdr_pointer (xdrs, (char **)&objp->p_power_20M, sizeof (int), (xdrproc_t) xdr_int))
			 return FALSE;
		 if (!xdr_pointer (xdrs, (char **)&objp->p_power_40M, sizeof (int), (xdrproc_t) xdr_int))
			 return FALSE;
		 if (!xdr_pointer (xdrs, (char **)&objp->p_power_80M, sizeof (int), (xdrproc_t) xdr_int))
			 return FALSE;
		 if (!xdr_int (xdrs, &objp->return_code))
			 return FALSE;
		return TRUE;
	} else if (xdrs->x_op == XDR_DECODE) {
		 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
			 return FALSE;
		buf = XDR_INLINE (xdrs, 3 * BYTES_PER_XDR_UNIT);
		if (buf == NULL) {
			 if (!xdr_u_int (xdrs, &objp->the_channel))
				 return FALSE;
			 if (!xdr_u_int (xdrs, &objp->bf_on))
				 return FALSE;
			 if (!xdr_u_int (xdrs, &objp->number_ss))
				 return FALSE;

		} else {
		objp->the_channel = IXDR_GET_U_LONG(buf);
		objp->bf_on = IXDR_GET_U_LONG(buf);
		objp->number_ss = IXDR_GET_U_LONG(buf);
		}
		 if (!xdr_pointer (xdrs, (char **)&objp->p_power_20M, sizeof (int), (xdrproc_t) xdr_int))
			 return FALSE;
		 if (!xdr_pointer (xdrs, (char **)&objp->p_power_40M, sizeof (int), (xdrproc_t) xdr_int))
			 return FALSE;
		 if (!xdr_pointer (xdrs, (char **)&objp->p_power_80M, sizeof (int), (xdrproc_t) xdr_int))
			 return FALSE;
		 if (!xdr_int (xdrs, &objp->return_code))
			 return FALSE;
	 return TRUE;
	}

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->the_channel))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->bf_on))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->number_ss))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_power_20M, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_power_40M, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_power_80M, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_tx_power_ext_rpcdata (XDR *xdrs, qcsapi_wifi_set_tx_power_ext_rpcdata *objp)
{
	register int32_t *buf;


	if (xdrs->x_op == XDR_ENCODE) {
		 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
			 return FALSE;
		buf = XDR_INLINE (xdrs, 7 * BYTES_PER_XDR_UNIT);
		if (buf == NULL) {
			 if (!xdr_u_int (xdrs, &objp->the_channel))
				 return FALSE;
			 if (!xdr_u_int (xdrs, &objp->bf_on))
				 return FALSE;
			 if (!xdr_u_int (xdrs, &objp->number_ss))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->power_20M))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->power_40M))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->power_80M))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->return_code))
				 return FALSE;
		} else {
			IXDR_PUT_U_LONG(buf, objp->the_channel);
			IXDR_PUT_U_LONG(buf, objp->bf_on);
			IXDR_PUT_U_LONG(buf, objp->number_ss);
			IXDR_PUT_LONG(buf, objp->power_20M);
			IXDR_PUT_LONG(buf, objp->power_40M);
			IXDR_PUT_LONG(buf, objp->power_80M);
			IXDR_PUT_LONG(buf, objp->return_code);
		}
		return TRUE;
	} else if (xdrs->x_op == XDR_DECODE) {
		 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
			 return FALSE;
		buf = XDR_INLINE (xdrs, 7 * BYTES_PER_XDR_UNIT);
		if (buf == NULL) {
			 if (!xdr_u_int (xdrs, &objp->the_channel))
				 return FALSE;
			 if (!xdr_u_int (xdrs, &objp->bf_on))
				 return FALSE;
			 if (!xdr_u_int (xdrs, &objp->number_ss))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->power_20M))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->power_40M))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->power_80M))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->return_code))
				 return FALSE;
		} else {
			objp->the_channel = IXDR_GET_U_LONG(buf);
			objp->bf_on = IXDR_GET_U_LONG(buf);
			objp->number_ss = IXDR_GET_U_LONG(buf);
			objp->power_20M = IXDR_GET_LONG(buf);
			objp->power_40M = IXDR_GET_LONG(buf);
			objp->power_80M = IXDR_GET_LONG(buf);
			objp->return_code = IXDR_GET_LONG(buf);
		}
	 return TRUE;
	}

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->the_channel))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->bf_on))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->number_ss))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->power_20M))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->power_40M))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->power_80M))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_chan_power_table_rpcdata (XDR *xdrs, qcsapi_wifi_get_chan_power_table_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->chan_power_table, sizeof (__rpc_qcsapi_channel_power_table), (xdrproc_t) xdr___rpc_qcsapi_channel_power_table))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_chan_power_table_rpcdata (XDR *xdrs, qcsapi_wifi_set_chan_power_table_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->chan_power_table, sizeof (__rpc_qcsapi_channel_power_table), (xdrproc_t) xdr___rpc_qcsapi_channel_power_table))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_power_selection_rpcdata (XDR *xdrs, qcsapi_wifi_get_power_selection_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->p_power_selection, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_power_selection_rpcdata (XDR *xdrs, qcsapi_wifi_set_power_selection_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_u_int (xdrs, &objp->power_selection))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_carrier_interference_rpcdata (XDR *xdrs, qcsapi_wifi_get_carrier_interference_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->ci, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_congestion_index_rpcdata (XDR *xdrs, qcsapi_wifi_get_congestion_index_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->ci, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_supported_tx_power_levels_rpcdata (XDR *xdrs, qcsapi_wifi_get_supported_tx_power_levels_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->available_percentages, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_current_tx_power_level_rpcdata (XDR *xdrs, qcsapi_wifi_get_current_tx_power_level_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_current_percentage, sizeof (uint32_t), (xdrproc_t) xdr_uint32_t))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_power_constraint_rpcdata (XDR *xdrs, qcsapi_wifi_set_power_constraint_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->pwr_constraint))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_power_constraint_rpcdata (XDR *xdrs, qcsapi_wifi_get_power_constraint_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_pwr_constraint, sizeof (uint32_t), (xdrproc_t) xdr_uint32_t))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_tpc_interval_rpcdata (XDR *xdrs, qcsapi_wifi_set_tpc_interval_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int32_t (xdrs, &objp->tpc_interval))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_tpc_interval_rpcdata (XDR *xdrs, qcsapi_wifi_get_tpc_interval_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_tpc_interval, sizeof (uint32_t), (xdrproc_t) xdr_uint32_t))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_assoc_records_rpcdata (XDR *xdrs, qcsapi_wifi_get_assoc_records_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->reset))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->records, sizeof (__rpc_qcsapi_assoc_records), (xdrproc_t) xdr___rpc_qcsapi_assoc_records))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_ap_isolate_rpcdata (XDR *xdrs, qcsapi_wifi_get_ap_isolate_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_ap_isolate, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_ap_isolate_rpcdata (XDR *xdrs, qcsapi_wifi_set_ap_isolate_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->new_ap_isolate))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_intra_bss_isolate_rpcdata (XDR *xdrs, qcsapi_wifi_get_intra_bss_isolate_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_ap_isolate, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_intra_bss_isolate_rpcdata (XDR *xdrs, qcsapi_wifi_set_intra_bss_isolate_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->new_ap_isolate))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_bss_isolate_rpcdata (XDR *xdrs, qcsapi_wifi_get_bss_isolate_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_ap_isolate, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_bss_isolate_rpcdata (XDR *xdrs, qcsapi_wifi_set_bss_isolate_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->new_ap_isolate))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_disable_dfs_channels_rpcdata (XDR *xdrs, qcsapi_wifi_disable_dfs_channels_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->disable_dfs))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->channel))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_create_restricted_bss_rpcdata (XDR *xdrs, qcsapi_wifi_create_restricted_bss_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr___rpc_qcsapi_mac_addr_p (xdrs, &objp->mac_addr))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_create_bss_rpcdata (XDR *xdrs, qcsapi_wifi_create_bss_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr___rpc_qcsapi_mac_addr_p (xdrs, &objp->mac_addr))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_remove_bss_rpcdata (XDR *xdrs, qcsapi_wifi_remove_bss_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wds_add_peer_rpcdata (XDR *xdrs, qcsapi_wds_add_peer_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr___rpc_qcsapi_mac_addr_p (xdrs, &objp->peer_address))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wds_add_peer_encrypt_rpcdata (XDR *xdrs, qcsapi_wds_add_peer_encrypt_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr___rpc_qcsapi_mac_addr_p (xdrs, &objp->peer_address))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->encryption))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wds_remove_peer_rpcdata (XDR *xdrs, qcsapi_wds_remove_peer_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr___rpc_qcsapi_mac_addr_p (xdrs, &objp->peer_address))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wds_get_peer_address_rpcdata (XDR *xdrs, qcsapi_wds_get_peer_address_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->index))
		 return FALSE;
	 if (!xdr___rpc_qcsapi_mac_addr_p (xdrs, &objp->peer_address))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wds_set_psk_rpcdata (XDR *xdrs, qcsapi_wds_set_psk_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr___rpc_qcsapi_mac_addr_p (xdrs, &objp->peer_address))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->pre_shared_key, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wds_set_mode_rpcdata (XDR *xdrs, qcsapi_wds_set_mode_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr___rpc_qcsapi_mac_addr_p (xdrs, &objp->peer_address))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->mode))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wds_get_mode_rpcdata (XDR *xdrs, qcsapi_wds_get_mode_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->index))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->mode, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_extender_params_rpcdata (XDR *xdrs, qcsapi_wifi_set_extender_params_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->type))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->param_value))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_extender_params_rpcdata (XDR *xdrs, qcsapi_wifi_get_extender_params_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->type))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_value, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_beacon_type_rpcdata (XDR *xdrs, qcsapi_wifi_get_beacon_type_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_current_beacon, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_beacon_type_rpcdata (XDR *xdrs, qcsapi_wifi_set_beacon_type_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_new_beacon, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_WEP_key_index_rpcdata (XDR *xdrs, qcsapi_wifi_get_WEP_key_index_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_key_index, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_WEP_key_index_rpcdata (XDR *xdrs, qcsapi_wifi_set_WEP_key_index_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->key_index))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_WEP_key_passphrase_rpcdata (XDR *xdrs, qcsapi_wifi_get_WEP_key_passphrase_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->current_passphrase, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_WEP_key_passphrase_rpcdata (XDR *xdrs, qcsapi_wifi_set_WEP_key_passphrase_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->new_passphrase, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_WEP_encryption_level_rpcdata (XDR *xdrs, qcsapi_wifi_get_WEP_encryption_level_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->current_encryption_level, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_basic_encryption_modes_rpcdata (XDR *xdrs, qcsapi_wifi_get_basic_encryption_modes_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->encryption_modes, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_basic_encryption_modes_rpcdata (XDR *xdrs, qcsapi_wifi_set_basic_encryption_modes_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->encryption_modes, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_basic_authentication_mode_rpcdata (XDR *xdrs, qcsapi_wifi_get_basic_authentication_mode_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->authentication_mode, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_basic_authentication_mode_rpcdata (XDR *xdrs, qcsapi_wifi_set_basic_authentication_mode_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->authentication_mode, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_WEP_key_rpcdata (XDR *xdrs, qcsapi_wifi_get_WEP_key_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->key_index))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->current_passphrase, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_WEP_key_rpcdata (XDR *xdrs, qcsapi_wifi_set_WEP_key_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->key_index))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->new_passphrase, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_WPA_encryption_modes_rpcdata (XDR *xdrs, qcsapi_wifi_get_WPA_encryption_modes_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->encryption_modes, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_WPA_encryption_modes_rpcdata (XDR *xdrs, qcsapi_wifi_set_WPA_encryption_modes_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->encryption_modes, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_WPA_authentication_mode_rpcdata (XDR *xdrs, qcsapi_wifi_get_WPA_authentication_mode_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->authentication_mode, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_WPA_authentication_mode_rpcdata (XDR *xdrs, qcsapi_wifi_set_WPA_authentication_mode_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->authentication_mode, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_interworking_rpcdata (XDR *xdrs, qcsapi_wifi_get_interworking_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->interworking, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_interworking_rpcdata (XDR *xdrs, qcsapi_wifi_set_interworking_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->interworking, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_80211u_params_rpcdata (XDR *xdrs, qcsapi_wifi_get_80211u_params_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->u_param, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_buffer, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_80211u_params_rpcdata (XDR *xdrs, qcsapi_wifi_set_80211u_params_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->param, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->value1, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->value2, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_security_get_nai_realms_rpcdata (XDR *xdrs, qcsapi_security_get_nai_realms_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_value, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_security_add_nai_realm_rpcdata (XDR *xdrs, qcsapi_security_add_nai_realm_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->encoding))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->nai_realm, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->eap_method, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_security_del_nai_realm_rpcdata (XDR *xdrs, qcsapi_security_del_nai_realm_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->nai_realm, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_security_get_roaming_consortium_rpcdata (XDR *xdrs, qcsapi_security_get_roaming_consortium_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_value, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_security_add_roaming_consortium_rpcdata (XDR *xdrs, qcsapi_security_add_roaming_consortium_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_value, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_security_del_roaming_consortium_rpcdata (XDR *xdrs, qcsapi_security_del_roaming_consortium_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_value, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_security_get_venue_name_rpcdata (XDR *xdrs, qcsapi_security_get_venue_name_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_value, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_security_add_venue_name_rpcdata (XDR *xdrs, qcsapi_security_add_venue_name_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->lang_code, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->venue_name, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_security_del_venue_name_rpcdata (XDR *xdrs, qcsapi_security_del_venue_name_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->lang_code, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->venue_name, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_security_get_oper_friendly_name_rpcdata (XDR *xdrs, qcsapi_security_get_oper_friendly_name_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_value, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_security_add_oper_friendly_name_rpcdata (XDR *xdrs, qcsapi_security_add_oper_friendly_name_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->lang_code, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->oper_friendly_name, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_security_del_oper_friendly_name_rpcdata (XDR *xdrs, qcsapi_security_del_oper_friendly_name_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->lang_code, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->oper_friendly_name, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_security_get_hs20_conn_capab_rpcdata (XDR *xdrs, qcsapi_security_get_hs20_conn_capab_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_value, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_security_add_hs20_conn_capab_rpcdata (XDR *xdrs, qcsapi_security_add_hs20_conn_capab_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->ip_proto, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->port_num, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->status, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_security_del_hs20_conn_capab_rpcdata (XDR *xdrs, qcsapi_security_del_hs20_conn_capab_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->ip_proto, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->port_num, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->status, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_hs20_status_rpcdata (XDR *xdrs, qcsapi_wifi_get_hs20_status_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_hs20, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_hs20_status_rpcdata (XDR *xdrs, qcsapi_wifi_set_hs20_status_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->hs20_val, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_proxy_arp_rpcdata (XDR *xdrs, qcsapi_wifi_get_proxy_arp_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_proxy_arp, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_proxy_arp_rpcdata (XDR *xdrs, qcsapi_wifi_set_proxy_arp_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->proxy_arp_val, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_l2_ext_filter_rpcdata (XDR *xdrs, qcsapi_wifi_get_l2_ext_filter_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->param, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->value, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_l2_ext_filter_rpcdata (XDR *xdrs, qcsapi_wifi_set_l2_ext_filter_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->param, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->value, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_hs20_params_rpcdata (XDR *xdrs, qcsapi_wifi_get_hs20_params_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->hs_param, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_buffer, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_hs20_params_rpcdata (XDR *xdrs, qcsapi_wifi_set_hs20_params_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->hs_param, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->value1, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->value2, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->value3, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->value4, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->value5, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->value6, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_remove_11u_param_rpcdata (XDR *xdrs, qcsapi_remove_11u_param_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->param, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_remove_hs20_param_rpcdata (XDR *xdrs, qcsapi_remove_hs20_param_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->hs_param, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_IEEE11i_encryption_modes_rpcdata (XDR *xdrs, qcsapi_wifi_get_IEEE11i_encryption_modes_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->encryption_modes, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_IEEE11i_encryption_modes_rpcdata (XDR *xdrs, qcsapi_wifi_set_IEEE11i_encryption_modes_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->encryption_modes, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_IEEE11i_authentication_mode_rpcdata (XDR *xdrs, qcsapi_wifi_get_IEEE11i_authentication_mode_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->authentication_mode, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_IEEE11i_authentication_mode_rpcdata (XDR *xdrs, qcsapi_wifi_set_IEEE11i_authentication_mode_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->authentication_mode, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_michael_errcnt_rpcdata (XDR *xdrs, qcsapi_wifi_get_michael_errcnt_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->errcount, sizeof (uint32_t), (xdrproc_t) xdr_uint32_t))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_pre_shared_key_rpcdata (XDR *xdrs, qcsapi_wifi_get_pre_shared_key_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->key_index))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->pre_shared_key, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_pre_shared_key_rpcdata (XDR *xdrs, qcsapi_wifi_set_pre_shared_key_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->key_index))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->pre_shared_key, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_add_radius_auth_server_cfg_rpcdata (XDR *xdrs, qcsapi_wifi_add_radius_auth_server_cfg_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->radius_auth_server_ipaddr, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->radius_auth_server_port, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->radius_auth_server_sh_key, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_del_radius_auth_server_cfg_rpcdata (XDR *xdrs, qcsapi_wifi_del_radius_auth_server_cfg_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->radius_auth_server_ipaddr, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->constp_radius_port, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_radius_auth_server_cfg_rpcdata (XDR *xdrs, qcsapi_wifi_get_radius_auth_server_cfg_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->radius_auth_server_cfg, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_own_ip_addr_rpcdata (XDR *xdrs, qcsapi_wifi_set_own_ip_addr_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->own_ip_addr, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_key_passphrase_rpcdata (XDR *xdrs, qcsapi_wifi_get_key_passphrase_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->key_index))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->passphrase, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_key_passphrase_rpcdata (XDR *xdrs, qcsapi_wifi_set_key_passphrase_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->key_index))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->passphrase, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_group_key_interval_rpcdata (XDR *xdrs, qcsapi_wifi_get_group_key_interval_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->group_key_interval, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_group_key_interval_rpcdata (XDR *xdrs, qcsapi_wifi_set_group_key_interval_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->group_key_interval, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_pmf_rpcdata (XDR *xdrs, qcsapi_wifi_get_pmf_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_pmf_cap, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_pmf_rpcdata (XDR *xdrs, qcsapi_wifi_set_pmf_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->pmf_cap))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_wpa_status_rpcdata (XDR *xdrs, qcsapi_wifi_get_wpa_status_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->mac_addr, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->max_len))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->wpa_status, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_psk_auth_failures_rpcdata (XDR *xdrs, qcsapi_wifi_get_psk_auth_failures_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->count, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_auth_state_rpcdata (XDR *xdrs, qcsapi_wifi_get_auth_state_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->mac_addr, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->auth_state, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_security_defer_mode_rpcdata (XDR *xdrs, qcsapi_wifi_set_security_defer_mode_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->defer))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_security_defer_mode_rpcdata (XDR *xdrs, qcsapi_wifi_get_security_defer_mode_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->defer, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_apply_security_config_rpcdata (XDR *xdrs, qcsapi_wifi_apply_security_config_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_mac_address_filtering_rpcdata (XDR *xdrs, qcsapi_wifi_set_mac_address_filtering_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->new_mac_address_filtering))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_mac_address_filtering_rpcdata (XDR *xdrs, qcsapi_wifi_get_mac_address_filtering_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->current_mac_address_filtering, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_authorize_mac_address_rpcdata (XDR *xdrs, qcsapi_wifi_authorize_mac_address_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr___rpc_qcsapi_mac_addr_p (xdrs, &objp->address_to_authorize))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_deny_mac_address_rpcdata (XDR *xdrs, qcsapi_wifi_deny_mac_address_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr___rpc_qcsapi_mac_addr_p (xdrs, &objp->address_to_deny))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_remove_mac_address_rpcdata (XDR *xdrs, qcsapi_wifi_remove_mac_address_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr___rpc_qcsapi_mac_addr_p (xdrs, &objp->address_to_remove))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_is_mac_address_authorized_rpcdata (XDR *xdrs, qcsapi_wifi_is_mac_address_authorized_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr___rpc_qcsapi_mac_addr_p (xdrs, &objp->address_to_verify))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_mac_address_authorized, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_authorized_mac_addresses_rpcdata (XDR *xdrs, qcsapi_wifi_get_authorized_mac_addresses_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->sizeof_list))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->list_mac_addresses, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_denied_mac_addresses_rpcdata (XDR *xdrs, qcsapi_wifi_get_denied_mac_addresses_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->sizeof_list))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->list_mac_addresses, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_accept_oui_filter_rpcdata (XDR *xdrs, qcsapi_wifi_set_accept_oui_filter_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr___rpc_qcsapi_mac_addr_p (xdrs, &objp->oui))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->flag))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_accept_oui_filter_rpcdata (XDR *xdrs, qcsapi_wifi_get_accept_oui_filter_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->sizeof_list))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->oui_list, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_clear_mac_address_filters_rpcdata (XDR *xdrs, qcsapi_wifi_clear_mac_address_filters_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_mac_address_reserve_rpcdata (XDR *xdrs, qcsapi_wifi_set_mac_address_reserve_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->addr, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->mask, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_mac_address_reserve_rpcdata (XDR *xdrs, qcsapi_wifi_get_mac_address_reserve_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->buf, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_clear_mac_address_reserve_rpcdata (XDR *xdrs, qcsapi_wifi_clear_mac_address_reserve_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_option_rpcdata (XDR *xdrs, qcsapi_wifi_get_option_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->qcsapi_option))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_current_option, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_option_rpcdata (XDR *xdrs, qcsapi_wifi_set_option_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->qcsapi_option))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->new_option))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_get_board_parameter_rpcdata (XDR *xdrs, qcsapi_get_board_parameter_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_int (xdrs, &objp->board_param))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_buffer, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_get_swfeat_list_rpcdata (XDR *xdrs, qcsapi_get_swfeat_list_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->p_buffer, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_SSID_create_SSID_rpcdata (XDR *xdrs, qcsapi_SSID_create_SSID_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->new_SSID, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_SSID_remove_SSID_rpcdata (XDR *xdrs, qcsapi_SSID_remove_SSID_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->del_SSID, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_SSID_verify_SSID_rpcdata (XDR *xdrs, qcsapi_SSID_verify_SSID_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->current_SSID, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_SSID_rename_SSID_rpcdata (XDR *xdrs, qcsapi_SSID_rename_SSID_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->current_SSID, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->new_SSID, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_SSID_get_SSID_list_rpcdata (XDR *xdrs, qcsapi_SSID_get_SSID_list_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->arrayc))
		 return FALSE;
	 if (!xdr_array (xdrs, (char **)&objp->list_SSID.list_SSID_val, (u_int *) &objp->list_SSID.list_SSID_len, ~0,
		sizeof (str), (xdrproc_t) xdr_str))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_SSID_set_protocol_rpcdata (XDR *xdrs, qcsapi_SSID_set_protocol_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->current_SSID, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->new_protocol, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_SSID_get_protocol_rpcdata (XDR *xdrs, qcsapi_SSID_get_protocol_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->current_SSID, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->current_protocol, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_SSID_get_encryption_modes_rpcdata (XDR *xdrs, qcsapi_SSID_get_encryption_modes_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->current_SSID, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->encryption_modes, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_SSID_set_encryption_modes_rpcdata (XDR *xdrs, qcsapi_SSID_set_encryption_modes_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->current_SSID, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->encryption_modes, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_SSID_get_group_encryption_rpcdata (XDR *xdrs, qcsapi_SSID_get_group_encryption_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->current_SSID, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->encryption_mode, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_SSID_set_group_encryption_rpcdata (XDR *xdrs, qcsapi_SSID_set_group_encryption_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->current_SSID, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->encryption_mode, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_SSID_get_authentication_mode_rpcdata (XDR *xdrs, qcsapi_SSID_get_authentication_mode_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->current_SSID, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->authentication_mode, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_SSID_set_authentication_mode_rpcdata (XDR *xdrs, qcsapi_SSID_set_authentication_mode_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->current_SSID, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->authentication_mode, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_SSID_get_pre_shared_key_rpcdata (XDR *xdrs, qcsapi_SSID_get_pre_shared_key_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->current_SSID, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->key_index))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->pre_shared_key, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_SSID_set_pre_shared_key_rpcdata (XDR *xdrs, qcsapi_SSID_set_pre_shared_key_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->current_SSID, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->key_index))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->pre_shared_key, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_SSID_get_key_passphrase_rpcdata (XDR *xdrs, qcsapi_SSID_get_key_passphrase_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->current_SSID, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->key_index))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->passphrase, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_SSID_set_key_passphrase_rpcdata (XDR *xdrs, qcsapi_SSID_set_key_passphrase_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->current_SSID, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->key_index))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->passphrase, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_SSID_get_pmf_rpcdata (XDR *xdrs, qcsapi_SSID_get_pmf_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->current_SSID, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_pmf_cap, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_SSID_set_pmf_rpcdata (XDR *xdrs, qcsapi_SSID_set_pmf_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->SSID_str, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->pmf_cap))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_SSID_get_wps_SSID_rpcdata (XDR *xdrs, qcsapi_SSID_get_wps_SSID_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->wps_SSID, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_vlan_config_rpcdata (XDR *xdrs, qcsapi_wifi_vlan_config_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->cmd))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->vlanid))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->flags))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_show_vlan_config_rpcdata (XDR *xdrs, qcsapi_wifi_show_vlan_config_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->vcfg, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_enable_vlan_pass_through_rpcdata (XDR *xdrs, qcsapi_enable_vlan_pass_through_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->enabled))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_vlan_promisc_rpcdata (XDR *xdrs, qcsapi_wifi_set_vlan_promisc_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_int (xdrs, &objp->enable))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wps_registrar_report_button_press_rpcdata (XDR *xdrs, qcsapi_wps_registrar_report_button_press_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wps_registrar_report_pin_rpcdata (XDR *xdrs, qcsapi_wps_registrar_report_pin_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->wps_pin, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wps_registrar_get_pp_devname_rpcdata (XDR *xdrs, qcsapi_wps_registrar_get_pp_devname_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->blacklist))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->pp_devname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wps_registrar_set_pp_devname_rpcdata (XDR *xdrs, qcsapi_wps_registrar_set_pp_devname_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->update_blacklist))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->pp_devname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wps_enrollee_report_button_press_rpcdata (XDR *xdrs, qcsapi_wps_enrollee_report_button_press_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr___rpc_qcsapi_mac_addr_p (xdrs, &objp->bssid))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wps_enrollee_report_pin_rpcdata (XDR *xdrs, qcsapi_wps_enrollee_report_pin_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr___rpc_qcsapi_mac_addr_p (xdrs, &objp->bssid))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->wps_pin, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wps_enrollee_generate_pin_rpcdata (XDR *xdrs, qcsapi_wps_enrollee_generate_pin_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr___rpc_qcsapi_mac_addr_p (xdrs, &objp->bssid))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->wps_pin, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wps_get_ap_pin_rpcdata (XDR *xdrs, qcsapi_wps_get_ap_pin_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->force_regenerate))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->wps_pin, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wps_set_ap_pin_rpcdata (XDR *xdrs, qcsapi_wps_set_ap_pin_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->wps_pin, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wps_save_ap_pin_rpcdata (XDR *xdrs, qcsapi_wps_save_ap_pin_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wps_enable_ap_pin_rpcdata (XDR *xdrs, qcsapi_wps_enable_ap_pin_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->enable))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wps_get_sta_pin_rpcdata (XDR *xdrs, qcsapi_wps_get_sta_pin_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->wps_pin, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wps_get_state_rpcdata (XDR *xdrs, qcsapi_wps_get_state_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->max_len))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->wps_state, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wps_get_configured_state_rpcdata (XDR *xdrs, qcsapi_wps_get_configured_state_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->max_len))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->wps_state, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wps_get_runtime_state_rpcdata (XDR *xdrs, qcsapi_wps_get_runtime_state_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->max_len))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->state, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wps_set_configured_state_rpcdata (XDR *xdrs, qcsapi_wps_set_configured_state_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->state))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wps_get_param_rpcdata (XDR *xdrs, qcsapi_wps_get_param_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->wps_type))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->max_len))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->wps_str, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wps_set_timeout_rpcdata (XDR *xdrs, qcsapi_wps_set_timeout_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->value))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wps_on_hidden_ssid_rpcdata (XDR *xdrs, qcsapi_wps_on_hidden_ssid_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->value))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wps_on_hidden_ssid_status_rpcdata (XDR *xdrs, qcsapi_wps_on_hidden_ssid_status_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->max_len))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->state, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wps_upnp_enable_rpcdata (XDR *xdrs, qcsapi_wps_upnp_enable_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->value))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wps_upnp_status_rpcdata (XDR *xdrs, qcsapi_wps_upnp_status_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->reply_len))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->reply, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wps_allow_pbc_overlap_rpcdata (XDR *xdrs, qcsapi_wps_allow_pbc_overlap_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->allow))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wps_get_allow_pbc_overlap_status_rpcdata (XDR *xdrs, qcsapi_wps_get_allow_pbc_overlap_status_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->status, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wps_set_access_control_rpcdata (XDR *xdrs, qcsapi_wps_set_access_control_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->ctrl_state))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wps_get_access_control_rpcdata (XDR *xdrs, qcsapi_wps_get_access_control_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->ctrl_state, sizeof (uint32_t), (xdrproc_t) xdr_uint32_t))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wps_set_param_rpcdata (XDR *xdrs, qcsapi_wps_set_param_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->param_type))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->param_value, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wps_cancel_rpcdata (XDR *xdrs, qcsapi_wps_cancel_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wps_set_pbc_in_srcm_rpcdata (XDR *xdrs, qcsapi_wps_set_pbc_in_srcm_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->enabled))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wps_get_pbc_in_srcm_rpcdata (XDR *xdrs, qcsapi_wps_get_pbc_in_srcm_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_enabled, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_registrar_set_default_pbc_bss_rpcdata (XDR *xdrs, qcsapi_registrar_set_default_pbc_bss_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_registrar_get_default_pbc_bss_rpcdata (XDR *xdrs, qcsapi_registrar_get_default_pbc_bss_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_int (xdrs, &objp->len))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->default_bss, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_gpio_set_config_rpcdata (XDR *xdrs, qcsapi_gpio_set_config_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_uint8_t (xdrs, &objp->gpio_pin))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->new_gpio_config))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_gpio_get_config_rpcdata (XDR *xdrs, qcsapi_gpio_get_config_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_uint8_t (xdrs, &objp->gpio_pin))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_gpio_config, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_led_get_rpcdata (XDR *xdrs, qcsapi_led_get_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_uint8_t (xdrs, &objp->led_ident))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_led_setting, sizeof (uint8_t), (xdrproc_t) xdr_uint8_t))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_led_set_rpcdata (XDR *xdrs, qcsapi_led_set_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_uint8_t (xdrs, &objp->led_ident))
		 return FALSE;
	 if (!xdr_uint8_t (xdrs, &objp->new_led_setting))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_led_pwm_enable_rpcdata (XDR *xdrs, qcsapi_led_pwm_enable_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_uint8_t (xdrs, &objp->led_ident))
		 return FALSE;
	 if (!xdr_uint8_t (xdrs, &objp->onoff))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->high_count))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->low_count))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_led_brightness_rpcdata (XDR *xdrs, qcsapi_led_brightness_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_uint8_t (xdrs, &objp->led_ident))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->level))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_gpio_enable_wps_push_button_rpcdata (XDR *xdrs, qcsapi_gpio_enable_wps_push_button_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_uint8_t (xdrs, &objp->wps_push_button))
		 return FALSE;
	 if (!xdr_uint8_t (xdrs, &objp->active_logic))
		 return FALSE;
	 if (!xdr_uint8_t (xdrs, &objp->use_interrupt_flag))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_count_associations_rpcdata (XDR *xdrs, qcsapi_wifi_get_count_associations_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_association_count, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_associated_device_mac_addr_rpcdata (XDR *xdrs, qcsapi_wifi_get_associated_device_mac_addr_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->device_index))
		 return FALSE;
	 if (!xdr___rpc_qcsapi_mac_addr_p (xdrs, &objp->device_mac_addr))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_associated_device_ip_addr_rpcdata (XDR *xdrs, qcsapi_wifi_get_associated_device_ip_addr_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->device_index))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->ip_addr, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_link_quality_rpcdata (XDR *xdrs, qcsapi_wifi_get_link_quality_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->association_index))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_link_quality, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_link_quality_max_rpcdata (XDR *xdrs, qcsapi_wifi_get_link_quality_max_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_max_quality, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_rx_bytes_per_association_rpcdata (XDR *xdrs, qcsapi_wifi_get_rx_bytes_per_association_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->association_index))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_rx_bytes, sizeof (uint64_t), (xdrproc_t) xdr_uint64_t))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_tx_bytes_per_association_rpcdata (XDR *xdrs, qcsapi_wifi_get_tx_bytes_per_association_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->association_index))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_tx_bytes, sizeof (uint64_t), (xdrproc_t) xdr_uint64_t))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_rx_packets_per_association_rpcdata (XDR *xdrs, qcsapi_wifi_get_rx_packets_per_association_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->association_index))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_rx_packets, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_tx_packets_per_association_rpcdata (XDR *xdrs, qcsapi_wifi_get_tx_packets_per_association_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->association_index))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_tx_packets, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_tx_err_packets_per_association_rpcdata (XDR *xdrs, qcsapi_wifi_get_tx_err_packets_per_association_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->association_index))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_tx_err_packets, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_rssi_per_association_rpcdata (XDR *xdrs, qcsapi_wifi_get_rssi_per_association_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->association_index))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_rssi, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_rssi_in_dbm_per_association_rpcdata (XDR *xdrs, qcsapi_wifi_get_rssi_in_dbm_per_association_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->association_index))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_rssi, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_bw_per_association_rpcdata (XDR *xdrs, qcsapi_wifi_get_bw_per_association_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->association_index))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_bw, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_tx_phy_rate_per_association_rpcdata (XDR *xdrs, qcsapi_wifi_get_tx_phy_rate_per_association_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->association_index))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_tx_phy_rate, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_rx_phy_rate_per_association_rpcdata (XDR *xdrs, qcsapi_wifi_get_rx_phy_rate_per_association_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->association_index))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_rx_phy_rate, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_tx_mcs_per_association_rpcdata (XDR *xdrs, qcsapi_wifi_get_tx_mcs_per_association_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->association_index))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_mcs, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_rx_mcs_per_association_rpcdata (XDR *xdrs, qcsapi_wifi_get_rx_mcs_per_association_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->association_index))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_mcs, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_achievable_tx_phy_rate_per_association_rpcdata (XDR *xdrs, qcsapi_wifi_get_achievable_tx_phy_rate_per_association_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->association_index))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_achievable_tx_phy_rate, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_achievable_rx_phy_rate_per_association_rpcdata (XDR *xdrs, qcsapi_wifi_get_achievable_rx_phy_rate_per_association_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->association_index))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_achievable_rx_phy_rate, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_auth_enc_per_association_rpcdata (XDR *xdrs, qcsapi_wifi_get_auth_enc_per_association_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->association_index))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_auth_enc, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_tput_caps_rpcdata (XDR *xdrs, qcsapi_wifi_get_tput_caps_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->association_index))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->tput_caps, sizeof (__rpc_ieee8011req_sta_tput_caps), (xdrproc_t) xdr___rpc_ieee8011req_sta_tput_caps))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_connection_mode_rpcdata (XDR *xdrs, qcsapi_wifi_get_connection_mode_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->association_index))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->connection_mode, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_vendor_per_association_rpcdata (XDR *xdrs, qcsapi_wifi_get_vendor_per_association_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->association_index))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_vendor, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_max_mimo_rpcdata (XDR *xdrs, qcsapi_wifi_get_max_mimo_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->association_index))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_max_mimo, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_snr_per_association_rpcdata (XDR *xdrs, qcsapi_wifi_get_snr_per_association_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->association_index))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_snr, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_time_associated_per_association_rpcdata (XDR *xdrs, qcsapi_wifi_get_time_associated_per_association_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->association_index))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->time_associated, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_node_param_rpcdata (XDR *xdrs, qcsapi_wifi_get_node_param_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->node_index))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->param_type))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->local_remote_flag))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->input_param_str, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->report_result, sizeof (__rpc_qcsapi_measure_report_result), (xdrproc_t) xdr___rpc_qcsapi_measure_report_result))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_node_counter_rpcdata (XDR *xdrs, qcsapi_wifi_get_node_counter_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->node_index))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->counter_type))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->local_remote_flag))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_value, sizeof (uint64_t), (xdrproc_t) xdr_uint64_t))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_node_stats_rpcdata (XDR *xdrs, qcsapi_wifi_get_node_stats_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->node_index))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->local_remote_flag))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_stats, sizeof (__rpc_qcsapi_node_stats), (xdrproc_t) xdr___rpc_qcsapi_node_stats))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_max_queued_rpcdata (XDR *xdrs, qcsapi_wifi_get_max_queued_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->node_index))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->local_remote_flag))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->reset_flag))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->max_queued, sizeof (uint32_t), (xdrproc_t) xdr_uint32_t))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_hw_noise_per_association_rpcdata (XDR *xdrs, qcsapi_wifi_get_hw_noise_per_association_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->association_index))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_hw_noise, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_mlme_stats_per_mac_rpcdata (XDR *xdrs, qcsapi_wifi_get_mlme_stats_per_mac_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr___rpc_qcsapi_mac_addr_p (xdrs, &objp->client_mac_addr))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->stats, sizeof (__rpc_qcsapi_mlme_stats), (xdrproc_t) xdr___rpc_qcsapi_mlme_stats))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_mlme_stats_per_association_rpcdata (XDR *xdrs, qcsapi_wifi_get_mlme_stats_per_association_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->association_index))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->stats, sizeof (__rpc_qcsapi_mlme_stats), (xdrproc_t) xdr___rpc_qcsapi_mlme_stats))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_mlme_stats_macs_list_rpcdata (XDR *xdrs, qcsapi_wifi_get_mlme_stats_macs_list_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->macs_list, sizeof (__rpc_qcsapi_mlme_stats_macs), (xdrproc_t) xdr___rpc_qcsapi_mlme_stats_macs))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_list_regulatory_regions_rpcdata (XDR *xdrs, qcsapi_wifi_get_list_regulatory_regions_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->list_regulatory_regions, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_regulatory_get_list_regulatory_regions_rpcdata (XDR *xdrs, qcsapi_regulatory_get_list_regulatory_regions_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->list_regulatory_regions, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_list_regulatory_channels_rpcdata (XDR *xdrs, qcsapi_wifi_get_list_regulatory_channels_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->region_by_name, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->bw))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->list_of_channels, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_regulatory_get_list_regulatory_channels_rpcdata (XDR *xdrs, qcsapi_regulatory_get_list_regulatory_channels_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->region_by_name, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->bw))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->list_of_channels, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_regulatory_get_list_regulatory_bands_rpcdata (XDR *xdrs, qcsapi_regulatory_get_list_regulatory_bands_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->region_by_name, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->list_of_bands, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_regulatory_tx_power_rpcdata (XDR *xdrs, qcsapi_wifi_get_regulatory_tx_power_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->the_channel))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->region_by_name, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_tx_power, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_regulatory_get_regulatory_tx_power_rpcdata (XDR *xdrs, qcsapi_regulatory_get_regulatory_tx_power_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->the_channel))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->region_by_name, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_tx_power, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_configured_tx_power_rpcdata (XDR *xdrs, qcsapi_wifi_get_configured_tx_power_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->the_channel))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->region_by_name, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->bw))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_tx_power, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_regulatory_get_configured_tx_power_rpcdata (XDR *xdrs, qcsapi_regulatory_get_configured_tx_power_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->the_channel))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->region_by_name, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->bw))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_tx_power, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_regulatory_get_configured_tx_power_ext_rpcdata (XDR *xdrs, qcsapi_regulatory_get_configured_tx_power_ext_rpcdata *objp)
{
	register int32_t *buf;


	if (xdrs->x_op == XDR_ENCODE) {
		 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
			 return FALSE;
		 if (!xdr_u_int (xdrs, &objp->the_channel))
			 return FALSE;
		 if (!xdr_pointer (xdrs, (char **)&objp->region_by_name, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
			 return FALSE;
		buf = XDR_INLINE (xdrs, 3 * BYTES_PER_XDR_UNIT);
		if (buf == NULL) {
			 if (!xdr_int (xdrs, &objp->the_bw))
				 return FALSE;
			 if (!xdr_u_int (xdrs, &objp->bf_on))
				 return FALSE;
			 if (!xdr_u_int (xdrs, &objp->number_ss))
				 return FALSE;

		} else {
		IXDR_PUT_LONG(buf, objp->the_bw);
		IXDR_PUT_U_LONG(buf, objp->bf_on);
		IXDR_PUT_U_LONG(buf, objp->number_ss);
		}
		 if (!xdr_pointer (xdrs, (char **)&objp->p_tx_power, sizeof (int), (xdrproc_t) xdr_int))
			 return FALSE;
		 if (!xdr_int (xdrs, &objp->return_code))
			 return FALSE;
		return TRUE;
	} else if (xdrs->x_op == XDR_DECODE) {
		 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
			 return FALSE;
		 if (!xdr_u_int (xdrs, &objp->the_channel))
			 return FALSE;
		 if (!xdr_pointer (xdrs, (char **)&objp->region_by_name, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
			 return FALSE;
		buf = XDR_INLINE (xdrs, 3 * BYTES_PER_XDR_UNIT);
		if (buf == NULL) {
			 if (!xdr_int (xdrs, &objp->the_bw))
				 return FALSE;
			 if (!xdr_u_int (xdrs, &objp->bf_on))
				 return FALSE;
			 if (!xdr_u_int (xdrs, &objp->number_ss))
				 return FALSE;

		} else {
		objp->the_bw = IXDR_GET_LONG(buf);
		objp->bf_on = IXDR_GET_U_LONG(buf);
		objp->number_ss = IXDR_GET_U_LONG(buf);
		}
		 if (!xdr_pointer (xdrs, (char **)&objp->p_tx_power, sizeof (int), (xdrproc_t) xdr_int))
			 return FALSE;
		 if (!xdr_int (xdrs, &objp->return_code))
			 return FALSE;
	 return TRUE;
	}

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->the_channel))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->region_by_name, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->the_bw))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->bf_on))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->number_ss))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_tx_power, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_regulatory_region_rpcdata (XDR *xdrs, qcsapi_wifi_set_regulatory_region_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->region_by_name, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_regulatory_set_regulatory_region_rpcdata (XDR *xdrs, qcsapi_regulatory_set_regulatory_region_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->region_by_name, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_regulatory_restore_regulatory_tx_power_rpcdata (XDR *xdrs, qcsapi_regulatory_restore_regulatory_tx_power_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_regulatory_region_rpcdata (XDR *xdrs, qcsapi_wifi_get_regulatory_region_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->region_by_name, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_regulatory_overwrite_country_code_rpcdata (XDR *xdrs, qcsapi_regulatory_overwrite_country_code_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->curr_country_name, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->new_country_name, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_regulatory_channel_rpcdata (XDR *xdrs, qcsapi_wifi_set_regulatory_channel_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->the_channel))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->region_by_name, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->tx_power_offset))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_regulatory_set_regulatory_channel_rpcdata (XDR *xdrs, qcsapi_regulatory_set_regulatory_channel_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->the_channel))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->region_by_name, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->tx_power_offset))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_regulatory_get_db_version_rpcdata (XDR *xdrs, qcsapi_regulatory_get_db_version_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_int (xdrs, &objp->index))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_version, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_regulatory_apply_tx_power_cap_rpcdata (XDR *xdrs, qcsapi_regulatory_apply_tx_power_cap_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_int (xdrs, &objp->capped))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_list_DFS_channels_rpcdata (XDR *xdrs, qcsapi_wifi_get_list_DFS_channels_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->region_by_name, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->DFS_flag))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->bw))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->list_of_channels, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_regulatory_get_list_DFS_channels_rpcdata (XDR *xdrs, qcsapi_regulatory_get_list_DFS_channels_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->region_by_name, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->DFS_flag))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->bw))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->list_of_channels, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_is_channel_DFS_rpcdata (XDR *xdrs, qcsapi_wifi_is_channel_DFS_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->region_by_name, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->the_channel))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_channel_is_DFS, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_regulatory_is_channel_DFS_rpcdata (XDR *xdrs, qcsapi_regulatory_is_channel_DFS_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->region_by_name, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->the_channel))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_channel_is_DFS, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_dfs_cce_channels_rpcdata (XDR *xdrs, qcsapi_wifi_get_dfs_cce_channels_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_prev_channel, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_cur_channel, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_DFS_alt_channel_rpcdata (XDR *xdrs, qcsapi_wifi_get_DFS_alt_channel_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_dfs_alt_chan, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_DFS_alt_channel_rpcdata (XDR *xdrs, qcsapi_wifi_set_DFS_alt_channel_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->dfs_alt_chan))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_start_dfs_reentry_rpcdata (XDR *xdrs, qcsapi_wifi_start_dfs_reentry_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_start_scan_ext_rpcdata (XDR *xdrs, qcsapi_wifi_start_scan_ext_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->scan_flag))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_csw_records_rpcdata (XDR *xdrs, qcsapi_wifi_get_csw_records_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->reset))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->record, sizeof (__rpc_qcsapi_csw_record), (xdrproc_t) xdr___rpc_qcsapi_csw_record))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_radar_status_rpcdata (XDR *xdrs, qcsapi_wifi_get_radar_status_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->rdstatus, sizeof (__rpc_qcsapi_radar_status), (xdrproc_t) xdr___rpc_qcsapi_radar_status))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_cac_status_rpcdata (XDR *xdrs, qcsapi_wifi_get_cac_status_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->cacstatus, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_results_AP_scan_rpcdata (XDR *xdrs, qcsapi_wifi_get_results_AP_scan_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_count_APs, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_count_APs_scanned_rpcdata (XDR *xdrs, qcsapi_wifi_get_count_APs_scanned_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_count_APs, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_properties_AP_rpcdata (XDR *xdrs, qcsapi_wifi_get_properties_AP_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->index_AP))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_ap_properties, sizeof (__rpc_qcsapi_ap_properties), (xdrproc_t) xdr___rpc_qcsapi_ap_properties))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_scan_chk_inv_rpcdata (XDR *xdrs, qcsapi_wifi_set_scan_chk_inv_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->scan_chk_inv))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_scan_chk_inv_rpcdata (XDR *xdrs, qcsapi_wifi_get_scan_chk_inv_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_scan_buf_max_size_rpcdata (XDR *xdrs, qcsapi_wifi_set_scan_buf_max_size_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->max_buf_size))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_scan_buf_max_size_rpcdata (XDR *xdrs, qcsapi_wifi_get_scan_buf_max_size_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->max_buf_size, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_scan_table_max_len_rpcdata (XDR *xdrs, qcsapi_wifi_set_scan_table_max_len_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->max_table_len))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_scan_table_max_len_rpcdata (XDR *xdrs, qcsapi_wifi_get_scan_table_max_len_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->max_table_len, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_dwell_times_rpcdata (XDR *xdrs, qcsapi_wifi_set_dwell_times_rpcdata *objp)
{
	register int32_t *buf;


	if (xdrs->x_op == XDR_ENCODE) {
		 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
			 return FALSE;
		buf = XDR_INLINE (xdrs, 5 * BYTES_PER_XDR_UNIT);
		if (buf == NULL) {
			 if (!xdr_u_int (xdrs, &objp->max_dwell_time_active_chan))
				 return FALSE;
			 if (!xdr_u_int (xdrs, &objp->min_dwell_time_active_chan))
				 return FALSE;
			 if (!xdr_u_int (xdrs, &objp->max_dwell_time_passive_chan))
				 return FALSE;
			 if (!xdr_u_int (xdrs, &objp->min_dwell_time_passive_chan))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->return_code))
				 return FALSE;
		} else {
			IXDR_PUT_U_LONG(buf, objp->max_dwell_time_active_chan);
			IXDR_PUT_U_LONG(buf, objp->min_dwell_time_active_chan);
			IXDR_PUT_U_LONG(buf, objp->max_dwell_time_passive_chan);
			IXDR_PUT_U_LONG(buf, objp->min_dwell_time_passive_chan);
			IXDR_PUT_LONG(buf, objp->return_code);
		}
		return TRUE;
	} else if (xdrs->x_op == XDR_DECODE) {
		 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
			 return FALSE;
		buf = XDR_INLINE (xdrs, 5 * BYTES_PER_XDR_UNIT);
		if (buf == NULL) {
			 if (!xdr_u_int (xdrs, &objp->max_dwell_time_active_chan))
				 return FALSE;
			 if (!xdr_u_int (xdrs, &objp->min_dwell_time_active_chan))
				 return FALSE;
			 if (!xdr_u_int (xdrs, &objp->max_dwell_time_passive_chan))
				 return FALSE;
			 if (!xdr_u_int (xdrs, &objp->min_dwell_time_passive_chan))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->return_code))
				 return FALSE;
		} else {
			objp->max_dwell_time_active_chan = IXDR_GET_U_LONG(buf);
			objp->min_dwell_time_active_chan = IXDR_GET_U_LONG(buf);
			objp->max_dwell_time_passive_chan = IXDR_GET_U_LONG(buf);
			objp->min_dwell_time_passive_chan = IXDR_GET_U_LONG(buf);
			objp->return_code = IXDR_GET_LONG(buf);
		}
	 return TRUE;
	}

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->max_dwell_time_active_chan))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->min_dwell_time_active_chan))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->max_dwell_time_passive_chan))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->min_dwell_time_passive_chan))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_dwell_times_rpcdata (XDR *xdrs, qcsapi_wifi_get_dwell_times_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_max_dwell_time_active_chan, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_min_dwell_time_active_chan, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_max_dwell_time_passive_chan, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_min_dwell_time_passive_chan, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_bgscan_dwell_times_rpcdata (XDR *xdrs, qcsapi_wifi_set_bgscan_dwell_times_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->dwell_time_active_chan))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->dwell_time_passive_chan))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_bgscan_dwell_times_rpcdata (XDR *xdrs, qcsapi_wifi_get_bgscan_dwell_times_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_dwell_time_active_chan, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_dwell_time_passive_chan, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_start_scan_rpcdata (XDR *xdrs, qcsapi_wifi_start_scan_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_cancel_scan_rpcdata (XDR *xdrs, qcsapi_wifi_cancel_scan_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->force))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_scan_status_rpcdata (XDR *xdrs, qcsapi_wifi_get_scan_status_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->scanstatus, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_enable_bgscan_rpcdata (XDR *xdrs, qcsapi_wifi_enable_bgscan_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->enable))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_bgscan_status_rpcdata (XDR *xdrs, qcsapi_wifi_get_bgscan_status_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->enable, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_wait_scan_completes_rpcdata (XDR *xdrs, qcsapi_wifi_wait_scan_completes_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->timeout))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_backoff_fail_max_rpcdata (XDR *xdrs, qcsapi_wifi_backoff_fail_max_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->fail_max))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_backoff_timeout_rpcdata (XDR *xdrs, qcsapi_wifi_backoff_timeout_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->timeout))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_mcs_rate_rpcdata (XDR *xdrs, qcsapi_wifi_get_mcs_rate_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->current_mcs_rate, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_mcs_rate_rpcdata (XDR *xdrs, qcsapi_wifi_set_mcs_rate_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->new_mcs_rate, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_pairing_id_rpcdata (XDR *xdrs, qcsapi_wifi_set_pairing_id_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->pairing_id, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_pairing_id_rpcdata (XDR *xdrs, qcsapi_wifi_get_pairing_id_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->pairing_id, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_pairing_enable_rpcdata (XDR *xdrs, qcsapi_wifi_set_pairing_enable_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->enable, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_pairing_enable_rpcdata (XDR *xdrs, qcsapi_wifi_get_pairing_enable_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->enable, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_non_wps_set_pp_enable_rpcdata (XDR *xdrs, qcsapi_non_wps_set_pp_enable_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->ctrl_state))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_non_wps_get_pp_enable_rpcdata (XDR *xdrs, qcsapi_non_wps_get_pp_enable_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->ctrl_state, sizeof (uint32_t), (xdrproc_t) xdr_uint32_t))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_vendor_fix_rpcdata (XDR *xdrs, qcsapi_wifi_set_vendor_fix_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->fix_param))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->value))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_errno_get_message_rpcdata (XDR *xdrs, qcsapi_errno_get_message_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_int (xdrs, &objp->qcsapi_retval))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->msglen))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->error_msg, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_get_interface_stats_rpcdata (XDR *xdrs, qcsapi_get_interface_stats_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->stats, sizeof (__rpc_qcsapi_interface_stats), (xdrproc_t) xdr___rpc_qcsapi_interface_stats))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_get_phy_stats_rpcdata (XDR *xdrs, qcsapi_get_phy_stats_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->stats, sizeof (__rpc_qcsapi_phy_stats), (xdrproc_t) xdr___rpc_qcsapi_phy_stats))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_reset_all_counters_rpcdata (XDR *xdrs, qcsapi_reset_all_counters_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->node_index))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->local_remote_flag))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_get_uboot_info_rpcdata (XDR *xdrs, qcsapi_get_uboot_info_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->uboot_version, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->ef_config, sizeof (__rpc_early_flash_config), (xdrproc_t) xdr___rpc_early_flash_config))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_firmware_get_version_rpcdata (XDR *xdrs, qcsapi_firmware_get_version_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_u_int (xdrs, &objp->version_size))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->firmware_version, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_flash_image_update_rpcdata (XDR *xdrs, qcsapi_flash_image_update_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->image_file, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->partition_to_upgrade))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_send_file_rpcdata (XDR *xdrs, qcsapi_send_file_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->image_file_path, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->image_flags))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_pm_set_mode_rpcdata (XDR *xdrs, qcsapi_pm_set_mode_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_int (xdrs, &objp->mode))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_pm_get_mode_rpcdata (XDR *xdrs, qcsapi_pm_get_mode_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->mode, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_get_qpm_level_rpcdata (XDR *xdrs, qcsapi_get_qpm_level_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->qpm_level, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_set_host_state_rpcdata (XDR *xdrs, qcsapi_set_host_state_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->host_state))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_qtm_get_state_rpcdata (XDR *xdrs, qcsapi_qtm_get_state_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->param))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->value, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_qtm_get_state_all_rpcdata (XDR *xdrs, qcsapi_qtm_get_state_all_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->max))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->value, sizeof (__rpc_qcsapi_data_128bytes), (xdrproc_t) xdr___rpc_qcsapi_data_128bytes))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_qtm_set_state_rpcdata (XDR *xdrs, qcsapi_qtm_set_state_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->param))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->value))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_qtm_get_config_rpcdata (XDR *xdrs, qcsapi_qtm_get_config_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->param))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->value, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_qtm_get_config_all_rpcdata (XDR *xdrs, qcsapi_qtm_get_config_all_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->max))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->value, sizeof (__rpc_qcsapi_data_1Kbytes), (xdrproc_t) xdr___rpc_qcsapi_data_1Kbytes))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_qtm_set_config_rpcdata (XDR *xdrs, qcsapi_qtm_set_config_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->param))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->value))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_qtm_add_rule_rpcdata (XDR *xdrs, qcsapi_qtm_add_rule_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->entry, sizeof (__rpc_qcsapi_data_128bytes), (xdrproc_t) xdr___rpc_qcsapi_data_128bytes))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_qtm_del_rule_rpcdata (XDR *xdrs, qcsapi_qtm_del_rule_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->entry, sizeof (__rpc_qcsapi_data_128bytes), (xdrproc_t) xdr___rpc_qcsapi_data_128bytes))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_qtm_del_rule_index_rpcdata (XDR *xdrs, qcsapi_qtm_del_rule_index_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->index))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_qtm_get_rule_rpcdata (XDR *xdrs, qcsapi_qtm_get_rule_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->max_entries))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->entries, sizeof (__rpc_qcsapi_data_3Kbytes), (xdrproc_t) xdr___rpc_qcsapi_data_3Kbytes))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_qtm_get_strm_rpcdata (XDR *xdrs, qcsapi_qtm_get_strm_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->max_entries))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->show_all))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->strms, sizeof (__rpc_qcsapi_data_4Kbytes), (xdrproc_t) xdr___rpc_qcsapi_data_4Kbytes))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_qtm_get_stats_rpcdata (XDR *xdrs, qcsapi_qtm_get_stats_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->stats, sizeof (__rpc_qcsapi_data_512bytes), (xdrproc_t) xdr___rpc_qcsapi_data_512bytes))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_qtm_get_inactive_flags_rpcdata (XDR *xdrs, qcsapi_qtm_get_inactive_flags_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->flags, sizeof (u_long), (xdrproc_t) xdr_u_long))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_run_script_rpcdata (XDR *xdrs, qcsapi_wifi_run_script_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->scriptname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->param, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_test_traffic_rpcdata (XDR *xdrs, qcsapi_wifi_test_traffic_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->period))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_add_ipff_rpcdata (XDR *xdrs, qcsapi_wifi_add_ipff_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_u_int (xdrs, &objp->ipaddr))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_del_ipff_rpcdata (XDR *xdrs, qcsapi_wifi_del_ipff_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_u_int (xdrs, &objp->ipaddr))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_ipff_rpcdata (XDR *xdrs, qcsapi_wifi_get_ipff_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_int (xdrs, &objp->buflen))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->buf, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_rts_threshold_rpcdata (XDR *xdrs, qcsapi_wifi_get_rts_threshold_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->rts_threshold, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_rts_threshold_rpcdata (XDR *xdrs, qcsapi_wifi_set_rts_threshold_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->rts_threshold))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_nss_cap_rpcdata (XDR *xdrs, qcsapi_wifi_set_nss_cap_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->modulation))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->nss))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_nss_cap_rpcdata (XDR *xdrs, qcsapi_wifi_get_nss_cap_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->modulation))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->nss, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_tx_amsdu_rpcdata (XDR *xdrs, qcsapi_wifi_get_tx_amsdu_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->enable, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_tx_amsdu_rpcdata (XDR *xdrs, qcsapi_wifi_set_tx_amsdu_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->enable))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_disassoc_reason_rpcdata (XDR *xdrs, qcsapi_wifi_get_disassoc_reason_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->reason, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_block_bss_rpcdata (XDR *xdrs, qcsapi_wifi_block_bss_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->flag))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_verify_repeater_mode_rpcdata (XDR *xdrs, qcsapi_wifi_verify_repeater_mode_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_ap_interface_name_rpcdata (XDR *xdrs, qcsapi_wifi_set_ap_interface_name_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_ap_interface_name_rpcdata (XDR *xdrs, qcsapi_wifi_get_ap_interface_name_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_get_temperature_info_rpcdata (XDR *xdrs, qcsapi_get_temperature_info_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->temp_exter, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->temp_inter, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->temp_bbic, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_calcmd_set_test_mode_rpcdata (XDR *xdrs, qcsapi_calcmd_set_test_mode_rpcdata *objp)
{
	register int32_t *buf;


	if (xdrs->x_op == XDR_ENCODE) {
		buf = XDR_INLINE (xdrs, 8 * BYTES_PER_XDR_UNIT);
		if (buf == NULL) {
			 if (!xdr_u_int (xdrs, &objp->channel))
				 return FALSE;
			 if (!xdr_u_int (xdrs, &objp->antenna))
				 return FALSE;
			 if (!xdr_u_int (xdrs, &objp->mcs))
				 return FALSE;
			 if (!xdr_u_int (xdrs, &objp->bw))
				 return FALSE;
			 if (!xdr_u_int (xdrs, &objp->pkt_size))
				 return FALSE;
			 if (!xdr_u_int (xdrs, &objp->eleven_n))
				 return FALSE;
			 if (!xdr_u_int (xdrs, &objp->bf))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->return_code))
				 return FALSE;
		} else {
			IXDR_PUT_U_LONG(buf, objp->channel);
			IXDR_PUT_U_LONG(buf, objp->antenna);
			IXDR_PUT_U_LONG(buf, objp->mcs);
			IXDR_PUT_U_LONG(buf, objp->bw);
			IXDR_PUT_U_LONG(buf, objp->pkt_size);
			IXDR_PUT_U_LONG(buf, objp->eleven_n);
			IXDR_PUT_U_LONG(buf, objp->bf);
			IXDR_PUT_LONG(buf, objp->return_code);
		}
		return TRUE;
	} else if (xdrs->x_op == XDR_DECODE) {
		buf = XDR_INLINE (xdrs, 8 * BYTES_PER_XDR_UNIT);
		if (buf == NULL) {
			 if (!xdr_u_int (xdrs, &objp->channel))
				 return FALSE;
			 if (!xdr_u_int (xdrs, &objp->antenna))
				 return FALSE;
			 if (!xdr_u_int (xdrs, &objp->mcs))
				 return FALSE;
			 if (!xdr_u_int (xdrs, &objp->bw))
				 return FALSE;
			 if (!xdr_u_int (xdrs, &objp->pkt_size))
				 return FALSE;
			 if (!xdr_u_int (xdrs, &objp->eleven_n))
				 return FALSE;
			 if (!xdr_u_int (xdrs, &objp->bf))
				 return FALSE;
			 if (!xdr_int (xdrs, &objp->return_code))
				 return FALSE;
		} else {
			objp->channel = IXDR_GET_U_LONG(buf);
			objp->antenna = IXDR_GET_U_LONG(buf);
			objp->mcs = IXDR_GET_U_LONG(buf);
			objp->bw = IXDR_GET_U_LONG(buf);
			objp->pkt_size = IXDR_GET_U_LONG(buf);
			objp->eleven_n = IXDR_GET_U_LONG(buf);
			objp->bf = IXDR_GET_U_LONG(buf);
			objp->return_code = IXDR_GET_LONG(buf);
		}
	 return TRUE;
	}

	 if (!xdr_u_int (xdrs, &objp->channel))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->antenna))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->mcs))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->bw))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->pkt_size))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->eleven_n))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->bf))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_calcmd_show_test_packet_rpcdata (XDR *xdrs, qcsapi_calcmd_show_test_packet_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->tx_packet_num, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->rx_packet_num, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->crc_packet_num, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_calcmd_send_test_packet_rpcdata (XDR *xdrs, qcsapi_calcmd_send_test_packet_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_u_int (xdrs, &objp->to_transmit_packet_num))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_calcmd_stop_test_packet_rpcdata (XDR *xdrs, qcsapi_calcmd_stop_test_packet_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_calcmd_send_dc_cw_signal_rpcdata (XDR *xdrs, qcsapi_calcmd_send_dc_cw_signal_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_u_int (xdrs, &objp->channel))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_calcmd_stop_dc_cw_signal_rpcdata (XDR *xdrs, qcsapi_calcmd_stop_dc_cw_signal_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_calcmd_get_test_mode_antenna_sel_rpcdata (XDR *xdrs, qcsapi_calcmd_get_test_mode_antenna_sel_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->antenna_bit_mask, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_calcmd_get_test_mode_mcs_rpcdata (XDR *xdrs, qcsapi_calcmd_get_test_mode_mcs_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->test_mode_mcs, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_calcmd_get_test_mode_bw_rpcdata (XDR *xdrs, qcsapi_calcmd_get_test_mode_bw_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->test_mode_bw, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_calcmd_get_tx_power_rpcdata (XDR *xdrs, qcsapi_calcmd_get_tx_power_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->tx_power, sizeof (__rpc_qcsapi_calcmd_tx_power_rsp), (xdrproc_t) xdr___rpc_qcsapi_calcmd_tx_power_rsp))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_calcmd_set_tx_power_rpcdata (XDR *xdrs, qcsapi_calcmd_set_tx_power_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_u_int (xdrs, &objp->tx_power))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_calcmd_get_test_mode_rssi_rpcdata (XDR *xdrs, qcsapi_calcmd_get_test_mode_rssi_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->test_mode_rssi, sizeof (__rpc_qcsapi_calcmd_rssi_rsp), (xdrproc_t) xdr___rpc_qcsapi_calcmd_rssi_rsp))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_calcmd_set_mac_filter_rpcdata (XDR *xdrs, qcsapi_calcmd_set_mac_filter_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_int (xdrs, &objp->q_num))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->sec_enable))
		 return FALSE;
	 if (!xdr___rpc_qcsapi_mac_addr_p (xdrs, &objp->mac_addr))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_calcmd_get_antenna_count_rpcdata (XDR *xdrs, qcsapi_calcmd_get_antenna_count_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->antenna_count, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_calcmd_clear_counter_rpcdata (XDR *xdrs, qcsapi_calcmd_clear_counter_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_calcmd_get_info_rpcdata (XDR *xdrs, qcsapi_calcmd_get_info_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->output_info, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wowlan_set_match_type_rpcdata (XDR *xdrs, qcsapi_wowlan_set_match_type_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->wowlan_match))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wowlan_set_L2_type_rpcdata (XDR *xdrs, qcsapi_wowlan_set_L2_type_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->ether_type))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wowlan_set_udp_port_rpcdata (XDR *xdrs, qcsapi_wowlan_set_udp_port_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->udp_port))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wowlan_set_magic_pattern_rpcdata (XDR *xdrs, qcsapi_wowlan_set_magic_pattern_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->len))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->pattern, sizeof (__rpc_qcsapi_data_256bytes), (xdrproc_t) xdr___rpc_qcsapi_data_256bytes))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_wowlan_get_host_state_rpcdata (XDR *xdrs, qcsapi_wifi_wowlan_get_host_state_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_value, sizeof (uint16_t), (xdrproc_t) xdr_uint16_t))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->len, sizeof (uint32_t), (xdrproc_t) xdr_uint32_t))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_wowlan_get_match_type_rpcdata (XDR *xdrs, qcsapi_wifi_wowlan_get_match_type_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_value, sizeof (uint16_t), (xdrproc_t) xdr_uint16_t))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->len, sizeof (uint32_t), (xdrproc_t) xdr_uint32_t))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_wowlan_get_l2_type_rpcdata (XDR *xdrs, qcsapi_wifi_wowlan_get_l2_type_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_value, sizeof (uint16_t), (xdrproc_t) xdr_uint16_t))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->len, sizeof (uint32_t), (xdrproc_t) xdr_uint32_t))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_wowlan_get_udp_port_rpcdata (XDR *xdrs, qcsapi_wifi_wowlan_get_udp_port_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_value, sizeof (uint16_t), (xdrproc_t) xdr_uint16_t))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->len, sizeof (uint32_t), (xdrproc_t) xdr_uint32_t))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_wowlan_get_magic_pattern_rpcdata (XDR *xdrs, qcsapi_wifi_wowlan_get_magic_pattern_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_value, sizeof (__rpc_qcsapi_data_256bytes), (xdrproc_t) xdr___rpc_qcsapi_data_256bytes))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->len, sizeof (uint32_t), (xdrproc_t) xdr_uint32_t))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_enable_mu_rpcdata (XDR *xdrs, qcsapi_wifi_set_enable_mu_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->mu_enable))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_enable_mu_rpcdata (XDR *xdrs, qcsapi_wifi_get_enable_mu_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->mu_enable, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_mu_use_precode_rpcdata (XDR *xdrs, qcsapi_wifi_set_mu_use_precode_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->grp))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->prec_enable))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_mu_use_precode_rpcdata (XDR *xdrs, qcsapi_wifi_get_mu_use_precode_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->grp))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->prec_enable, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_mu_use_eq_rpcdata (XDR *xdrs, qcsapi_wifi_set_mu_use_eq_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->eq_enable))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_mu_use_eq_rpcdata (XDR *xdrs, qcsapi_wifi_get_mu_use_eq_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->meq_enable, sizeof (u_int), (xdrproc_t) xdr_u_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_mu_groups_rpcdata (XDR *xdrs, qcsapi_wifi_get_mu_groups_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_u_int (xdrs, &objp->size))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->buf, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_enable_tdls_rpcdata (XDR *xdrs, qcsapi_wifi_enable_tdls_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->enable_tdls))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_enable_tdls_over_qhop_rpcdata (XDR *xdrs, qcsapi_wifi_enable_tdls_over_qhop_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_uint32_t (xdrs, &objp->tdls_over_qhop_en))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_tdls_status_rpcdata (XDR *xdrs, qcsapi_wifi_get_tdls_status_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_tdls_status, sizeof (uint32_t), (xdrproc_t) xdr_uint32_t))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_set_tdls_params_rpcdata (XDR *xdrs, qcsapi_wifi_set_tdls_params_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->type))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->param_value))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_get_tdls_params_rpcdata (XDR *xdrs, qcsapi_wifi_get_tdls_params_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->type))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->p_value, sizeof (int), (xdrproc_t) xdr_int))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

bool_t
xdr_qcsapi_wifi_tdls_operate_rpcdata (XDR *xdrs, qcsapi_wifi_tdls_operate_rpcdata *objp)
{
	register int32_t *buf;

	 if (!xdr_pointer (xdrs, (char **)&objp->ifname, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->operate))
		 return FALSE;
	 if (!xdr_pointer (xdrs, (char **)&objp->mac_addr_str, sizeof (__rpc_string), (xdrproc_t) xdr___rpc_string))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->cs_interval))
		 return FALSE;
	 if (!xdr_int (xdrs, &objp->return_code))
		 return FALSE;
	return TRUE;
}

/* defines for local-only functions */
#define QCSAPI_GPIO_MONITOR_RESET_DEVICE_REMOTE 2771
