/*
 * Please do not edit this file.
 * It was generated using rpcgen.
 */

#ifndef _QCSAPI_RPC_H_RPCGEN
#define _QCSAPI_RPC_H_RPCGEN

#include <rpc/rpc.h>

#include <pthread.h>

#ifdef __cplusplus
extern "C" {
#endif


typedef char *str;

struct __rpc_string {
	char *data;
};
typedef struct __rpc_string __rpc_string;

typedef __rpc_string *__rpc_string_p;

struct __rpc_qcsapi_mac_addr {
	u_char data[6];
};
typedef struct __rpc_qcsapi_mac_addr __rpc_qcsapi_mac_addr;

typedef __rpc_qcsapi_mac_addr *__rpc_qcsapi_mac_addr_p;

struct __rpc_qcsapi_int_a32 {
	int data[32];
};
typedef struct __rpc_qcsapi_int_a32 __rpc_qcsapi_int_a32;

typedef __rpc_qcsapi_int_a32 *__rpc_qcsapi_int_a32_p;

struct __rpc_qcsapi_SSID {
	u_char data[33];
};
typedef struct __rpc_qcsapi_SSID __rpc_qcsapi_SSID;

struct __rpc_qcsapi_scs_ranking_rpt {
	uint8_t num;
	uint8_t chan[32];
	uint8_t dfs[32];
	uint8_t txpwr[32];
	int32_t metric[32];
	uint32_t metric_age[32];
	uint16_t cca_intf[32];
	uint32_t pmbl_ap[32];
	uint32_t pmbl_sta[32];
	uint32_t duration[32];
	uint32_t times[32];
};
typedef struct __rpc_qcsapi_scs_ranking_rpt __rpc_qcsapi_scs_ranking_rpt;

struct __rpc_qcsapi_scs_score_rpt {
	uint8_t num;
	uint8_t chan[32];
	uint8_t score[32];
};
typedef struct __rpc_qcsapi_scs_score_rpt __rpc_qcsapi_scs_score_rpt;

struct __rpc_qcsapi_scs_currchan_rpt {
	uint8_t chan;
	uint16_t cca_try;
	uint16_t cca_idle;
	uint16_t cca_busy;
	uint16_t cca_intf;
	uint16_t cca_tx;
	uint16_t tx_ms;
	uint16_t rx_ms;
	uint32_t pmbl;
};
typedef struct __rpc_qcsapi_scs_currchan_rpt __rpc_qcsapi_scs_currchan_rpt;

struct __rpc_qcsapi_autochan_rpt {
	uint8_t num;
	uint8_t chan[32];
	uint8_t dfs[32];
	uint8_t txpwr[32];
	int32_t metric[32];
	uint32_t numbeacons[32];
	uint32_t cci[32];
	uint32_t aci[32];
};
typedef struct __rpc_qcsapi_autochan_rpt __rpc_qcsapi_autochan_rpt;

struct __rpc_qcsapi_scs_param_rpt {
	uint32_t scs_cfg_param;
	uint32_t scs_signed_param_flag;
};
typedef struct __rpc_qcsapi_scs_param_rpt __rpc_qcsapi_scs_param_rpt;

struct __rpc_qcsapi_data_512bytes {
	uint8_t data[512];
};
typedef struct __rpc_qcsapi_data_512bytes __rpc_qcsapi_data_512bytes;

struct __rpc_qcsapi_data_256bytes {
	uint8_t data[256];
};
typedef struct __rpc_qcsapi_data_256bytes __rpc_qcsapi_data_256bytes;

struct __rpc_qcsapi_disconn_info {
	uint32_t asso_sta_count;
	uint32_t disconn_count;
	uint32_t sequence;
	uint32_t up_time;
	uint32_t resetflag;
};
typedef struct __rpc_qcsapi_disconn_info __rpc_qcsapi_disconn_info;

struct __rpc_qcsapi_data_64bytes {
	uint8_t data[64];
};
typedef struct __rpc_qcsapi_data_64bytes __rpc_qcsapi_data_64bytes;

struct __rpc_qcsapi_channel_power_table {
	uint8_t channel;
	int power_20M[8];
	int power_40M[8];
	int power_80M[8];
};
typedef struct __rpc_qcsapi_channel_power_table __rpc_qcsapi_channel_power_table;

struct __rpc_qcsapi_assoc_records {
	__rpc_qcsapi_mac_addr addr[32];
	uint32_t timestamp[32];
};
typedef struct __rpc_qcsapi_assoc_records __rpc_qcsapi_assoc_records;

struct __rpc_ieee8011req_sta_tput_caps {
	uint8_t macaddr[6];
	uint8_t mode;
	uint8_t htcap_ie[28];
	uint8_t vhtcap_ie[14];
};
typedef struct __rpc_ieee8011req_sta_tput_caps __rpc_ieee8011req_sta_tput_caps;

struct __rpc_qcsapi_measure_report_result {
	int common[16];
	uint8_t basic;
	uint8_t cca;
	uint8_t rpi[8];
	uint8_t channel_load;
};
typedef struct __rpc_qcsapi_measure_report_result __rpc_qcsapi_measure_report_result;

struct __rpc_qcsapi_node_stats {
	uint64_t tx_bytes;
	uint32_t tx_pkts;
	uint32_t tx_discard;
	uint32_t tx_err;
	uint32_t tx_unicast;
	uint32_t tx_multicast;
	uint32_t tx_broadcast;
	uint32_t tx_phy_rate;
	uint64_t rx_bytes;
	uint32_t rx_pkts;
	uint32_t rx_discard;
	uint32_t rx_err;
	uint32_t rx_unicast;
	uint32_t rx_multicast;
	uint32_t rx_broadcast;
	uint32_t rx_unknown;
	uint32_t rx_phy_rate;
	__rpc_qcsapi_mac_addr mac_addr;
	int32_t hw_noise;
	int32_t snr;
	int32_t rssi;
	int32_t bw;
};
typedef struct __rpc_qcsapi_node_stats __rpc_qcsapi_node_stats;

struct __rpc_qcsapi_mlme_stats {
	u_int auth;
	u_int auth_fails;
	u_int assoc;
	u_int assoc_fails;
	u_int deauth;
	u_int diassoc;
};
typedef struct __rpc_qcsapi_mlme_stats __rpc_qcsapi_mlme_stats;

struct __rpc_qcsapi_mlme_stats_macs {
	__rpc_qcsapi_mac_addr addr[128];
};
typedef struct __rpc_qcsapi_mlme_stats_macs __rpc_qcsapi_mlme_stats_macs;

struct __rpc_qcsapi_csw_record {
	uint32_t cnt;
	int32_t index;
	uint32_t channel[32];
	uint32_t timestamp[32];
	uint32_t reason[32];
};
typedef struct __rpc_qcsapi_csw_record __rpc_qcsapi_csw_record;

struct __rpc_qcsapi_radar_status {
	uint32_t channel;
	uint32_t flags;
	uint32_t ic_radardetected;
};
typedef struct __rpc_qcsapi_radar_status __rpc_qcsapi_radar_status;

struct __rpc_qcsapi_ap_properties {
	__rpc_qcsapi_SSID ap_name_SSID;
	__rpc_qcsapi_mac_addr ap_mac_addr;
	u_int ap_flags;
	int ap_channel;
	int ap_RSSI;
	int ap_protocol;
	int ap_encryption_modes;
	int ap_authentication_mode;
	int ap_best_data_rate;
	int ap_wps;
	int ap_80211_proto;
	int ap_qhop_role;
};
typedef struct __rpc_qcsapi_ap_properties __rpc_qcsapi_ap_properties;

struct __rpc_qcsapi_interface_stats {
	uint64_t tx_bytes;
	uint32_t tx_pkts;
	uint32_t tx_discard;
	uint32_t tx_err;
	uint32_t tx_unicast;
	uint32_t tx_multicast;
	uint32_t tx_broadcast;
	uint64_t rx_bytes;
	uint32_t rx_pkts;
	uint32_t rx_discard;
	uint32_t rx_err;
	uint32_t rx_unicast;
	uint32_t rx_multicast;
	uint32_t rx_broadcast;
	uint32_t rx_unknown;
};
typedef struct __rpc_qcsapi_interface_stats __rpc_qcsapi_interface_stats;

struct __rpc_qcsapi_phy_stats {
	uint32_t tstamp;
	uint32_t assoc;
	uint32_t channel;
	uint32_t atten;
	uint32_t cca_total;
	uint32_t cca_tx;
	uint32_t cca_rx;
	uint32_t cca_int;
	uint32_t cca_idle;
	uint32_t rx_pkts;
	uint32_t rx_gain;
	uint32_t rx_cnt_crc;
	float rx_noise;
	uint32_t tx_pkts;
	uint32_t tx_defers;
	uint32_t tx_touts;
	uint32_t tx_retries;
	uint32_t cnt_sp_fail;
	uint32_t cnt_lp_fail;
	uint32_t last_rx_mcs;
	uint32_t last_tx_mcs;
	float last_rssi;
	float last_rssi_array[4];
	float last_rcpi;
	float last_evm;
	float last_evm_array[4];
};
typedef struct __rpc_qcsapi_phy_stats __rpc_qcsapi_phy_stats;

struct __rpc_early_flash_config {
	uint32_t method;
	uint32_t ipaddr;
	uint32_t serverip;
	uint8_t built_time_utc_sec[11];
	uint8_t uboot_type;
};
typedef struct __rpc_early_flash_config __rpc_early_flash_config;

struct __rpc_qcsapi_data_128bytes {
	uint8_t data[128];
};
typedef struct __rpc_qcsapi_data_128bytes __rpc_qcsapi_data_128bytes;

struct __rpc_qcsapi_data_1Kbytes {
	uint8_t data[1024];
};
typedef struct __rpc_qcsapi_data_1Kbytes __rpc_qcsapi_data_1Kbytes;

struct __rpc_qcsapi_data_3Kbytes {
	uint8_t data[3072];
};
typedef struct __rpc_qcsapi_data_3Kbytes __rpc_qcsapi_data_3Kbytes;

struct __rpc_qcsapi_data_4Kbytes {
	uint8_t data[4096];
};
typedef struct __rpc_qcsapi_data_4Kbytes __rpc_qcsapi_data_4Kbytes;

struct __rpc_qcsapi_calcmd_tx_power_rsp {
	uint32_t value[4];
};
typedef struct __rpc_qcsapi_calcmd_tx_power_rsp __rpc_qcsapi_calcmd_tx_power_rsp;

struct __rpc_qcsapi_calcmd_rssi_rsp {
	int32_t value[4];
};
typedef struct __rpc_qcsapi_calcmd_rssi_rsp __rpc_qcsapi_calcmd_rssi_rsp;

struct qcsapi_bootcfg_get_parameter_rpcdata {
	__rpc_string *param_name;
	uint32_t max_param_len;
	__rpc_string *param_value;
	int return_code;
};
typedef struct qcsapi_bootcfg_get_parameter_rpcdata qcsapi_bootcfg_get_parameter_rpcdata;

struct qcsapi_bootcfg_update_parameter_rpcdata {
	__rpc_string *param_name;
	__rpc_string *param_value;
	int return_code;
};
typedef struct qcsapi_bootcfg_update_parameter_rpcdata qcsapi_bootcfg_update_parameter_rpcdata;

struct qcsapi_bootcfg_commit_rpcdata {
	int return_code;
};
typedef struct qcsapi_bootcfg_commit_rpcdata qcsapi_bootcfg_commit_rpcdata;

struct qcsapi_telnet_enable_rpcdata {
	u_int onoff;
	int return_code;
};
typedef struct qcsapi_telnet_enable_rpcdata qcsapi_telnet_enable_rpcdata;

struct qcsapi_get_service_name_enum_rpcdata {
	__rpc_string *lookup_service;
	int *serv_name;
	int return_code;
};
typedef struct qcsapi_get_service_name_enum_rpcdata qcsapi_get_service_name_enum_rpcdata;

struct qcsapi_get_service_action_enum_rpcdata {
	__rpc_string *lookup_action;
	int *serv_action;
	int return_code;
};
typedef struct qcsapi_get_service_action_enum_rpcdata qcsapi_get_service_action_enum_rpcdata;

struct qcsapi_service_control_rpcdata {
	int service;
	int action;
	int return_code;
};
typedef struct qcsapi_service_control_rpcdata qcsapi_service_control_rpcdata;

struct qcsapi_wfa_cert_mode_enable_rpcdata {
	uint16_t enable;
	int return_code;
};
typedef struct qcsapi_wfa_cert_mode_enable_rpcdata qcsapi_wfa_cert_mode_enable_rpcdata;

struct qcsapi_wifi_get_scs_cce_channels_rpcdata {
	__rpc_string *ifname;
	u_int *p_prev_channel;
	u_int *p_cur_channel;
	int return_code;
};
typedef struct qcsapi_wifi_get_scs_cce_channels_rpcdata qcsapi_wifi_get_scs_cce_channels_rpcdata;

struct qcsapi_wifi_scs_enable_rpcdata {
	__rpc_string *ifname;
	uint16_t enable_val;
	int return_code;
};
typedef struct qcsapi_wifi_scs_enable_rpcdata qcsapi_wifi_scs_enable_rpcdata;

struct qcsapi_wifi_scs_switch_channel_rpcdata {
	__rpc_string *ifname;
	int return_code;
};
typedef struct qcsapi_wifi_scs_switch_channel_rpcdata qcsapi_wifi_scs_switch_channel_rpcdata;

struct qcsapi_wifi_set_scs_verbose_rpcdata {
	__rpc_string *ifname;
	uint16_t enable_val;
	int return_code;
};
typedef struct qcsapi_wifi_set_scs_verbose_rpcdata qcsapi_wifi_set_scs_verbose_rpcdata;

struct qcsapi_wifi_get_scs_status_rpcdata {
	__rpc_string *ifname;
	u_int *p_scs_status;
	int return_code;
};
typedef struct qcsapi_wifi_get_scs_status_rpcdata qcsapi_wifi_get_scs_status_rpcdata;

struct qcsapi_wifi_set_scs_smpl_enable_rpcdata {
	__rpc_string *ifname;
	uint16_t enable_val;
	int return_code;
};
typedef struct qcsapi_wifi_set_scs_smpl_enable_rpcdata qcsapi_wifi_set_scs_smpl_enable_rpcdata;

struct qcsapi_wifi_set_scs_smpl_dwell_time_rpcdata {
	__rpc_string *ifname;
	uint16_t scs_sample_time;
	int return_code;
};
typedef struct qcsapi_wifi_set_scs_smpl_dwell_time_rpcdata qcsapi_wifi_set_scs_smpl_dwell_time_rpcdata;

struct qcsapi_wifi_set_scs_sample_intv_rpcdata {
	__rpc_string *ifname;
	uint16_t scs_sample_intv;
	int return_code;
};
typedef struct qcsapi_wifi_set_scs_sample_intv_rpcdata qcsapi_wifi_set_scs_sample_intv_rpcdata;

struct qcsapi_wifi_set_scs_intf_detect_intv_rpcdata {
	__rpc_string *ifname;
	uint16_t scs_intf_detect_intv;
	int return_code;
};
typedef struct qcsapi_wifi_set_scs_intf_detect_intv_rpcdata qcsapi_wifi_set_scs_intf_detect_intv_rpcdata;

struct qcsapi_wifi_set_scs_thrshld_rpcdata {
	__rpc_string *ifname;
	__rpc_string *scs_param_name;
	uint16_t scs_threshold;
	int return_code;
};
typedef struct qcsapi_wifi_set_scs_thrshld_rpcdata qcsapi_wifi_set_scs_thrshld_rpcdata;

struct qcsapi_wifi_set_scs_report_only_rpcdata {
	__rpc_string *ifname;
	uint16_t scs_report_only;
	int return_code;
};
typedef struct qcsapi_wifi_set_scs_report_only_rpcdata qcsapi_wifi_set_scs_report_only_rpcdata;

struct qcsapi_wifi_get_scs_stat_report_rpcdata {
	__rpc_string *ifname;
	__rpc_qcsapi_scs_ranking_rpt *scs_rpt;
	int return_code;
};
typedef struct qcsapi_wifi_get_scs_stat_report_rpcdata qcsapi_wifi_get_scs_stat_report_rpcdata;

struct qcsapi_wifi_get_scs_score_report_rpcdata {
	__rpc_string *ifname;
	__rpc_qcsapi_scs_score_rpt *scs_rpt;
	int return_code;
};
typedef struct qcsapi_wifi_get_scs_score_report_rpcdata qcsapi_wifi_get_scs_score_report_rpcdata;

struct qcsapi_wifi_get_scs_currchan_report_rpcdata {
	__rpc_string *ifname;
	__rpc_qcsapi_scs_currchan_rpt *scs_currchan_rpt;
	int return_code;
};
typedef struct qcsapi_wifi_get_scs_currchan_report_rpcdata qcsapi_wifi_get_scs_currchan_report_rpcdata;

struct qcsapi_wifi_set_scs_stats_rpcdata {
	__rpc_string *ifname;
	uint16_t start;
	int return_code;
};
typedef struct qcsapi_wifi_set_scs_stats_rpcdata qcsapi_wifi_set_scs_stats_rpcdata;

struct qcsapi_wifi_get_autochan_report_rpcdata {
	__rpc_string *ifname;
	__rpc_qcsapi_autochan_rpt *autochan_rpt;
	int return_code;
};
typedef struct qcsapi_wifi_get_autochan_report_rpcdata qcsapi_wifi_get_autochan_report_rpcdata;

struct qcsapi_wifi_set_scs_cca_intf_smth_fctr_rpcdata {
	__rpc_string *ifname;
	uint8_t smth_fctr_noxp;
	uint8_t smth_fctr_xped;
	int return_code;
};
typedef struct qcsapi_wifi_set_scs_cca_intf_smth_fctr_rpcdata qcsapi_wifi_set_scs_cca_intf_smth_fctr_rpcdata;

struct qcsapi_wifi_set_scs_chan_mtrc_mrgn_rpcdata {
	__rpc_string *ifname;
	uint8_t chan_mtrc_mrgn;
	int return_code;
};
typedef struct qcsapi_wifi_set_scs_chan_mtrc_mrgn_rpcdata qcsapi_wifi_set_scs_chan_mtrc_mrgn_rpcdata;

struct qcsapi_wifi_get_scs_cca_intf_rpcdata {
	__rpc_string *ifname;
	u_int the_channel;
	int *p_cca_intf;
	int return_code;
};
typedef struct qcsapi_wifi_get_scs_cca_intf_rpcdata qcsapi_wifi_get_scs_cca_intf_rpcdata;

struct qcsapi_wifi_get_scs_param_report_rpcdata {
	__rpc_string *ifname;
	uint32_t param_num;
	__rpc_qcsapi_scs_param_rpt *p_scs_param_rpt;
	int return_code;
};
typedef struct qcsapi_wifi_get_scs_param_report_rpcdata qcsapi_wifi_get_scs_param_report_rpcdata;

struct qcsapi_wifi_get_scs_dfs_reentry_request_rpcdata {
	__rpc_string *ifname;
	u_int *p_scs_dfs_reentry_request;
	int return_code;
};
typedef struct qcsapi_wifi_get_scs_dfs_reentry_request_rpcdata qcsapi_wifi_get_scs_dfs_reentry_request_rpcdata;

struct qcsapi_wifi_start_ocac_rpcdata {
	__rpc_string *ifname;
	uint16_t channel;
	int return_code;
};
typedef struct qcsapi_wifi_start_ocac_rpcdata qcsapi_wifi_start_ocac_rpcdata;

struct qcsapi_wifi_stop_ocac_rpcdata {
	__rpc_string *ifname;
	int return_code;
};
typedef struct qcsapi_wifi_stop_ocac_rpcdata qcsapi_wifi_stop_ocac_rpcdata;

struct qcsapi_wifi_get_ocac_status_rpcdata {
	__rpc_string *ifname;
	u_int *status;
	int return_code;
};
typedef struct qcsapi_wifi_get_ocac_status_rpcdata qcsapi_wifi_get_ocac_status_rpcdata;

struct qcsapi_wifi_set_ocac_dwell_time_rpcdata {
	__rpc_string *ifname;
	uint16_t dwell_time;
	int return_code;
};
typedef struct qcsapi_wifi_set_ocac_dwell_time_rpcdata qcsapi_wifi_set_ocac_dwell_time_rpcdata;

struct qcsapi_wifi_set_ocac_duration_rpcdata {
	__rpc_string *ifname;
	uint16_t duration;
	int return_code;
};
typedef struct qcsapi_wifi_set_ocac_duration_rpcdata qcsapi_wifi_set_ocac_duration_rpcdata;

struct qcsapi_wifi_set_ocac_cac_time_rpcdata {
	__rpc_string *ifname;
	uint16_t cac_time;
	int return_code;
};
typedef struct qcsapi_wifi_set_ocac_cac_time_rpcdata qcsapi_wifi_set_ocac_cac_time_rpcdata;

struct qcsapi_wifi_set_ocac_report_only_rpcdata {
	__rpc_string *ifname;
	uint16_t enable;
	int return_code;
};
typedef struct qcsapi_wifi_set_ocac_report_only_rpcdata qcsapi_wifi_set_ocac_report_only_rpcdata;

struct qcsapi_wifi_set_ocac_thrshld_rpcdata {
	__rpc_string *ifname;
	__rpc_string *param_name;
	uint16_t threshold;
	int return_code;
};
typedef struct qcsapi_wifi_set_ocac_thrshld_rpcdata qcsapi_wifi_set_ocac_thrshld_rpcdata;

struct qcsapi_wifi_start_dfs_s_radio_rpcdata {
	__rpc_string *ifname;
	uint16_t channel;
	int return_code;
};
typedef struct qcsapi_wifi_start_dfs_s_radio_rpcdata qcsapi_wifi_start_dfs_s_radio_rpcdata;

struct qcsapi_wifi_stop_dfs_s_radio_rpcdata {
	__rpc_string *ifname;
	int return_code;
};
typedef struct qcsapi_wifi_stop_dfs_s_radio_rpcdata qcsapi_wifi_stop_dfs_s_radio_rpcdata;

struct qcsapi_wifi_get_dfs_s_radio_status_rpcdata {
	__rpc_string *ifname;
	u_int *status;
	int return_code;
};
typedef struct qcsapi_wifi_get_dfs_s_radio_status_rpcdata qcsapi_wifi_get_dfs_s_radio_status_rpcdata;

struct qcsapi_wifi_get_dfs_s_radio_availability_rpcdata {
	__rpc_string *ifname;
	u_int *available;
	int return_code;
};
typedef struct qcsapi_wifi_get_dfs_s_radio_availability_rpcdata qcsapi_wifi_get_dfs_s_radio_availability_rpcdata;

struct qcsapi_wifi_set_dfs_s_radio_dwell_time_rpcdata {
	__rpc_string *ifname;
	uint16_t dwell_time;
	int return_code;
};
typedef struct qcsapi_wifi_set_dfs_s_radio_dwell_time_rpcdata qcsapi_wifi_set_dfs_s_radio_dwell_time_rpcdata;

struct qcsapi_wifi_set_dfs_s_radio_duration_rpcdata {
	__rpc_string *ifname;
	uint16_t duration;
	int return_code;
};
typedef struct qcsapi_wifi_set_dfs_s_radio_duration_rpcdata qcsapi_wifi_set_dfs_s_radio_duration_rpcdata;

struct qcsapi_wifi_set_dfs_s_radio_wea_duration_rpcdata {
	__rpc_string *ifname;
	uint32_t duration;
	int return_code;
};
typedef struct qcsapi_wifi_set_dfs_s_radio_wea_duration_rpcdata qcsapi_wifi_set_dfs_s_radio_wea_duration_rpcdata;

struct qcsapi_wifi_set_dfs_s_radio_cac_time_rpcdata {
	__rpc_string *ifname;
	uint16_t cac_time;
	int return_code;
};
typedef struct qcsapi_wifi_set_dfs_s_radio_cac_time_rpcdata qcsapi_wifi_set_dfs_s_radio_cac_time_rpcdata;

struct qcsapi_wifi_set_dfs_s_radio_wea_cac_time_rpcdata {
	__rpc_string *ifname;
	uint32_t cac_time;
	int return_code;
};
typedef struct qcsapi_wifi_set_dfs_s_radio_wea_cac_time_rpcdata qcsapi_wifi_set_dfs_s_radio_wea_cac_time_rpcdata;

struct qcsapi_wifi_set_dfs_s_radio_report_only_rpcdata {
	__rpc_string *ifname;
	uint16_t enable;
	int return_code;
};
typedef struct qcsapi_wifi_set_dfs_s_radio_report_only_rpcdata qcsapi_wifi_set_dfs_s_radio_report_only_rpcdata;

struct qcsapi_wifi_set_dfs_s_radio_thrshld_rpcdata {
	__rpc_string *ifname;
	__rpc_string *param_name;
	uint16_t threshold;
	int return_code;
};
typedef struct qcsapi_wifi_set_dfs_s_radio_thrshld_rpcdata qcsapi_wifi_set_dfs_s_radio_thrshld_rpcdata;

struct qcsapi_init_rpcdata {
	int return_code;
};
typedef struct qcsapi_init_rpcdata qcsapi_init_rpcdata;

struct qcsapi_console_disconnect_rpcdata {
	int return_code;
};
typedef struct qcsapi_console_disconnect_rpcdata qcsapi_console_disconnect_rpcdata;

struct qcsapi_wifi_startprod_rpcdata {
	int return_code;
};
typedef struct qcsapi_wifi_startprod_rpcdata qcsapi_wifi_startprod_rpcdata;

struct qcsapi_is_startprod_done_rpcdata {
	int *p_status;
	int return_code;
};
typedef struct qcsapi_is_startprod_done_rpcdata qcsapi_is_startprod_done_rpcdata;

struct qcsapi_system_get_time_since_start_rpcdata {
	u_int *p_elapsed_time;
	int return_code;
};
typedef struct qcsapi_system_get_time_since_start_rpcdata qcsapi_system_get_time_since_start_rpcdata;

struct qcsapi_get_system_status_rpcdata {
	u_int *p_status;
	int return_code;
};
typedef struct qcsapi_get_system_status_rpcdata qcsapi_get_system_status_rpcdata;

struct qcsapi_get_random_seed_rpcdata {
	__rpc_qcsapi_data_512bytes *random_buf;
	int return_code;
};
typedef struct qcsapi_get_random_seed_rpcdata qcsapi_get_random_seed_rpcdata;

struct qcsapi_set_random_seed_rpcdata {
	__rpc_qcsapi_data_512bytes *random_buf;
	u_int entropy;
	int return_code;
};
typedef struct qcsapi_set_random_seed_rpcdata qcsapi_set_random_seed_rpcdata;

struct qcsapi_get_carrier_id_rpcdata {
	u_int *p_carrier_id;
	int return_code;
};
typedef struct qcsapi_get_carrier_id_rpcdata qcsapi_get_carrier_id_rpcdata;

struct qcsapi_set_carrier_id_rpcdata {
	uint32_t carrier_id;
	uint32_t update_uboot;
	int return_code;
};
typedef struct qcsapi_set_carrier_id_rpcdata qcsapi_set_carrier_id_rpcdata;

struct qcsapi_wifi_get_spinor_jedecid_rpcdata {
	__rpc_string *ifname;
	u_int *p_jedecid;
	int return_code;
};
typedef struct qcsapi_wifi_get_spinor_jedecid_rpcdata qcsapi_wifi_get_spinor_jedecid_rpcdata;

struct qcsapi_wifi_get_bb_param_rpcdata {
	__rpc_string *ifname;
	u_int *p_jedecid;
	int return_code;
};
typedef struct qcsapi_wifi_get_bb_param_rpcdata qcsapi_wifi_get_bb_param_rpcdata;

struct qcsapi_wifi_set_bb_param_rpcdata {
	__rpc_string *ifname;
	u_int p_jedecid;
	int return_code;
};
typedef struct qcsapi_wifi_set_bb_param_rpcdata qcsapi_wifi_set_bb_param_rpcdata;

struct qcsapi_wifi_set_optim_stats_rpcdata {
	__rpc_string *ifname;
	u_int p_jedecid;
	int return_code;
};
typedef struct qcsapi_wifi_set_optim_stats_rpcdata qcsapi_wifi_set_optim_stats_rpcdata;

struct qcsapi_wifi_set_sys_time_rpcdata {
	uint32_t timestamp;
	int return_code;
};
typedef struct qcsapi_wifi_set_sys_time_rpcdata qcsapi_wifi_set_sys_time_rpcdata;

struct qcsapi_wifi_get_sys_time_rpcdata {
	uint32_t *timestamp;
	int return_code;
};
typedef struct qcsapi_wifi_get_sys_time_rpcdata qcsapi_wifi_get_sys_time_rpcdata;

struct qcsapi_set_soc_mac_addr_rpcdata {
	__rpc_string *ifname;
	__rpc_qcsapi_mac_addr_p soc_mac_addr;
	int return_code;
};
typedef struct qcsapi_set_soc_mac_addr_rpcdata qcsapi_set_soc_mac_addr_rpcdata;

struct qcsapi_get_custom_value_rpcdata {
	__rpc_string *custom_key;
	__rpc_string *custom_value;
	int return_code;
};
typedef struct qcsapi_get_custom_value_rpcdata qcsapi_get_custom_value_rpcdata;

struct qcsapi_config_get_parameter_rpcdata {
	__rpc_string *ifname;
	__rpc_string *param_name;
	uint32_t max_param_len;
	__rpc_string *param_value;
	int return_code;
};
typedef struct qcsapi_config_get_parameter_rpcdata qcsapi_config_get_parameter_rpcdata;

struct qcsapi_config_update_parameter_rpcdata {
	__rpc_string *ifname;
	__rpc_string *param_name;
	__rpc_string *param_value;
	int return_code;
};
typedef struct qcsapi_config_update_parameter_rpcdata qcsapi_config_update_parameter_rpcdata;

struct qcsapi_config_get_ssid_parameter_rpcdata {
	__rpc_string *ifname;
	__rpc_string *param_name;
	uint32_t max_param_len;
	__rpc_string *param_value;
	int return_code;
};
typedef struct qcsapi_config_get_ssid_parameter_rpcdata qcsapi_config_get_ssid_parameter_rpcdata;

struct qcsapi_config_update_ssid_parameter_rpcdata {
	__rpc_string *ifname;
	__rpc_string *param_name;
	__rpc_string *param_value;
	int return_code;
};
typedef struct qcsapi_config_update_ssid_parameter_rpcdata qcsapi_config_update_ssid_parameter_rpcdata;

struct qcsapi_file_path_get_config_rpcdata {
	int e_file_path;
	u_int path_size;
	__rpc_string *file_path;
	int return_code;
};
typedef struct qcsapi_file_path_get_config_rpcdata qcsapi_file_path_get_config_rpcdata;

struct qcsapi_file_path_set_config_rpcdata {
	int e_file_path;
	__rpc_string *new_path;
	int return_code;
};
typedef struct qcsapi_file_path_set_config_rpcdata qcsapi_file_path_set_config_rpcdata;

struct qcsapi_restore_default_config_rpcdata {
	int flag;
	int return_code;
};
typedef struct qcsapi_restore_default_config_rpcdata qcsapi_restore_default_config_rpcdata;

struct qcsapi_store_ipaddr_rpcdata {
	u_int ipaddr;
	u_int netmask;
	int return_code;
};
typedef struct qcsapi_store_ipaddr_rpcdata qcsapi_store_ipaddr_rpcdata;

struct qcsapi_interface_enable_rpcdata {
	__rpc_string *ifname;
	int enable_flag;
	int return_code;
};
typedef struct qcsapi_interface_enable_rpcdata qcsapi_interface_enable_rpcdata;

struct qcsapi_interface_get_status_rpcdata {
	__rpc_string *ifname;
	__rpc_string *interface_status;
	int return_code;
};
typedef struct qcsapi_interface_get_status_rpcdata qcsapi_interface_get_status_rpcdata;

struct qcsapi_interface_set_ip4_rpcdata {
	__rpc_string *ifname;
	__rpc_string *if_param;
	uint32_t if_param_val;
	int return_code;
};
typedef struct qcsapi_interface_set_ip4_rpcdata qcsapi_interface_set_ip4_rpcdata;

struct qcsapi_interface_get_ip4_rpcdata {
	__rpc_string *ifname;
	__rpc_string *if_param;
	__rpc_string *if_param_val;
	int return_code;
};
typedef struct qcsapi_interface_get_ip4_rpcdata qcsapi_interface_get_ip4_rpcdata;

struct qcsapi_interface_get_counter_rpcdata {
	__rpc_string *ifname;
	int qcsapi_counter;
	u_int *p_counter_value;
	int return_code;
};
typedef struct qcsapi_interface_get_counter_rpcdata qcsapi_interface_get_counter_rpcdata;

struct qcsapi_interface_get_counter64_rpcdata {
	__rpc_string *ifname;
	int qcsapi_counter;
	uint64_t *p_counter_value;
	int return_code;
};
typedef struct qcsapi_interface_get_counter64_rpcdata qcsapi_interface_get_counter64_rpcdata;

struct qcsapi_interface_get_mac_addr_rpcdata {
	__rpc_string *ifname;
	__rpc_qcsapi_mac_addr_p current_mac_addr;
	int return_code;
};
typedef struct qcsapi_interface_get_mac_addr_rpcdata qcsapi_interface_get_mac_addr_rpcdata;

struct qcsapi_interface_set_mac_addr_rpcdata {
	__rpc_string *ifname;
	__rpc_qcsapi_mac_addr_p interface_mac_addr;
	int return_code;
};
typedef struct qcsapi_interface_set_mac_addr_rpcdata qcsapi_interface_set_mac_addr_rpcdata;

struct qcsapi_pm_get_counter_rpcdata {
	__rpc_string *ifname;
	int qcsapi_counter;
	__rpc_string *pm_interval;
	u_int *p_counter_value;
	int return_code;
};
typedef struct qcsapi_pm_get_counter_rpcdata qcsapi_pm_get_counter_rpcdata;

struct qcsapi_set_aspm_l1_rpcdata {
	int enable;
	int latency;
	int return_code;
};
typedef struct qcsapi_set_aspm_l1_rpcdata qcsapi_set_aspm_l1_rpcdata;

struct qcsapi_set_l1_rpcdata {
	int enter;
	int return_code;
};
typedef struct qcsapi_set_l1_rpcdata qcsapi_set_l1_rpcdata;

struct qcsapi_pm_get_elapsed_time_rpcdata {
	__rpc_string *pm_interval;
	u_int *p_elapsed_time;
	int return_code;
};
typedef struct qcsapi_pm_get_elapsed_time_rpcdata qcsapi_pm_get_elapsed_time_rpcdata;

struct qcsapi_eth_phy_power_control_rpcdata {
	int on_off;
	__rpc_string *interface;
	int return_code;
};
typedef struct qcsapi_eth_phy_power_control_rpcdata qcsapi_eth_phy_power_control_rpcdata;

struct qcsapi_get_emac_switch_rpcdata {
	__rpc_string *buf;
	int return_code;
};
typedef struct qcsapi_get_emac_switch_rpcdata qcsapi_get_emac_switch_rpcdata;

struct qcsapi_set_emac_switch_rpcdata {
	int value;
	int return_code;
};
typedef struct qcsapi_set_emac_switch_rpcdata qcsapi_set_emac_switch_rpcdata;

struct qcsapi_eth_dscp_map_rpcdata {
	int oper;
	__rpc_string *eth_type;
	__rpc_string *level;
	__rpc_string *value;
	u_int size;
	__rpc_string *buf;
	int return_code;
};
typedef struct qcsapi_eth_dscp_map_rpcdata qcsapi_eth_dscp_map_rpcdata;

struct qcsapi_get_eth_info_rpcdata {
	__rpc_string *ifname;
	int eth_info_type;
	int return_code;
};
typedef struct qcsapi_get_eth_info_rpcdata qcsapi_get_eth_info_rpcdata;

struct qcsapi_wifi_get_mode_rpcdata {
	__rpc_string *ifname;
	int *p_wifi_mode;
	int return_code;
};
typedef struct qcsapi_wifi_get_mode_rpcdata qcsapi_wifi_get_mode_rpcdata;

struct qcsapi_wifi_set_mode_rpcdata {
	__rpc_string *ifname;
	int new_wifi_mode;
	int return_code;
};
typedef struct qcsapi_wifi_set_mode_rpcdata qcsapi_wifi_set_mode_rpcdata;

struct qcsapi_wifi_get_phy_mode_rpcdata {
	__rpc_string *ifname;
	__rpc_string *p_wifi_phy_mode;
	int return_code;
};
typedef struct qcsapi_wifi_get_phy_mode_rpcdata qcsapi_wifi_get_phy_mode_rpcdata;

struct qcsapi_wifi_set_phy_mode_rpcdata {
	__rpc_string *ifname;
	__rpc_string *new_phy_mode;
	int return_code;
};
typedef struct qcsapi_wifi_set_phy_mode_rpcdata qcsapi_wifi_set_phy_mode_rpcdata;

struct qcsapi_wifi_reload_in_mode_rpcdata {
	__rpc_string *ifname;
	int new_wifi_mode;
	int return_code;
};
typedef struct qcsapi_wifi_reload_in_mode_rpcdata qcsapi_wifi_reload_in_mode_rpcdata;

struct qcsapi_wifi_rfenable_rpcdata {
	u_int onoff;
	int return_code;
};
typedef struct qcsapi_wifi_rfenable_rpcdata qcsapi_wifi_rfenable_rpcdata;

struct qcsapi_wifi_rfstatus_rpcdata {
	u_int *rfstatus;
	int return_code;
};
typedef struct qcsapi_wifi_rfstatus_rpcdata qcsapi_wifi_rfstatus_rpcdata;

struct qcsapi_wifi_get_bw_rpcdata {
	__rpc_string *ifname;
	u_int *p_bw;
	int return_code;
};
typedef struct qcsapi_wifi_get_bw_rpcdata qcsapi_wifi_get_bw_rpcdata;

struct qcsapi_wifi_set_bw_rpcdata {
	__rpc_string *ifname;
	u_int bw;
	int return_code;
};
typedef struct qcsapi_wifi_set_bw_rpcdata qcsapi_wifi_set_bw_rpcdata;

struct qcsapi_wifi_set_vht_rpcdata {
	__rpc_string *ifname;
	u_int the_vht;
	int return_code;
};
typedef struct qcsapi_wifi_set_vht_rpcdata qcsapi_wifi_set_vht_rpcdata;

struct qcsapi_wifi_get_vht_rpcdata {
	__rpc_string *ifname;
	u_int *vht;
	int return_code;
};
typedef struct qcsapi_wifi_get_vht_rpcdata qcsapi_wifi_get_vht_rpcdata;

struct qcsapi_wifi_get_channel_rpcdata {
	__rpc_string *ifname;
	u_int *p_current_channel;
	int return_code;
};
typedef struct qcsapi_wifi_get_channel_rpcdata qcsapi_wifi_get_channel_rpcdata;

struct qcsapi_wifi_set_channel_rpcdata {
	__rpc_string *ifname;
	u_int new_channel;
	int return_code;
};
typedef struct qcsapi_wifi_set_channel_rpcdata qcsapi_wifi_set_channel_rpcdata;

struct qcsapi_wifi_set_chan_pri_inactive_rpcdata {
	__rpc_string *ifname;
	u_int channel;
	u_int inactive;
	int return_code;
};
typedef struct qcsapi_wifi_set_chan_pri_inactive_rpcdata qcsapi_wifi_set_chan_pri_inactive_rpcdata;

struct qcsapi_wifi_chan_control_rpcdata {
	__rpc_string *ifname;
	__rpc_qcsapi_data_256bytes *chans;
	uint32_t cnt;
	uint8_t flag;
	int return_code;
};
typedef struct qcsapi_wifi_chan_control_rpcdata qcsapi_wifi_chan_control_rpcdata;

struct qcsapi_wifi_get_chan_disabled_rpcdata {
	__rpc_string *ifname;
	__rpc_qcsapi_data_256bytes *p_chans;
	uint8_t *p_cnt;
	int return_code;
};
typedef struct qcsapi_wifi_get_chan_disabled_rpcdata qcsapi_wifi_get_chan_disabled_rpcdata;

struct qcsapi_wifi_get_beacon_interval_rpcdata {
	__rpc_string *ifname;
	u_int *p_current_intval;
	int return_code;
};
typedef struct qcsapi_wifi_get_beacon_interval_rpcdata qcsapi_wifi_get_beacon_interval_rpcdata;

struct qcsapi_wifi_set_beacon_interval_rpcdata {
	__rpc_string *ifname;
	u_int new_intval;
	int return_code;
};
typedef struct qcsapi_wifi_set_beacon_interval_rpcdata qcsapi_wifi_set_beacon_interval_rpcdata;

struct qcsapi_wifi_get_dtim_rpcdata {
	__rpc_string *ifname;
	u_int *p_dtim;
	int return_code;
};
typedef struct qcsapi_wifi_get_dtim_rpcdata qcsapi_wifi_get_dtim_rpcdata;

struct qcsapi_wifi_set_dtim_rpcdata {
	__rpc_string *ifname;
	u_int new_dtim;
	int return_code;
};
typedef struct qcsapi_wifi_set_dtim_rpcdata qcsapi_wifi_set_dtim_rpcdata;

struct qcsapi_wifi_get_assoc_limit_rpcdata {
	__rpc_string *ifname;
	u_int *p_assoc_limit;
	int return_code;
};
typedef struct qcsapi_wifi_get_assoc_limit_rpcdata qcsapi_wifi_get_assoc_limit_rpcdata;

struct qcsapi_wifi_get_bss_assoc_limit_rpcdata {
	__rpc_string *ifname;
	u_int *p_bss_lim_pri;
	int return_code;
};
typedef struct qcsapi_wifi_get_bss_assoc_limit_rpcdata qcsapi_wifi_get_bss_assoc_limit_rpcdata;

struct qcsapi_wifi_set_assoc_limit_rpcdata {
	__rpc_string *ifname;
	u_int new_assoc_limit;
	int return_code;
};
typedef struct qcsapi_wifi_set_assoc_limit_rpcdata qcsapi_wifi_set_assoc_limit_rpcdata;

struct qcsapi_wifi_set_bss_assoc_limit_rpcdata {
	__rpc_string *ifname;
	u_int bss_lim_pri;
	int return_code;
};
typedef struct qcsapi_wifi_set_bss_assoc_limit_rpcdata qcsapi_wifi_set_bss_assoc_limit_rpcdata;

struct qcsapi_wifi_get_BSSID_rpcdata {
	__rpc_string *ifname;
	__rpc_qcsapi_mac_addr_p current_BSSID;
	int return_code;
};
typedef struct qcsapi_wifi_get_BSSID_rpcdata qcsapi_wifi_get_BSSID_rpcdata;

struct qcsapi_wifi_get_config_BSSID_rpcdata {
	__rpc_string *ifname;
	__rpc_qcsapi_mac_addr_p config_BSSID;
	int return_code;
};
typedef struct qcsapi_wifi_get_config_BSSID_rpcdata qcsapi_wifi_get_config_BSSID_rpcdata;

struct qcsapi_wifi_ssid_get_bssid_rpcdata {
	__rpc_string *ifname;
	__rpc_string *ssid_str;
	__rpc_qcsapi_mac_addr_p bssid;
	int return_code;
};
typedef struct qcsapi_wifi_ssid_get_bssid_rpcdata qcsapi_wifi_ssid_get_bssid_rpcdata;

struct qcsapi_wifi_ssid_set_bssid_rpcdata {
	__rpc_string *ifname;
	__rpc_string *ssid_str;
	__rpc_qcsapi_mac_addr_p bssid;
	int return_code;
};
typedef struct qcsapi_wifi_ssid_set_bssid_rpcdata qcsapi_wifi_ssid_set_bssid_rpcdata;

struct qcsapi_wifi_get_SSID_rpcdata {
	__rpc_string *ifname;
	__rpc_string *SSID_str;
	int return_code;
};
typedef struct qcsapi_wifi_get_SSID_rpcdata qcsapi_wifi_get_SSID_rpcdata;

struct qcsapi_wifi_set_SSID_rpcdata {
	__rpc_string *ifname;
	__rpc_string *SSID_str;
	int return_code;
};
typedef struct qcsapi_wifi_set_SSID_rpcdata qcsapi_wifi_set_SSID_rpcdata;

struct qcsapi_wifi_get_IEEE_802_11_standard_rpcdata {
	__rpc_string *ifname;
	__rpc_string *IEEE_802_11_standard;
	int return_code;
};
typedef struct qcsapi_wifi_get_IEEE_802_11_standard_rpcdata qcsapi_wifi_get_IEEE_802_11_standard_rpcdata;

struct qcsapi_wifi_get_list_channels_rpcdata {
	__rpc_string *ifname;
	__rpc_string *list_of_channels;
	int return_code;
};
typedef struct qcsapi_wifi_get_list_channels_rpcdata qcsapi_wifi_get_list_channels_rpcdata;

struct qcsapi_wifi_get_mode_switch_rpcdata {
	uint8_t *p_wifi_mode_switch_setting;
	int return_code;
};
typedef struct qcsapi_wifi_get_mode_switch_rpcdata qcsapi_wifi_get_mode_switch_rpcdata;

struct qcsapi_wifi_disassociate_rpcdata {
	__rpc_string *ifname;
	int return_code;
};
typedef struct qcsapi_wifi_disassociate_rpcdata qcsapi_wifi_disassociate_rpcdata;

struct qcsapi_wifi_disassociate_sta_rpcdata {
	__rpc_string *ifname;
	__rpc_qcsapi_mac_addr_p mac;
	int return_code;
};
typedef struct qcsapi_wifi_disassociate_sta_rpcdata qcsapi_wifi_disassociate_sta_rpcdata;

struct qcsapi_wifi_reassociate_rpcdata {
	__rpc_string *ifname;
	int return_code;
};
typedef struct qcsapi_wifi_reassociate_rpcdata qcsapi_wifi_reassociate_rpcdata;

struct qcsapi_wifi_get_disconn_info_rpcdata {
	__rpc_string *ifname;
	__rpc_qcsapi_disconn_info *disconn_info;
	int return_code;
};
typedef struct qcsapi_wifi_get_disconn_info_rpcdata qcsapi_wifi_get_disconn_info_rpcdata;

struct qcsapi_wifi_disable_wps_rpcdata {
	__rpc_string *ifname;
	int disable_wps;
	int return_code;
};
typedef struct qcsapi_wifi_disable_wps_rpcdata qcsapi_wifi_disable_wps_rpcdata;

struct qcsapi_wifi_associate_rpcdata {
	__rpc_string *ifname;
	__rpc_string *join_ssid;
	int return_code;
};
typedef struct qcsapi_wifi_associate_rpcdata qcsapi_wifi_associate_rpcdata;

struct qcsapi_wifi_start_cca_rpcdata {
	__rpc_string *ifname;
	int channel;
	int duration;
	int return_code;
};
typedef struct qcsapi_wifi_start_cca_rpcdata qcsapi_wifi_start_cca_rpcdata;

struct qcsapi_wifi_get_noise_rpcdata {
	__rpc_string *ifname;
	int *p_noise;
	int return_code;
};
typedef struct qcsapi_wifi_get_noise_rpcdata qcsapi_wifi_get_noise_rpcdata;

struct qcsapi_wifi_get_rssi_by_chain_rpcdata {
	__rpc_string *ifname;
	int rf_chain;
	int *p_rssi;
	int return_code;
};
typedef struct qcsapi_wifi_get_rssi_by_chain_rpcdata qcsapi_wifi_get_rssi_by_chain_rpcdata;

struct qcsapi_wifi_get_avg_snr_rpcdata {
	__rpc_string *ifname;
	int *p_snr;
	int return_code;
};
typedef struct qcsapi_wifi_get_avg_snr_rpcdata qcsapi_wifi_get_avg_snr_rpcdata;

struct qcsapi_get_primary_interface_rpcdata {
	uint32_t maxlen;
	__rpc_string *ifname;
	int return_code;
};
typedef struct qcsapi_get_primary_interface_rpcdata qcsapi_get_primary_interface_rpcdata;

struct qcsapi_get_interface_by_index_rpcdata {
	u_int if_index;
	uint32_t maxlen;
	__rpc_string *ifname;
	int return_code;
};
typedef struct qcsapi_get_interface_by_index_rpcdata qcsapi_get_interface_by_index_rpcdata;

struct qcsapi_wifi_set_wifi_macaddr_rpcdata {
	__rpc_qcsapi_mac_addr_p new_mac_addr;
	int return_code;
};
typedef struct qcsapi_wifi_set_wifi_macaddr_rpcdata qcsapi_wifi_set_wifi_macaddr_rpcdata;

struct qcsapi_interface_get_BSSID_rpcdata {
	__rpc_string *ifname;
	__rpc_qcsapi_mac_addr_p current_BSSID;
	int return_code;
};
typedef struct qcsapi_interface_get_BSSID_rpcdata qcsapi_interface_get_BSSID_rpcdata;

struct qcsapi_wifi_get_rates_rpcdata {
	__rpc_string *ifname;
	int rate_type;
	__rpc_string *supported_rates;
	int return_code;
};
typedef struct qcsapi_wifi_get_rates_rpcdata qcsapi_wifi_get_rates_rpcdata;

struct qcsapi_wifi_set_rates_rpcdata {
	__rpc_string *ifname;
	int rate_type;
	__rpc_string *current_rates;
	int num_rates;
	int return_code;
};
typedef struct qcsapi_wifi_set_rates_rpcdata qcsapi_wifi_set_rates_rpcdata;

struct qcsapi_get_max_bitrate_rpcdata {
	__rpc_string *ifname;
	int max_str_len;
	__rpc_string *max_bitrate;
	int return_code;
};
typedef struct qcsapi_get_max_bitrate_rpcdata qcsapi_get_max_bitrate_rpcdata;

struct qcsapi_set_max_bitrate_rpcdata {
	__rpc_string *ifname;
	__rpc_string *max_bitrate;
	int return_code;
};
typedef struct qcsapi_set_max_bitrate_rpcdata qcsapi_set_max_bitrate_rpcdata;

struct qcsapi_wifi_qos_get_param_rpcdata {
	__rpc_string *ifname;
	int the_queue;
	int the_param;
	int ap_bss_flag;
	int *p_value;
	int return_code;
};
typedef struct qcsapi_wifi_qos_get_param_rpcdata qcsapi_wifi_qos_get_param_rpcdata;

struct qcsapi_wifi_qos_set_param_rpcdata {
	__rpc_string *ifname;
	int the_queue;
	int the_param;
	int ap_bss_flag;
	int value;
	int return_code;
};
typedef struct qcsapi_wifi_qos_set_param_rpcdata qcsapi_wifi_qos_set_param_rpcdata;

struct qcsapi_wifi_get_wmm_ac_map_rpcdata {
	__rpc_string *ifname;
	__rpc_string *mapping_table;
	int return_code;
};
typedef struct qcsapi_wifi_get_wmm_ac_map_rpcdata qcsapi_wifi_get_wmm_ac_map_rpcdata;

struct qcsapi_wifi_set_wmm_ac_map_rpcdata {
	__rpc_string *ifname;
	int user_prio;
	int ac_index;
	int return_code;
};
typedef struct qcsapi_wifi_set_wmm_ac_map_rpcdata qcsapi_wifi_set_wmm_ac_map_rpcdata;

struct qcsapi_wifi_get_dscp_8021p_map_rpcdata {
	__rpc_string *ifname;
	__rpc_string *mapping_table;
	int return_code;
};
typedef struct qcsapi_wifi_get_dscp_8021p_map_rpcdata qcsapi_wifi_get_dscp_8021p_map_rpcdata;

struct qcsapi_wifi_get_dscp_ac_map_rpcdata {
	__rpc_string *ifname;
	__rpc_qcsapi_data_64bytes *mapping_table;
	int return_code;
};
typedef struct qcsapi_wifi_get_dscp_ac_map_rpcdata qcsapi_wifi_get_dscp_ac_map_rpcdata;

struct qcsapi_wifi_set_dscp_8021p_map_rpcdata {
	__rpc_string *ifname;
	__rpc_string *ip_dscp_list;
	uint8_t dot1p_up;
	int return_code;
};
typedef struct qcsapi_wifi_set_dscp_8021p_map_rpcdata qcsapi_wifi_set_dscp_8021p_map_rpcdata;

struct qcsapi_wifi_set_dscp_ac_map_rpcdata {
	__rpc_string *ifname;
	__rpc_qcsapi_data_64bytes *dscp_list;
	uint8_t dscp_list_len;
	uint8_t ac;
	int return_code;
};
typedef struct qcsapi_wifi_set_dscp_ac_map_rpcdata qcsapi_wifi_set_dscp_ac_map_rpcdata;

struct qcsapi_wifi_get_priority_rpcdata {
	__rpc_string *ifname;
	uint8_t *p_priority;
	int return_code;
};
typedef struct qcsapi_wifi_get_priority_rpcdata qcsapi_wifi_get_priority_rpcdata;

struct qcsapi_wifi_set_priority_rpcdata {
	__rpc_string *ifname;
	uint8_t priority;
	int return_code;
};
typedef struct qcsapi_wifi_set_priority_rpcdata qcsapi_wifi_set_priority_rpcdata;

struct qcsapi_wifi_get_airfair_rpcdata {
	__rpc_string *ifname;
	uint8_t *p_airfair;
	int return_code;
};
typedef struct qcsapi_wifi_get_airfair_rpcdata qcsapi_wifi_get_airfair_rpcdata;

struct qcsapi_wifi_set_airfair_rpcdata {
	__rpc_string *ifname;
	uint8_t airfair;
	int return_code;
};
typedef struct qcsapi_wifi_set_airfair_rpcdata qcsapi_wifi_set_airfair_rpcdata;

struct qcsapi_wifi_get_tx_power_rpcdata {
	__rpc_string *ifname;
	u_int the_channel;
	int *p_tx_power;
	int return_code;
};
typedef struct qcsapi_wifi_get_tx_power_rpcdata qcsapi_wifi_get_tx_power_rpcdata;

struct qcsapi_wifi_set_tx_power_rpcdata {
	__rpc_string *ifname;
	u_int the_channel;
	int tx_power;
	int return_code;
};
typedef struct qcsapi_wifi_set_tx_power_rpcdata qcsapi_wifi_set_tx_power_rpcdata;

struct qcsapi_wifi_get_bw_power_rpcdata {
	__rpc_string *ifname;
	u_int the_channel;
	int *p_power_20M;
	int *p_power_40M;
	int *p_power_80M;
	int return_code;
};
typedef struct qcsapi_wifi_get_bw_power_rpcdata qcsapi_wifi_get_bw_power_rpcdata;

struct qcsapi_wifi_set_bw_power_rpcdata {
	__rpc_string *ifname;
	u_int the_channel;
	int power_20M;
	int power_40M;
	int power_80M;
	int return_code;
};
typedef struct qcsapi_wifi_set_bw_power_rpcdata qcsapi_wifi_set_bw_power_rpcdata;

struct qcsapi_wifi_get_bf_power_rpcdata {
	__rpc_string *ifname;
	u_int the_channel;
	u_int number_ss;
	int *p_power_20M;
	int *p_power_40M;
	int *p_power_80M;
	int return_code;
};
typedef struct qcsapi_wifi_get_bf_power_rpcdata qcsapi_wifi_get_bf_power_rpcdata;

struct qcsapi_wifi_set_bf_power_rpcdata {
	__rpc_string *ifname;
	u_int the_channel;
	u_int number_ss;
	int power_20M;
	int power_40M;
	int power_80M;
	int return_code;
};
typedef struct qcsapi_wifi_set_bf_power_rpcdata qcsapi_wifi_set_bf_power_rpcdata;

struct qcsapi_wifi_get_tx_power_ext_rpcdata {
	__rpc_string *ifname;
	u_int the_channel;
	u_int bf_on;
	u_int number_ss;
	int *p_power_20M;
	int *p_power_40M;
	int *p_power_80M;
	int return_code;
};
typedef struct qcsapi_wifi_get_tx_power_ext_rpcdata qcsapi_wifi_get_tx_power_ext_rpcdata;

struct qcsapi_wifi_set_tx_power_ext_rpcdata {
	__rpc_string *ifname;
	u_int the_channel;
	u_int bf_on;
	u_int number_ss;
	int power_20M;
	int power_40M;
	int power_80M;
	int return_code;
};
typedef struct qcsapi_wifi_set_tx_power_ext_rpcdata qcsapi_wifi_set_tx_power_ext_rpcdata;

struct qcsapi_wifi_get_chan_power_table_rpcdata {
	__rpc_string *ifname;
	__rpc_qcsapi_channel_power_table *chan_power_table;
	int return_code;
};
typedef struct qcsapi_wifi_get_chan_power_table_rpcdata qcsapi_wifi_get_chan_power_table_rpcdata;

struct qcsapi_wifi_set_chan_power_table_rpcdata {
	__rpc_string *ifname;
	__rpc_qcsapi_channel_power_table *chan_power_table;
	int return_code;
};
typedef struct qcsapi_wifi_set_chan_power_table_rpcdata qcsapi_wifi_set_chan_power_table_rpcdata;

struct qcsapi_wifi_get_power_selection_rpcdata {
	u_int *p_power_selection;
	int return_code;
};
typedef struct qcsapi_wifi_get_power_selection_rpcdata qcsapi_wifi_get_power_selection_rpcdata;

struct qcsapi_wifi_set_power_selection_rpcdata {
	u_int power_selection;
	int return_code;
};
typedef struct qcsapi_wifi_set_power_selection_rpcdata qcsapi_wifi_set_power_selection_rpcdata;

struct qcsapi_wifi_get_carrier_interference_rpcdata {
	__rpc_string *ifname;
	int *ci;
	int return_code;
};
typedef struct qcsapi_wifi_get_carrier_interference_rpcdata qcsapi_wifi_get_carrier_interference_rpcdata;

struct qcsapi_wifi_get_congestion_index_rpcdata {
	__rpc_string *ifname;
	int *ci;
	int return_code;
};
typedef struct qcsapi_wifi_get_congestion_index_rpcdata qcsapi_wifi_get_congestion_index_rpcdata;

struct qcsapi_wifi_get_supported_tx_power_levels_rpcdata {
	__rpc_string *ifname;
	__rpc_string *available_percentages;
	int return_code;
};
typedef struct qcsapi_wifi_get_supported_tx_power_levels_rpcdata qcsapi_wifi_get_supported_tx_power_levels_rpcdata;

struct qcsapi_wifi_get_current_tx_power_level_rpcdata {
	__rpc_string *ifname;
	uint32_t *p_current_percentage;
	int return_code;
};
typedef struct qcsapi_wifi_get_current_tx_power_level_rpcdata qcsapi_wifi_get_current_tx_power_level_rpcdata;

struct qcsapi_wifi_set_power_constraint_rpcdata {
	__rpc_string *ifname;
	uint32_t pwr_constraint;
	int return_code;
};
typedef struct qcsapi_wifi_set_power_constraint_rpcdata qcsapi_wifi_set_power_constraint_rpcdata;

struct qcsapi_wifi_get_power_constraint_rpcdata {
	__rpc_string *ifname;
	uint32_t *p_pwr_constraint;
	int return_code;
};
typedef struct qcsapi_wifi_get_power_constraint_rpcdata qcsapi_wifi_get_power_constraint_rpcdata;

struct qcsapi_wifi_set_tpc_interval_rpcdata {
	__rpc_string *ifname;
	int32_t tpc_interval;
	int return_code;
};
typedef struct qcsapi_wifi_set_tpc_interval_rpcdata qcsapi_wifi_set_tpc_interval_rpcdata;

struct qcsapi_wifi_get_tpc_interval_rpcdata {
	__rpc_string *ifname;
	uint32_t *p_tpc_interval;
	int return_code;
};
typedef struct qcsapi_wifi_get_tpc_interval_rpcdata qcsapi_wifi_get_tpc_interval_rpcdata;

struct qcsapi_wifi_get_assoc_records_rpcdata {
	__rpc_string *ifname;
	int reset;
	__rpc_qcsapi_assoc_records *records;
	int return_code;
};
typedef struct qcsapi_wifi_get_assoc_records_rpcdata qcsapi_wifi_get_assoc_records_rpcdata;

struct qcsapi_wifi_get_ap_isolate_rpcdata {
	__rpc_string *ifname;
	int *p_ap_isolate;
	int return_code;
};
typedef struct qcsapi_wifi_get_ap_isolate_rpcdata qcsapi_wifi_get_ap_isolate_rpcdata;

struct qcsapi_wifi_set_ap_isolate_rpcdata {
	__rpc_string *ifname;
	int new_ap_isolate;
	int return_code;
};
typedef struct qcsapi_wifi_set_ap_isolate_rpcdata qcsapi_wifi_set_ap_isolate_rpcdata;

struct qcsapi_wifi_get_intra_bss_isolate_rpcdata {
	__rpc_string *ifname;
	u_int *p_ap_isolate;
	int return_code;
};
typedef struct qcsapi_wifi_get_intra_bss_isolate_rpcdata qcsapi_wifi_get_intra_bss_isolate_rpcdata;

struct qcsapi_wifi_set_intra_bss_isolate_rpcdata {
	__rpc_string *ifname;
	u_int new_ap_isolate;
	int return_code;
};
typedef struct qcsapi_wifi_set_intra_bss_isolate_rpcdata qcsapi_wifi_set_intra_bss_isolate_rpcdata;

struct qcsapi_wifi_get_bss_isolate_rpcdata {
	__rpc_string *ifname;
	u_int *p_ap_isolate;
	int return_code;
};
typedef struct qcsapi_wifi_get_bss_isolate_rpcdata qcsapi_wifi_get_bss_isolate_rpcdata;

struct qcsapi_wifi_set_bss_isolate_rpcdata {
	__rpc_string *ifname;
	u_int new_ap_isolate;
	int return_code;
};
typedef struct qcsapi_wifi_set_bss_isolate_rpcdata qcsapi_wifi_set_bss_isolate_rpcdata;

struct qcsapi_wifi_disable_dfs_channels_rpcdata {
	__rpc_string *ifname;
	int disable_dfs;
	int channel;
	int return_code;
};
typedef struct qcsapi_wifi_disable_dfs_channels_rpcdata qcsapi_wifi_disable_dfs_channels_rpcdata;

struct qcsapi_wifi_create_restricted_bss_rpcdata {
	__rpc_string *ifname;
	__rpc_qcsapi_mac_addr_p mac_addr;
	int return_code;
};
typedef struct qcsapi_wifi_create_restricted_bss_rpcdata qcsapi_wifi_create_restricted_bss_rpcdata;

struct qcsapi_wifi_create_bss_rpcdata {
	__rpc_string *ifname;
	__rpc_qcsapi_mac_addr_p mac_addr;
	int return_code;
};
typedef struct qcsapi_wifi_create_bss_rpcdata qcsapi_wifi_create_bss_rpcdata;

struct qcsapi_wifi_remove_bss_rpcdata {
	__rpc_string *ifname;
	int return_code;
};
typedef struct qcsapi_wifi_remove_bss_rpcdata qcsapi_wifi_remove_bss_rpcdata;

struct qcsapi_wds_add_peer_rpcdata {
	__rpc_string *ifname;
	__rpc_qcsapi_mac_addr_p peer_address;
	int return_code;
};
typedef struct qcsapi_wds_add_peer_rpcdata qcsapi_wds_add_peer_rpcdata;

struct qcsapi_wds_add_peer_encrypt_rpcdata {
	__rpc_string *ifname;
	__rpc_qcsapi_mac_addr_p peer_address;
	u_int encryption;
	int return_code;
};
typedef struct qcsapi_wds_add_peer_encrypt_rpcdata qcsapi_wds_add_peer_encrypt_rpcdata;

struct qcsapi_wds_remove_peer_rpcdata {
	__rpc_string *ifname;
	__rpc_qcsapi_mac_addr_p peer_address;
	int return_code;
};
typedef struct qcsapi_wds_remove_peer_rpcdata qcsapi_wds_remove_peer_rpcdata;

struct qcsapi_wds_get_peer_address_rpcdata {
	__rpc_string *ifname;
	int index;
	__rpc_qcsapi_mac_addr_p peer_address;
	int return_code;
};
typedef struct qcsapi_wds_get_peer_address_rpcdata qcsapi_wds_get_peer_address_rpcdata;

struct qcsapi_wds_set_psk_rpcdata {
	__rpc_string *ifname;
	__rpc_qcsapi_mac_addr_p peer_address;
	__rpc_string *pre_shared_key;
	int return_code;
};
typedef struct qcsapi_wds_set_psk_rpcdata qcsapi_wds_set_psk_rpcdata;

struct qcsapi_wds_set_mode_rpcdata {
	__rpc_string *ifname;
	__rpc_qcsapi_mac_addr_p peer_address;
	int mode;
	int return_code;
};
typedef struct qcsapi_wds_set_mode_rpcdata qcsapi_wds_set_mode_rpcdata;

struct qcsapi_wds_get_mode_rpcdata {
	__rpc_string *ifname;
	int index;
	int *mode;
	int return_code;
};
typedef struct qcsapi_wds_get_mode_rpcdata qcsapi_wds_get_mode_rpcdata;

struct qcsapi_wifi_set_extender_params_rpcdata {
	__rpc_string *ifname;
	int type;
	int param_value;
	int return_code;
};
typedef struct qcsapi_wifi_set_extender_params_rpcdata qcsapi_wifi_set_extender_params_rpcdata;

struct qcsapi_wifi_get_extender_params_rpcdata {
	__rpc_string *ifname;
	int type;
	int *p_value;
	int return_code;
};
typedef struct qcsapi_wifi_get_extender_params_rpcdata qcsapi_wifi_get_extender_params_rpcdata;

struct qcsapi_wifi_get_beacon_type_rpcdata {
	__rpc_string *ifname;
	__rpc_string *p_current_beacon;
	int return_code;
};
typedef struct qcsapi_wifi_get_beacon_type_rpcdata qcsapi_wifi_get_beacon_type_rpcdata;

struct qcsapi_wifi_set_beacon_type_rpcdata {
	__rpc_string *ifname;
	__rpc_string *p_new_beacon;
	int return_code;
};
typedef struct qcsapi_wifi_set_beacon_type_rpcdata qcsapi_wifi_set_beacon_type_rpcdata;

struct qcsapi_wifi_get_WEP_key_index_rpcdata {
	__rpc_string *ifname;
	u_int *p_key_index;
	int return_code;
};
typedef struct qcsapi_wifi_get_WEP_key_index_rpcdata qcsapi_wifi_get_WEP_key_index_rpcdata;

struct qcsapi_wifi_set_WEP_key_index_rpcdata {
	__rpc_string *ifname;
	u_int key_index;
	int return_code;
};
typedef struct qcsapi_wifi_set_WEP_key_index_rpcdata qcsapi_wifi_set_WEP_key_index_rpcdata;

struct qcsapi_wifi_get_WEP_key_passphrase_rpcdata {
	__rpc_string *ifname;
	__rpc_string *current_passphrase;
	int return_code;
};
typedef struct qcsapi_wifi_get_WEP_key_passphrase_rpcdata qcsapi_wifi_get_WEP_key_passphrase_rpcdata;

struct qcsapi_wifi_set_WEP_key_passphrase_rpcdata {
	__rpc_string *ifname;
	__rpc_string *new_passphrase;
	int return_code;
};
typedef struct qcsapi_wifi_set_WEP_key_passphrase_rpcdata qcsapi_wifi_set_WEP_key_passphrase_rpcdata;

struct qcsapi_wifi_get_WEP_encryption_level_rpcdata {
	__rpc_string *ifname;
	__rpc_string *current_encryption_level;
	int return_code;
};
typedef struct qcsapi_wifi_get_WEP_encryption_level_rpcdata qcsapi_wifi_get_WEP_encryption_level_rpcdata;

struct qcsapi_wifi_get_basic_encryption_modes_rpcdata {
	__rpc_string *ifname;
	__rpc_string *encryption_modes;
	int return_code;
};
typedef struct qcsapi_wifi_get_basic_encryption_modes_rpcdata qcsapi_wifi_get_basic_encryption_modes_rpcdata;

struct qcsapi_wifi_set_basic_encryption_modes_rpcdata {
	__rpc_string *ifname;
	__rpc_string *encryption_modes;
	int return_code;
};
typedef struct qcsapi_wifi_set_basic_encryption_modes_rpcdata qcsapi_wifi_set_basic_encryption_modes_rpcdata;

struct qcsapi_wifi_get_basic_authentication_mode_rpcdata {
	__rpc_string *ifname;
	__rpc_string *authentication_mode;
	int return_code;
};
typedef struct qcsapi_wifi_get_basic_authentication_mode_rpcdata qcsapi_wifi_get_basic_authentication_mode_rpcdata;

struct qcsapi_wifi_set_basic_authentication_mode_rpcdata {
	__rpc_string *ifname;
	__rpc_string *authentication_mode;
	int return_code;
};
typedef struct qcsapi_wifi_set_basic_authentication_mode_rpcdata qcsapi_wifi_set_basic_authentication_mode_rpcdata;

struct qcsapi_wifi_get_WEP_key_rpcdata {
	__rpc_string *ifname;
	u_int key_index;
	__rpc_string *current_passphrase;
	int return_code;
};
typedef struct qcsapi_wifi_get_WEP_key_rpcdata qcsapi_wifi_get_WEP_key_rpcdata;

struct qcsapi_wifi_set_WEP_key_rpcdata {
	__rpc_string *ifname;
	u_int key_index;
	__rpc_string *new_passphrase;
	int return_code;
};
typedef struct qcsapi_wifi_set_WEP_key_rpcdata qcsapi_wifi_set_WEP_key_rpcdata;

struct qcsapi_wifi_get_WPA_encryption_modes_rpcdata {
	__rpc_string *ifname;
	__rpc_string *encryption_modes;
	int return_code;
};
typedef struct qcsapi_wifi_get_WPA_encryption_modes_rpcdata qcsapi_wifi_get_WPA_encryption_modes_rpcdata;

struct qcsapi_wifi_set_WPA_encryption_modes_rpcdata {
	__rpc_string *ifname;
	__rpc_string *encryption_modes;
	int return_code;
};
typedef struct qcsapi_wifi_set_WPA_encryption_modes_rpcdata qcsapi_wifi_set_WPA_encryption_modes_rpcdata;

struct qcsapi_wifi_get_WPA_authentication_mode_rpcdata {
	__rpc_string *ifname;
	__rpc_string *authentication_mode;
	int return_code;
};
typedef struct qcsapi_wifi_get_WPA_authentication_mode_rpcdata qcsapi_wifi_get_WPA_authentication_mode_rpcdata;

struct qcsapi_wifi_set_WPA_authentication_mode_rpcdata {
	__rpc_string *ifname;
	__rpc_string *authentication_mode;
	int return_code;
};
typedef struct qcsapi_wifi_set_WPA_authentication_mode_rpcdata qcsapi_wifi_set_WPA_authentication_mode_rpcdata;

struct qcsapi_wifi_get_interworking_rpcdata {
	__rpc_string *ifname;
	__rpc_string *interworking;
	int return_code;
};
typedef struct qcsapi_wifi_get_interworking_rpcdata qcsapi_wifi_get_interworking_rpcdata;

struct qcsapi_wifi_set_interworking_rpcdata {
	__rpc_string *ifname;
	__rpc_string *interworking;
	int return_code;
};
typedef struct qcsapi_wifi_set_interworking_rpcdata qcsapi_wifi_set_interworking_rpcdata;

struct qcsapi_wifi_get_80211u_params_rpcdata {
	__rpc_string *ifname;
	__rpc_string *u_param;
	__rpc_string *p_buffer;
	int return_code;
};
typedef struct qcsapi_wifi_get_80211u_params_rpcdata qcsapi_wifi_get_80211u_params_rpcdata;

struct qcsapi_wifi_set_80211u_params_rpcdata {
	__rpc_string *ifname;
	__rpc_string *param;
	__rpc_string *value1;
	__rpc_string *value2;
	int return_code;
};
typedef struct qcsapi_wifi_set_80211u_params_rpcdata qcsapi_wifi_set_80211u_params_rpcdata;

struct qcsapi_security_get_nai_realms_rpcdata {
	__rpc_string *ifname;
	__rpc_string *p_value;
	int return_code;
};
typedef struct qcsapi_security_get_nai_realms_rpcdata qcsapi_security_get_nai_realms_rpcdata;

struct qcsapi_security_add_nai_realm_rpcdata {
	__rpc_string *ifname;
	int encoding;
	__rpc_string *nai_realm;
	__rpc_string *eap_method;
	int return_code;
};
typedef struct qcsapi_security_add_nai_realm_rpcdata qcsapi_security_add_nai_realm_rpcdata;

struct qcsapi_security_del_nai_realm_rpcdata {
	__rpc_string *ifname;
	__rpc_string *nai_realm;
	int return_code;
};
typedef struct qcsapi_security_del_nai_realm_rpcdata qcsapi_security_del_nai_realm_rpcdata;

struct qcsapi_security_get_roaming_consortium_rpcdata {
	__rpc_string *ifname;
	__rpc_string *p_value;
	int return_code;
};
typedef struct qcsapi_security_get_roaming_consortium_rpcdata qcsapi_security_get_roaming_consortium_rpcdata;

struct qcsapi_security_add_roaming_consortium_rpcdata {
	__rpc_string *ifname;
	__rpc_string *p_value;
	int return_code;
};
typedef struct qcsapi_security_add_roaming_consortium_rpcdata qcsapi_security_add_roaming_consortium_rpcdata;

struct qcsapi_security_del_roaming_consortium_rpcdata {
	__rpc_string *ifname;
	__rpc_string *p_value;
	int return_code;
};
typedef struct qcsapi_security_del_roaming_consortium_rpcdata qcsapi_security_del_roaming_consortium_rpcdata;

struct qcsapi_security_get_venue_name_rpcdata {
	__rpc_string *ifname;
	__rpc_string *p_value;
	int return_code;
};
typedef struct qcsapi_security_get_venue_name_rpcdata qcsapi_security_get_venue_name_rpcdata;

struct qcsapi_security_add_venue_name_rpcdata {
	__rpc_string *ifname;
	__rpc_string *lang_code;
	__rpc_string *venue_name;
	int return_code;
};
typedef struct qcsapi_security_add_venue_name_rpcdata qcsapi_security_add_venue_name_rpcdata;

struct qcsapi_security_del_venue_name_rpcdata {
	__rpc_string *ifname;
	__rpc_string *lang_code;
	__rpc_string *venue_name;
	int return_code;
};
typedef struct qcsapi_security_del_venue_name_rpcdata qcsapi_security_del_venue_name_rpcdata;

struct qcsapi_security_get_oper_friendly_name_rpcdata {
	__rpc_string *ifname;
	__rpc_string *p_value;
	int return_code;
};
typedef struct qcsapi_security_get_oper_friendly_name_rpcdata qcsapi_security_get_oper_friendly_name_rpcdata;

struct qcsapi_security_add_oper_friendly_name_rpcdata {
	__rpc_string *ifname;
	__rpc_string *lang_code;
	__rpc_string *oper_friendly_name;
	int return_code;
};
typedef struct qcsapi_security_add_oper_friendly_name_rpcdata qcsapi_security_add_oper_friendly_name_rpcdata;

struct qcsapi_security_del_oper_friendly_name_rpcdata {
	__rpc_string *ifname;
	__rpc_string *lang_code;
	__rpc_string *oper_friendly_name;
	int return_code;
};
typedef struct qcsapi_security_del_oper_friendly_name_rpcdata qcsapi_security_del_oper_friendly_name_rpcdata;

struct qcsapi_security_get_hs20_conn_capab_rpcdata {
	__rpc_string *ifname;
	__rpc_string *p_value;
	int return_code;
};
typedef struct qcsapi_security_get_hs20_conn_capab_rpcdata qcsapi_security_get_hs20_conn_capab_rpcdata;

struct qcsapi_security_add_hs20_conn_capab_rpcdata {
	__rpc_string *ifname;
	__rpc_string *ip_proto;
	__rpc_string *port_num;
	__rpc_string *status;
	int return_code;
};
typedef struct qcsapi_security_add_hs20_conn_capab_rpcdata qcsapi_security_add_hs20_conn_capab_rpcdata;

struct qcsapi_security_del_hs20_conn_capab_rpcdata {
	__rpc_string *ifname;
	__rpc_string *ip_proto;
	__rpc_string *port_num;
	__rpc_string *status;
	int return_code;
};
typedef struct qcsapi_security_del_hs20_conn_capab_rpcdata qcsapi_security_del_hs20_conn_capab_rpcdata;

struct qcsapi_wifi_get_hs20_status_rpcdata {
	__rpc_string *ifname;
	__rpc_string *p_hs20;
	int return_code;
};
typedef struct qcsapi_wifi_get_hs20_status_rpcdata qcsapi_wifi_get_hs20_status_rpcdata;

struct qcsapi_wifi_set_hs20_status_rpcdata {
	__rpc_string *ifname;
	__rpc_string *hs20_val;
	int return_code;
};
typedef struct qcsapi_wifi_set_hs20_status_rpcdata qcsapi_wifi_set_hs20_status_rpcdata;

struct qcsapi_wifi_get_proxy_arp_rpcdata {
	__rpc_string *ifname;
	__rpc_string *p_proxy_arp;
	int return_code;
};
typedef struct qcsapi_wifi_get_proxy_arp_rpcdata qcsapi_wifi_get_proxy_arp_rpcdata;

struct qcsapi_wifi_set_proxy_arp_rpcdata {
	__rpc_string *ifname;
	__rpc_string *proxy_arp_val;
	int return_code;
};
typedef struct qcsapi_wifi_set_proxy_arp_rpcdata qcsapi_wifi_set_proxy_arp_rpcdata;

struct qcsapi_wifi_get_l2_ext_filter_rpcdata {
	__rpc_string *ifname;
	__rpc_string *param;
	__rpc_string *value;
	int return_code;
};
typedef struct qcsapi_wifi_get_l2_ext_filter_rpcdata qcsapi_wifi_get_l2_ext_filter_rpcdata;

struct qcsapi_wifi_set_l2_ext_filter_rpcdata {
	__rpc_string *ifname;
	__rpc_string *param;
	__rpc_string *value;
	int return_code;
};
typedef struct qcsapi_wifi_set_l2_ext_filter_rpcdata qcsapi_wifi_set_l2_ext_filter_rpcdata;

struct qcsapi_wifi_get_hs20_params_rpcdata {
	__rpc_string *ifname;
	__rpc_string *hs_param;
	__rpc_string *p_buffer;
	int return_code;
};
typedef struct qcsapi_wifi_get_hs20_params_rpcdata qcsapi_wifi_get_hs20_params_rpcdata;

struct qcsapi_wifi_set_hs20_params_rpcdata {
	__rpc_string *ifname;
	__rpc_string *hs_param;
	__rpc_string *value1;
	__rpc_string *value2;
	__rpc_string *value3;
	__rpc_string *value4;
	__rpc_string *value5;
	__rpc_string *value6;
	int return_code;
};
typedef struct qcsapi_wifi_set_hs20_params_rpcdata qcsapi_wifi_set_hs20_params_rpcdata;

struct qcsapi_remove_11u_param_rpcdata {
	__rpc_string *ifname;
	__rpc_string *param;
	int return_code;
};
typedef struct qcsapi_remove_11u_param_rpcdata qcsapi_remove_11u_param_rpcdata;

struct qcsapi_remove_hs20_param_rpcdata {
	__rpc_string *ifname;
	__rpc_string *hs_param;
	int return_code;
};
typedef struct qcsapi_remove_hs20_param_rpcdata qcsapi_remove_hs20_param_rpcdata;

struct qcsapi_wifi_get_IEEE11i_encryption_modes_rpcdata {
	__rpc_string *ifname;
	__rpc_string *encryption_modes;
	int return_code;
};
typedef struct qcsapi_wifi_get_IEEE11i_encryption_modes_rpcdata qcsapi_wifi_get_IEEE11i_encryption_modes_rpcdata;

struct qcsapi_wifi_set_IEEE11i_encryption_modes_rpcdata {
	__rpc_string *ifname;
	__rpc_string *encryption_modes;
	int return_code;
};
typedef struct qcsapi_wifi_set_IEEE11i_encryption_modes_rpcdata qcsapi_wifi_set_IEEE11i_encryption_modes_rpcdata;

struct qcsapi_wifi_get_IEEE11i_authentication_mode_rpcdata {
	__rpc_string *ifname;
	__rpc_string *authentication_mode;
	int return_code;
};
typedef struct qcsapi_wifi_get_IEEE11i_authentication_mode_rpcdata qcsapi_wifi_get_IEEE11i_authentication_mode_rpcdata;

struct qcsapi_wifi_set_IEEE11i_authentication_mode_rpcdata {
	__rpc_string *ifname;
	__rpc_string *authentication_mode;
	int return_code;
};
typedef struct qcsapi_wifi_set_IEEE11i_authentication_mode_rpcdata qcsapi_wifi_set_IEEE11i_authentication_mode_rpcdata;

struct qcsapi_wifi_get_michael_errcnt_rpcdata {
	__rpc_string *ifname;
	uint32_t *errcount;
	int return_code;
};
typedef struct qcsapi_wifi_get_michael_errcnt_rpcdata qcsapi_wifi_get_michael_errcnt_rpcdata;

struct qcsapi_wifi_get_pre_shared_key_rpcdata {
	__rpc_string *ifname;
	u_int key_index;
	__rpc_string *pre_shared_key;
	int return_code;
};
typedef struct qcsapi_wifi_get_pre_shared_key_rpcdata qcsapi_wifi_get_pre_shared_key_rpcdata;

struct qcsapi_wifi_set_pre_shared_key_rpcdata {
	__rpc_string *ifname;
	u_int key_index;
	__rpc_string *pre_shared_key;
	int return_code;
};
typedef struct qcsapi_wifi_set_pre_shared_key_rpcdata qcsapi_wifi_set_pre_shared_key_rpcdata;

struct qcsapi_wifi_add_radius_auth_server_cfg_rpcdata {
	__rpc_string *ifname;
	__rpc_string *radius_auth_server_ipaddr;
	__rpc_string *radius_auth_server_port;
	__rpc_string *radius_auth_server_sh_key;
	int return_code;
};
typedef struct qcsapi_wifi_add_radius_auth_server_cfg_rpcdata qcsapi_wifi_add_radius_auth_server_cfg_rpcdata;

struct qcsapi_wifi_del_radius_auth_server_cfg_rpcdata {
	__rpc_string *ifname;
	__rpc_string *radius_auth_server_ipaddr;
	__rpc_string *constp_radius_port;
	int return_code;
};
typedef struct qcsapi_wifi_del_radius_auth_server_cfg_rpcdata qcsapi_wifi_del_radius_auth_server_cfg_rpcdata;

struct qcsapi_wifi_get_radius_auth_server_cfg_rpcdata {
	__rpc_string *ifname;
	__rpc_string *radius_auth_server_cfg;
	int return_code;
};
typedef struct qcsapi_wifi_get_radius_auth_server_cfg_rpcdata qcsapi_wifi_get_radius_auth_server_cfg_rpcdata;

struct qcsapi_wifi_set_own_ip_addr_rpcdata {
	__rpc_string *ifname;
	__rpc_string *own_ip_addr;
	int return_code;
};
typedef struct qcsapi_wifi_set_own_ip_addr_rpcdata qcsapi_wifi_set_own_ip_addr_rpcdata;

struct qcsapi_wifi_get_key_passphrase_rpcdata {
	__rpc_string *ifname;
	u_int key_index;
	__rpc_string *passphrase;
	int return_code;
};
typedef struct qcsapi_wifi_get_key_passphrase_rpcdata qcsapi_wifi_get_key_passphrase_rpcdata;

struct qcsapi_wifi_set_key_passphrase_rpcdata {
	__rpc_string *ifname;
	u_int key_index;
	__rpc_string *passphrase;
	int return_code;
};
typedef struct qcsapi_wifi_set_key_passphrase_rpcdata qcsapi_wifi_set_key_passphrase_rpcdata;

struct qcsapi_wifi_get_group_key_interval_rpcdata {
	__rpc_string *ifname;
	__rpc_string *group_key_interval;
	int return_code;
};
typedef struct qcsapi_wifi_get_group_key_interval_rpcdata qcsapi_wifi_get_group_key_interval_rpcdata;

struct qcsapi_wifi_set_group_key_interval_rpcdata {
	__rpc_string *ifname;
	__rpc_string *group_key_interval;
	int return_code;
};
typedef struct qcsapi_wifi_set_group_key_interval_rpcdata qcsapi_wifi_set_group_key_interval_rpcdata;

struct qcsapi_wifi_get_pmf_rpcdata {
	__rpc_string *ifname;
	int *p_pmf_cap;
	int return_code;
};
typedef struct qcsapi_wifi_get_pmf_rpcdata qcsapi_wifi_get_pmf_rpcdata;

struct qcsapi_wifi_set_pmf_rpcdata {
	__rpc_string *ifname;
	int pmf_cap;
	int return_code;
};
typedef struct qcsapi_wifi_set_pmf_rpcdata qcsapi_wifi_set_pmf_rpcdata;

struct qcsapi_wifi_get_wpa_status_rpcdata {
	__rpc_string *ifname;
	__rpc_string *mac_addr;
	u_int max_len;
	__rpc_string *wpa_status;
	int return_code;
};
typedef struct qcsapi_wifi_get_wpa_status_rpcdata qcsapi_wifi_get_wpa_status_rpcdata;

struct qcsapi_wifi_get_psk_auth_failures_rpcdata {
	__rpc_string *ifname;
	u_int *count;
	int return_code;
};
typedef struct qcsapi_wifi_get_psk_auth_failures_rpcdata qcsapi_wifi_get_psk_auth_failures_rpcdata;

struct qcsapi_wifi_get_auth_state_rpcdata {
	__rpc_string *ifname;
	__rpc_string *mac_addr;
	int *auth_state;
	int return_code;
};
typedef struct qcsapi_wifi_get_auth_state_rpcdata qcsapi_wifi_get_auth_state_rpcdata;

struct qcsapi_wifi_set_security_defer_mode_rpcdata {
	__rpc_string *ifname;
	int defer;
	int return_code;
};
typedef struct qcsapi_wifi_set_security_defer_mode_rpcdata qcsapi_wifi_set_security_defer_mode_rpcdata;

struct qcsapi_wifi_get_security_defer_mode_rpcdata {
	__rpc_string *ifname;
	int *defer;
	int return_code;
};
typedef struct qcsapi_wifi_get_security_defer_mode_rpcdata qcsapi_wifi_get_security_defer_mode_rpcdata;

struct qcsapi_wifi_apply_security_config_rpcdata {
	__rpc_string *ifname;
	int return_code;
};
typedef struct qcsapi_wifi_apply_security_config_rpcdata qcsapi_wifi_apply_security_config_rpcdata;

struct qcsapi_wifi_set_mac_address_filtering_rpcdata {
	__rpc_string *ifname;
	int new_mac_address_filtering;
	int return_code;
};
typedef struct qcsapi_wifi_set_mac_address_filtering_rpcdata qcsapi_wifi_set_mac_address_filtering_rpcdata;

struct qcsapi_wifi_get_mac_address_filtering_rpcdata {
	__rpc_string *ifname;
	int *current_mac_address_filtering;
	int return_code;
};
typedef struct qcsapi_wifi_get_mac_address_filtering_rpcdata qcsapi_wifi_get_mac_address_filtering_rpcdata;

struct qcsapi_wifi_authorize_mac_address_rpcdata {
	__rpc_string *ifname;
	__rpc_qcsapi_mac_addr_p address_to_authorize;
	int return_code;
};
typedef struct qcsapi_wifi_authorize_mac_address_rpcdata qcsapi_wifi_authorize_mac_address_rpcdata;

struct qcsapi_wifi_deny_mac_address_rpcdata {
	__rpc_string *ifname;
	__rpc_qcsapi_mac_addr_p address_to_deny;
	int return_code;
};
typedef struct qcsapi_wifi_deny_mac_address_rpcdata qcsapi_wifi_deny_mac_address_rpcdata;

struct qcsapi_wifi_remove_mac_address_rpcdata {
	__rpc_string *ifname;
	__rpc_qcsapi_mac_addr_p address_to_remove;
	int return_code;
};
typedef struct qcsapi_wifi_remove_mac_address_rpcdata qcsapi_wifi_remove_mac_address_rpcdata;

struct qcsapi_wifi_is_mac_address_authorized_rpcdata {
	__rpc_string *ifname;
	__rpc_qcsapi_mac_addr_p address_to_verify;
	int *p_mac_address_authorized;
	int return_code;
};
typedef struct qcsapi_wifi_is_mac_address_authorized_rpcdata qcsapi_wifi_is_mac_address_authorized_rpcdata;

struct qcsapi_wifi_get_authorized_mac_addresses_rpcdata {
	__rpc_string *ifname;
	u_int sizeof_list;
	__rpc_string *list_mac_addresses;
	int return_code;
};
typedef struct qcsapi_wifi_get_authorized_mac_addresses_rpcdata qcsapi_wifi_get_authorized_mac_addresses_rpcdata;

struct qcsapi_wifi_get_denied_mac_addresses_rpcdata {
	__rpc_string *ifname;
	u_int sizeof_list;
	__rpc_string *list_mac_addresses;
	int return_code;
};
typedef struct qcsapi_wifi_get_denied_mac_addresses_rpcdata qcsapi_wifi_get_denied_mac_addresses_rpcdata;

struct qcsapi_wifi_set_accept_oui_filter_rpcdata {
	__rpc_string *ifname;
	__rpc_qcsapi_mac_addr_p oui;
	int flag;
	int return_code;
};
typedef struct qcsapi_wifi_set_accept_oui_filter_rpcdata qcsapi_wifi_set_accept_oui_filter_rpcdata;

struct qcsapi_wifi_get_accept_oui_filter_rpcdata {
	__rpc_string *ifname;
	u_int sizeof_list;
	__rpc_string *oui_list;
	int return_code;
};
typedef struct qcsapi_wifi_get_accept_oui_filter_rpcdata qcsapi_wifi_get_accept_oui_filter_rpcdata;

struct qcsapi_wifi_clear_mac_address_filters_rpcdata {
	__rpc_string *ifname;
	int return_code;
};
typedef struct qcsapi_wifi_clear_mac_address_filters_rpcdata qcsapi_wifi_clear_mac_address_filters_rpcdata;

struct qcsapi_wifi_set_mac_address_reserve_rpcdata {
	__rpc_string *ifname;
	__rpc_string *addr;
	__rpc_string *mask;
	int return_code;
};
typedef struct qcsapi_wifi_set_mac_address_reserve_rpcdata qcsapi_wifi_set_mac_address_reserve_rpcdata;

struct qcsapi_wifi_get_mac_address_reserve_rpcdata {
	__rpc_string *ifname;
	__rpc_string *buf;
	int return_code;
};
typedef struct qcsapi_wifi_get_mac_address_reserve_rpcdata qcsapi_wifi_get_mac_address_reserve_rpcdata;

struct qcsapi_wifi_clear_mac_address_reserve_rpcdata {
	__rpc_string *ifname;
	int return_code;
};
typedef struct qcsapi_wifi_clear_mac_address_reserve_rpcdata qcsapi_wifi_clear_mac_address_reserve_rpcdata;

struct qcsapi_wifi_get_option_rpcdata {
	__rpc_string *ifname;
	int qcsapi_option;
	int *p_current_option;
	int return_code;
};
typedef struct qcsapi_wifi_get_option_rpcdata qcsapi_wifi_get_option_rpcdata;

struct qcsapi_wifi_set_option_rpcdata {
	__rpc_string *ifname;
	int qcsapi_option;
	int new_option;
	int return_code;
};
typedef struct qcsapi_wifi_set_option_rpcdata qcsapi_wifi_set_option_rpcdata;

struct qcsapi_get_board_parameter_rpcdata {
	int board_param;
	__rpc_string *p_buffer;
	int return_code;
};
typedef struct qcsapi_get_board_parameter_rpcdata qcsapi_get_board_parameter_rpcdata;

struct qcsapi_get_swfeat_list_rpcdata {
	__rpc_string *p_buffer;
	int return_code;
};
typedef struct qcsapi_get_swfeat_list_rpcdata qcsapi_get_swfeat_list_rpcdata;

struct qcsapi_SSID_create_SSID_rpcdata {
	__rpc_string *ifname;
	__rpc_string *new_SSID;
	int return_code;
};
typedef struct qcsapi_SSID_create_SSID_rpcdata qcsapi_SSID_create_SSID_rpcdata;

struct qcsapi_SSID_remove_SSID_rpcdata {
	__rpc_string *ifname;
	__rpc_string *del_SSID;
	int return_code;
};
typedef struct qcsapi_SSID_remove_SSID_rpcdata qcsapi_SSID_remove_SSID_rpcdata;

struct qcsapi_SSID_verify_SSID_rpcdata {
	__rpc_string *ifname;
	__rpc_string *current_SSID;
	int return_code;
};
typedef struct qcsapi_SSID_verify_SSID_rpcdata qcsapi_SSID_verify_SSID_rpcdata;

struct qcsapi_SSID_rename_SSID_rpcdata {
	__rpc_string *ifname;
	__rpc_string *current_SSID;
	__rpc_string *new_SSID;
	int return_code;
};
typedef struct qcsapi_SSID_rename_SSID_rpcdata qcsapi_SSID_rename_SSID_rpcdata;

struct qcsapi_SSID_get_SSID_list_rpcdata {
	__rpc_string *ifname;
	u_int arrayc;
	struct {
		u_int list_SSID_len;
		str *list_SSID_val;
	} list_SSID;
	int return_code;
};
typedef struct qcsapi_SSID_get_SSID_list_rpcdata qcsapi_SSID_get_SSID_list_rpcdata;

struct qcsapi_SSID_set_protocol_rpcdata {
	__rpc_string *ifname;
	__rpc_string *current_SSID;
	__rpc_string *new_protocol;
	int return_code;
};
typedef struct qcsapi_SSID_set_protocol_rpcdata qcsapi_SSID_set_protocol_rpcdata;

struct qcsapi_SSID_get_protocol_rpcdata {
	__rpc_string *ifname;
	__rpc_string *current_SSID;
	__rpc_string *current_protocol;
	int return_code;
};
typedef struct qcsapi_SSID_get_protocol_rpcdata qcsapi_SSID_get_protocol_rpcdata;

struct qcsapi_SSID_get_encryption_modes_rpcdata {
	__rpc_string *ifname;
	__rpc_string *current_SSID;
	__rpc_string *encryption_modes;
	int return_code;
};
typedef struct qcsapi_SSID_get_encryption_modes_rpcdata qcsapi_SSID_get_encryption_modes_rpcdata;

struct qcsapi_SSID_set_encryption_modes_rpcdata {
	__rpc_string *ifname;
	__rpc_string *current_SSID;
	__rpc_string *encryption_modes;
	int return_code;
};
typedef struct qcsapi_SSID_set_encryption_modes_rpcdata qcsapi_SSID_set_encryption_modes_rpcdata;

struct qcsapi_SSID_get_group_encryption_rpcdata {
	__rpc_string *ifname;
	__rpc_string *current_SSID;
	__rpc_string *encryption_mode;
	int return_code;
};
typedef struct qcsapi_SSID_get_group_encryption_rpcdata qcsapi_SSID_get_group_encryption_rpcdata;

struct qcsapi_SSID_set_group_encryption_rpcdata {
	__rpc_string *ifname;
	__rpc_string *current_SSID;
	__rpc_string *encryption_mode;
	int return_code;
};
typedef struct qcsapi_SSID_set_group_encryption_rpcdata qcsapi_SSID_set_group_encryption_rpcdata;

struct qcsapi_SSID_get_authentication_mode_rpcdata {
	__rpc_string *ifname;
	__rpc_string *current_SSID;
	__rpc_string *authentication_mode;
	int return_code;
};
typedef struct qcsapi_SSID_get_authentication_mode_rpcdata qcsapi_SSID_get_authentication_mode_rpcdata;

struct qcsapi_SSID_set_authentication_mode_rpcdata {
	__rpc_string *ifname;
	__rpc_string *current_SSID;
	__rpc_string *authentication_mode;
	int return_code;
};
typedef struct qcsapi_SSID_set_authentication_mode_rpcdata qcsapi_SSID_set_authentication_mode_rpcdata;

struct qcsapi_SSID_get_pre_shared_key_rpcdata {
	__rpc_string *ifname;
	__rpc_string *current_SSID;
	u_int key_index;
	__rpc_string *pre_shared_key;
	int return_code;
};
typedef struct qcsapi_SSID_get_pre_shared_key_rpcdata qcsapi_SSID_get_pre_shared_key_rpcdata;

struct qcsapi_SSID_set_pre_shared_key_rpcdata {
	__rpc_string *ifname;
	__rpc_string *current_SSID;
	u_int key_index;
	__rpc_string *pre_shared_key;
	int return_code;
};
typedef struct qcsapi_SSID_set_pre_shared_key_rpcdata qcsapi_SSID_set_pre_shared_key_rpcdata;

struct qcsapi_SSID_get_key_passphrase_rpcdata {
	__rpc_string *ifname;
	__rpc_string *current_SSID;
	u_int key_index;
	__rpc_string *passphrase;
	int return_code;
};
typedef struct qcsapi_SSID_get_key_passphrase_rpcdata qcsapi_SSID_get_key_passphrase_rpcdata;

struct qcsapi_SSID_set_key_passphrase_rpcdata {
	__rpc_string *ifname;
	__rpc_string *current_SSID;
	u_int key_index;
	__rpc_string *passphrase;
	int return_code;
};
typedef struct qcsapi_SSID_set_key_passphrase_rpcdata qcsapi_SSID_set_key_passphrase_rpcdata;

struct qcsapi_SSID_get_pmf_rpcdata {
	__rpc_string *ifname;
	__rpc_string *current_SSID;
	int *p_pmf_cap;
	int return_code;
};
typedef struct qcsapi_SSID_get_pmf_rpcdata qcsapi_SSID_get_pmf_rpcdata;

struct qcsapi_SSID_set_pmf_rpcdata {
	__rpc_string *ifname;
	__rpc_string *SSID_str;
	int pmf_cap;
	int return_code;
};
typedef struct qcsapi_SSID_set_pmf_rpcdata qcsapi_SSID_set_pmf_rpcdata;

struct qcsapi_SSID_get_wps_SSID_rpcdata {
	__rpc_string *ifname;
	__rpc_string *wps_SSID;
	int return_code;
};
typedef struct qcsapi_SSID_get_wps_SSID_rpcdata qcsapi_SSID_get_wps_SSID_rpcdata;

struct qcsapi_wifi_vlan_config_rpcdata {
	__rpc_string *ifname;
	int cmd;
	uint32_t vlanid;
	uint32_t flags;
	int return_code;
};
typedef struct qcsapi_wifi_vlan_config_rpcdata qcsapi_wifi_vlan_config_rpcdata;

struct qcsapi_wifi_show_vlan_config_rpcdata {
	__rpc_string *ifname;
	__rpc_string *vcfg;
	int return_code;
};
typedef struct qcsapi_wifi_show_vlan_config_rpcdata qcsapi_wifi_show_vlan_config_rpcdata;

struct qcsapi_enable_vlan_pass_through_rpcdata {
	__rpc_string *ifname;
	int enabled;
	int return_code;
};
typedef struct qcsapi_enable_vlan_pass_through_rpcdata qcsapi_enable_vlan_pass_through_rpcdata;

struct qcsapi_wifi_set_vlan_promisc_rpcdata {
	int enable;
	int return_code;
};
typedef struct qcsapi_wifi_set_vlan_promisc_rpcdata qcsapi_wifi_set_vlan_promisc_rpcdata;

struct qcsapi_wps_registrar_report_button_press_rpcdata {
	__rpc_string *ifname;
	int return_code;
};
typedef struct qcsapi_wps_registrar_report_button_press_rpcdata qcsapi_wps_registrar_report_button_press_rpcdata;

struct qcsapi_wps_registrar_report_pin_rpcdata {
	__rpc_string *ifname;
	__rpc_string *wps_pin;
	int return_code;
};
typedef struct qcsapi_wps_registrar_report_pin_rpcdata qcsapi_wps_registrar_report_pin_rpcdata;

struct qcsapi_wps_registrar_get_pp_devname_rpcdata {
	__rpc_string *ifname;
	int blacklist;
	__rpc_string *pp_devname;
	int return_code;
};
typedef struct qcsapi_wps_registrar_get_pp_devname_rpcdata qcsapi_wps_registrar_get_pp_devname_rpcdata;

struct qcsapi_wps_registrar_set_pp_devname_rpcdata {
	__rpc_string *ifname;
	int update_blacklist;
	__rpc_string *pp_devname;
	int return_code;
};
typedef struct qcsapi_wps_registrar_set_pp_devname_rpcdata qcsapi_wps_registrar_set_pp_devname_rpcdata;

struct qcsapi_wps_enrollee_report_button_press_rpcdata {
	__rpc_string *ifname;
	__rpc_qcsapi_mac_addr_p bssid;
	int return_code;
};
typedef struct qcsapi_wps_enrollee_report_button_press_rpcdata qcsapi_wps_enrollee_report_button_press_rpcdata;

struct qcsapi_wps_enrollee_report_pin_rpcdata {
	__rpc_string *ifname;
	__rpc_qcsapi_mac_addr_p bssid;
	__rpc_string *wps_pin;
	int return_code;
};
typedef struct qcsapi_wps_enrollee_report_pin_rpcdata qcsapi_wps_enrollee_report_pin_rpcdata;

struct qcsapi_wps_enrollee_generate_pin_rpcdata {
	__rpc_string *ifname;
	__rpc_qcsapi_mac_addr_p bssid;
	__rpc_string *wps_pin;
	int return_code;
};
typedef struct qcsapi_wps_enrollee_generate_pin_rpcdata qcsapi_wps_enrollee_generate_pin_rpcdata;

struct qcsapi_wps_get_ap_pin_rpcdata {
	__rpc_string *ifname;
	int force_regenerate;
	__rpc_string *wps_pin;
	int return_code;
};
typedef struct qcsapi_wps_get_ap_pin_rpcdata qcsapi_wps_get_ap_pin_rpcdata;

struct qcsapi_wps_set_ap_pin_rpcdata {
	__rpc_string *ifname;
	__rpc_string *wps_pin;
	int return_code;
};
typedef struct qcsapi_wps_set_ap_pin_rpcdata qcsapi_wps_set_ap_pin_rpcdata;

struct qcsapi_wps_save_ap_pin_rpcdata {
	__rpc_string *ifname;
	int return_code;
};
typedef struct qcsapi_wps_save_ap_pin_rpcdata qcsapi_wps_save_ap_pin_rpcdata;

struct qcsapi_wps_enable_ap_pin_rpcdata {
	__rpc_string *ifname;
	int enable;
	int return_code;
};
typedef struct qcsapi_wps_enable_ap_pin_rpcdata qcsapi_wps_enable_ap_pin_rpcdata;

struct qcsapi_wps_get_sta_pin_rpcdata {
	__rpc_string *ifname;
	__rpc_string *wps_pin;
	int return_code;
};
typedef struct qcsapi_wps_get_sta_pin_rpcdata qcsapi_wps_get_sta_pin_rpcdata;

struct qcsapi_wps_get_state_rpcdata {
	__rpc_string *ifname;
	u_int max_len;
	__rpc_string *wps_state;
	int return_code;
};
typedef struct qcsapi_wps_get_state_rpcdata qcsapi_wps_get_state_rpcdata;

struct qcsapi_wps_get_configured_state_rpcdata {
	__rpc_string *ifname;
	u_int max_len;
	__rpc_string *wps_state;
	int return_code;
};
typedef struct qcsapi_wps_get_configured_state_rpcdata qcsapi_wps_get_configured_state_rpcdata;

struct qcsapi_wps_get_runtime_state_rpcdata {
	__rpc_string *ifname;
	int max_len;
	__rpc_string *state;
	int return_code;
};
typedef struct qcsapi_wps_get_runtime_state_rpcdata qcsapi_wps_get_runtime_state_rpcdata;

struct qcsapi_wps_set_configured_state_rpcdata {
	__rpc_string *ifname;
	u_int state;
	int return_code;
};
typedef struct qcsapi_wps_set_configured_state_rpcdata qcsapi_wps_set_configured_state_rpcdata;

struct qcsapi_wps_get_param_rpcdata {
	__rpc_string *ifname;
	int wps_type;
	u_int max_len;
	__rpc_string *wps_str;
	int return_code;
};
typedef struct qcsapi_wps_get_param_rpcdata qcsapi_wps_get_param_rpcdata;

struct qcsapi_wps_set_timeout_rpcdata {
	__rpc_string *ifname;
	int value;
	int return_code;
};
typedef struct qcsapi_wps_set_timeout_rpcdata qcsapi_wps_set_timeout_rpcdata;

struct qcsapi_wps_on_hidden_ssid_rpcdata {
	__rpc_string *ifname;
	int value;
	int return_code;
};
typedef struct qcsapi_wps_on_hidden_ssid_rpcdata qcsapi_wps_on_hidden_ssid_rpcdata;

struct qcsapi_wps_on_hidden_ssid_status_rpcdata {
	__rpc_string *ifname;
	int max_len;
	__rpc_string *state;
	int return_code;
};
typedef struct qcsapi_wps_on_hidden_ssid_status_rpcdata qcsapi_wps_on_hidden_ssid_status_rpcdata;

struct qcsapi_wps_upnp_enable_rpcdata {
	__rpc_string *ifname;
	int value;
	int return_code;
};
typedef struct qcsapi_wps_upnp_enable_rpcdata qcsapi_wps_upnp_enable_rpcdata;

struct qcsapi_wps_upnp_status_rpcdata {
	__rpc_string *ifname;
	int reply_len;
	__rpc_string *reply;
	int return_code;
};
typedef struct qcsapi_wps_upnp_status_rpcdata qcsapi_wps_upnp_status_rpcdata;

struct qcsapi_wps_allow_pbc_overlap_rpcdata {
	__rpc_string *ifname;
	u_int allow;
	int return_code;
};
typedef struct qcsapi_wps_allow_pbc_overlap_rpcdata qcsapi_wps_allow_pbc_overlap_rpcdata;

struct qcsapi_wps_get_allow_pbc_overlap_status_rpcdata {
	__rpc_string *ifname;
	int *status;
	int return_code;
};
typedef struct qcsapi_wps_get_allow_pbc_overlap_status_rpcdata qcsapi_wps_get_allow_pbc_overlap_status_rpcdata;

struct qcsapi_wps_set_access_control_rpcdata {
	__rpc_string *ifname;
	uint32_t ctrl_state;
	int return_code;
};
typedef struct qcsapi_wps_set_access_control_rpcdata qcsapi_wps_set_access_control_rpcdata;

struct qcsapi_wps_get_access_control_rpcdata {
	__rpc_string *ifname;
	uint32_t *ctrl_state;
	int return_code;
};
typedef struct qcsapi_wps_get_access_control_rpcdata qcsapi_wps_get_access_control_rpcdata;

struct qcsapi_wps_set_param_rpcdata {
	__rpc_string *ifname;
	int param_type;
	__rpc_string *param_value;
	int return_code;
};
typedef struct qcsapi_wps_set_param_rpcdata qcsapi_wps_set_param_rpcdata;

struct qcsapi_wps_cancel_rpcdata {
	__rpc_string *ifname;
	int return_code;
};
typedef struct qcsapi_wps_cancel_rpcdata qcsapi_wps_cancel_rpcdata;

struct qcsapi_wps_set_pbc_in_srcm_rpcdata {
	__rpc_string *ifname;
	u_int enabled;
	int return_code;
};
typedef struct qcsapi_wps_set_pbc_in_srcm_rpcdata qcsapi_wps_set_pbc_in_srcm_rpcdata;

struct qcsapi_wps_get_pbc_in_srcm_rpcdata {
	__rpc_string *ifname;
	u_int *p_enabled;
	int return_code;
};
typedef struct qcsapi_wps_get_pbc_in_srcm_rpcdata qcsapi_wps_get_pbc_in_srcm_rpcdata;

struct qcsapi_registrar_set_default_pbc_bss_rpcdata {
	__rpc_string *ifname;
	int return_code;
};
typedef struct qcsapi_registrar_set_default_pbc_bss_rpcdata qcsapi_registrar_set_default_pbc_bss_rpcdata;

struct qcsapi_registrar_get_default_pbc_bss_rpcdata {
	int len;
	__rpc_string *default_bss;
	int return_code;
};
typedef struct qcsapi_registrar_get_default_pbc_bss_rpcdata qcsapi_registrar_get_default_pbc_bss_rpcdata;

struct qcsapi_gpio_set_config_rpcdata {
	uint8_t gpio_pin;
	int new_gpio_config;
	int return_code;
};
typedef struct qcsapi_gpio_set_config_rpcdata qcsapi_gpio_set_config_rpcdata;

struct qcsapi_gpio_get_config_rpcdata {
	uint8_t gpio_pin;
	int *p_gpio_config;
	int return_code;
};
typedef struct qcsapi_gpio_get_config_rpcdata qcsapi_gpio_get_config_rpcdata;

struct qcsapi_led_get_rpcdata {
	uint8_t led_ident;
	uint8_t *p_led_setting;
	int return_code;
};
typedef struct qcsapi_led_get_rpcdata qcsapi_led_get_rpcdata;

struct qcsapi_led_set_rpcdata {
	uint8_t led_ident;
	uint8_t new_led_setting;
	int return_code;
};
typedef struct qcsapi_led_set_rpcdata qcsapi_led_set_rpcdata;

struct qcsapi_led_pwm_enable_rpcdata {
	uint8_t led_ident;
	uint8_t onoff;
	u_int high_count;
	u_int low_count;
	int return_code;
};
typedef struct qcsapi_led_pwm_enable_rpcdata qcsapi_led_pwm_enable_rpcdata;

struct qcsapi_led_brightness_rpcdata {
	uint8_t led_ident;
	u_int level;
	int return_code;
};
typedef struct qcsapi_led_brightness_rpcdata qcsapi_led_brightness_rpcdata;

struct qcsapi_gpio_enable_wps_push_button_rpcdata {
	uint8_t wps_push_button;
	uint8_t active_logic;
	uint8_t use_interrupt_flag;
	int return_code;
};
typedef struct qcsapi_gpio_enable_wps_push_button_rpcdata qcsapi_gpio_enable_wps_push_button_rpcdata;

struct qcsapi_wifi_get_count_associations_rpcdata {
	__rpc_string *ifname;
	u_int *p_association_count;
	int return_code;
};
typedef struct qcsapi_wifi_get_count_associations_rpcdata qcsapi_wifi_get_count_associations_rpcdata;

struct qcsapi_wifi_get_associated_device_mac_addr_rpcdata {
	__rpc_string *ifname;
	u_int device_index;
	__rpc_qcsapi_mac_addr_p device_mac_addr;
	int return_code;
};
typedef struct qcsapi_wifi_get_associated_device_mac_addr_rpcdata qcsapi_wifi_get_associated_device_mac_addr_rpcdata;

struct qcsapi_wifi_get_associated_device_ip_addr_rpcdata {
	__rpc_string *ifname;
	u_int device_index;
	u_int *ip_addr;
	int return_code;
};
typedef struct qcsapi_wifi_get_associated_device_ip_addr_rpcdata qcsapi_wifi_get_associated_device_ip_addr_rpcdata;

struct qcsapi_wifi_get_link_quality_rpcdata {
	__rpc_string *ifname;
	u_int association_index;
	u_int *p_link_quality;
	int return_code;
};
typedef struct qcsapi_wifi_get_link_quality_rpcdata qcsapi_wifi_get_link_quality_rpcdata;

struct qcsapi_wifi_get_link_quality_max_rpcdata {
	__rpc_string *ifname;
	u_int *p_max_quality;
	int return_code;
};
typedef struct qcsapi_wifi_get_link_quality_max_rpcdata qcsapi_wifi_get_link_quality_max_rpcdata;

struct qcsapi_wifi_get_rx_bytes_per_association_rpcdata {
	__rpc_string *ifname;
	u_int association_index;
	uint64_t *p_rx_bytes;
	int return_code;
};
typedef struct qcsapi_wifi_get_rx_bytes_per_association_rpcdata qcsapi_wifi_get_rx_bytes_per_association_rpcdata;

struct qcsapi_wifi_get_tx_bytes_per_association_rpcdata {
	__rpc_string *ifname;
	u_int association_index;
	uint64_t *p_tx_bytes;
	int return_code;
};
typedef struct qcsapi_wifi_get_tx_bytes_per_association_rpcdata qcsapi_wifi_get_tx_bytes_per_association_rpcdata;

struct qcsapi_wifi_get_rx_packets_per_association_rpcdata {
	__rpc_string *ifname;
	u_int association_index;
	u_int *p_rx_packets;
	int return_code;
};
typedef struct qcsapi_wifi_get_rx_packets_per_association_rpcdata qcsapi_wifi_get_rx_packets_per_association_rpcdata;

struct qcsapi_wifi_get_tx_packets_per_association_rpcdata {
	__rpc_string *ifname;
	u_int association_index;
	u_int *p_tx_packets;
	int return_code;
};
typedef struct qcsapi_wifi_get_tx_packets_per_association_rpcdata qcsapi_wifi_get_tx_packets_per_association_rpcdata;

struct qcsapi_wifi_get_tx_err_packets_per_association_rpcdata {
	__rpc_string *ifname;
	u_int association_index;
	u_int *p_tx_err_packets;
	int return_code;
};
typedef struct qcsapi_wifi_get_tx_err_packets_per_association_rpcdata qcsapi_wifi_get_tx_err_packets_per_association_rpcdata;

struct qcsapi_wifi_get_rssi_per_association_rpcdata {
	__rpc_string *ifname;
	u_int association_index;
	u_int *p_rssi;
	int return_code;
};
typedef struct qcsapi_wifi_get_rssi_per_association_rpcdata qcsapi_wifi_get_rssi_per_association_rpcdata;

struct qcsapi_wifi_get_rssi_in_dbm_per_association_rpcdata {
	__rpc_string *ifname;
	u_int association_index;
	int *p_rssi;
	int return_code;
};
typedef struct qcsapi_wifi_get_rssi_in_dbm_per_association_rpcdata qcsapi_wifi_get_rssi_in_dbm_per_association_rpcdata;

struct qcsapi_wifi_get_bw_per_association_rpcdata {
	__rpc_string *ifname;
	u_int association_index;
	u_int *p_bw;
	int return_code;
};
typedef struct qcsapi_wifi_get_bw_per_association_rpcdata qcsapi_wifi_get_bw_per_association_rpcdata;

struct qcsapi_wifi_get_tx_phy_rate_per_association_rpcdata {
	__rpc_string *ifname;
	u_int association_index;
	u_int *p_tx_phy_rate;
	int return_code;
};
typedef struct qcsapi_wifi_get_tx_phy_rate_per_association_rpcdata qcsapi_wifi_get_tx_phy_rate_per_association_rpcdata;

struct qcsapi_wifi_get_rx_phy_rate_per_association_rpcdata {
	__rpc_string *ifname;
	u_int association_index;
	u_int *p_rx_phy_rate;
	int return_code;
};
typedef struct qcsapi_wifi_get_rx_phy_rate_per_association_rpcdata qcsapi_wifi_get_rx_phy_rate_per_association_rpcdata;

struct qcsapi_wifi_get_tx_mcs_per_association_rpcdata {
	__rpc_string *ifname;
	u_int association_index;
	u_int *p_mcs;
	int return_code;
};
typedef struct qcsapi_wifi_get_tx_mcs_per_association_rpcdata qcsapi_wifi_get_tx_mcs_per_association_rpcdata;

struct qcsapi_wifi_get_rx_mcs_per_association_rpcdata {
	__rpc_string *ifname;
	u_int association_index;
	u_int *p_mcs;
	int return_code;
};
typedef struct qcsapi_wifi_get_rx_mcs_per_association_rpcdata qcsapi_wifi_get_rx_mcs_per_association_rpcdata;

struct qcsapi_wifi_get_achievable_tx_phy_rate_per_association_rpcdata {
	__rpc_string *ifname;
	u_int association_index;
	u_int *p_achievable_tx_phy_rate;
	int return_code;
};
typedef struct qcsapi_wifi_get_achievable_tx_phy_rate_per_association_rpcdata qcsapi_wifi_get_achievable_tx_phy_rate_per_association_rpcdata;

struct qcsapi_wifi_get_achievable_rx_phy_rate_per_association_rpcdata {
	__rpc_string *ifname;
	u_int association_index;
	u_int *p_achievable_rx_phy_rate;
	int return_code;
};
typedef struct qcsapi_wifi_get_achievable_rx_phy_rate_per_association_rpcdata qcsapi_wifi_get_achievable_rx_phy_rate_per_association_rpcdata;

struct qcsapi_wifi_get_auth_enc_per_association_rpcdata {
	__rpc_string *ifname;
	u_int association_index;
	u_int *p_auth_enc;
	int return_code;
};
typedef struct qcsapi_wifi_get_auth_enc_per_association_rpcdata qcsapi_wifi_get_auth_enc_per_association_rpcdata;

struct qcsapi_wifi_get_tput_caps_rpcdata {
	__rpc_string *ifname;
	u_int association_index;
	__rpc_ieee8011req_sta_tput_caps *tput_caps;
	int return_code;
};
typedef struct qcsapi_wifi_get_tput_caps_rpcdata qcsapi_wifi_get_tput_caps_rpcdata;

struct qcsapi_wifi_get_connection_mode_rpcdata {
	__rpc_string *ifname;
	u_int association_index;
	u_int *connection_mode;
	int return_code;
};
typedef struct qcsapi_wifi_get_connection_mode_rpcdata qcsapi_wifi_get_connection_mode_rpcdata;

struct qcsapi_wifi_get_vendor_per_association_rpcdata {
	__rpc_string *ifname;
	u_int association_index;
	u_int *p_vendor;
	int return_code;
};
typedef struct qcsapi_wifi_get_vendor_per_association_rpcdata qcsapi_wifi_get_vendor_per_association_rpcdata;

struct qcsapi_wifi_get_max_mimo_rpcdata {
	__rpc_string *ifname;
	u_int association_index;
	__rpc_string *p_max_mimo;
	int return_code;
};
typedef struct qcsapi_wifi_get_max_mimo_rpcdata qcsapi_wifi_get_max_mimo_rpcdata;

struct qcsapi_wifi_get_snr_per_association_rpcdata {
	__rpc_string *ifname;
	u_int association_index;
	int *p_snr;
	int return_code;
};
typedef struct qcsapi_wifi_get_snr_per_association_rpcdata qcsapi_wifi_get_snr_per_association_rpcdata;

struct qcsapi_wifi_get_time_associated_per_association_rpcdata {
	__rpc_string *ifname;
	u_int association_index;
	u_int *time_associated;
	int return_code;
};
typedef struct qcsapi_wifi_get_time_associated_per_association_rpcdata qcsapi_wifi_get_time_associated_per_association_rpcdata;

struct qcsapi_wifi_get_node_param_rpcdata {
	__rpc_string *ifname;
	uint32_t node_index;
	int param_type;
	int local_remote_flag;
	__rpc_string *input_param_str;
	__rpc_qcsapi_measure_report_result *report_result;
	int return_code;
};
typedef struct qcsapi_wifi_get_node_param_rpcdata qcsapi_wifi_get_node_param_rpcdata;

struct qcsapi_wifi_get_node_counter_rpcdata {
	__rpc_string *ifname;
	uint32_t node_index;
	int counter_type;
	int local_remote_flag;
	uint64_t *p_value;
	int return_code;
};
typedef struct qcsapi_wifi_get_node_counter_rpcdata qcsapi_wifi_get_node_counter_rpcdata;

struct qcsapi_wifi_get_node_stats_rpcdata {
	__rpc_string *ifname;
	uint32_t node_index;
	int local_remote_flag;
	__rpc_qcsapi_node_stats *p_stats;
	int return_code;
};
typedef struct qcsapi_wifi_get_node_stats_rpcdata qcsapi_wifi_get_node_stats_rpcdata;

struct qcsapi_wifi_get_max_queued_rpcdata {
	__rpc_string *ifname;
	uint32_t node_index;
	int local_remote_flag;
	int reset_flag;
	uint32_t *max_queued;
	int return_code;
};
typedef struct qcsapi_wifi_get_max_queued_rpcdata qcsapi_wifi_get_max_queued_rpcdata;

struct qcsapi_wifi_get_hw_noise_per_association_rpcdata {
	__rpc_string *ifname;
	u_int association_index;
	int *p_hw_noise;
	int return_code;
};
typedef struct qcsapi_wifi_get_hw_noise_per_association_rpcdata qcsapi_wifi_get_hw_noise_per_association_rpcdata;

struct qcsapi_wifi_get_mlme_stats_per_mac_rpcdata {
	__rpc_qcsapi_mac_addr_p client_mac_addr;
	__rpc_qcsapi_mlme_stats *stats;
	int return_code;
};
typedef struct qcsapi_wifi_get_mlme_stats_per_mac_rpcdata qcsapi_wifi_get_mlme_stats_per_mac_rpcdata;

struct qcsapi_wifi_get_mlme_stats_per_association_rpcdata {
	__rpc_string *ifname;
	u_int association_index;
	__rpc_qcsapi_mlme_stats *stats;
	int return_code;
};
typedef struct qcsapi_wifi_get_mlme_stats_per_association_rpcdata qcsapi_wifi_get_mlme_stats_per_association_rpcdata;

struct qcsapi_wifi_get_mlme_stats_macs_list_rpcdata {
	__rpc_qcsapi_mlme_stats_macs *macs_list;
	int return_code;
};
typedef struct qcsapi_wifi_get_mlme_stats_macs_list_rpcdata qcsapi_wifi_get_mlme_stats_macs_list_rpcdata;

struct qcsapi_wifi_get_list_regulatory_regions_rpcdata {
	__rpc_string *list_regulatory_regions;
	int return_code;
};
typedef struct qcsapi_wifi_get_list_regulatory_regions_rpcdata qcsapi_wifi_get_list_regulatory_regions_rpcdata;

struct qcsapi_regulatory_get_list_regulatory_regions_rpcdata {
	__rpc_string *list_regulatory_regions;
	int return_code;
};
typedef struct qcsapi_regulatory_get_list_regulatory_regions_rpcdata qcsapi_regulatory_get_list_regulatory_regions_rpcdata;

struct qcsapi_wifi_get_list_regulatory_channels_rpcdata {
	__rpc_string *region_by_name;
	u_int bw;
	__rpc_string *list_of_channels;
	int return_code;
};
typedef struct qcsapi_wifi_get_list_regulatory_channels_rpcdata qcsapi_wifi_get_list_regulatory_channels_rpcdata;

struct qcsapi_regulatory_get_list_regulatory_channels_rpcdata {
	__rpc_string *region_by_name;
	u_int bw;
	__rpc_string *list_of_channels;
	int return_code;
};
typedef struct qcsapi_regulatory_get_list_regulatory_channels_rpcdata qcsapi_regulatory_get_list_regulatory_channels_rpcdata;

struct qcsapi_regulatory_get_list_regulatory_bands_rpcdata {
	__rpc_string *region_by_name;
	__rpc_string *list_of_bands;
	int return_code;
};
typedef struct qcsapi_regulatory_get_list_regulatory_bands_rpcdata qcsapi_regulatory_get_list_regulatory_bands_rpcdata;

struct qcsapi_wifi_get_regulatory_tx_power_rpcdata {
	__rpc_string *ifname;
	u_int the_channel;
	__rpc_string *region_by_name;
	int *p_tx_power;
	int return_code;
};
typedef struct qcsapi_wifi_get_regulatory_tx_power_rpcdata qcsapi_wifi_get_regulatory_tx_power_rpcdata;

struct qcsapi_regulatory_get_regulatory_tx_power_rpcdata {
	__rpc_string *ifname;
	u_int the_channel;
	__rpc_string *region_by_name;
	int *p_tx_power;
	int return_code;
};
typedef struct qcsapi_regulatory_get_regulatory_tx_power_rpcdata qcsapi_regulatory_get_regulatory_tx_power_rpcdata;

struct qcsapi_wifi_get_configured_tx_power_rpcdata {
	__rpc_string *ifname;
	u_int the_channel;
	__rpc_string *region_by_name;
	u_int bw;
	int *p_tx_power;
	int return_code;
};
typedef struct qcsapi_wifi_get_configured_tx_power_rpcdata qcsapi_wifi_get_configured_tx_power_rpcdata;

struct qcsapi_regulatory_get_configured_tx_power_rpcdata {
	__rpc_string *ifname;
	u_int the_channel;
	__rpc_string *region_by_name;
	u_int bw;
	int *p_tx_power;
	int return_code;
};
typedef struct qcsapi_regulatory_get_configured_tx_power_rpcdata qcsapi_regulatory_get_configured_tx_power_rpcdata;

struct qcsapi_regulatory_get_configured_tx_power_ext_rpcdata {
	__rpc_string *ifname;
	u_int the_channel;
	__rpc_string *region_by_name;
	int the_bw;
	u_int bf_on;
	u_int number_ss;
	int *p_tx_power;
	int return_code;
};
typedef struct qcsapi_regulatory_get_configured_tx_power_ext_rpcdata qcsapi_regulatory_get_configured_tx_power_ext_rpcdata;

struct qcsapi_wifi_set_regulatory_region_rpcdata {
	__rpc_string *ifname;
	__rpc_string *region_by_name;
	int return_code;
};
typedef struct qcsapi_wifi_set_regulatory_region_rpcdata qcsapi_wifi_set_regulatory_region_rpcdata;

struct qcsapi_regulatory_set_regulatory_region_rpcdata {
	__rpc_string *ifname;
	__rpc_string *region_by_name;
	int return_code;
};
typedef struct qcsapi_regulatory_set_regulatory_region_rpcdata qcsapi_regulatory_set_regulatory_region_rpcdata;

struct qcsapi_regulatory_restore_regulatory_tx_power_rpcdata {
	__rpc_string *ifname;
	int return_code;
};
typedef struct qcsapi_regulatory_restore_regulatory_tx_power_rpcdata qcsapi_regulatory_restore_regulatory_tx_power_rpcdata;

struct qcsapi_wifi_get_regulatory_region_rpcdata {
	__rpc_string *ifname;
	__rpc_string *region_by_name;
	int return_code;
};
typedef struct qcsapi_wifi_get_regulatory_region_rpcdata qcsapi_wifi_get_regulatory_region_rpcdata;

struct qcsapi_regulatory_overwrite_country_code_rpcdata {
	__rpc_string *ifname;
	__rpc_string *curr_country_name;
	__rpc_string *new_country_name;
	int return_code;
};
typedef struct qcsapi_regulatory_overwrite_country_code_rpcdata qcsapi_regulatory_overwrite_country_code_rpcdata;

struct qcsapi_wifi_set_regulatory_channel_rpcdata {
	__rpc_string *ifname;
	u_int the_channel;
	__rpc_string *region_by_name;
	u_int tx_power_offset;
	int return_code;
};
typedef struct qcsapi_wifi_set_regulatory_channel_rpcdata qcsapi_wifi_set_regulatory_channel_rpcdata;

struct qcsapi_regulatory_set_regulatory_channel_rpcdata {
	__rpc_string *ifname;
	u_int the_channel;
	__rpc_string *region_by_name;
	u_int tx_power_offset;
	int return_code;
};
typedef struct qcsapi_regulatory_set_regulatory_channel_rpcdata qcsapi_regulatory_set_regulatory_channel_rpcdata;

struct qcsapi_regulatory_get_db_version_rpcdata {
	int index;
	int *p_version;
	int return_code;
};
typedef struct qcsapi_regulatory_get_db_version_rpcdata qcsapi_regulatory_get_db_version_rpcdata;

struct qcsapi_regulatory_apply_tx_power_cap_rpcdata {
	int capped;
	int return_code;
};
typedef struct qcsapi_regulatory_apply_tx_power_cap_rpcdata qcsapi_regulatory_apply_tx_power_cap_rpcdata;

struct qcsapi_wifi_get_list_DFS_channels_rpcdata {
	__rpc_string *region_by_name;
	int DFS_flag;
	u_int bw;
	__rpc_string *list_of_channels;
	int return_code;
};
typedef struct qcsapi_wifi_get_list_DFS_channels_rpcdata qcsapi_wifi_get_list_DFS_channels_rpcdata;

struct qcsapi_regulatory_get_list_DFS_channels_rpcdata {
	__rpc_string *region_by_name;
	int DFS_flag;
	u_int bw;
	__rpc_string *list_of_channels;
	int return_code;
};
typedef struct qcsapi_regulatory_get_list_DFS_channels_rpcdata qcsapi_regulatory_get_list_DFS_channels_rpcdata;

struct qcsapi_wifi_is_channel_DFS_rpcdata {
	__rpc_string *region_by_name;
	u_int the_channel;
	int *p_channel_is_DFS;
	int return_code;
};
typedef struct qcsapi_wifi_is_channel_DFS_rpcdata qcsapi_wifi_is_channel_DFS_rpcdata;

struct qcsapi_regulatory_is_channel_DFS_rpcdata {
	__rpc_string *region_by_name;
	u_int the_channel;
	int *p_channel_is_DFS;
	int return_code;
};
typedef struct qcsapi_regulatory_is_channel_DFS_rpcdata qcsapi_regulatory_is_channel_DFS_rpcdata;

struct qcsapi_wifi_get_dfs_cce_channels_rpcdata {
	__rpc_string *ifname;
	u_int *p_prev_channel;
	u_int *p_cur_channel;
	int return_code;
};
typedef struct qcsapi_wifi_get_dfs_cce_channels_rpcdata qcsapi_wifi_get_dfs_cce_channels_rpcdata;

struct qcsapi_wifi_get_DFS_alt_channel_rpcdata {
	__rpc_string *ifname;
	u_int *p_dfs_alt_chan;
	int return_code;
};
typedef struct qcsapi_wifi_get_DFS_alt_channel_rpcdata qcsapi_wifi_get_DFS_alt_channel_rpcdata;

struct qcsapi_wifi_set_DFS_alt_channel_rpcdata {
	__rpc_string *ifname;
	u_int dfs_alt_chan;
	int return_code;
};
typedef struct qcsapi_wifi_set_DFS_alt_channel_rpcdata qcsapi_wifi_set_DFS_alt_channel_rpcdata;

struct qcsapi_wifi_start_dfs_reentry_rpcdata {
	__rpc_string *ifname;
	int return_code;
};
typedef struct qcsapi_wifi_start_dfs_reentry_rpcdata qcsapi_wifi_start_dfs_reentry_rpcdata;

struct qcsapi_wifi_start_scan_ext_rpcdata {
	__rpc_string *ifname;
	int scan_flag;
	int return_code;
};
typedef struct qcsapi_wifi_start_scan_ext_rpcdata qcsapi_wifi_start_scan_ext_rpcdata;

struct qcsapi_wifi_get_csw_records_rpcdata {
	__rpc_string *ifname;
	int reset;
	__rpc_qcsapi_csw_record *record;
	int return_code;
};
typedef struct qcsapi_wifi_get_csw_records_rpcdata qcsapi_wifi_get_csw_records_rpcdata;

struct qcsapi_wifi_get_radar_status_rpcdata {
	__rpc_string *ifname;
	__rpc_qcsapi_radar_status *rdstatus;
	int return_code;
};
typedef struct qcsapi_wifi_get_radar_status_rpcdata qcsapi_wifi_get_radar_status_rpcdata;

struct qcsapi_wifi_get_cac_status_rpcdata {
	__rpc_string *ifname;
	int *cacstatus;
	int return_code;
};
typedef struct qcsapi_wifi_get_cac_status_rpcdata qcsapi_wifi_get_cac_status_rpcdata;

struct qcsapi_wifi_get_results_AP_scan_rpcdata {
	__rpc_string *ifname;
	u_int *p_count_APs;
	int return_code;
};
typedef struct qcsapi_wifi_get_results_AP_scan_rpcdata qcsapi_wifi_get_results_AP_scan_rpcdata;

struct qcsapi_wifi_get_count_APs_scanned_rpcdata {
	__rpc_string *ifname;
	u_int *p_count_APs;
	int return_code;
};
typedef struct qcsapi_wifi_get_count_APs_scanned_rpcdata qcsapi_wifi_get_count_APs_scanned_rpcdata;

struct qcsapi_wifi_get_properties_AP_rpcdata {
	__rpc_string *ifname;
	u_int index_AP;
	__rpc_qcsapi_ap_properties *p_ap_properties;
	int return_code;
};
typedef struct qcsapi_wifi_get_properties_AP_rpcdata qcsapi_wifi_get_properties_AP_rpcdata;

struct qcsapi_wifi_set_scan_chk_inv_rpcdata {
	__rpc_string *ifname;
	int scan_chk_inv;
	int return_code;
};
typedef struct qcsapi_wifi_set_scan_chk_inv_rpcdata qcsapi_wifi_set_scan_chk_inv_rpcdata;

struct qcsapi_wifi_get_scan_chk_inv_rpcdata {
	__rpc_string *ifname;
	int *p;
	int return_code;
};
typedef struct qcsapi_wifi_get_scan_chk_inv_rpcdata qcsapi_wifi_get_scan_chk_inv_rpcdata;

struct qcsapi_wifi_set_scan_buf_max_size_rpcdata {
	__rpc_string *ifname;
	u_int max_buf_size;
	int return_code;
};
typedef struct qcsapi_wifi_set_scan_buf_max_size_rpcdata qcsapi_wifi_set_scan_buf_max_size_rpcdata;

struct qcsapi_wifi_get_scan_buf_max_size_rpcdata {
	__rpc_string *ifname;
	u_int *max_buf_size;
	int return_code;
};
typedef struct qcsapi_wifi_get_scan_buf_max_size_rpcdata qcsapi_wifi_get_scan_buf_max_size_rpcdata;

struct qcsapi_wifi_set_scan_table_max_len_rpcdata {
	__rpc_string *ifname;
	u_int max_table_len;
	int return_code;
};
typedef struct qcsapi_wifi_set_scan_table_max_len_rpcdata qcsapi_wifi_set_scan_table_max_len_rpcdata;

struct qcsapi_wifi_get_scan_table_max_len_rpcdata {
	__rpc_string *ifname;
	u_int *max_table_len;
	int return_code;
};
typedef struct qcsapi_wifi_get_scan_table_max_len_rpcdata qcsapi_wifi_get_scan_table_max_len_rpcdata;

struct qcsapi_wifi_set_dwell_times_rpcdata {
	__rpc_string *ifname;
	u_int max_dwell_time_active_chan;
	u_int min_dwell_time_active_chan;
	u_int max_dwell_time_passive_chan;
	u_int min_dwell_time_passive_chan;
	int return_code;
};
typedef struct qcsapi_wifi_set_dwell_times_rpcdata qcsapi_wifi_set_dwell_times_rpcdata;

struct qcsapi_wifi_get_dwell_times_rpcdata {
	__rpc_string *ifname;
	u_int *p_max_dwell_time_active_chan;
	u_int *p_min_dwell_time_active_chan;
	u_int *p_max_dwell_time_passive_chan;
	u_int *p_min_dwell_time_passive_chan;
	int return_code;
};
typedef struct qcsapi_wifi_get_dwell_times_rpcdata qcsapi_wifi_get_dwell_times_rpcdata;

struct qcsapi_wifi_set_bgscan_dwell_times_rpcdata {
	__rpc_string *ifname;
	u_int dwell_time_active_chan;
	u_int dwell_time_passive_chan;
	int return_code;
};
typedef struct qcsapi_wifi_set_bgscan_dwell_times_rpcdata qcsapi_wifi_set_bgscan_dwell_times_rpcdata;

struct qcsapi_wifi_get_bgscan_dwell_times_rpcdata {
	__rpc_string *ifname;
	u_int *p_dwell_time_active_chan;
	u_int *p_dwell_time_passive_chan;
	int return_code;
};
typedef struct qcsapi_wifi_get_bgscan_dwell_times_rpcdata qcsapi_wifi_get_bgscan_dwell_times_rpcdata;

struct qcsapi_wifi_start_scan_rpcdata {
	__rpc_string *ifname;
	int return_code;
};
typedef struct qcsapi_wifi_start_scan_rpcdata qcsapi_wifi_start_scan_rpcdata;

struct qcsapi_wifi_cancel_scan_rpcdata {
	__rpc_string *ifname;
	int force;
	int return_code;
};
typedef struct qcsapi_wifi_cancel_scan_rpcdata qcsapi_wifi_cancel_scan_rpcdata;

struct qcsapi_wifi_get_scan_status_rpcdata {
	__rpc_string *ifname;
	int *scanstatus;
	int return_code;
};
typedef struct qcsapi_wifi_get_scan_status_rpcdata qcsapi_wifi_get_scan_status_rpcdata;

struct qcsapi_wifi_enable_bgscan_rpcdata {
	__rpc_string *ifname;
	int enable;
	int return_code;
};
typedef struct qcsapi_wifi_enable_bgscan_rpcdata qcsapi_wifi_enable_bgscan_rpcdata;

struct qcsapi_wifi_get_bgscan_status_rpcdata {
	__rpc_string *ifname;
	int *enable;
	int return_code;
};
typedef struct qcsapi_wifi_get_bgscan_status_rpcdata qcsapi_wifi_get_bgscan_status_rpcdata;

struct qcsapi_wifi_wait_scan_completes_rpcdata {
	__rpc_string *ifname;
	uint32_t timeout;
	int return_code;
};
typedef struct qcsapi_wifi_wait_scan_completes_rpcdata qcsapi_wifi_wait_scan_completes_rpcdata;

struct qcsapi_wifi_backoff_fail_max_rpcdata {
	__rpc_string *ifname;
	int fail_max;
	int return_code;
};
typedef struct qcsapi_wifi_backoff_fail_max_rpcdata qcsapi_wifi_backoff_fail_max_rpcdata;

struct qcsapi_wifi_backoff_timeout_rpcdata {
	__rpc_string *ifname;
	int timeout;
	int return_code;
};
typedef struct qcsapi_wifi_backoff_timeout_rpcdata qcsapi_wifi_backoff_timeout_rpcdata;

struct qcsapi_wifi_get_mcs_rate_rpcdata {
	__rpc_string *ifname;
	__rpc_string *current_mcs_rate;
	int return_code;
};
typedef struct qcsapi_wifi_get_mcs_rate_rpcdata qcsapi_wifi_get_mcs_rate_rpcdata;

struct qcsapi_wifi_set_mcs_rate_rpcdata {
	__rpc_string *ifname;
	__rpc_string *new_mcs_rate;
	int return_code;
};
typedef struct qcsapi_wifi_set_mcs_rate_rpcdata qcsapi_wifi_set_mcs_rate_rpcdata;

struct qcsapi_wifi_set_pairing_id_rpcdata {
	__rpc_string *ifname;
	__rpc_string *pairing_id;
	int return_code;
};
typedef struct qcsapi_wifi_set_pairing_id_rpcdata qcsapi_wifi_set_pairing_id_rpcdata;

struct qcsapi_wifi_get_pairing_id_rpcdata {
	__rpc_string *ifname;
	__rpc_string *pairing_id;
	int return_code;
};
typedef struct qcsapi_wifi_get_pairing_id_rpcdata qcsapi_wifi_get_pairing_id_rpcdata;

struct qcsapi_wifi_set_pairing_enable_rpcdata {
	__rpc_string *ifname;
	__rpc_string *enable;
	int return_code;
};
typedef struct qcsapi_wifi_set_pairing_enable_rpcdata qcsapi_wifi_set_pairing_enable_rpcdata;

struct qcsapi_wifi_get_pairing_enable_rpcdata {
	__rpc_string *ifname;
	__rpc_string *enable;
	int return_code;
};
typedef struct qcsapi_wifi_get_pairing_enable_rpcdata qcsapi_wifi_get_pairing_enable_rpcdata;

struct qcsapi_non_wps_set_pp_enable_rpcdata {
	__rpc_string *ifname;
	uint32_t ctrl_state;
	int return_code;
};
typedef struct qcsapi_non_wps_set_pp_enable_rpcdata qcsapi_non_wps_set_pp_enable_rpcdata;

struct qcsapi_non_wps_get_pp_enable_rpcdata {
	__rpc_string *ifname;
	uint32_t *ctrl_state;
	int return_code;
};
typedef struct qcsapi_non_wps_get_pp_enable_rpcdata qcsapi_non_wps_get_pp_enable_rpcdata;

struct qcsapi_wifi_set_vendor_fix_rpcdata {
	__rpc_string *ifname;
	int fix_param;
	int value;
	int return_code;
};
typedef struct qcsapi_wifi_set_vendor_fix_rpcdata qcsapi_wifi_set_vendor_fix_rpcdata;

struct qcsapi_errno_get_message_rpcdata {
	int qcsapi_retval;
	u_int msglen;
	__rpc_string *error_msg;
	int return_code;
};
typedef struct qcsapi_errno_get_message_rpcdata qcsapi_errno_get_message_rpcdata;

struct qcsapi_get_interface_stats_rpcdata {
	__rpc_string *ifname;
	__rpc_qcsapi_interface_stats *stats;
	int return_code;
};
typedef struct qcsapi_get_interface_stats_rpcdata qcsapi_get_interface_stats_rpcdata;

struct qcsapi_get_phy_stats_rpcdata {
	__rpc_string *ifname;
	__rpc_qcsapi_phy_stats *stats;
	int return_code;
};
typedef struct qcsapi_get_phy_stats_rpcdata qcsapi_get_phy_stats_rpcdata;

struct qcsapi_reset_all_counters_rpcdata {
	__rpc_string *ifname;
	uint32_t node_index;
	int local_remote_flag;
	int return_code;
};
typedef struct qcsapi_reset_all_counters_rpcdata qcsapi_reset_all_counters_rpcdata;

struct qcsapi_get_uboot_info_rpcdata {
	__rpc_string *uboot_version;
	__rpc_early_flash_config *ef_config;
	int return_code;
};
typedef struct qcsapi_get_uboot_info_rpcdata qcsapi_get_uboot_info_rpcdata;

struct qcsapi_firmware_get_version_rpcdata {
	u_int version_size;
	__rpc_string *firmware_version;
	int return_code;
};
typedef struct qcsapi_firmware_get_version_rpcdata qcsapi_firmware_get_version_rpcdata;

struct qcsapi_flash_image_update_rpcdata {
	__rpc_string *image_file;
	int partition_to_upgrade;
	int return_code;
};
typedef struct qcsapi_flash_image_update_rpcdata qcsapi_flash_image_update_rpcdata;

struct qcsapi_send_file_rpcdata {
	__rpc_string *image_file_path;
	int image_flags;
	int return_code;
};
typedef struct qcsapi_send_file_rpcdata qcsapi_send_file_rpcdata;

struct qcsapi_pm_set_mode_rpcdata {
	int mode;
	int return_code;
};
typedef struct qcsapi_pm_set_mode_rpcdata qcsapi_pm_set_mode_rpcdata;

struct qcsapi_pm_get_mode_rpcdata {
	int *mode;
	int return_code;
};
typedef struct qcsapi_pm_get_mode_rpcdata qcsapi_pm_get_mode_rpcdata;

struct qcsapi_get_qpm_level_rpcdata {
	int *qpm_level;
	int return_code;
};
typedef struct qcsapi_get_qpm_level_rpcdata qcsapi_get_qpm_level_rpcdata;

struct qcsapi_set_host_state_rpcdata {
	__rpc_string *ifname;
	uint32_t host_state;
	int return_code;
};
typedef struct qcsapi_set_host_state_rpcdata qcsapi_set_host_state_rpcdata;

struct qcsapi_qtm_get_state_rpcdata {
	__rpc_string *ifname;
	u_int param;
	u_int *value;
	int return_code;
};
typedef struct qcsapi_qtm_get_state_rpcdata qcsapi_qtm_get_state_rpcdata;

struct qcsapi_qtm_get_state_all_rpcdata {
	__rpc_string *ifname;
	u_int max;
	__rpc_qcsapi_data_128bytes *value;
	int return_code;
};
typedef struct qcsapi_qtm_get_state_all_rpcdata qcsapi_qtm_get_state_all_rpcdata;

struct qcsapi_qtm_set_state_rpcdata {
	__rpc_string *ifname;
	u_int param;
	u_int value;
	int return_code;
};
typedef struct qcsapi_qtm_set_state_rpcdata qcsapi_qtm_set_state_rpcdata;

struct qcsapi_qtm_get_config_rpcdata {
	__rpc_string *ifname;
	u_int param;
	u_int *value;
	int return_code;
};
typedef struct qcsapi_qtm_get_config_rpcdata qcsapi_qtm_get_config_rpcdata;

struct qcsapi_qtm_get_config_all_rpcdata {
	__rpc_string *ifname;
	u_int max;
	__rpc_qcsapi_data_1Kbytes *value;
	int return_code;
};
typedef struct qcsapi_qtm_get_config_all_rpcdata qcsapi_qtm_get_config_all_rpcdata;

struct qcsapi_qtm_set_config_rpcdata {
	__rpc_string *ifname;
	u_int param;
	u_int value;
	int return_code;
};
typedef struct qcsapi_qtm_set_config_rpcdata qcsapi_qtm_set_config_rpcdata;

struct qcsapi_qtm_add_rule_rpcdata {
	__rpc_string *ifname;
	__rpc_qcsapi_data_128bytes *entry;
	int return_code;
};
typedef struct qcsapi_qtm_add_rule_rpcdata qcsapi_qtm_add_rule_rpcdata;

struct qcsapi_qtm_del_rule_rpcdata {
	__rpc_string *ifname;
	__rpc_qcsapi_data_128bytes *entry;
	int return_code;
};
typedef struct qcsapi_qtm_del_rule_rpcdata qcsapi_qtm_del_rule_rpcdata;

struct qcsapi_qtm_del_rule_index_rpcdata {
	__rpc_string *ifname;
	u_int index;
	int return_code;
};
typedef struct qcsapi_qtm_del_rule_index_rpcdata qcsapi_qtm_del_rule_index_rpcdata;

struct qcsapi_qtm_get_rule_rpcdata {
	__rpc_string *ifname;
	u_int max_entries;
	__rpc_qcsapi_data_3Kbytes *entries;
	int return_code;
};
typedef struct qcsapi_qtm_get_rule_rpcdata qcsapi_qtm_get_rule_rpcdata;

struct qcsapi_qtm_get_strm_rpcdata {
	__rpc_string *ifname;
	u_int max_entries;
	int show_all;
	__rpc_qcsapi_data_4Kbytes *strms;
	int return_code;
};
typedef struct qcsapi_qtm_get_strm_rpcdata qcsapi_qtm_get_strm_rpcdata;

struct qcsapi_qtm_get_stats_rpcdata {
	__rpc_string *ifname;
	__rpc_qcsapi_data_512bytes *stats;
	int return_code;
};
typedef struct qcsapi_qtm_get_stats_rpcdata qcsapi_qtm_get_stats_rpcdata;

struct qcsapi_qtm_get_inactive_flags_rpcdata {
	__rpc_string *ifname;
	u_long *flags;
	int return_code;
};
typedef struct qcsapi_qtm_get_inactive_flags_rpcdata qcsapi_qtm_get_inactive_flags_rpcdata;

struct qcsapi_wifi_run_script_rpcdata {
	__rpc_string *scriptname;
	__rpc_string *param;
	int return_code;
};
typedef struct qcsapi_wifi_run_script_rpcdata qcsapi_wifi_run_script_rpcdata;

struct qcsapi_wifi_test_traffic_rpcdata {
	__rpc_string *ifname;
	uint32_t period;
	int return_code;
};
typedef struct qcsapi_wifi_test_traffic_rpcdata qcsapi_wifi_test_traffic_rpcdata;

struct qcsapi_wifi_add_ipff_rpcdata {
	u_int ipaddr;
	int return_code;
};
typedef struct qcsapi_wifi_add_ipff_rpcdata qcsapi_wifi_add_ipff_rpcdata;

struct qcsapi_wifi_del_ipff_rpcdata {
	u_int ipaddr;
	int return_code;
};
typedef struct qcsapi_wifi_del_ipff_rpcdata qcsapi_wifi_del_ipff_rpcdata;

struct qcsapi_wifi_get_ipff_rpcdata {
	int buflen;
	__rpc_string *buf;
	int return_code;
};
typedef struct qcsapi_wifi_get_ipff_rpcdata qcsapi_wifi_get_ipff_rpcdata;

struct qcsapi_wifi_get_rts_threshold_rpcdata {
	__rpc_string *ifname;
	u_int *rts_threshold;
	int return_code;
};
typedef struct qcsapi_wifi_get_rts_threshold_rpcdata qcsapi_wifi_get_rts_threshold_rpcdata;

struct qcsapi_wifi_set_rts_threshold_rpcdata {
	__rpc_string *ifname;
	u_int rts_threshold;
	int return_code;
};
typedef struct qcsapi_wifi_set_rts_threshold_rpcdata qcsapi_wifi_set_rts_threshold_rpcdata;

struct qcsapi_wifi_set_nss_cap_rpcdata {
	__rpc_string *ifname;
	int modulation;
	u_int nss;
	int return_code;
};
typedef struct qcsapi_wifi_set_nss_cap_rpcdata qcsapi_wifi_set_nss_cap_rpcdata;

struct qcsapi_wifi_get_nss_cap_rpcdata {
	__rpc_string *ifname;
	int modulation;
	u_int *nss;
	int return_code;
};
typedef struct qcsapi_wifi_get_nss_cap_rpcdata qcsapi_wifi_get_nss_cap_rpcdata;

struct qcsapi_wifi_get_tx_amsdu_rpcdata {
	__rpc_string *ifname;
	int *enable;
	int return_code;
};
typedef struct qcsapi_wifi_get_tx_amsdu_rpcdata qcsapi_wifi_get_tx_amsdu_rpcdata;

struct qcsapi_wifi_set_tx_amsdu_rpcdata {
	__rpc_string *ifname;
	int enable;
	int return_code;
};
typedef struct qcsapi_wifi_set_tx_amsdu_rpcdata qcsapi_wifi_set_tx_amsdu_rpcdata;

struct qcsapi_wifi_get_disassoc_reason_rpcdata {
	__rpc_string *ifname;
	u_int *reason;
	int return_code;
};
typedef struct qcsapi_wifi_get_disassoc_reason_rpcdata qcsapi_wifi_get_disassoc_reason_rpcdata;

struct qcsapi_wifi_block_bss_rpcdata {
	__rpc_string *ifname;
	u_int flag;
	int return_code;
};
typedef struct qcsapi_wifi_block_bss_rpcdata qcsapi_wifi_block_bss_rpcdata;

struct qcsapi_wifi_verify_repeater_mode_rpcdata {
	int return_code;
};
typedef struct qcsapi_wifi_verify_repeater_mode_rpcdata qcsapi_wifi_verify_repeater_mode_rpcdata;

struct qcsapi_wifi_set_ap_interface_name_rpcdata {
	__rpc_string *ifname;
	int return_code;
};
typedef struct qcsapi_wifi_set_ap_interface_name_rpcdata qcsapi_wifi_set_ap_interface_name_rpcdata;

struct qcsapi_wifi_get_ap_interface_name_rpcdata {
	__rpc_string *ifname;
	int return_code;
};
typedef struct qcsapi_wifi_get_ap_interface_name_rpcdata qcsapi_wifi_get_ap_interface_name_rpcdata;

struct qcsapi_get_temperature_info_rpcdata {
	int *temp_exter;
	int *temp_inter;
	int *temp_bbic;
	int return_code;
};
typedef struct qcsapi_get_temperature_info_rpcdata qcsapi_get_temperature_info_rpcdata;

struct qcsapi_calcmd_set_test_mode_rpcdata {
	u_int channel;
	u_int antenna;
	u_int mcs;
	u_int bw;
	u_int pkt_size;
	u_int eleven_n;
	u_int bf;
	int return_code;
};
typedef struct qcsapi_calcmd_set_test_mode_rpcdata qcsapi_calcmd_set_test_mode_rpcdata;

struct qcsapi_calcmd_show_test_packet_rpcdata {
	u_int *tx_packet_num;
	u_int *rx_packet_num;
	u_int *crc_packet_num;
	int return_code;
};
typedef struct qcsapi_calcmd_show_test_packet_rpcdata qcsapi_calcmd_show_test_packet_rpcdata;

struct qcsapi_calcmd_send_test_packet_rpcdata {
	u_int to_transmit_packet_num;
	int return_code;
};
typedef struct qcsapi_calcmd_send_test_packet_rpcdata qcsapi_calcmd_send_test_packet_rpcdata;

struct qcsapi_calcmd_stop_test_packet_rpcdata {
	int return_code;
};
typedef struct qcsapi_calcmd_stop_test_packet_rpcdata qcsapi_calcmd_stop_test_packet_rpcdata;

struct qcsapi_calcmd_send_dc_cw_signal_rpcdata {
	u_int channel;
	int return_code;
};
typedef struct qcsapi_calcmd_send_dc_cw_signal_rpcdata qcsapi_calcmd_send_dc_cw_signal_rpcdata;

struct qcsapi_calcmd_stop_dc_cw_signal_rpcdata {
	int return_code;
};
typedef struct qcsapi_calcmd_stop_dc_cw_signal_rpcdata qcsapi_calcmd_stop_dc_cw_signal_rpcdata;

struct qcsapi_calcmd_get_test_mode_antenna_sel_rpcdata {
	u_int *antenna_bit_mask;
	int return_code;
};
typedef struct qcsapi_calcmd_get_test_mode_antenna_sel_rpcdata qcsapi_calcmd_get_test_mode_antenna_sel_rpcdata;

struct qcsapi_calcmd_get_test_mode_mcs_rpcdata {
	u_int *test_mode_mcs;
	int return_code;
};
typedef struct qcsapi_calcmd_get_test_mode_mcs_rpcdata qcsapi_calcmd_get_test_mode_mcs_rpcdata;

struct qcsapi_calcmd_get_test_mode_bw_rpcdata {
	u_int *test_mode_bw;
	int return_code;
};
typedef struct qcsapi_calcmd_get_test_mode_bw_rpcdata qcsapi_calcmd_get_test_mode_bw_rpcdata;

struct qcsapi_calcmd_get_tx_power_rpcdata {
	__rpc_qcsapi_calcmd_tx_power_rsp *tx_power;
	int return_code;
};
typedef struct qcsapi_calcmd_get_tx_power_rpcdata qcsapi_calcmd_get_tx_power_rpcdata;

struct qcsapi_calcmd_set_tx_power_rpcdata {
	u_int tx_power;
	int return_code;
};
typedef struct qcsapi_calcmd_set_tx_power_rpcdata qcsapi_calcmd_set_tx_power_rpcdata;

struct qcsapi_calcmd_get_test_mode_rssi_rpcdata {
	__rpc_qcsapi_calcmd_rssi_rsp *test_mode_rssi;
	int return_code;
};
typedef struct qcsapi_calcmd_get_test_mode_rssi_rpcdata qcsapi_calcmd_get_test_mode_rssi_rpcdata;

struct qcsapi_calcmd_set_mac_filter_rpcdata {
	int q_num;
	int sec_enable;
	__rpc_qcsapi_mac_addr_p mac_addr;
	int return_code;
};
typedef struct qcsapi_calcmd_set_mac_filter_rpcdata qcsapi_calcmd_set_mac_filter_rpcdata;

struct qcsapi_calcmd_get_antenna_count_rpcdata {
	u_int *antenna_count;
	int return_code;
};
typedef struct qcsapi_calcmd_get_antenna_count_rpcdata qcsapi_calcmd_get_antenna_count_rpcdata;

struct qcsapi_calcmd_clear_counter_rpcdata {
	int return_code;
};
typedef struct qcsapi_calcmd_clear_counter_rpcdata qcsapi_calcmd_clear_counter_rpcdata;

struct qcsapi_calcmd_get_info_rpcdata {
	__rpc_string *output_info;
	int return_code;
};
typedef struct qcsapi_calcmd_get_info_rpcdata qcsapi_calcmd_get_info_rpcdata;

struct qcsapi_wowlan_set_match_type_rpcdata {
	__rpc_string *ifname;
	uint32_t wowlan_match;
	int return_code;
};
typedef struct qcsapi_wowlan_set_match_type_rpcdata qcsapi_wowlan_set_match_type_rpcdata;

struct qcsapi_wowlan_set_L2_type_rpcdata {
	__rpc_string *ifname;
	uint32_t ether_type;
	int return_code;
};
typedef struct qcsapi_wowlan_set_L2_type_rpcdata qcsapi_wowlan_set_L2_type_rpcdata;

struct qcsapi_wowlan_set_udp_port_rpcdata {
	__rpc_string *ifname;
	uint32_t udp_port;
	int return_code;
};
typedef struct qcsapi_wowlan_set_udp_port_rpcdata qcsapi_wowlan_set_udp_port_rpcdata;

struct qcsapi_wowlan_set_magic_pattern_rpcdata {
	__rpc_string *ifname;
	uint32_t len;
	__rpc_qcsapi_data_256bytes *pattern;
	int return_code;
};
typedef struct qcsapi_wowlan_set_magic_pattern_rpcdata qcsapi_wowlan_set_magic_pattern_rpcdata;

struct qcsapi_wifi_wowlan_get_host_state_rpcdata {
	__rpc_string *ifname;
	uint16_t *p_value;
	uint32_t *len;
	int return_code;
};
typedef struct qcsapi_wifi_wowlan_get_host_state_rpcdata qcsapi_wifi_wowlan_get_host_state_rpcdata;

struct qcsapi_wifi_wowlan_get_match_type_rpcdata {
	__rpc_string *ifname;
	uint16_t *p_value;
	uint32_t *len;
	int return_code;
};
typedef struct qcsapi_wifi_wowlan_get_match_type_rpcdata qcsapi_wifi_wowlan_get_match_type_rpcdata;

struct qcsapi_wifi_wowlan_get_l2_type_rpcdata {
	__rpc_string *ifname;
	uint16_t *p_value;
	uint32_t *len;
	int return_code;
};
typedef struct qcsapi_wifi_wowlan_get_l2_type_rpcdata qcsapi_wifi_wowlan_get_l2_type_rpcdata;

struct qcsapi_wifi_wowlan_get_udp_port_rpcdata {
	__rpc_string *ifname;
	uint16_t *p_value;
	uint32_t *len;
	int return_code;
};
typedef struct qcsapi_wifi_wowlan_get_udp_port_rpcdata qcsapi_wifi_wowlan_get_udp_port_rpcdata;

struct qcsapi_wifi_wowlan_get_magic_pattern_rpcdata {
	__rpc_string *ifname;
	__rpc_qcsapi_data_256bytes *p_value;
	uint32_t *len;
	int return_code;
};
typedef struct qcsapi_wifi_wowlan_get_magic_pattern_rpcdata qcsapi_wifi_wowlan_get_magic_pattern_rpcdata;

struct qcsapi_wifi_set_enable_mu_rpcdata {
	__rpc_string *ifname;
	u_int mu_enable;
	int return_code;
};
typedef struct qcsapi_wifi_set_enable_mu_rpcdata qcsapi_wifi_set_enable_mu_rpcdata;

struct qcsapi_wifi_get_enable_mu_rpcdata {
	__rpc_string *ifname;
	u_int *mu_enable;
	int return_code;
};
typedef struct qcsapi_wifi_get_enable_mu_rpcdata qcsapi_wifi_get_enable_mu_rpcdata;

struct qcsapi_wifi_set_mu_use_precode_rpcdata {
	__rpc_string *ifname;
	u_int grp;
	u_int prec_enable;
	int return_code;
};
typedef struct qcsapi_wifi_set_mu_use_precode_rpcdata qcsapi_wifi_set_mu_use_precode_rpcdata;

struct qcsapi_wifi_get_mu_use_precode_rpcdata {
	__rpc_string *ifname;
	u_int grp;
	u_int *prec_enable;
	int return_code;
};
typedef struct qcsapi_wifi_get_mu_use_precode_rpcdata qcsapi_wifi_get_mu_use_precode_rpcdata;

struct qcsapi_wifi_set_mu_use_eq_rpcdata {
	__rpc_string *ifname;
	u_int eq_enable;
	int return_code;
};
typedef struct qcsapi_wifi_set_mu_use_eq_rpcdata qcsapi_wifi_set_mu_use_eq_rpcdata;

struct qcsapi_wifi_get_mu_use_eq_rpcdata {
	__rpc_string *ifname;
	u_int *meq_enable;
	int return_code;
};
typedef struct qcsapi_wifi_get_mu_use_eq_rpcdata qcsapi_wifi_get_mu_use_eq_rpcdata;

struct qcsapi_wifi_get_mu_groups_rpcdata {
	__rpc_string *ifname;
	u_int size;
	__rpc_string *buf;
	int return_code;
};
typedef struct qcsapi_wifi_get_mu_groups_rpcdata qcsapi_wifi_get_mu_groups_rpcdata;

struct qcsapi_wifi_enable_tdls_rpcdata {
	__rpc_string *ifname;
	uint32_t enable_tdls;
	int return_code;
};
typedef struct qcsapi_wifi_enable_tdls_rpcdata qcsapi_wifi_enable_tdls_rpcdata;

struct qcsapi_wifi_enable_tdls_over_qhop_rpcdata {
	__rpc_string *ifname;
	uint32_t tdls_over_qhop_en;
	int return_code;
};
typedef struct qcsapi_wifi_enable_tdls_over_qhop_rpcdata qcsapi_wifi_enable_tdls_over_qhop_rpcdata;

struct qcsapi_wifi_get_tdls_status_rpcdata {
	__rpc_string *ifname;
	uint32_t *p_tdls_status;
	int return_code;
};
typedef struct qcsapi_wifi_get_tdls_status_rpcdata qcsapi_wifi_get_tdls_status_rpcdata;

struct qcsapi_wifi_set_tdls_params_rpcdata {
	__rpc_string *ifname;
	int type;
	int param_value;
	int return_code;
};
typedef struct qcsapi_wifi_set_tdls_params_rpcdata qcsapi_wifi_set_tdls_params_rpcdata;

struct qcsapi_wifi_get_tdls_params_rpcdata {
	__rpc_string *ifname;
	int type;
	int *p_value;
	int return_code;
};
typedef struct qcsapi_wifi_get_tdls_params_rpcdata qcsapi_wifi_get_tdls_params_rpcdata;

struct qcsapi_wifi_tdls_operate_rpcdata {
	__rpc_string *ifname;
	int operate;
	__rpc_string *mac_addr_str;
	int cs_interval;
	int return_code;
};
typedef struct qcsapi_wifi_tdls_operate_rpcdata qcsapi_wifi_tdls_operate_rpcdata;

/* defines for local-only functions */
#define QCSAPI_GPIO_MONITOR_RESET_DEVICE_REMOTE 2771

#define QCSAPI_PROG 0x20000002
#define QCSAPI_VERS 1

#if defined(__STDC__) || defined(__cplusplus)
#define QCSAPI_BOOTCFG_GET_PARAMETER_REMOTE 1
extern  enum clnt_stat qcsapi_bootcfg_get_parameter_remote_1(qcsapi_bootcfg_get_parameter_rpcdata *, qcsapi_bootcfg_get_parameter_rpcdata *, CLIENT *);
extern  bool_t qcsapi_bootcfg_get_parameter_remote_1_svc(qcsapi_bootcfg_get_parameter_rpcdata *, qcsapi_bootcfg_get_parameter_rpcdata *, struct svc_req *);
#define QCSAPI_BOOTCFG_UPDATE_PARAMETER_REMOTE 11
extern  enum clnt_stat qcsapi_bootcfg_update_parameter_remote_1(qcsapi_bootcfg_update_parameter_rpcdata *, qcsapi_bootcfg_update_parameter_rpcdata *, CLIENT *);
extern  bool_t qcsapi_bootcfg_update_parameter_remote_1_svc(qcsapi_bootcfg_update_parameter_rpcdata *, qcsapi_bootcfg_update_parameter_rpcdata *, struct svc_req *);
#define QCSAPI_BOOTCFG_COMMIT_REMOTE 21
extern  enum clnt_stat qcsapi_bootcfg_commit_remote_1(qcsapi_bootcfg_commit_rpcdata *, qcsapi_bootcfg_commit_rpcdata *, CLIENT *);
extern  bool_t qcsapi_bootcfg_commit_remote_1_svc(qcsapi_bootcfg_commit_rpcdata *, qcsapi_bootcfg_commit_rpcdata *, struct svc_req *);
#define QCSAPI_TELNET_ENABLE_REMOTE 31
extern  enum clnt_stat qcsapi_telnet_enable_remote_1(qcsapi_telnet_enable_rpcdata *, qcsapi_telnet_enable_rpcdata *, CLIENT *);
extern  bool_t qcsapi_telnet_enable_remote_1_svc(qcsapi_telnet_enable_rpcdata *, qcsapi_telnet_enable_rpcdata *, struct svc_req *);
#define QCSAPI_GET_SERVICE_NAME_ENUM_REMOTE 5651
extern  enum clnt_stat qcsapi_get_service_name_enum_remote_1(qcsapi_get_service_name_enum_rpcdata *, qcsapi_get_service_name_enum_rpcdata *, CLIENT *);
extern  bool_t qcsapi_get_service_name_enum_remote_1_svc(qcsapi_get_service_name_enum_rpcdata *, qcsapi_get_service_name_enum_rpcdata *, struct svc_req *);
#define QCSAPI_GET_SERVICE_ACTION_ENUM_REMOTE 5661
extern  enum clnt_stat qcsapi_get_service_action_enum_remote_1(qcsapi_get_service_action_enum_rpcdata *, qcsapi_get_service_action_enum_rpcdata *, CLIENT *);
extern  bool_t qcsapi_get_service_action_enum_remote_1_svc(qcsapi_get_service_action_enum_rpcdata *, qcsapi_get_service_action_enum_rpcdata *, struct svc_req *);
#define QCSAPI_SERVICE_CONTROL_REMOTE 5671
extern  enum clnt_stat qcsapi_service_control_remote_1(qcsapi_service_control_rpcdata *, qcsapi_service_control_rpcdata *, CLIENT *);
extern  bool_t qcsapi_service_control_remote_1_svc(qcsapi_service_control_rpcdata *, qcsapi_service_control_rpcdata *, struct svc_req *);
#define QCSAPI_WFA_CERT_MODE_ENABLE_REMOTE 5931
extern  enum clnt_stat qcsapi_wfa_cert_mode_enable_remote_1(qcsapi_wfa_cert_mode_enable_rpcdata *, qcsapi_wfa_cert_mode_enable_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wfa_cert_mode_enable_remote_1_svc(qcsapi_wfa_cert_mode_enable_rpcdata *, qcsapi_wfa_cert_mode_enable_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_SCS_CCE_CHANNELS_REMOTE 41
extern  enum clnt_stat qcsapi_wifi_get_scs_cce_channels_remote_1(qcsapi_wifi_get_scs_cce_channels_rpcdata *, qcsapi_wifi_get_scs_cce_channels_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_scs_cce_channels_remote_1_svc(qcsapi_wifi_get_scs_cce_channels_rpcdata *, qcsapi_wifi_get_scs_cce_channels_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SCS_ENABLE_REMOTE 51
extern  enum clnt_stat qcsapi_wifi_scs_enable_remote_1(qcsapi_wifi_scs_enable_rpcdata *, qcsapi_wifi_scs_enable_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_scs_enable_remote_1_svc(qcsapi_wifi_scs_enable_rpcdata *, qcsapi_wifi_scs_enable_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SCS_SWITCH_CHANNEL_REMOTE 61
extern  enum clnt_stat qcsapi_wifi_scs_switch_channel_remote_1(qcsapi_wifi_scs_switch_channel_rpcdata *, qcsapi_wifi_scs_switch_channel_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_scs_switch_channel_remote_1_svc(qcsapi_wifi_scs_switch_channel_rpcdata *, qcsapi_wifi_scs_switch_channel_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_SCS_VERBOSE_REMOTE 71
extern  enum clnt_stat qcsapi_wifi_set_scs_verbose_remote_1(qcsapi_wifi_set_scs_verbose_rpcdata *, qcsapi_wifi_set_scs_verbose_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_scs_verbose_remote_1_svc(qcsapi_wifi_set_scs_verbose_rpcdata *, qcsapi_wifi_set_scs_verbose_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_SCS_STATUS_REMOTE 81
extern  enum clnt_stat qcsapi_wifi_get_scs_status_remote_1(qcsapi_wifi_get_scs_status_rpcdata *, qcsapi_wifi_get_scs_status_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_scs_status_remote_1_svc(qcsapi_wifi_get_scs_status_rpcdata *, qcsapi_wifi_get_scs_status_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_SCS_SMPL_ENABLE_REMOTE 91
extern  enum clnt_stat qcsapi_wifi_set_scs_smpl_enable_remote_1(qcsapi_wifi_set_scs_smpl_enable_rpcdata *, qcsapi_wifi_set_scs_smpl_enable_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_scs_smpl_enable_remote_1_svc(qcsapi_wifi_set_scs_smpl_enable_rpcdata *, qcsapi_wifi_set_scs_smpl_enable_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_SCS_SMPL_DWELL_TIME_REMOTE 101
extern  enum clnt_stat qcsapi_wifi_set_scs_smpl_dwell_time_remote_1(qcsapi_wifi_set_scs_smpl_dwell_time_rpcdata *, qcsapi_wifi_set_scs_smpl_dwell_time_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_scs_smpl_dwell_time_remote_1_svc(qcsapi_wifi_set_scs_smpl_dwell_time_rpcdata *, qcsapi_wifi_set_scs_smpl_dwell_time_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_SCS_SAMPLE_INTV_REMOTE 111
extern  enum clnt_stat qcsapi_wifi_set_scs_sample_intv_remote_1(qcsapi_wifi_set_scs_sample_intv_rpcdata *, qcsapi_wifi_set_scs_sample_intv_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_scs_sample_intv_remote_1_svc(qcsapi_wifi_set_scs_sample_intv_rpcdata *, qcsapi_wifi_set_scs_sample_intv_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_SCS_INTF_DETECT_INTV_REMOTE 121
extern  enum clnt_stat qcsapi_wifi_set_scs_intf_detect_intv_remote_1(qcsapi_wifi_set_scs_intf_detect_intv_rpcdata *, qcsapi_wifi_set_scs_intf_detect_intv_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_scs_intf_detect_intv_remote_1_svc(qcsapi_wifi_set_scs_intf_detect_intv_rpcdata *, qcsapi_wifi_set_scs_intf_detect_intv_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_SCS_THRSHLD_REMOTE 131
extern  enum clnt_stat qcsapi_wifi_set_scs_thrshld_remote_1(qcsapi_wifi_set_scs_thrshld_rpcdata *, qcsapi_wifi_set_scs_thrshld_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_scs_thrshld_remote_1_svc(qcsapi_wifi_set_scs_thrshld_rpcdata *, qcsapi_wifi_set_scs_thrshld_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_SCS_REPORT_ONLY_REMOTE 141
extern  enum clnt_stat qcsapi_wifi_set_scs_report_only_remote_1(qcsapi_wifi_set_scs_report_only_rpcdata *, qcsapi_wifi_set_scs_report_only_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_scs_report_only_remote_1_svc(qcsapi_wifi_set_scs_report_only_rpcdata *, qcsapi_wifi_set_scs_report_only_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_SCS_STAT_REPORT_REMOTE 151
extern  enum clnt_stat qcsapi_wifi_get_scs_stat_report_remote_1(qcsapi_wifi_get_scs_stat_report_rpcdata *, qcsapi_wifi_get_scs_stat_report_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_scs_stat_report_remote_1_svc(qcsapi_wifi_get_scs_stat_report_rpcdata *, qcsapi_wifi_get_scs_stat_report_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_SCS_SCORE_REPORT_REMOTE 152
extern  enum clnt_stat qcsapi_wifi_get_scs_score_report_remote_1(qcsapi_wifi_get_scs_score_report_rpcdata *, qcsapi_wifi_get_scs_score_report_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_scs_score_report_remote_1_svc(qcsapi_wifi_get_scs_score_report_rpcdata *, qcsapi_wifi_get_scs_score_report_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_SCS_CURRCHAN_REPORT_REMOTE 161
extern  enum clnt_stat qcsapi_wifi_get_scs_currchan_report_remote_1(qcsapi_wifi_get_scs_currchan_report_rpcdata *, qcsapi_wifi_get_scs_currchan_report_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_scs_currchan_report_remote_1_svc(qcsapi_wifi_get_scs_currchan_report_rpcdata *, qcsapi_wifi_get_scs_currchan_report_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_SCS_STATS_REMOTE 171
extern  enum clnt_stat qcsapi_wifi_set_scs_stats_remote_1(qcsapi_wifi_set_scs_stats_rpcdata *, qcsapi_wifi_set_scs_stats_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_scs_stats_remote_1_svc(qcsapi_wifi_set_scs_stats_rpcdata *, qcsapi_wifi_set_scs_stats_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_AUTOCHAN_REPORT_REMOTE 181
extern  enum clnt_stat qcsapi_wifi_get_autochan_report_remote_1(qcsapi_wifi_get_autochan_report_rpcdata *, qcsapi_wifi_get_autochan_report_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_autochan_report_remote_1_svc(qcsapi_wifi_get_autochan_report_rpcdata *, qcsapi_wifi_get_autochan_report_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_SCS_CCA_INTF_SMTH_FCTR_REMOTE 191
extern  enum clnt_stat qcsapi_wifi_set_scs_cca_intf_smth_fctr_remote_1(qcsapi_wifi_set_scs_cca_intf_smth_fctr_rpcdata *, qcsapi_wifi_set_scs_cca_intf_smth_fctr_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_scs_cca_intf_smth_fctr_remote_1_svc(qcsapi_wifi_set_scs_cca_intf_smth_fctr_rpcdata *, qcsapi_wifi_set_scs_cca_intf_smth_fctr_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_SCS_CHAN_MTRC_MRGN_REMOTE 201
extern  enum clnt_stat qcsapi_wifi_set_scs_chan_mtrc_mrgn_remote_1(qcsapi_wifi_set_scs_chan_mtrc_mrgn_rpcdata *, qcsapi_wifi_set_scs_chan_mtrc_mrgn_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_scs_chan_mtrc_mrgn_remote_1_svc(qcsapi_wifi_set_scs_chan_mtrc_mrgn_rpcdata *, qcsapi_wifi_set_scs_chan_mtrc_mrgn_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_SCS_CCA_INTF_REMOTE 211
extern  enum clnt_stat qcsapi_wifi_get_scs_cca_intf_remote_1(qcsapi_wifi_get_scs_cca_intf_rpcdata *, qcsapi_wifi_get_scs_cca_intf_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_scs_cca_intf_remote_1_svc(qcsapi_wifi_get_scs_cca_intf_rpcdata *, qcsapi_wifi_get_scs_cca_intf_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_SCS_PARAM_REPORT_REMOTE 221
extern  enum clnt_stat qcsapi_wifi_get_scs_param_report_remote_1(qcsapi_wifi_get_scs_param_report_rpcdata *, qcsapi_wifi_get_scs_param_report_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_scs_param_report_remote_1_svc(qcsapi_wifi_get_scs_param_report_rpcdata *, qcsapi_wifi_get_scs_param_report_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_SCS_DFS_REENTRY_REQUEST_REMOTE 231
extern  enum clnt_stat qcsapi_wifi_get_scs_dfs_reentry_request_remote_1(qcsapi_wifi_get_scs_dfs_reentry_request_rpcdata *, qcsapi_wifi_get_scs_dfs_reentry_request_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_scs_dfs_reentry_request_remote_1_svc(qcsapi_wifi_get_scs_dfs_reentry_request_rpcdata *, qcsapi_wifi_get_scs_dfs_reentry_request_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_START_OCAC_REMOTE 241
extern  enum clnt_stat qcsapi_wifi_start_ocac_remote_1(qcsapi_wifi_start_ocac_rpcdata *, qcsapi_wifi_start_ocac_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_start_ocac_remote_1_svc(qcsapi_wifi_start_ocac_rpcdata *, qcsapi_wifi_start_ocac_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_STOP_OCAC_REMOTE 251
extern  enum clnt_stat qcsapi_wifi_stop_ocac_remote_1(qcsapi_wifi_stop_ocac_rpcdata *, qcsapi_wifi_stop_ocac_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_stop_ocac_remote_1_svc(qcsapi_wifi_stop_ocac_rpcdata *, qcsapi_wifi_stop_ocac_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_OCAC_STATUS_REMOTE 261
extern  enum clnt_stat qcsapi_wifi_get_ocac_status_remote_1(qcsapi_wifi_get_ocac_status_rpcdata *, qcsapi_wifi_get_ocac_status_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_ocac_status_remote_1_svc(qcsapi_wifi_get_ocac_status_rpcdata *, qcsapi_wifi_get_ocac_status_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_OCAC_DWELL_TIME_REMOTE 271
extern  enum clnt_stat qcsapi_wifi_set_ocac_dwell_time_remote_1(qcsapi_wifi_set_ocac_dwell_time_rpcdata *, qcsapi_wifi_set_ocac_dwell_time_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_ocac_dwell_time_remote_1_svc(qcsapi_wifi_set_ocac_dwell_time_rpcdata *, qcsapi_wifi_set_ocac_dwell_time_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_OCAC_DURATION_REMOTE 281
extern  enum clnt_stat qcsapi_wifi_set_ocac_duration_remote_1(qcsapi_wifi_set_ocac_duration_rpcdata *, qcsapi_wifi_set_ocac_duration_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_ocac_duration_remote_1_svc(qcsapi_wifi_set_ocac_duration_rpcdata *, qcsapi_wifi_set_ocac_duration_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_OCAC_CAC_TIME_REMOTE 291
extern  enum clnt_stat qcsapi_wifi_set_ocac_cac_time_remote_1(qcsapi_wifi_set_ocac_cac_time_rpcdata *, qcsapi_wifi_set_ocac_cac_time_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_ocac_cac_time_remote_1_svc(qcsapi_wifi_set_ocac_cac_time_rpcdata *, qcsapi_wifi_set_ocac_cac_time_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_OCAC_REPORT_ONLY_REMOTE 301
extern  enum clnt_stat qcsapi_wifi_set_ocac_report_only_remote_1(qcsapi_wifi_set_ocac_report_only_rpcdata *, qcsapi_wifi_set_ocac_report_only_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_ocac_report_only_remote_1_svc(qcsapi_wifi_set_ocac_report_only_rpcdata *, qcsapi_wifi_set_ocac_report_only_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_OCAC_THRSHLD_REMOTE 311
extern  enum clnt_stat qcsapi_wifi_set_ocac_thrshld_remote_1(qcsapi_wifi_set_ocac_thrshld_rpcdata *, qcsapi_wifi_set_ocac_thrshld_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_ocac_thrshld_remote_1_svc(qcsapi_wifi_set_ocac_thrshld_rpcdata *, qcsapi_wifi_set_ocac_thrshld_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_START_DFS_S_RADIO_REMOTE 242
extern  enum clnt_stat qcsapi_wifi_start_dfs_s_radio_remote_1(qcsapi_wifi_start_dfs_s_radio_rpcdata *, qcsapi_wifi_start_dfs_s_radio_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_start_dfs_s_radio_remote_1_svc(qcsapi_wifi_start_dfs_s_radio_rpcdata *, qcsapi_wifi_start_dfs_s_radio_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_STOP_DFS_S_RADIO_REMOTE 252
extern  enum clnt_stat qcsapi_wifi_stop_dfs_s_radio_remote_1(qcsapi_wifi_stop_dfs_s_radio_rpcdata *, qcsapi_wifi_stop_dfs_s_radio_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_stop_dfs_s_radio_remote_1_svc(qcsapi_wifi_stop_dfs_s_radio_rpcdata *, qcsapi_wifi_stop_dfs_s_radio_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_DFS_S_RADIO_STATUS_REMOTE 262
extern  enum clnt_stat qcsapi_wifi_get_dfs_s_radio_status_remote_1(qcsapi_wifi_get_dfs_s_radio_status_rpcdata *, qcsapi_wifi_get_dfs_s_radio_status_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_dfs_s_radio_status_remote_1_svc(qcsapi_wifi_get_dfs_s_radio_status_rpcdata *, qcsapi_wifi_get_dfs_s_radio_status_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_DFS_S_RADIO_AVAILABILITY_REMOTE 263
extern  enum clnt_stat qcsapi_wifi_get_dfs_s_radio_availability_remote_1(qcsapi_wifi_get_dfs_s_radio_availability_rpcdata *, qcsapi_wifi_get_dfs_s_radio_availability_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_dfs_s_radio_availability_remote_1_svc(qcsapi_wifi_get_dfs_s_radio_availability_rpcdata *, qcsapi_wifi_get_dfs_s_radio_availability_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_DFS_S_RADIO_DWELL_TIME_REMOTE 272
extern  enum clnt_stat qcsapi_wifi_set_dfs_s_radio_dwell_time_remote_1(qcsapi_wifi_set_dfs_s_radio_dwell_time_rpcdata *, qcsapi_wifi_set_dfs_s_radio_dwell_time_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_dfs_s_radio_dwell_time_remote_1_svc(qcsapi_wifi_set_dfs_s_radio_dwell_time_rpcdata *, qcsapi_wifi_set_dfs_s_radio_dwell_time_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_DFS_S_RADIO_DURATION_REMOTE 282
extern  enum clnt_stat qcsapi_wifi_set_dfs_s_radio_duration_remote_1(qcsapi_wifi_set_dfs_s_radio_duration_rpcdata *, qcsapi_wifi_set_dfs_s_radio_duration_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_dfs_s_radio_duration_remote_1_svc(qcsapi_wifi_set_dfs_s_radio_duration_rpcdata *, qcsapi_wifi_set_dfs_s_radio_duration_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_DFS_S_RADIO_WEA_DURATION_REMOTE 283
extern  enum clnt_stat qcsapi_wifi_set_dfs_s_radio_wea_duration_remote_1(qcsapi_wifi_set_dfs_s_radio_wea_duration_rpcdata *, qcsapi_wifi_set_dfs_s_radio_wea_duration_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_dfs_s_radio_wea_duration_remote_1_svc(qcsapi_wifi_set_dfs_s_radio_wea_duration_rpcdata *, qcsapi_wifi_set_dfs_s_radio_wea_duration_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_DFS_S_RADIO_CAC_TIME_REMOTE 292
extern  enum clnt_stat qcsapi_wifi_set_dfs_s_radio_cac_time_remote_1(qcsapi_wifi_set_dfs_s_radio_cac_time_rpcdata *, qcsapi_wifi_set_dfs_s_radio_cac_time_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_dfs_s_radio_cac_time_remote_1_svc(qcsapi_wifi_set_dfs_s_radio_cac_time_rpcdata *, qcsapi_wifi_set_dfs_s_radio_cac_time_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_DFS_S_RADIO_WEA_CAC_TIME_REMOTE 293
extern  enum clnt_stat qcsapi_wifi_set_dfs_s_radio_wea_cac_time_remote_1(qcsapi_wifi_set_dfs_s_radio_wea_cac_time_rpcdata *, qcsapi_wifi_set_dfs_s_radio_wea_cac_time_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_dfs_s_radio_wea_cac_time_remote_1_svc(qcsapi_wifi_set_dfs_s_radio_wea_cac_time_rpcdata *, qcsapi_wifi_set_dfs_s_radio_wea_cac_time_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_DFS_S_RADIO_REPORT_ONLY_REMOTE 302
extern  enum clnt_stat qcsapi_wifi_set_dfs_s_radio_report_only_remote_1(qcsapi_wifi_set_dfs_s_radio_report_only_rpcdata *, qcsapi_wifi_set_dfs_s_radio_report_only_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_dfs_s_radio_report_only_remote_1_svc(qcsapi_wifi_set_dfs_s_radio_report_only_rpcdata *, qcsapi_wifi_set_dfs_s_radio_report_only_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_DFS_S_RADIO_THRSHLD_REMOTE 312
extern  enum clnt_stat qcsapi_wifi_set_dfs_s_radio_thrshld_remote_1(qcsapi_wifi_set_dfs_s_radio_thrshld_rpcdata *, qcsapi_wifi_set_dfs_s_radio_thrshld_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_dfs_s_radio_thrshld_remote_1_svc(qcsapi_wifi_set_dfs_s_radio_thrshld_rpcdata *, qcsapi_wifi_set_dfs_s_radio_thrshld_rpcdata *, struct svc_req *);
#define QCSAPI_INIT_REMOTE 321
extern  enum clnt_stat qcsapi_init_remote_1(qcsapi_init_rpcdata *, qcsapi_init_rpcdata *, CLIENT *);
extern  bool_t qcsapi_init_remote_1_svc(qcsapi_init_rpcdata *, qcsapi_init_rpcdata *, struct svc_req *);
#define QCSAPI_CONSOLE_DISCONNECT_REMOTE 331
extern  enum clnt_stat qcsapi_console_disconnect_remote_1(qcsapi_console_disconnect_rpcdata *, qcsapi_console_disconnect_rpcdata *, CLIENT *);
extern  bool_t qcsapi_console_disconnect_remote_1_svc(qcsapi_console_disconnect_rpcdata *, qcsapi_console_disconnect_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_STARTPROD_REMOTE 611
extern  enum clnt_stat qcsapi_wifi_startprod_remote_1(qcsapi_wifi_startprod_rpcdata *, qcsapi_wifi_startprod_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_startprod_remote_1_svc(qcsapi_wifi_startprod_rpcdata *, qcsapi_wifi_startprod_rpcdata *, struct svc_req *);
#define QCSAPI_IS_STARTPROD_DONE_REMOTE 621
extern  enum clnt_stat qcsapi_is_startprod_done_remote_1(qcsapi_is_startprod_done_rpcdata *, qcsapi_is_startprod_done_rpcdata *, CLIENT *);
extern  bool_t qcsapi_is_startprod_done_remote_1_svc(qcsapi_is_startprod_done_rpcdata *, qcsapi_is_startprod_done_rpcdata *, struct svc_req *);
#define QCSAPI_SYSTEM_GET_TIME_SINCE_START_REMOTE 341
extern  enum clnt_stat qcsapi_system_get_time_since_start_remote_1(qcsapi_system_get_time_since_start_rpcdata *, qcsapi_system_get_time_since_start_rpcdata *, CLIENT *);
extern  bool_t qcsapi_system_get_time_since_start_remote_1_svc(qcsapi_system_get_time_since_start_rpcdata *, qcsapi_system_get_time_since_start_rpcdata *, struct svc_req *);
#define QCSAPI_GET_SYSTEM_STATUS_REMOTE 351
extern  enum clnt_stat qcsapi_get_system_status_remote_1(qcsapi_get_system_status_rpcdata *, qcsapi_get_system_status_rpcdata *, CLIENT *);
extern  bool_t qcsapi_get_system_status_remote_1_svc(qcsapi_get_system_status_rpcdata *, qcsapi_get_system_status_rpcdata *, struct svc_req *);
#define QCSAPI_GET_RANDOM_SEED_REMOTE 5831
extern  enum clnt_stat qcsapi_get_random_seed_remote_1(qcsapi_get_random_seed_rpcdata *, qcsapi_get_random_seed_rpcdata *, CLIENT *);
extern  bool_t qcsapi_get_random_seed_remote_1_svc(qcsapi_get_random_seed_rpcdata *, qcsapi_get_random_seed_rpcdata *, struct svc_req *);
#define QCSAPI_SET_RANDOM_SEED_REMOTE 5841
extern  enum clnt_stat qcsapi_set_random_seed_remote_1(qcsapi_set_random_seed_rpcdata *, qcsapi_set_random_seed_rpcdata *, CLIENT *);
extern  bool_t qcsapi_set_random_seed_remote_1_svc(qcsapi_set_random_seed_rpcdata *, qcsapi_set_random_seed_rpcdata *, struct svc_req *);
#define QCSAPI_GET_CARRIER_ID_REMOTE 4071
extern  enum clnt_stat qcsapi_get_carrier_id_remote_1(qcsapi_get_carrier_id_rpcdata *, qcsapi_get_carrier_id_rpcdata *, CLIENT *);
extern  bool_t qcsapi_get_carrier_id_remote_1_svc(qcsapi_get_carrier_id_rpcdata *, qcsapi_get_carrier_id_rpcdata *, struct svc_req *);
#define QCSAPI_SET_CARRIER_ID_REMOTE 4081
extern  enum clnt_stat qcsapi_set_carrier_id_remote_1(qcsapi_set_carrier_id_rpcdata *, qcsapi_set_carrier_id_rpcdata *, CLIENT *);
extern  bool_t qcsapi_set_carrier_id_remote_1_svc(qcsapi_set_carrier_id_rpcdata *, qcsapi_set_carrier_id_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_SPINOR_JEDECID_REMOTE 4121
extern  enum clnt_stat qcsapi_wifi_get_spinor_jedecid_remote_1(qcsapi_wifi_get_spinor_jedecid_rpcdata *, qcsapi_wifi_get_spinor_jedecid_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_spinor_jedecid_remote_1_svc(qcsapi_wifi_get_spinor_jedecid_rpcdata *, qcsapi_wifi_get_spinor_jedecid_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_BB_PARAM_REMOTE 4281
extern  enum clnt_stat qcsapi_wifi_get_bb_param_remote_1(qcsapi_wifi_get_bb_param_rpcdata *, qcsapi_wifi_get_bb_param_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_bb_param_remote_1_svc(qcsapi_wifi_get_bb_param_rpcdata *, qcsapi_wifi_get_bb_param_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_BB_PARAM_REMOTE 4291
extern  enum clnt_stat qcsapi_wifi_set_bb_param_remote_1(qcsapi_wifi_set_bb_param_rpcdata *, qcsapi_wifi_set_bb_param_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_bb_param_remote_1_svc(qcsapi_wifi_set_bb_param_rpcdata *, qcsapi_wifi_set_bb_param_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_OPTIM_STATS_REMOTE 6001
extern  enum clnt_stat qcsapi_wifi_set_optim_stats_remote_1(qcsapi_wifi_set_optim_stats_rpcdata *, qcsapi_wifi_set_optim_stats_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_optim_stats_remote_1_svc(qcsapi_wifi_set_optim_stats_rpcdata *, qcsapi_wifi_set_optim_stats_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_SYS_TIME_REMOTE 6101
extern  enum clnt_stat qcsapi_wifi_set_sys_time_remote_1(qcsapi_wifi_set_sys_time_rpcdata *, qcsapi_wifi_set_sys_time_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_sys_time_remote_1_svc(qcsapi_wifi_set_sys_time_rpcdata *, qcsapi_wifi_set_sys_time_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_SYS_TIME_REMOTE 6111
extern  enum clnt_stat qcsapi_wifi_get_sys_time_remote_1(qcsapi_wifi_get_sys_time_rpcdata *, qcsapi_wifi_get_sys_time_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_sys_time_remote_1_svc(qcsapi_wifi_get_sys_time_rpcdata *, qcsapi_wifi_get_sys_time_rpcdata *, struct svc_req *);
#define QCSAPI_SET_SOC_MAC_ADDR_REMOTE 3571
extern  enum clnt_stat qcsapi_set_soc_mac_addr_remote_1(qcsapi_set_soc_mac_addr_rpcdata *, qcsapi_set_soc_mac_addr_rpcdata *, CLIENT *);
extern  bool_t qcsapi_set_soc_mac_addr_remote_1_svc(qcsapi_set_soc_mac_addr_rpcdata *, qcsapi_set_soc_mac_addr_rpcdata *, struct svc_req *);
#define QCSAPI_GET_CUSTOM_VALUE_REMOTE 3581
extern  enum clnt_stat qcsapi_get_custom_value_remote_1(qcsapi_get_custom_value_rpcdata *, qcsapi_get_custom_value_rpcdata *, CLIENT *);
extern  bool_t qcsapi_get_custom_value_remote_1_svc(qcsapi_get_custom_value_rpcdata *, qcsapi_get_custom_value_rpcdata *, struct svc_req *);
#define QCSAPI_CONFIG_GET_PARAMETER_REMOTE 361
extern  enum clnt_stat qcsapi_config_get_parameter_remote_1(qcsapi_config_get_parameter_rpcdata *, qcsapi_config_get_parameter_rpcdata *, CLIENT *);
extern  bool_t qcsapi_config_get_parameter_remote_1_svc(qcsapi_config_get_parameter_rpcdata *, qcsapi_config_get_parameter_rpcdata *, struct svc_req *);
#define QCSAPI_CONFIG_UPDATE_PARAMETER_REMOTE 371
extern  enum clnt_stat qcsapi_config_update_parameter_remote_1(qcsapi_config_update_parameter_rpcdata *, qcsapi_config_update_parameter_rpcdata *, CLIENT *);
extern  bool_t qcsapi_config_update_parameter_remote_1_svc(qcsapi_config_update_parameter_rpcdata *, qcsapi_config_update_parameter_rpcdata *, struct svc_req *);
#define QCSAPI_CONFIG_GET_SSID_PARAMETER_REMOTE 381
extern  enum clnt_stat qcsapi_config_get_ssid_parameter_remote_1(qcsapi_config_get_ssid_parameter_rpcdata *, qcsapi_config_get_ssid_parameter_rpcdata *, CLIENT *);
extern  bool_t qcsapi_config_get_ssid_parameter_remote_1_svc(qcsapi_config_get_ssid_parameter_rpcdata *, qcsapi_config_get_ssid_parameter_rpcdata *, struct svc_req *);
#define QCSAPI_CONFIG_UPDATE_SSID_PARAMETER_REMOTE 391
extern  enum clnt_stat qcsapi_config_update_ssid_parameter_remote_1(qcsapi_config_update_ssid_parameter_rpcdata *, qcsapi_config_update_ssid_parameter_rpcdata *, CLIENT *);
extern  bool_t qcsapi_config_update_ssid_parameter_remote_1_svc(qcsapi_config_update_ssid_parameter_rpcdata *, qcsapi_config_update_ssid_parameter_rpcdata *, struct svc_req *);
#define QCSAPI_FILE_PATH_GET_CONFIG_REMOTE 401
extern  enum clnt_stat qcsapi_file_path_get_config_remote_1(qcsapi_file_path_get_config_rpcdata *, qcsapi_file_path_get_config_rpcdata *, CLIENT *);
extern  bool_t qcsapi_file_path_get_config_remote_1_svc(qcsapi_file_path_get_config_rpcdata *, qcsapi_file_path_get_config_rpcdata *, struct svc_req *);
#define QCSAPI_FILE_PATH_SET_CONFIG_REMOTE 411
extern  enum clnt_stat qcsapi_file_path_set_config_remote_1(qcsapi_file_path_set_config_rpcdata *, qcsapi_file_path_set_config_rpcdata *, CLIENT *);
extern  bool_t qcsapi_file_path_set_config_remote_1_svc(qcsapi_file_path_set_config_rpcdata *, qcsapi_file_path_set_config_rpcdata *, struct svc_req *);
#define QCSAPI_RESTORE_DEFAULT_CONFIG_REMOTE 421
extern  enum clnt_stat qcsapi_restore_default_config_remote_1(qcsapi_restore_default_config_rpcdata *, qcsapi_restore_default_config_rpcdata *, CLIENT *);
extern  bool_t qcsapi_restore_default_config_remote_1_svc(qcsapi_restore_default_config_rpcdata *, qcsapi_restore_default_config_rpcdata *, struct svc_req *);
#define QCSAPI_STORE_IPADDR_REMOTE 431
extern  enum clnt_stat qcsapi_store_ipaddr_remote_1(qcsapi_store_ipaddr_rpcdata *, qcsapi_store_ipaddr_rpcdata *, CLIENT *);
extern  bool_t qcsapi_store_ipaddr_remote_1_svc(qcsapi_store_ipaddr_rpcdata *, qcsapi_store_ipaddr_rpcdata *, struct svc_req *);
#define QCSAPI_INTERFACE_ENABLE_REMOTE 441
extern  enum clnt_stat qcsapi_interface_enable_remote_1(qcsapi_interface_enable_rpcdata *, qcsapi_interface_enable_rpcdata *, CLIENT *);
extern  bool_t qcsapi_interface_enable_remote_1_svc(qcsapi_interface_enable_rpcdata *, qcsapi_interface_enable_rpcdata *, struct svc_req *);
#define QCSAPI_INTERFACE_GET_STATUS_REMOTE 451
extern  enum clnt_stat qcsapi_interface_get_status_remote_1(qcsapi_interface_get_status_rpcdata *, qcsapi_interface_get_status_rpcdata *, CLIENT *);
extern  bool_t qcsapi_interface_get_status_remote_1_svc(qcsapi_interface_get_status_rpcdata *, qcsapi_interface_get_status_rpcdata *, struct svc_req *);
#define QCSAPI_INTERFACE_SET_IP4_REMOTE 5691
extern  enum clnt_stat qcsapi_interface_set_ip4_remote_1(qcsapi_interface_set_ip4_rpcdata *, qcsapi_interface_set_ip4_rpcdata *, CLIENT *);
extern  bool_t qcsapi_interface_set_ip4_remote_1_svc(qcsapi_interface_set_ip4_rpcdata *, qcsapi_interface_set_ip4_rpcdata *, struct svc_req *);
#define QCSAPI_INTERFACE_GET_IP4_REMOTE 5701
extern  enum clnt_stat qcsapi_interface_get_ip4_remote_1(qcsapi_interface_get_ip4_rpcdata *, qcsapi_interface_get_ip4_rpcdata *, CLIENT *);
extern  bool_t qcsapi_interface_get_ip4_remote_1_svc(qcsapi_interface_get_ip4_rpcdata *, qcsapi_interface_get_ip4_rpcdata *, struct svc_req *);
#define QCSAPI_INTERFACE_GET_COUNTER_REMOTE 461
extern  enum clnt_stat qcsapi_interface_get_counter_remote_1(qcsapi_interface_get_counter_rpcdata *, qcsapi_interface_get_counter_rpcdata *, CLIENT *);
extern  bool_t qcsapi_interface_get_counter_remote_1_svc(qcsapi_interface_get_counter_rpcdata *, qcsapi_interface_get_counter_rpcdata *, struct svc_req *);
#define QCSAPI_INTERFACE_GET_COUNTER64_REMOTE 471
extern  enum clnt_stat qcsapi_interface_get_counter64_remote_1(qcsapi_interface_get_counter64_rpcdata *, qcsapi_interface_get_counter64_rpcdata *, CLIENT *);
extern  bool_t qcsapi_interface_get_counter64_remote_1_svc(qcsapi_interface_get_counter64_rpcdata *, qcsapi_interface_get_counter64_rpcdata *, struct svc_req *);
#define QCSAPI_INTERFACE_GET_MAC_ADDR_REMOTE 481
extern  enum clnt_stat qcsapi_interface_get_mac_addr_remote_1(qcsapi_interface_get_mac_addr_rpcdata *, qcsapi_interface_get_mac_addr_rpcdata *, CLIENT *);
extern  bool_t qcsapi_interface_get_mac_addr_remote_1_svc(qcsapi_interface_get_mac_addr_rpcdata *, qcsapi_interface_get_mac_addr_rpcdata *, struct svc_req *);
#define QCSAPI_INTERFACE_SET_MAC_ADDR_REMOTE 491
extern  enum clnt_stat qcsapi_interface_set_mac_addr_remote_1(qcsapi_interface_set_mac_addr_rpcdata *, qcsapi_interface_set_mac_addr_rpcdata *, CLIENT *);
extern  bool_t qcsapi_interface_set_mac_addr_remote_1_svc(qcsapi_interface_set_mac_addr_rpcdata *, qcsapi_interface_set_mac_addr_rpcdata *, struct svc_req *);
#define QCSAPI_PM_GET_COUNTER_REMOTE 501
extern  enum clnt_stat qcsapi_pm_get_counter_remote_1(qcsapi_pm_get_counter_rpcdata *, qcsapi_pm_get_counter_rpcdata *, CLIENT *);
extern  bool_t qcsapi_pm_get_counter_remote_1_svc(qcsapi_pm_get_counter_rpcdata *, qcsapi_pm_get_counter_rpcdata *, struct svc_req *);
#define QCSAPI_SET_ASPM_L1_REMOTE 511
extern  enum clnt_stat qcsapi_set_aspm_l1_remote_1(qcsapi_set_aspm_l1_rpcdata *, qcsapi_set_aspm_l1_rpcdata *, CLIENT *);
extern  bool_t qcsapi_set_aspm_l1_remote_1_svc(qcsapi_set_aspm_l1_rpcdata *, qcsapi_set_aspm_l1_rpcdata *, struct svc_req *);
#define QCSAPI_SET_L1_REMOTE 521
extern  enum clnt_stat qcsapi_set_l1_remote_1(qcsapi_set_l1_rpcdata *, qcsapi_set_l1_rpcdata *, CLIENT *);
extern  bool_t qcsapi_set_l1_remote_1_svc(qcsapi_set_l1_rpcdata *, qcsapi_set_l1_rpcdata *, struct svc_req *);
#define QCSAPI_PM_GET_ELAPSED_TIME_REMOTE 531
extern  enum clnt_stat qcsapi_pm_get_elapsed_time_remote_1(qcsapi_pm_get_elapsed_time_rpcdata *, qcsapi_pm_get_elapsed_time_rpcdata *, CLIENT *);
extern  bool_t qcsapi_pm_get_elapsed_time_remote_1_svc(qcsapi_pm_get_elapsed_time_rpcdata *, qcsapi_pm_get_elapsed_time_rpcdata *, struct svc_req *);
#define QCSAPI_ETH_PHY_POWER_CONTROL_REMOTE 541
extern  enum clnt_stat qcsapi_eth_phy_power_control_remote_1(qcsapi_eth_phy_power_control_rpcdata *, qcsapi_eth_phy_power_control_rpcdata *, CLIENT *);
extern  bool_t qcsapi_eth_phy_power_control_remote_1_svc(qcsapi_eth_phy_power_control_rpcdata *, qcsapi_eth_phy_power_control_rpcdata *, struct svc_req *);
#define QCSAPI_GET_EMAC_SWITCH_REMOTE 5971
extern  enum clnt_stat qcsapi_get_emac_switch_remote_1(qcsapi_get_emac_switch_rpcdata *, qcsapi_get_emac_switch_rpcdata *, CLIENT *);
extern  bool_t qcsapi_get_emac_switch_remote_1_svc(qcsapi_get_emac_switch_rpcdata *, qcsapi_get_emac_switch_rpcdata *, struct svc_req *);
#define QCSAPI_SET_EMAC_SWITCH_REMOTE 5981
extern  enum clnt_stat qcsapi_set_emac_switch_remote_1(qcsapi_set_emac_switch_rpcdata *, qcsapi_set_emac_switch_rpcdata *, CLIENT *);
extern  bool_t qcsapi_set_emac_switch_remote_1_svc(qcsapi_set_emac_switch_rpcdata *, qcsapi_set_emac_switch_rpcdata *, struct svc_req *);
#define QCSAPI_ETH_DSCP_MAP_REMOTE 5991
extern  enum clnt_stat qcsapi_eth_dscp_map_remote_1(qcsapi_eth_dscp_map_rpcdata *, qcsapi_eth_dscp_map_rpcdata *, CLIENT *);
extern  bool_t qcsapi_eth_dscp_map_remote_1_svc(qcsapi_eth_dscp_map_rpcdata *, qcsapi_eth_dscp_map_rpcdata *, struct svc_req *);
#define QCSAPI_GET_ETH_INFO_REMOTE 6121
extern  enum clnt_stat qcsapi_get_eth_info_remote_1(qcsapi_get_eth_info_rpcdata *, qcsapi_get_eth_info_rpcdata *, CLIENT *);
extern  bool_t qcsapi_get_eth_info_remote_1_svc(qcsapi_get_eth_info_rpcdata *, qcsapi_get_eth_info_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_MODE_REMOTE 551
extern  enum clnt_stat qcsapi_wifi_get_mode_remote_1(qcsapi_wifi_get_mode_rpcdata *, qcsapi_wifi_get_mode_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_mode_remote_1_svc(qcsapi_wifi_get_mode_rpcdata *, qcsapi_wifi_get_mode_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_MODE_REMOTE 561
extern  enum clnt_stat qcsapi_wifi_set_mode_remote_1(qcsapi_wifi_set_mode_rpcdata *, qcsapi_wifi_set_mode_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_mode_remote_1_svc(qcsapi_wifi_set_mode_rpcdata *, qcsapi_wifi_set_mode_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_PHY_MODE_REMOTE 571
extern  enum clnt_stat qcsapi_wifi_get_phy_mode_remote_1(qcsapi_wifi_get_phy_mode_rpcdata *, qcsapi_wifi_get_phy_mode_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_phy_mode_remote_1_svc(qcsapi_wifi_get_phy_mode_rpcdata *, qcsapi_wifi_get_phy_mode_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_PHY_MODE_REMOTE 581
extern  enum clnt_stat qcsapi_wifi_set_phy_mode_remote_1(qcsapi_wifi_set_phy_mode_rpcdata *, qcsapi_wifi_set_phy_mode_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_phy_mode_remote_1_svc(qcsapi_wifi_set_phy_mode_rpcdata *, qcsapi_wifi_set_phy_mode_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_RELOAD_IN_MODE_REMOTE 591
extern  enum clnt_stat qcsapi_wifi_reload_in_mode_remote_1(qcsapi_wifi_reload_in_mode_rpcdata *, qcsapi_wifi_reload_in_mode_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_reload_in_mode_remote_1_svc(qcsapi_wifi_reload_in_mode_rpcdata *, qcsapi_wifi_reload_in_mode_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_RFENABLE_REMOTE 601
extern  enum clnt_stat qcsapi_wifi_rfenable_remote_1(qcsapi_wifi_rfenable_rpcdata *, qcsapi_wifi_rfenable_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_rfenable_remote_1_svc(qcsapi_wifi_rfenable_rpcdata *, qcsapi_wifi_rfenable_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_RFSTATUS_REMOTE 631
extern  enum clnt_stat qcsapi_wifi_rfstatus_remote_1(qcsapi_wifi_rfstatus_rpcdata *, qcsapi_wifi_rfstatus_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_rfstatus_remote_1_svc(qcsapi_wifi_rfstatus_rpcdata *, qcsapi_wifi_rfstatus_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_BW_REMOTE 641
extern  enum clnt_stat qcsapi_wifi_get_bw_remote_1(qcsapi_wifi_get_bw_rpcdata *, qcsapi_wifi_get_bw_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_bw_remote_1_svc(qcsapi_wifi_get_bw_rpcdata *, qcsapi_wifi_get_bw_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_BW_REMOTE 651
extern  enum clnt_stat qcsapi_wifi_set_bw_remote_1(qcsapi_wifi_set_bw_rpcdata *, qcsapi_wifi_set_bw_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_bw_remote_1_svc(qcsapi_wifi_set_bw_rpcdata *, qcsapi_wifi_set_bw_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_VHT_REMOTE 4091
extern  enum clnt_stat qcsapi_wifi_set_vht_remote_1(qcsapi_wifi_set_vht_rpcdata *, qcsapi_wifi_set_vht_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_vht_remote_1_svc(qcsapi_wifi_set_vht_rpcdata *, qcsapi_wifi_set_vht_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_VHT_REMOTE 4101
extern  enum clnt_stat qcsapi_wifi_get_vht_remote_1(qcsapi_wifi_get_vht_rpcdata *, qcsapi_wifi_get_vht_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_vht_remote_1_svc(qcsapi_wifi_get_vht_rpcdata *, qcsapi_wifi_get_vht_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_CHANNEL_REMOTE 671
extern  enum clnt_stat qcsapi_wifi_get_channel_remote_1(qcsapi_wifi_get_channel_rpcdata *, qcsapi_wifi_get_channel_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_channel_remote_1_svc(qcsapi_wifi_get_channel_rpcdata *, qcsapi_wifi_get_channel_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_CHANNEL_REMOTE 681
extern  enum clnt_stat qcsapi_wifi_set_channel_remote_1(qcsapi_wifi_set_channel_rpcdata *, qcsapi_wifi_set_channel_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_channel_remote_1_svc(qcsapi_wifi_set_channel_rpcdata *, qcsapi_wifi_set_channel_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_CHAN_PRI_INACTIVE_REMOTE 691
extern  enum clnt_stat qcsapi_wifi_set_chan_pri_inactive_remote_1(qcsapi_wifi_set_chan_pri_inactive_rpcdata *, qcsapi_wifi_set_chan_pri_inactive_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_chan_pri_inactive_remote_1_svc(qcsapi_wifi_set_chan_pri_inactive_rpcdata *, qcsapi_wifi_set_chan_pri_inactive_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_CHAN_CONTROL_REMOTE 6211
extern  enum clnt_stat qcsapi_wifi_chan_control_remote_1(qcsapi_wifi_chan_control_rpcdata *, qcsapi_wifi_chan_control_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_chan_control_remote_1_svc(qcsapi_wifi_chan_control_rpcdata *, qcsapi_wifi_chan_control_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_CHAN_DISABLED_REMOTE 6221
extern  enum clnt_stat qcsapi_wifi_get_chan_disabled_remote_1(qcsapi_wifi_get_chan_disabled_rpcdata *, qcsapi_wifi_get_chan_disabled_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_chan_disabled_remote_1_svc(qcsapi_wifi_get_chan_disabled_rpcdata *, qcsapi_wifi_get_chan_disabled_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_BEACON_INTERVAL_REMOTE 701
extern  enum clnt_stat qcsapi_wifi_get_beacon_interval_remote_1(qcsapi_wifi_get_beacon_interval_rpcdata *, qcsapi_wifi_get_beacon_interval_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_beacon_interval_remote_1_svc(qcsapi_wifi_get_beacon_interval_rpcdata *, qcsapi_wifi_get_beacon_interval_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_BEACON_INTERVAL_REMOTE 711
extern  enum clnt_stat qcsapi_wifi_set_beacon_interval_remote_1(qcsapi_wifi_set_beacon_interval_rpcdata *, qcsapi_wifi_set_beacon_interval_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_beacon_interval_remote_1_svc(qcsapi_wifi_set_beacon_interval_rpcdata *, qcsapi_wifi_set_beacon_interval_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_DTIM_REMOTE 721
extern  enum clnt_stat qcsapi_wifi_get_dtim_remote_1(qcsapi_wifi_get_dtim_rpcdata *, qcsapi_wifi_get_dtim_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_dtim_remote_1_svc(qcsapi_wifi_get_dtim_rpcdata *, qcsapi_wifi_get_dtim_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_DTIM_REMOTE 731
extern  enum clnt_stat qcsapi_wifi_set_dtim_remote_1(qcsapi_wifi_set_dtim_rpcdata *, qcsapi_wifi_set_dtim_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_dtim_remote_1_svc(qcsapi_wifi_set_dtim_rpcdata *, qcsapi_wifi_set_dtim_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_ASSOC_LIMIT_REMOTE 741
extern  enum clnt_stat qcsapi_wifi_get_assoc_limit_remote_1(qcsapi_wifi_get_assoc_limit_rpcdata *, qcsapi_wifi_get_assoc_limit_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_assoc_limit_remote_1_svc(qcsapi_wifi_get_assoc_limit_rpcdata *, qcsapi_wifi_get_assoc_limit_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_BSS_ASSOC_LIMIT_REMOTE 5721
extern  enum clnt_stat qcsapi_wifi_get_bss_assoc_limit_remote_1(qcsapi_wifi_get_bss_assoc_limit_rpcdata *, qcsapi_wifi_get_bss_assoc_limit_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_bss_assoc_limit_remote_1_svc(qcsapi_wifi_get_bss_assoc_limit_rpcdata *, qcsapi_wifi_get_bss_assoc_limit_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_ASSOC_LIMIT_REMOTE 751
extern  enum clnt_stat qcsapi_wifi_set_assoc_limit_remote_1(qcsapi_wifi_set_assoc_limit_rpcdata *, qcsapi_wifi_set_assoc_limit_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_assoc_limit_remote_1_svc(qcsapi_wifi_set_assoc_limit_rpcdata *, qcsapi_wifi_set_assoc_limit_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_BSS_ASSOC_LIMIT_REMOTE 5711
extern  enum clnt_stat qcsapi_wifi_set_bss_assoc_limit_remote_1(qcsapi_wifi_set_bss_assoc_limit_rpcdata *, qcsapi_wifi_set_bss_assoc_limit_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_bss_assoc_limit_remote_1_svc(qcsapi_wifi_set_bss_assoc_limit_rpcdata *, qcsapi_wifi_set_bss_assoc_limit_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_BSSID_REMOTE 761
extern  enum clnt_stat qcsapi_wifi_get_bssid_remote_1(qcsapi_wifi_get_BSSID_rpcdata *, qcsapi_wifi_get_BSSID_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_bssid_remote_1_svc(qcsapi_wifi_get_BSSID_rpcdata *, qcsapi_wifi_get_BSSID_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_CONFIG_BSSID_REMOTE 771
extern  enum clnt_stat qcsapi_wifi_get_config_bssid_remote_1(qcsapi_wifi_get_config_BSSID_rpcdata *, qcsapi_wifi_get_config_BSSID_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_config_bssid_remote_1_svc(qcsapi_wifi_get_config_BSSID_rpcdata *, qcsapi_wifi_get_config_BSSID_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SSID_GET_BSSID_REMOTE 6131
extern  enum clnt_stat qcsapi_wifi_ssid_get_bssid_remote_1(qcsapi_wifi_ssid_get_bssid_rpcdata *, qcsapi_wifi_ssid_get_bssid_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_ssid_get_bssid_remote_1_svc(qcsapi_wifi_ssid_get_bssid_rpcdata *, qcsapi_wifi_ssid_get_bssid_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SSID_SET_BSSID_REMOTE 6141
extern  enum clnt_stat qcsapi_wifi_ssid_set_bssid_remote_1(qcsapi_wifi_ssid_set_bssid_rpcdata *, qcsapi_wifi_ssid_set_bssid_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_ssid_set_bssid_remote_1_svc(qcsapi_wifi_ssid_set_bssid_rpcdata *, qcsapi_wifi_ssid_set_bssid_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_SSID_REMOTE 781
extern  enum clnt_stat qcsapi_wifi_get_ssid_remote_1(qcsapi_wifi_get_SSID_rpcdata *, qcsapi_wifi_get_SSID_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_ssid_remote_1_svc(qcsapi_wifi_get_SSID_rpcdata *, qcsapi_wifi_get_SSID_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_SSID_REMOTE 791
extern  enum clnt_stat qcsapi_wifi_set_ssid_remote_1(qcsapi_wifi_set_SSID_rpcdata *, qcsapi_wifi_set_SSID_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_ssid_remote_1_svc(qcsapi_wifi_set_SSID_rpcdata *, qcsapi_wifi_set_SSID_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_IEEE_802_11_STANDARD_REMOTE 801
extern  enum clnt_stat qcsapi_wifi_get_ieee_802_11_standard_remote_1(qcsapi_wifi_get_IEEE_802_11_standard_rpcdata *, qcsapi_wifi_get_IEEE_802_11_standard_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_ieee_802_11_standard_remote_1_svc(qcsapi_wifi_get_IEEE_802_11_standard_rpcdata *, qcsapi_wifi_get_IEEE_802_11_standard_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_LIST_CHANNELS_REMOTE 811
extern  enum clnt_stat qcsapi_wifi_get_list_channels_remote_1(qcsapi_wifi_get_list_channels_rpcdata *, qcsapi_wifi_get_list_channels_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_list_channels_remote_1_svc(qcsapi_wifi_get_list_channels_rpcdata *, qcsapi_wifi_get_list_channels_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_MODE_SWITCH_REMOTE 821
extern  enum clnt_stat qcsapi_wifi_get_mode_switch_remote_1(qcsapi_wifi_get_mode_switch_rpcdata *, qcsapi_wifi_get_mode_switch_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_mode_switch_remote_1_svc(qcsapi_wifi_get_mode_switch_rpcdata *, qcsapi_wifi_get_mode_switch_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_DISASSOCIATE_REMOTE 831
extern  enum clnt_stat qcsapi_wifi_disassociate_remote_1(qcsapi_wifi_disassociate_rpcdata *, qcsapi_wifi_disassociate_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_disassociate_remote_1_svc(qcsapi_wifi_disassociate_rpcdata *, qcsapi_wifi_disassociate_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_DISASSOCIATE_STA_REMOTE 841
extern  enum clnt_stat qcsapi_wifi_disassociate_sta_remote_1(qcsapi_wifi_disassociate_sta_rpcdata *, qcsapi_wifi_disassociate_sta_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_disassociate_sta_remote_1_svc(qcsapi_wifi_disassociate_sta_rpcdata *, qcsapi_wifi_disassociate_sta_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_REASSOCIATE_REMOTE 4441
extern  enum clnt_stat qcsapi_wifi_reassociate_remote_1(qcsapi_wifi_reassociate_rpcdata *, qcsapi_wifi_reassociate_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_reassociate_remote_1_svc(qcsapi_wifi_reassociate_rpcdata *, qcsapi_wifi_reassociate_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_DISCONN_INFO_REMOTE 851
extern  enum clnt_stat qcsapi_wifi_get_disconn_info_remote_1(qcsapi_wifi_get_disconn_info_rpcdata *, qcsapi_wifi_get_disconn_info_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_disconn_info_remote_1_svc(qcsapi_wifi_get_disconn_info_rpcdata *, qcsapi_wifi_get_disconn_info_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_DISABLE_WPS_REMOTE 861
extern  enum clnt_stat qcsapi_wifi_disable_wps_remote_1(qcsapi_wifi_disable_wps_rpcdata *, qcsapi_wifi_disable_wps_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_disable_wps_remote_1_svc(qcsapi_wifi_disable_wps_rpcdata *, qcsapi_wifi_disable_wps_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_ASSOCIATE_REMOTE 871
extern  enum clnt_stat qcsapi_wifi_associate_remote_1(qcsapi_wifi_associate_rpcdata *, qcsapi_wifi_associate_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_associate_remote_1_svc(qcsapi_wifi_associate_rpcdata *, qcsapi_wifi_associate_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_START_CCA_REMOTE 881
extern  enum clnt_stat qcsapi_wifi_start_cca_remote_1(qcsapi_wifi_start_cca_rpcdata *, qcsapi_wifi_start_cca_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_start_cca_remote_1_svc(qcsapi_wifi_start_cca_rpcdata *, qcsapi_wifi_start_cca_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_NOISE_REMOTE 891
extern  enum clnt_stat qcsapi_wifi_get_noise_remote_1(qcsapi_wifi_get_noise_rpcdata *, qcsapi_wifi_get_noise_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_noise_remote_1_svc(qcsapi_wifi_get_noise_rpcdata *, qcsapi_wifi_get_noise_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_RSSI_BY_CHAIN_REMOTE 901
extern  enum clnt_stat qcsapi_wifi_get_rssi_by_chain_remote_1(qcsapi_wifi_get_rssi_by_chain_rpcdata *, qcsapi_wifi_get_rssi_by_chain_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_rssi_by_chain_remote_1_svc(qcsapi_wifi_get_rssi_by_chain_rpcdata *, qcsapi_wifi_get_rssi_by_chain_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_AVG_SNR_REMOTE 911
extern  enum clnt_stat qcsapi_wifi_get_avg_snr_remote_1(qcsapi_wifi_get_avg_snr_rpcdata *, qcsapi_wifi_get_avg_snr_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_avg_snr_remote_1_svc(qcsapi_wifi_get_avg_snr_rpcdata *, qcsapi_wifi_get_avg_snr_rpcdata *, struct svc_req *);
#define QCSAPI_GET_PRIMARY_INTERFACE_REMOTE 921
extern  enum clnt_stat qcsapi_get_primary_interface_remote_1(qcsapi_get_primary_interface_rpcdata *, qcsapi_get_primary_interface_rpcdata *, CLIENT *);
extern  bool_t qcsapi_get_primary_interface_remote_1_svc(qcsapi_get_primary_interface_rpcdata *, qcsapi_get_primary_interface_rpcdata *, struct svc_req *);
#define QCSAPI_GET_INTERFACE_BY_INDEX_REMOTE 931
extern  enum clnt_stat qcsapi_get_interface_by_index_remote_1(qcsapi_get_interface_by_index_rpcdata *, qcsapi_get_interface_by_index_rpcdata *, CLIENT *);
extern  bool_t qcsapi_get_interface_by_index_remote_1_svc(qcsapi_get_interface_by_index_rpcdata *, qcsapi_get_interface_by_index_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_WIFI_MACADDR_REMOTE 941
extern  enum clnt_stat qcsapi_wifi_set_wifi_macaddr_remote_1(qcsapi_wifi_set_wifi_macaddr_rpcdata *, qcsapi_wifi_set_wifi_macaddr_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_wifi_macaddr_remote_1_svc(qcsapi_wifi_set_wifi_macaddr_rpcdata *, qcsapi_wifi_set_wifi_macaddr_rpcdata *, struct svc_req *);
#define QCSAPI_INTERFACE_GET_BSSID_REMOTE 951
extern  enum clnt_stat qcsapi_interface_get_bssid_remote_1(qcsapi_interface_get_BSSID_rpcdata *, qcsapi_interface_get_BSSID_rpcdata *, CLIENT *);
extern  bool_t qcsapi_interface_get_bssid_remote_1_svc(qcsapi_interface_get_BSSID_rpcdata *, qcsapi_interface_get_BSSID_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_RATES_REMOTE 961
extern  enum clnt_stat qcsapi_wifi_get_rates_remote_1(qcsapi_wifi_get_rates_rpcdata *, qcsapi_wifi_get_rates_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_rates_remote_1_svc(qcsapi_wifi_get_rates_rpcdata *, qcsapi_wifi_get_rates_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_RATES_REMOTE 971
extern  enum clnt_stat qcsapi_wifi_set_rates_remote_1(qcsapi_wifi_set_rates_rpcdata *, qcsapi_wifi_set_rates_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_rates_remote_1_svc(qcsapi_wifi_set_rates_rpcdata *, qcsapi_wifi_set_rates_rpcdata *, struct svc_req *);
#define QCSAPI_GET_MAX_BITRATE_REMOTE 981
extern  enum clnt_stat qcsapi_get_max_bitrate_remote_1(qcsapi_get_max_bitrate_rpcdata *, qcsapi_get_max_bitrate_rpcdata *, CLIENT *);
extern  bool_t qcsapi_get_max_bitrate_remote_1_svc(qcsapi_get_max_bitrate_rpcdata *, qcsapi_get_max_bitrate_rpcdata *, struct svc_req *);
#define QCSAPI_SET_MAX_BITRATE_REMOTE 991
extern  enum clnt_stat qcsapi_set_max_bitrate_remote_1(qcsapi_set_max_bitrate_rpcdata *, qcsapi_set_max_bitrate_rpcdata *, CLIENT *);
extern  bool_t qcsapi_set_max_bitrate_remote_1_svc(qcsapi_set_max_bitrate_rpcdata *, qcsapi_set_max_bitrate_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_QOS_GET_PARAM_REMOTE 1001
extern  enum clnt_stat qcsapi_wifi_qos_get_param_remote_1(qcsapi_wifi_qos_get_param_rpcdata *, qcsapi_wifi_qos_get_param_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_qos_get_param_remote_1_svc(qcsapi_wifi_qos_get_param_rpcdata *, qcsapi_wifi_qos_get_param_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_QOS_SET_PARAM_REMOTE 1011
extern  enum clnt_stat qcsapi_wifi_qos_set_param_remote_1(qcsapi_wifi_qos_set_param_rpcdata *, qcsapi_wifi_qos_set_param_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_qos_set_param_remote_1_svc(qcsapi_wifi_qos_set_param_rpcdata *, qcsapi_wifi_qos_set_param_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_WMM_AC_MAP_REMOTE 1021
extern  enum clnt_stat qcsapi_wifi_get_wmm_ac_map_remote_1(qcsapi_wifi_get_wmm_ac_map_rpcdata *, qcsapi_wifi_get_wmm_ac_map_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_wmm_ac_map_remote_1_svc(qcsapi_wifi_get_wmm_ac_map_rpcdata *, qcsapi_wifi_get_wmm_ac_map_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_WMM_AC_MAP_REMOTE 1031
extern  enum clnt_stat qcsapi_wifi_set_wmm_ac_map_remote_1(qcsapi_wifi_set_wmm_ac_map_rpcdata *, qcsapi_wifi_set_wmm_ac_map_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_wmm_ac_map_remote_1_svc(qcsapi_wifi_set_wmm_ac_map_rpcdata *, qcsapi_wifi_set_wmm_ac_map_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_DSCP_8021P_MAP_REMOTE 1041
extern  enum clnt_stat qcsapi_wifi_get_dscp_8021p_map_remote_1(qcsapi_wifi_get_dscp_8021p_map_rpcdata *, qcsapi_wifi_get_dscp_8021p_map_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_dscp_8021p_map_remote_1_svc(qcsapi_wifi_get_dscp_8021p_map_rpcdata *, qcsapi_wifi_get_dscp_8021p_map_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_DSCP_AC_MAP_REMOTE 1051
extern  enum clnt_stat qcsapi_wifi_get_dscp_ac_map_remote_1(qcsapi_wifi_get_dscp_ac_map_rpcdata *, qcsapi_wifi_get_dscp_ac_map_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_dscp_ac_map_remote_1_svc(qcsapi_wifi_get_dscp_ac_map_rpcdata *, qcsapi_wifi_get_dscp_ac_map_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_DSCP_8021P_MAP_REMOTE 1061
extern  enum clnt_stat qcsapi_wifi_set_dscp_8021p_map_remote_1(qcsapi_wifi_set_dscp_8021p_map_rpcdata *, qcsapi_wifi_set_dscp_8021p_map_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_dscp_8021p_map_remote_1_svc(qcsapi_wifi_set_dscp_8021p_map_rpcdata *, qcsapi_wifi_set_dscp_8021p_map_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_DSCP_AC_MAP_REMOTE 1071
extern  enum clnt_stat qcsapi_wifi_set_dscp_ac_map_remote_1(qcsapi_wifi_set_dscp_ac_map_rpcdata *, qcsapi_wifi_set_dscp_ac_map_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_dscp_ac_map_remote_1_svc(qcsapi_wifi_set_dscp_ac_map_rpcdata *, qcsapi_wifi_set_dscp_ac_map_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_PRIORITY_REMOTE 1081
extern  enum clnt_stat qcsapi_wifi_get_priority_remote_1(qcsapi_wifi_get_priority_rpcdata *, qcsapi_wifi_get_priority_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_priority_remote_1_svc(qcsapi_wifi_get_priority_rpcdata *, qcsapi_wifi_get_priority_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_PRIORITY_REMOTE 1091
extern  enum clnt_stat qcsapi_wifi_set_priority_remote_1(qcsapi_wifi_set_priority_rpcdata *, qcsapi_wifi_set_priority_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_priority_remote_1_svc(qcsapi_wifi_set_priority_rpcdata *, qcsapi_wifi_set_priority_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_AIRFAIR_REMOTE 1101
extern  enum clnt_stat qcsapi_wifi_get_airfair_remote_1(qcsapi_wifi_get_airfair_rpcdata *, qcsapi_wifi_get_airfair_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_airfair_remote_1_svc(qcsapi_wifi_get_airfair_rpcdata *, qcsapi_wifi_get_airfair_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_AIRFAIR_REMOTE 1111
extern  enum clnt_stat qcsapi_wifi_set_airfair_remote_1(qcsapi_wifi_set_airfair_rpcdata *, qcsapi_wifi_set_airfair_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_airfair_remote_1_svc(qcsapi_wifi_set_airfair_rpcdata *, qcsapi_wifi_set_airfair_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_TX_POWER_REMOTE 1211
extern  enum clnt_stat qcsapi_wifi_get_tx_power_remote_1(qcsapi_wifi_get_tx_power_rpcdata *, qcsapi_wifi_get_tx_power_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_tx_power_remote_1_svc(qcsapi_wifi_get_tx_power_rpcdata *, qcsapi_wifi_get_tx_power_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_TX_POWER_REMOTE 1221
extern  enum clnt_stat qcsapi_wifi_set_tx_power_remote_1(qcsapi_wifi_set_tx_power_rpcdata *, qcsapi_wifi_set_tx_power_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_tx_power_remote_1_svc(qcsapi_wifi_set_tx_power_rpcdata *, qcsapi_wifi_set_tx_power_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_BW_POWER_REMOTE 1231
extern  enum clnt_stat qcsapi_wifi_get_bw_power_remote_1(qcsapi_wifi_get_bw_power_rpcdata *, qcsapi_wifi_get_bw_power_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_bw_power_remote_1_svc(qcsapi_wifi_get_bw_power_rpcdata *, qcsapi_wifi_get_bw_power_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_BW_POWER_REMOTE 1241
extern  enum clnt_stat qcsapi_wifi_set_bw_power_remote_1(qcsapi_wifi_set_bw_power_rpcdata *, qcsapi_wifi_set_bw_power_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_bw_power_remote_1_svc(qcsapi_wifi_set_bw_power_rpcdata *, qcsapi_wifi_set_bw_power_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_BF_POWER_REMOTE 1261
extern  enum clnt_stat qcsapi_wifi_get_bf_power_remote_1(qcsapi_wifi_get_bf_power_rpcdata *, qcsapi_wifi_get_bf_power_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_bf_power_remote_1_svc(qcsapi_wifi_get_bf_power_rpcdata *, qcsapi_wifi_get_bf_power_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_BF_POWER_REMOTE 1271
extern  enum clnt_stat qcsapi_wifi_set_bf_power_remote_1(qcsapi_wifi_set_bf_power_rpcdata *, qcsapi_wifi_set_bf_power_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_bf_power_remote_1_svc(qcsapi_wifi_set_bf_power_rpcdata *, qcsapi_wifi_set_bf_power_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_TX_POWER_EXT_REMOTE 4541
extern  enum clnt_stat qcsapi_wifi_get_tx_power_ext_remote_1(qcsapi_wifi_get_tx_power_ext_rpcdata *, qcsapi_wifi_get_tx_power_ext_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_tx_power_ext_remote_1_svc(qcsapi_wifi_get_tx_power_ext_rpcdata *, qcsapi_wifi_get_tx_power_ext_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_TX_POWER_EXT_REMOTE 4551
extern  enum clnt_stat qcsapi_wifi_set_tx_power_ext_remote_1(qcsapi_wifi_set_tx_power_ext_rpcdata *, qcsapi_wifi_set_tx_power_ext_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_tx_power_ext_remote_1_svc(qcsapi_wifi_set_tx_power_ext_rpcdata *, qcsapi_wifi_set_tx_power_ext_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_CHAN_POWER_TABLE_REMOTE 6151
extern  enum clnt_stat qcsapi_wifi_get_chan_power_table_remote_1(qcsapi_wifi_get_chan_power_table_rpcdata *, qcsapi_wifi_get_chan_power_table_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_chan_power_table_remote_1_svc(qcsapi_wifi_get_chan_power_table_rpcdata *, qcsapi_wifi_get_chan_power_table_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_CHAN_POWER_TABLE_REMOTE 6161
extern  enum clnt_stat qcsapi_wifi_set_chan_power_table_remote_1(qcsapi_wifi_set_chan_power_table_rpcdata *, qcsapi_wifi_set_chan_power_table_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_chan_power_table_remote_1_svc(qcsapi_wifi_set_chan_power_table_rpcdata *, qcsapi_wifi_set_chan_power_table_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_POWER_SELECTION_REMOTE 4471
extern  enum clnt_stat qcsapi_wifi_get_power_selection_remote_1(qcsapi_wifi_get_power_selection_rpcdata *, qcsapi_wifi_get_power_selection_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_power_selection_remote_1_svc(qcsapi_wifi_get_power_selection_rpcdata *, qcsapi_wifi_get_power_selection_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_POWER_SELECTION_REMOTE 4481
extern  enum clnt_stat qcsapi_wifi_set_power_selection_remote_1(qcsapi_wifi_set_power_selection_rpcdata *, qcsapi_wifi_set_power_selection_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_power_selection_remote_1_svc(qcsapi_wifi_set_power_selection_rpcdata *, qcsapi_wifi_set_power_selection_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_CARRIER_INTERFERENCE_REMOTE 1291
extern  enum clnt_stat qcsapi_wifi_get_carrier_interference_remote_1(qcsapi_wifi_get_carrier_interference_rpcdata *, qcsapi_wifi_get_carrier_interference_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_carrier_interference_remote_1_svc(qcsapi_wifi_get_carrier_interference_rpcdata *, qcsapi_wifi_get_carrier_interference_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_CONGESTION_INDEX_REMOTE 1301
extern  enum clnt_stat qcsapi_wifi_get_congestion_index_remote_1(qcsapi_wifi_get_congestion_index_rpcdata *, qcsapi_wifi_get_congestion_index_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_congestion_index_remote_1_svc(qcsapi_wifi_get_congestion_index_rpcdata *, qcsapi_wifi_get_congestion_index_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_SUPPORTED_TX_POWER_LEVELS_REMOTE 1311
extern  enum clnt_stat qcsapi_wifi_get_supported_tx_power_levels_remote_1(qcsapi_wifi_get_supported_tx_power_levels_rpcdata *, qcsapi_wifi_get_supported_tx_power_levels_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_supported_tx_power_levels_remote_1_svc(qcsapi_wifi_get_supported_tx_power_levels_rpcdata *, qcsapi_wifi_get_supported_tx_power_levels_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_CURRENT_TX_POWER_LEVEL_REMOTE 1321
extern  enum clnt_stat qcsapi_wifi_get_current_tx_power_level_remote_1(qcsapi_wifi_get_current_tx_power_level_rpcdata *, qcsapi_wifi_get_current_tx_power_level_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_current_tx_power_level_remote_1_svc(qcsapi_wifi_get_current_tx_power_level_rpcdata *, qcsapi_wifi_get_current_tx_power_level_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_POWER_CONSTRAINT_REMOTE 1331
extern  enum clnt_stat qcsapi_wifi_set_power_constraint_remote_1(qcsapi_wifi_set_power_constraint_rpcdata *, qcsapi_wifi_set_power_constraint_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_power_constraint_remote_1_svc(qcsapi_wifi_set_power_constraint_rpcdata *, qcsapi_wifi_set_power_constraint_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_POWER_CONSTRAINT_REMOTE 1341
extern  enum clnt_stat qcsapi_wifi_get_power_constraint_remote_1(qcsapi_wifi_get_power_constraint_rpcdata *, qcsapi_wifi_get_power_constraint_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_power_constraint_remote_1_svc(qcsapi_wifi_get_power_constraint_rpcdata *, qcsapi_wifi_get_power_constraint_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_TPC_INTERVAL_REMOTE 1351
extern  enum clnt_stat qcsapi_wifi_set_tpc_interval_remote_1(qcsapi_wifi_set_tpc_interval_rpcdata *, qcsapi_wifi_set_tpc_interval_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_tpc_interval_remote_1_svc(qcsapi_wifi_set_tpc_interval_rpcdata *, qcsapi_wifi_set_tpc_interval_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_TPC_INTERVAL_REMOTE 1361
extern  enum clnt_stat qcsapi_wifi_get_tpc_interval_remote_1(qcsapi_wifi_get_tpc_interval_rpcdata *, qcsapi_wifi_get_tpc_interval_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_tpc_interval_remote_1_svc(qcsapi_wifi_get_tpc_interval_rpcdata *, qcsapi_wifi_get_tpc_interval_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_ASSOC_RECORDS_REMOTE 1371
extern  enum clnt_stat qcsapi_wifi_get_assoc_records_remote_1(qcsapi_wifi_get_assoc_records_rpcdata *, qcsapi_wifi_get_assoc_records_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_assoc_records_remote_1_svc(qcsapi_wifi_get_assoc_records_rpcdata *, qcsapi_wifi_get_assoc_records_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_AP_ISOLATE_REMOTE 1381
extern  enum clnt_stat qcsapi_wifi_get_ap_isolate_remote_1(qcsapi_wifi_get_ap_isolate_rpcdata *, qcsapi_wifi_get_ap_isolate_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_ap_isolate_remote_1_svc(qcsapi_wifi_get_ap_isolate_rpcdata *, qcsapi_wifi_get_ap_isolate_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_AP_ISOLATE_REMOTE 1391
extern  enum clnt_stat qcsapi_wifi_set_ap_isolate_remote_1(qcsapi_wifi_set_ap_isolate_rpcdata *, qcsapi_wifi_set_ap_isolate_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_ap_isolate_remote_1_svc(qcsapi_wifi_set_ap_isolate_rpcdata *, qcsapi_wifi_set_ap_isolate_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_INTRA_BSS_ISOLATE_REMOTE 1401
extern  enum clnt_stat qcsapi_wifi_get_intra_bss_isolate_remote_1(qcsapi_wifi_get_intra_bss_isolate_rpcdata *, qcsapi_wifi_get_intra_bss_isolate_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_intra_bss_isolate_remote_1_svc(qcsapi_wifi_get_intra_bss_isolate_rpcdata *, qcsapi_wifi_get_intra_bss_isolate_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_INTRA_BSS_ISOLATE_REMOTE 1411
extern  enum clnt_stat qcsapi_wifi_set_intra_bss_isolate_remote_1(qcsapi_wifi_set_intra_bss_isolate_rpcdata *, qcsapi_wifi_set_intra_bss_isolate_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_intra_bss_isolate_remote_1_svc(qcsapi_wifi_set_intra_bss_isolate_rpcdata *, qcsapi_wifi_set_intra_bss_isolate_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_BSS_ISOLATE_REMOTE 1421
extern  enum clnt_stat qcsapi_wifi_get_bss_isolate_remote_1(qcsapi_wifi_get_bss_isolate_rpcdata *, qcsapi_wifi_get_bss_isolate_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_bss_isolate_remote_1_svc(qcsapi_wifi_get_bss_isolate_rpcdata *, qcsapi_wifi_get_bss_isolate_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_BSS_ISOLATE_REMOTE 1431
extern  enum clnt_stat qcsapi_wifi_set_bss_isolate_remote_1(qcsapi_wifi_set_bss_isolate_rpcdata *, qcsapi_wifi_set_bss_isolate_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_bss_isolate_remote_1_svc(qcsapi_wifi_set_bss_isolate_rpcdata *, qcsapi_wifi_set_bss_isolate_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_DISABLE_DFS_CHANNELS_REMOTE 4061
extern  enum clnt_stat qcsapi_wifi_disable_dfs_channels_remote_1(qcsapi_wifi_disable_dfs_channels_rpcdata *, qcsapi_wifi_disable_dfs_channels_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_disable_dfs_channels_remote_1_svc(qcsapi_wifi_disable_dfs_channels_rpcdata *, qcsapi_wifi_disable_dfs_channels_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_CREATE_RESTRICTED_BSS_REMOTE 1441
extern  enum clnt_stat qcsapi_wifi_create_restricted_bss_remote_1(qcsapi_wifi_create_restricted_bss_rpcdata *, qcsapi_wifi_create_restricted_bss_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_create_restricted_bss_remote_1_svc(qcsapi_wifi_create_restricted_bss_rpcdata *, qcsapi_wifi_create_restricted_bss_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_CREATE_BSS_REMOTE 1451
extern  enum clnt_stat qcsapi_wifi_create_bss_remote_1(qcsapi_wifi_create_bss_rpcdata *, qcsapi_wifi_create_bss_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_create_bss_remote_1_svc(qcsapi_wifi_create_bss_rpcdata *, qcsapi_wifi_create_bss_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_REMOVE_BSS_REMOTE 1461
extern  enum clnt_stat qcsapi_wifi_remove_bss_remote_1(qcsapi_wifi_remove_bss_rpcdata *, qcsapi_wifi_remove_bss_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_remove_bss_remote_1_svc(qcsapi_wifi_remove_bss_rpcdata *, qcsapi_wifi_remove_bss_rpcdata *, struct svc_req *);
#define QCSAPI_WDS_ADD_PEER_REMOTE 1471
extern  enum clnt_stat qcsapi_wds_add_peer_remote_1(qcsapi_wds_add_peer_rpcdata *, qcsapi_wds_add_peer_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wds_add_peer_remote_1_svc(qcsapi_wds_add_peer_rpcdata *, qcsapi_wds_add_peer_rpcdata *, struct svc_req *);
#define QCSAPI_WDS_ADD_PEER_ENCRYPT_REMOTE 1481
extern  enum clnt_stat qcsapi_wds_add_peer_encrypt_remote_1(qcsapi_wds_add_peer_encrypt_rpcdata *, qcsapi_wds_add_peer_encrypt_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wds_add_peer_encrypt_remote_1_svc(qcsapi_wds_add_peer_encrypt_rpcdata *, qcsapi_wds_add_peer_encrypt_rpcdata *, struct svc_req *);
#define QCSAPI_WDS_REMOVE_PEER_REMOTE 1491
extern  enum clnt_stat qcsapi_wds_remove_peer_remote_1(qcsapi_wds_remove_peer_rpcdata *, qcsapi_wds_remove_peer_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wds_remove_peer_remote_1_svc(qcsapi_wds_remove_peer_rpcdata *, qcsapi_wds_remove_peer_rpcdata *, struct svc_req *);
#define QCSAPI_WDS_GET_PEER_ADDRESS_REMOTE 1501
extern  enum clnt_stat qcsapi_wds_get_peer_address_remote_1(qcsapi_wds_get_peer_address_rpcdata *, qcsapi_wds_get_peer_address_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wds_get_peer_address_remote_1_svc(qcsapi_wds_get_peer_address_rpcdata *, qcsapi_wds_get_peer_address_rpcdata *, struct svc_req *);
#define QCSAPI_WDS_SET_PSK_REMOTE 1511
extern  enum clnt_stat qcsapi_wds_set_psk_remote_1(qcsapi_wds_set_psk_rpcdata *, qcsapi_wds_set_psk_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wds_set_psk_remote_1_svc(qcsapi_wds_set_psk_rpcdata *, qcsapi_wds_set_psk_rpcdata *, struct svc_req *);
#define QCSAPI_WDS_SET_MODE_REMOTE 1521
extern  enum clnt_stat qcsapi_wds_set_mode_remote_1(qcsapi_wds_set_mode_rpcdata *, qcsapi_wds_set_mode_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wds_set_mode_remote_1_svc(qcsapi_wds_set_mode_rpcdata *, qcsapi_wds_set_mode_rpcdata *, struct svc_req *);
#define QCSAPI_WDS_GET_MODE_REMOTE 1531
extern  enum clnt_stat qcsapi_wds_get_mode_remote_1(qcsapi_wds_get_mode_rpcdata *, qcsapi_wds_get_mode_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wds_get_mode_remote_1_svc(qcsapi_wds_get_mode_rpcdata *, qcsapi_wds_get_mode_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_EXTENDER_PARAMS_REMOTE 1541
extern  enum clnt_stat qcsapi_wifi_set_extender_params_remote_1(qcsapi_wifi_set_extender_params_rpcdata *, qcsapi_wifi_set_extender_params_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_extender_params_remote_1_svc(qcsapi_wifi_set_extender_params_rpcdata *, qcsapi_wifi_set_extender_params_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_EXTENDER_PARAMS_REMOTE 1551
extern  enum clnt_stat qcsapi_wifi_get_extender_params_remote_1(qcsapi_wifi_get_extender_params_rpcdata *, qcsapi_wifi_get_extender_params_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_extender_params_remote_1_svc(qcsapi_wifi_get_extender_params_rpcdata *, qcsapi_wifi_get_extender_params_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_BEACON_TYPE_REMOTE 1581
extern  enum clnt_stat qcsapi_wifi_get_beacon_type_remote_1(qcsapi_wifi_get_beacon_type_rpcdata *, qcsapi_wifi_get_beacon_type_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_beacon_type_remote_1_svc(qcsapi_wifi_get_beacon_type_rpcdata *, qcsapi_wifi_get_beacon_type_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_BEACON_TYPE_REMOTE 1591
extern  enum clnt_stat qcsapi_wifi_set_beacon_type_remote_1(qcsapi_wifi_set_beacon_type_rpcdata *, qcsapi_wifi_set_beacon_type_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_beacon_type_remote_1_svc(qcsapi_wifi_set_beacon_type_rpcdata *, qcsapi_wifi_set_beacon_type_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_WEP_KEY_INDEX_REMOTE 1601
extern  enum clnt_stat qcsapi_wifi_get_wep_key_index_remote_1(qcsapi_wifi_get_WEP_key_index_rpcdata *, qcsapi_wifi_get_WEP_key_index_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_wep_key_index_remote_1_svc(qcsapi_wifi_get_WEP_key_index_rpcdata *, qcsapi_wifi_get_WEP_key_index_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_WEP_KEY_INDEX_REMOTE 1611
extern  enum clnt_stat qcsapi_wifi_set_wep_key_index_remote_1(qcsapi_wifi_set_WEP_key_index_rpcdata *, qcsapi_wifi_set_WEP_key_index_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_wep_key_index_remote_1_svc(qcsapi_wifi_set_WEP_key_index_rpcdata *, qcsapi_wifi_set_WEP_key_index_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_WEP_KEY_PASSPHRASE_REMOTE 1621
extern  enum clnt_stat qcsapi_wifi_get_wep_key_passphrase_remote_1(qcsapi_wifi_get_WEP_key_passphrase_rpcdata *, qcsapi_wifi_get_WEP_key_passphrase_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_wep_key_passphrase_remote_1_svc(qcsapi_wifi_get_WEP_key_passphrase_rpcdata *, qcsapi_wifi_get_WEP_key_passphrase_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_WEP_KEY_PASSPHRASE_REMOTE 1631
extern  enum clnt_stat qcsapi_wifi_set_wep_key_passphrase_remote_1(qcsapi_wifi_set_WEP_key_passphrase_rpcdata *, qcsapi_wifi_set_WEP_key_passphrase_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_wep_key_passphrase_remote_1_svc(qcsapi_wifi_set_WEP_key_passphrase_rpcdata *, qcsapi_wifi_set_WEP_key_passphrase_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_WEP_ENCRYPTION_LEVEL_REMOTE 1641
extern  enum clnt_stat qcsapi_wifi_get_wep_encryption_level_remote_1(qcsapi_wifi_get_WEP_encryption_level_rpcdata *, qcsapi_wifi_get_WEP_encryption_level_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_wep_encryption_level_remote_1_svc(qcsapi_wifi_get_WEP_encryption_level_rpcdata *, qcsapi_wifi_get_WEP_encryption_level_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_BASIC_ENCRYPTION_MODES_REMOTE 1651
extern  enum clnt_stat qcsapi_wifi_get_basic_encryption_modes_remote_1(qcsapi_wifi_get_basic_encryption_modes_rpcdata *, qcsapi_wifi_get_basic_encryption_modes_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_basic_encryption_modes_remote_1_svc(qcsapi_wifi_get_basic_encryption_modes_rpcdata *, qcsapi_wifi_get_basic_encryption_modes_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_BASIC_ENCRYPTION_MODES_REMOTE 1661
extern  enum clnt_stat qcsapi_wifi_set_basic_encryption_modes_remote_1(qcsapi_wifi_set_basic_encryption_modes_rpcdata *, qcsapi_wifi_set_basic_encryption_modes_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_basic_encryption_modes_remote_1_svc(qcsapi_wifi_set_basic_encryption_modes_rpcdata *, qcsapi_wifi_set_basic_encryption_modes_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_BASIC_AUTHENTICATION_MODE_REMOTE 1671
extern  enum clnt_stat qcsapi_wifi_get_basic_authentication_mode_remote_1(qcsapi_wifi_get_basic_authentication_mode_rpcdata *, qcsapi_wifi_get_basic_authentication_mode_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_basic_authentication_mode_remote_1_svc(qcsapi_wifi_get_basic_authentication_mode_rpcdata *, qcsapi_wifi_get_basic_authentication_mode_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_BASIC_AUTHENTICATION_MODE_REMOTE 1681
extern  enum clnt_stat qcsapi_wifi_set_basic_authentication_mode_remote_1(qcsapi_wifi_set_basic_authentication_mode_rpcdata *, qcsapi_wifi_set_basic_authentication_mode_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_basic_authentication_mode_remote_1_svc(qcsapi_wifi_set_basic_authentication_mode_rpcdata *, qcsapi_wifi_set_basic_authentication_mode_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_WEP_KEY_REMOTE 1691
extern  enum clnt_stat qcsapi_wifi_get_wep_key_remote_1(qcsapi_wifi_get_WEP_key_rpcdata *, qcsapi_wifi_get_WEP_key_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_wep_key_remote_1_svc(qcsapi_wifi_get_WEP_key_rpcdata *, qcsapi_wifi_get_WEP_key_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_WEP_KEY_REMOTE 1701
extern  enum clnt_stat qcsapi_wifi_set_wep_key_remote_1(qcsapi_wifi_set_WEP_key_rpcdata *, qcsapi_wifi_set_WEP_key_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_wep_key_remote_1_svc(qcsapi_wifi_set_WEP_key_rpcdata *, qcsapi_wifi_set_WEP_key_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_WPA_ENCRYPTION_MODES_REMOTE 1711
extern  enum clnt_stat qcsapi_wifi_get_wpa_encryption_modes_remote_1(qcsapi_wifi_get_WPA_encryption_modes_rpcdata *, qcsapi_wifi_get_WPA_encryption_modes_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_wpa_encryption_modes_remote_1_svc(qcsapi_wifi_get_WPA_encryption_modes_rpcdata *, qcsapi_wifi_get_WPA_encryption_modes_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_WPA_ENCRYPTION_MODES_REMOTE 1721
extern  enum clnt_stat qcsapi_wifi_set_wpa_encryption_modes_remote_1(qcsapi_wifi_set_WPA_encryption_modes_rpcdata *, qcsapi_wifi_set_WPA_encryption_modes_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_wpa_encryption_modes_remote_1_svc(qcsapi_wifi_set_WPA_encryption_modes_rpcdata *, qcsapi_wifi_set_WPA_encryption_modes_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_WPA_AUTHENTICATION_MODE_REMOTE 1731
extern  enum clnt_stat qcsapi_wifi_get_wpa_authentication_mode_remote_1(qcsapi_wifi_get_WPA_authentication_mode_rpcdata *, qcsapi_wifi_get_WPA_authentication_mode_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_wpa_authentication_mode_remote_1_svc(qcsapi_wifi_get_WPA_authentication_mode_rpcdata *, qcsapi_wifi_get_WPA_authentication_mode_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_WPA_AUTHENTICATION_MODE_REMOTE 1741
extern  enum clnt_stat qcsapi_wifi_set_wpa_authentication_mode_remote_1(qcsapi_wifi_set_WPA_authentication_mode_rpcdata *, qcsapi_wifi_set_WPA_authentication_mode_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_wpa_authentication_mode_remote_1_svc(qcsapi_wifi_set_WPA_authentication_mode_rpcdata *, qcsapi_wifi_set_WPA_authentication_mode_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_INTERWORKING_REMOTE 5451
extern  enum clnt_stat qcsapi_wifi_get_interworking_remote_1(qcsapi_wifi_get_interworking_rpcdata *, qcsapi_wifi_get_interworking_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_interworking_remote_1_svc(qcsapi_wifi_get_interworking_rpcdata *, qcsapi_wifi_get_interworking_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_INTERWORKING_REMOTE 5461
extern  enum clnt_stat qcsapi_wifi_set_interworking_remote_1(qcsapi_wifi_set_interworking_rpcdata *, qcsapi_wifi_set_interworking_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_interworking_remote_1_svc(qcsapi_wifi_set_interworking_rpcdata *, qcsapi_wifi_set_interworking_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_80211U_PARAMS_REMOTE 5471
extern  enum clnt_stat qcsapi_wifi_get_80211u_params_remote_1(qcsapi_wifi_get_80211u_params_rpcdata *, qcsapi_wifi_get_80211u_params_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_80211u_params_remote_1_svc(qcsapi_wifi_get_80211u_params_rpcdata *, qcsapi_wifi_get_80211u_params_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_80211U_PARAMS_REMOTE 5481
extern  enum clnt_stat qcsapi_wifi_set_80211u_params_remote_1(qcsapi_wifi_set_80211u_params_rpcdata *, qcsapi_wifi_set_80211u_params_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_80211u_params_remote_1_svc(qcsapi_wifi_set_80211u_params_rpcdata *, qcsapi_wifi_set_80211u_params_rpcdata *, struct svc_req *);
#define QCSAPI_SECURITY_GET_NAI_REALMS_REMOTE 5491
extern  enum clnt_stat qcsapi_security_get_nai_realms_remote_1(qcsapi_security_get_nai_realms_rpcdata *, qcsapi_security_get_nai_realms_rpcdata *, CLIENT *);
extern  bool_t qcsapi_security_get_nai_realms_remote_1_svc(qcsapi_security_get_nai_realms_rpcdata *, qcsapi_security_get_nai_realms_rpcdata *, struct svc_req *);
#define QCSAPI_SECURITY_ADD_NAI_REALM_REMOTE 5501
extern  enum clnt_stat qcsapi_security_add_nai_realm_remote_1(qcsapi_security_add_nai_realm_rpcdata *, qcsapi_security_add_nai_realm_rpcdata *, CLIENT *);
extern  bool_t qcsapi_security_add_nai_realm_remote_1_svc(qcsapi_security_add_nai_realm_rpcdata *, qcsapi_security_add_nai_realm_rpcdata *, struct svc_req *);
#define QCSAPI_SECURITY_DEL_NAI_REALM_REMOTE 5511
extern  enum clnt_stat qcsapi_security_del_nai_realm_remote_1(qcsapi_security_del_nai_realm_rpcdata *, qcsapi_security_del_nai_realm_rpcdata *, CLIENT *);
extern  bool_t qcsapi_security_del_nai_realm_remote_1_svc(qcsapi_security_del_nai_realm_rpcdata *, qcsapi_security_del_nai_realm_rpcdata *, struct svc_req *);
#define QCSAPI_SECURITY_GET_ROAMING_CONSORTIUM_REMOTE 5521
extern  enum clnt_stat qcsapi_security_get_roaming_consortium_remote_1(qcsapi_security_get_roaming_consortium_rpcdata *, qcsapi_security_get_roaming_consortium_rpcdata *, CLIENT *);
extern  bool_t qcsapi_security_get_roaming_consortium_remote_1_svc(qcsapi_security_get_roaming_consortium_rpcdata *, qcsapi_security_get_roaming_consortium_rpcdata *, struct svc_req *);
#define QCSAPI_SECURITY_ADD_ROAMING_CONSORTIUM_REMOTE 5531
extern  enum clnt_stat qcsapi_security_add_roaming_consortium_remote_1(qcsapi_security_add_roaming_consortium_rpcdata *, qcsapi_security_add_roaming_consortium_rpcdata *, CLIENT *);
extern  bool_t qcsapi_security_add_roaming_consortium_remote_1_svc(qcsapi_security_add_roaming_consortium_rpcdata *, qcsapi_security_add_roaming_consortium_rpcdata *, struct svc_req *);
#define QCSAPI_SECURITY_DEL_ROAMING_CONSORTIUM_REMOTE 5541
extern  enum clnt_stat qcsapi_security_del_roaming_consortium_remote_1(qcsapi_security_del_roaming_consortium_rpcdata *, qcsapi_security_del_roaming_consortium_rpcdata *, CLIENT *);
extern  bool_t qcsapi_security_del_roaming_consortium_remote_1_svc(qcsapi_security_del_roaming_consortium_rpcdata *, qcsapi_security_del_roaming_consortium_rpcdata *, struct svc_req *);
#define QCSAPI_SECURITY_GET_VENUE_NAME_REMOTE 5551
extern  enum clnt_stat qcsapi_security_get_venue_name_remote_1(qcsapi_security_get_venue_name_rpcdata *, qcsapi_security_get_venue_name_rpcdata *, CLIENT *);
extern  bool_t qcsapi_security_get_venue_name_remote_1_svc(qcsapi_security_get_venue_name_rpcdata *, qcsapi_security_get_venue_name_rpcdata *, struct svc_req *);
#define QCSAPI_SECURITY_ADD_VENUE_NAME_REMOTE 5561
extern  enum clnt_stat qcsapi_security_add_venue_name_remote_1(qcsapi_security_add_venue_name_rpcdata *, qcsapi_security_add_venue_name_rpcdata *, CLIENT *);
extern  bool_t qcsapi_security_add_venue_name_remote_1_svc(qcsapi_security_add_venue_name_rpcdata *, qcsapi_security_add_venue_name_rpcdata *, struct svc_req *);
#define QCSAPI_SECURITY_DEL_VENUE_NAME_REMOTE 5731
extern  enum clnt_stat qcsapi_security_del_venue_name_remote_1(qcsapi_security_del_venue_name_rpcdata *, qcsapi_security_del_venue_name_rpcdata *, CLIENT *);
extern  bool_t qcsapi_security_del_venue_name_remote_1_svc(qcsapi_security_del_venue_name_rpcdata *, qcsapi_security_del_venue_name_rpcdata *, struct svc_req *);
#define QCSAPI_SECURITY_GET_OPER_FRIENDLY_NAME_REMOTE 5741
extern  enum clnt_stat qcsapi_security_get_oper_friendly_name_remote_1(qcsapi_security_get_oper_friendly_name_rpcdata *, qcsapi_security_get_oper_friendly_name_rpcdata *, CLIENT *);
extern  bool_t qcsapi_security_get_oper_friendly_name_remote_1_svc(qcsapi_security_get_oper_friendly_name_rpcdata *, qcsapi_security_get_oper_friendly_name_rpcdata *, struct svc_req *);
#define QCSAPI_SECURITY_ADD_OPER_FRIENDLY_NAME_REMOTE 5751
extern  enum clnt_stat qcsapi_security_add_oper_friendly_name_remote_1(qcsapi_security_add_oper_friendly_name_rpcdata *, qcsapi_security_add_oper_friendly_name_rpcdata *, CLIENT *);
extern  bool_t qcsapi_security_add_oper_friendly_name_remote_1_svc(qcsapi_security_add_oper_friendly_name_rpcdata *, qcsapi_security_add_oper_friendly_name_rpcdata *, struct svc_req *);
#define QCSAPI_SECURITY_DEL_OPER_FRIENDLY_NAME_REMOTE 5761
extern  enum clnt_stat qcsapi_security_del_oper_friendly_name_remote_1(qcsapi_security_del_oper_friendly_name_rpcdata *, qcsapi_security_del_oper_friendly_name_rpcdata *, CLIENT *);
extern  bool_t qcsapi_security_del_oper_friendly_name_remote_1_svc(qcsapi_security_del_oper_friendly_name_rpcdata *, qcsapi_security_del_oper_friendly_name_rpcdata *, struct svc_req *);
#define QCSAPI_SECURITY_GET_HS20_CONN_CAPAB_REMOTE 5771
extern  enum clnt_stat qcsapi_security_get_hs20_conn_capab_remote_1(qcsapi_security_get_hs20_conn_capab_rpcdata *, qcsapi_security_get_hs20_conn_capab_rpcdata *, CLIENT *);
extern  bool_t qcsapi_security_get_hs20_conn_capab_remote_1_svc(qcsapi_security_get_hs20_conn_capab_rpcdata *, qcsapi_security_get_hs20_conn_capab_rpcdata *, struct svc_req *);
#define QCSAPI_SECURITY_ADD_HS20_CONN_CAPAB_REMOTE 5781
extern  enum clnt_stat qcsapi_security_add_hs20_conn_capab_remote_1(qcsapi_security_add_hs20_conn_capab_rpcdata *, qcsapi_security_add_hs20_conn_capab_rpcdata *, CLIENT *);
extern  bool_t qcsapi_security_add_hs20_conn_capab_remote_1_svc(qcsapi_security_add_hs20_conn_capab_rpcdata *, qcsapi_security_add_hs20_conn_capab_rpcdata *, struct svc_req *);
#define QCSAPI_SECURITY_DEL_HS20_CONN_CAPAB_REMOTE 5791
extern  enum clnt_stat qcsapi_security_del_hs20_conn_capab_remote_1(qcsapi_security_del_hs20_conn_capab_rpcdata *, qcsapi_security_del_hs20_conn_capab_rpcdata *, CLIENT *);
extern  bool_t qcsapi_security_del_hs20_conn_capab_remote_1_svc(qcsapi_security_del_hs20_conn_capab_rpcdata *, qcsapi_security_del_hs20_conn_capab_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_HS20_STATUS_REMOTE 5571
extern  enum clnt_stat qcsapi_wifi_get_hs20_status_remote_1(qcsapi_wifi_get_hs20_status_rpcdata *, qcsapi_wifi_get_hs20_status_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_hs20_status_remote_1_svc(qcsapi_wifi_get_hs20_status_rpcdata *, qcsapi_wifi_get_hs20_status_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_HS20_STATUS_REMOTE 5581
extern  enum clnt_stat qcsapi_wifi_set_hs20_status_remote_1(qcsapi_wifi_set_hs20_status_rpcdata *, qcsapi_wifi_set_hs20_status_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_hs20_status_remote_1_svc(qcsapi_wifi_set_hs20_status_rpcdata *, qcsapi_wifi_set_hs20_status_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_PROXY_ARP_REMOTE 5641
extern  enum clnt_stat qcsapi_wifi_get_proxy_arp_remote_1(qcsapi_wifi_get_proxy_arp_rpcdata *, qcsapi_wifi_get_proxy_arp_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_proxy_arp_remote_1_svc(qcsapi_wifi_get_proxy_arp_rpcdata *, qcsapi_wifi_get_proxy_arp_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_PROXY_ARP_REMOTE 5631
extern  enum clnt_stat qcsapi_wifi_set_proxy_arp_remote_1(qcsapi_wifi_set_proxy_arp_rpcdata *, qcsapi_wifi_set_proxy_arp_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_proxy_arp_remote_1_svc(qcsapi_wifi_set_proxy_arp_rpcdata *, qcsapi_wifi_set_proxy_arp_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_L2_EXT_FILTER_REMOTE 5941
extern  enum clnt_stat qcsapi_wifi_get_l2_ext_filter_remote_1(qcsapi_wifi_get_l2_ext_filter_rpcdata *, qcsapi_wifi_get_l2_ext_filter_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_l2_ext_filter_remote_1_svc(qcsapi_wifi_get_l2_ext_filter_rpcdata *, qcsapi_wifi_get_l2_ext_filter_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_L2_EXT_FILTER_REMOTE 5951
extern  enum clnt_stat qcsapi_wifi_set_l2_ext_filter_remote_1(qcsapi_wifi_set_l2_ext_filter_rpcdata *, qcsapi_wifi_set_l2_ext_filter_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_l2_ext_filter_remote_1_svc(qcsapi_wifi_set_l2_ext_filter_rpcdata *, qcsapi_wifi_set_l2_ext_filter_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_HS20_PARAMS_REMOTE 5591
extern  enum clnt_stat qcsapi_wifi_get_hs20_params_remote_1(qcsapi_wifi_get_hs20_params_rpcdata *, qcsapi_wifi_get_hs20_params_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_hs20_params_remote_1_svc(qcsapi_wifi_get_hs20_params_rpcdata *, qcsapi_wifi_get_hs20_params_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_HS20_PARAMS_REMOTE 5601
extern  enum clnt_stat qcsapi_wifi_set_hs20_params_remote_1(qcsapi_wifi_set_hs20_params_rpcdata *, qcsapi_wifi_set_hs20_params_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_hs20_params_remote_1_svc(qcsapi_wifi_set_hs20_params_rpcdata *, qcsapi_wifi_set_hs20_params_rpcdata *, struct svc_req *);
#define QCSAPI_REMOVE_11U_PARAM_REMOTE 5611
extern  enum clnt_stat qcsapi_remove_11u_param_remote_1(qcsapi_remove_11u_param_rpcdata *, qcsapi_remove_11u_param_rpcdata *, CLIENT *);
extern  bool_t qcsapi_remove_11u_param_remote_1_svc(qcsapi_remove_11u_param_rpcdata *, qcsapi_remove_11u_param_rpcdata *, struct svc_req *);
#define QCSAPI_REMOVE_HS20_PARAM_REMOTE 5621
extern  enum clnt_stat qcsapi_remove_hs20_param_remote_1(qcsapi_remove_hs20_param_rpcdata *, qcsapi_remove_hs20_param_rpcdata *, CLIENT *);
extern  bool_t qcsapi_remove_hs20_param_remote_1_svc(qcsapi_remove_hs20_param_rpcdata *, qcsapi_remove_hs20_param_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_IEEE11I_ENCRYPTION_MODES_REMOTE 1751
extern  enum clnt_stat qcsapi_wifi_get_ieee11i_encryption_modes_remote_1(qcsapi_wifi_get_IEEE11i_encryption_modes_rpcdata *, qcsapi_wifi_get_IEEE11i_encryption_modes_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_ieee11i_encryption_modes_remote_1_svc(qcsapi_wifi_get_IEEE11i_encryption_modes_rpcdata *, qcsapi_wifi_get_IEEE11i_encryption_modes_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_IEEE11I_ENCRYPTION_MODES_REMOTE 1761
extern  enum clnt_stat qcsapi_wifi_set_ieee11i_encryption_modes_remote_1(qcsapi_wifi_set_IEEE11i_encryption_modes_rpcdata *, qcsapi_wifi_set_IEEE11i_encryption_modes_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_ieee11i_encryption_modes_remote_1_svc(qcsapi_wifi_set_IEEE11i_encryption_modes_rpcdata *, qcsapi_wifi_set_IEEE11i_encryption_modes_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_IEEE11I_AUTHENTICATION_MODE_REMOTE 1771
extern  enum clnt_stat qcsapi_wifi_get_ieee11i_authentication_mode_remote_1(qcsapi_wifi_get_IEEE11i_authentication_mode_rpcdata *, qcsapi_wifi_get_IEEE11i_authentication_mode_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_ieee11i_authentication_mode_remote_1_svc(qcsapi_wifi_get_IEEE11i_authentication_mode_rpcdata *, qcsapi_wifi_get_IEEE11i_authentication_mode_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_IEEE11I_AUTHENTICATION_MODE_REMOTE 1781
extern  enum clnt_stat qcsapi_wifi_set_ieee11i_authentication_mode_remote_1(qcsapi_wifi_set_IEEE11i_authentication_mode_rpcdata *, qcsapi_wifi_set_IEEE11i_authentication_mode_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_ieee11i_authentication_mode_remote_1_svc(qcsapi_wifi_set_IEEE11i_authentication_mode_rpcdata *, qcsapi_wifi_set_IEEE11i_authentication_mode_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_MICHAEL_ERRCNT_REMOTE 1791
extern  enum clnt_stat qcsapi_wifi_get_michael_errcnt_remote_1(qcsapi_wifi_get_michael_errcnt_rpcdata *, qcsapi_wifi_get_michael_errcnt_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_michael_errcnt_remote_1_svc(qcsapi_wifi_get_michael_errcnt_rpcdata *, qcsapi_wifi_get_michael_errcnt_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_PRE_SHARED_KEY_REMOTE 1801
extern  enum clnt_stat qcsapi_wifi_get_pre_shared_key_remote_1(qcsapi_wifi_get_pre_shared_key_rpcdata *, qcsapi_wifi_get_pre_shared_key_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_pre_shared_key_remote_1_svc(qcsapi_wifi_get_pre_shared_key_rpcdata *, qcsapi_wifi_get_pre_shared_key_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_PRE_SHARED_KEY_REMOTE 1811
extern  enum clnt_stat qcsapi_wifi_set_pre_shared_key_remote_1(qcsapi_wifi_set_pre_shared_key_rpcdata *, qcsapi_wifi_set_pre_shared_key_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_pre_shared_key_remote_1_svc(qcsapi_wifi_set_pre_shared_key_rpcdata *, qcsapi_wifi_set_pre_shared_key_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_ADD_RADIUS_AUTH_SERVER_CFG_REMOTE 5801
extern  enum clnt_stat qcsapi_wifi_add_radius_auth_server_cfg_remote_1(qcsapi_wifi_add_radius_auth_server_cfg_rpcdata *, qcsapi_wifi_add_radius_auth_server_cfg_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_add_radius_auth_server_cfg_remote_1_svc(qcsapi_wifi_add_radius_auth_server_cfg_rpcdata *, qcsapi_wifi_add_radius_auth_server_cfg_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_DEL_RADIUS_AUTH_SERVER_CFG_REMOTE 5811
extern  enum clnt_stat qcsapi_wifi_del_radius_auth_server_cfg_remote_1(qcsapi_wifi_del_radius_auth_server_cfg_rpcdata *, qcsapi_wifi_del_radius_auth_server_cfg_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_del_radius_auth_server_cfg_remote_1_svc(qcsapi_wifi_del_radius_auth_server_cfg_rpcdata *, qcsapi_wifi_del_radius_auth_server_cfg_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_RADIUS_AUTH_SERVER_CFG_REMOTE 5821
extern  enum clnt_stat qcsapi_wifi_get_radius_auth_server_cfg_remote_1(qcsapi_wifi_get_radius_auth_server_cfg_rpcdata *, qcsapi_wifi_get_radius_auth_server_cfg_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_radius_auth_server_cfg_remote_1_svc(qcsapi_wifi_get_radius_auth_server_cfg_rpcdata *, qcsapi_wifi_get_radius_auth_server_cfg_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_OWN_IP_ADDR_REMOTE 1881
extern  enum clnt_stat qcsapi_wifi_set_own_ip_addr_remote_1(qcsapi_wifi_set_own_ip_addr_rpcdata *, qcsapi_wifi_set_own_ip_addr_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_own_ip_addr_remote_1_svc(qcsapi_wifi_set_own_ip_addr_rpcdata *, qcsapi_wifi_set_own_ip_addr_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_KEY_PASSPHRASE_REMOTE 1891
extern  enum clnt_stat qcsapi_wifi_get_key_passphrase_remote_1(qcsapi_wifi_get_key_passphrase_rpcdata *, qcsapi_wifi_get_key_passphrase_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_key_passphrase_remote_1_svc(qcsapi_wifi_get_key_passphrase_rpcdata *, qcsapi_wifi_get_key_passphrase_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_KEY_PASSPHRASE_REMOTE 1901
extern  enum clnt_stat qcsapi_wifi_set_key_passphrase_remote_1(qcsapi_wifi_set_key_passphrase_rpcdata *, qcsapi_wifi_set_key_passphrase_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_key_passphrase_remote_1_svc(qcsapi_wifi_set_key_passphrase_rpcdata *, qcsapi_wifi_set_key_passphrase_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_GROUP_KEY_INTERVAL_REMOTE 1911
extern  enum clnt_stat qcsapi_wifi_get_group_key_interval_remote_1(qcsapi_wifi_get_group_key_interval_rpcdata *, qcsapi_wifi_get_group_key_interval_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_group_key_interval_remote_1_svc(qcsapi_wifi_get_group_key_interval_rpcdata *, qcsapi_wifi_get_group_key_interval_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_GROUP_KEY_INTERVAL_REMOTE 1921
extern  enum clnt_stat qcsapi_wifi_set_group_key_interval_remote_1(qcsapi_wifi_set_group_key_interval_rpcdata *, qcsapi_wifi_set_group_key_interval_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_group_key_interval_remote_1_svc(qcsapi_wifi_set_group_key_interval_rpcdata *, qcsapi_wifi_set_group_key_interval_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_PMF_REMOTE 1931
extern  enum clnt_stat qcsapi_wifi_get_pmf_remote_1(qcsapi_wifi_get_pmf_rpcdata *, qcsapi_wifi_get_pmf_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_pmf_remote_1_svc(qcsapi_wifi_get_pmf_rpcdata *, qcsapi_wifi_get_pmf_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_PMF_REMOTE 1941
extern  enum clnt_stat qcsapi_wifi_set_pmf_remote_1(qcsapi_wifi_set_pmf_rpcdata *, qcsapi_wifi_set_pmf_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_pmf_remote_1_svc(qcsapi_wifi_set_pmf_rpcdata *, qcsapi_wifi_set_pmf_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_WPA_STATUS_REMOTE 1951
extern  enum clnt_stat qcsapi_wifi_get_wpa_status_remote_1(qcsapi_wifi_get_wpa_status_rpcdata *, qcsapi_wifi_get_wpa_status_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_wpa_status_remote_1_svc(qcsapi_wifi_get_wpa_status_rpcdata *, qcsapi_wifi_get_wpa_status_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_PSK_AUTH_FAILURES_REMOTE 1961
extern  enum clnt_stat qcsapi_wifi_get_psk_auth_failures_remote_1(qcsapi_wifi_get_psk_auth_failures_rpcdata *, qcsapi_wifi_get_psk_auth_failures_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_psk_auth_failures_remote_1_svc(qcsapi_wifi_get_psk_auth_failures_rpcdata *, qcsapi_wifi_get_psk_auth_failures_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_AUTH_STATE_REMOTE 1971
extern  enum clnt_stat qcsapi_wifi_get_auth_state_remote_1(qcsapi_wifi_get_auth_state_rpcdata *, qcsapi_wifi_get_auth_state_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_auth_state_remote_1_svc(qcsapi_wifi_get_auth_state_rpcdata *, qcsapi_wifi_get_auth_state_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_SECURITY_DEFER_MODE_REMOTE 1981
extern  enum clnt_stat qcsapi_wifi_set_security_defer_mode_remote_1(qcsapi_wifi_set_security_defer_mode_rpcdata *, qcsapi_wifi_set_security_defer_mode_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_security_defer_mode_remote_1_svc(qcsapi_wifi_set_security_defer_mode_rpcdata *, qcsapi_wifi_set_security_defer_mode_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_SECURITY_DEFER_MODE_REMOTE 1991
extern  enum clnt_stat qcsapi_wifi_get_security_defer_mode_remote_1(qcsapi_wifi_get_security_defer_mode_rpcdata *, qcsapi_wifi_get_security_defer_mode_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_security_defer_mode_remote_1_svc(qcsapi_wifi_get_security_defer_mode_rpcdata *, qcsapi_wifi_get_security_defer_mode_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_APPLY_SECURITY_CONFIG_REMOTE 2001
extern  enum clnt_stat qcsapi_wifi_apply_security_config_remote_1(qcsapi_wifi_apply_security_config_rpcdata *, qcsapi_wifi_apply_security_config_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_apply_security_config_remote_1_svc(qcsapi_wifi_apply_security_config_rpcdata *, qcsapi_wifi_apply_security_config_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_MAC_ADDRESS_FILTERING_REMOTE 2011
extern  enum clnt_stat qcsapi_wifi_set_mac_address_filtering_remote_1(qcsapi_wifi_set_mac_address_filtering_rpcdata *, qcsapi_wifi_set_mac_address_filtering_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_mac_address_filtering_remote_1_svc(qcsapi_wifi_set_mac_address_filtering_rpcdata *, qcsapi_wifi_set_mac_address_filtering_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_MAC_ADDRESS_FILTERING_REMOTE 2021
extern  enum clnt_stat qcsapi_wifi_get_mac_address_filtering_remote_1(qcsapi_wifi_get_mac_address_filtering_rpcdata *, qcsapi_wifi_get_mac_address_filtering_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_mac_address_filtering_remote_1_svc(qcsapi_wifi_get_mac_address_filtering_rpcdata *, qcsapi_wifi_get_mac_address_filtering_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_AUTHORIZE_MAC_ADDRESS_REMOTE 2031
extern  enum clnt_stat qcsapi_wifi_authorize_mac_address_remote_1(qcsapi_wifi_authorize_mac_address_rpcdata *, qcsapi_wifi_authorize_mac_address_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_authorize_mac_address_remote_1_svc(qcsapi_wifi_authorize_mac_address_rpcdata *, qcsapi_wifi_authorize_mac_address_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_DENY_MAC_ADDRESS_REMOTE 2041
extern  enum clnt_stat qcsapi_wifi_deny_mac_address_remote_1(qcsapi_wifi_deny_mac_address_rpcdata *, qcsapi_wifi_deny_mac_address_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_deny_mac_address_remote_1_svc(qcsapi_wifi_deny_mac_address_rpcdata *, qcsapi_wifi_deny_mac_address_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_REMOVE_MAC_ADDRESS_REMOTE 2051
extern  enum clnt_stat qcsapi_wifi_remove_mac_address_remote_1(qcsapi_wifi_remove_mac_address_rpcdata *, qcsapi_wifi_remove_mac_address_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_remove_mac_address_remote_1_svc(qcsapi_wifi_remove_mac_address_rpcdata *, qcsapi_wifi_remove_mac_address_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_IS_MAC_ADDRESS_AUTHORIZED_REMOTE 2061
extern  enum clnt_stat qcsapi_wifi_is_mac_address_authorized_remote_1(qcsapi_wifi_is_mac_address_authorized_rpcdata *, qcsapi_wifi_is_mac_address_authorized_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_is_mac_address_authorized_remote_1_svc(qcsapi_wifi_is_mac_address_authorized_rpcdata *, qcsapi_wifi_is_mac_address_authorized_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_AUTHORIZED_MAC_ADDRESSES_REMOTE 2071
extern  enum clnt_stat qcsapi_wifi_get_authorized_mac_addresses_remote_1(qcsapi_wifi_get_authorized_mac_addresses_rpcdata *, qcsapi_wifi_get_authorized_mac_addresses_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_authorized_mac_addresses_remote_1_svc(qcsapi_wifi_get_authorized_mac_addresses_rpcdata *, qcsapi_wifi_get_authorized_mac_addresses_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_DENIED_MAC_ADDRESSES_REMOTE 2081
extern  enum clnt_stat qcsapi_wifi_get_denied_mac_addresses_remote_1(qcsapi_wifi_get_denied_mac_addresses_rpcdata *, qcsapi_wifi_get_denied_mac_addresses_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_denied_mac_addresses_remote_1_svc(qcsapi_wifi_get_denied_mac_addresses_rpcdata *, qcsapi_wifi_get_denied_mac_addresses_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_ACCEPT_OUI_FILTER_REMOTE 2091
extern  enum clnt_stat qcsapi_wifi_set_accept_oui_filter_remote_1(qcsapi_wifi_set_accept_oui_filter_rpcdata *, qcsapi_wifi_set_accept_oui_filter_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_accept_oui_filter_remote_1_svc(qcsapi_wifi_set_accept_oui_filter_rpcdata *, qcsapi_wifi_set_accept_oui_filter_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_ACCEPT_OUI_FILTER_REMOTE 2101
extern  enum clnt_stat qcsapi_wifi_get_accept_oui_filter_remote_1(qcsapi_wifi_get_accept_oui_filter_rpcdata *, qcsapi_wifi_get_accept_oui_filter_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_accept_oui_filter_remote_1_svc(qcsapi_wifi_get_accept_oui_filter_rpcdata *, qcsapi_wifi_get_accept_oui_filter_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_CLEAR_MAC_ADDRESS_FILTERS_REMOTE 2111
extern  enum clnt_stat qcsapi_wifi_clear_mac_address_filters_remote_1(qcsapi_wifi_clear_mac_address_filters_rpcdata *, qcsapi_wifi_clear_mac_address_filters_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_clear_mac_address_filters_remote_1_svc(qcsapi_wifi_clear_mac_address_filters_rpcdata *, qcsapi_wifi_clear_mac_address_filters_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_MAC_ADDRESS_RESERVE_REMOTE 6011
extern  enum clnt_stat qcsapi_wifi_set_mac_address_reserve_remote_1(qcsapi_wifi_set_mac_address_reserve_rpcdata *, qcsapi_wifi_set_mac_address_reserve_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_mac_address_reserve_remote_1_svc(qcsapi_wifi_set_mac_address_reserve_rpcdata *, qcsapi_wifi_set_mac_address_reserve_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_MAC_ADDRESS_RESERVE_REMOTE 6021
extern  enum clnt_stat qcsapi_wifi_get_mac_address_reserve_remote_1(qcsapi_wifi_get_mac_address_reserve_rpcdata *, qcsapi_wifi_get_mac_address_reserve_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_mac_address_reserve_remote_1_svc(qcsapi_wifi_get_mac_address_reserve_rpcdata *, qcsapi_wifi_get_mac_address_reserve_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_CLEAR_MAC_ADDRESS_RESERVE_REMOTE 6031
extern  enum clnt_stat qcsapi_wifi_clear_mac_address_reserve_remote_1(qcsapi_wifi_clear_mac_address_reserve_rpcdata *, qcsapi_wifi_clear_mac_address_reserve_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_clear_mac_address_reserve_remote_1_svc(qcsapi_wifi_clear_mac_address_reserve_rpcdata *, qcsapi_wifi_clear_mac_address_reserve_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_OPTION_REMOTE 2121
extern  enum clnt_stat qcsapi_wifi_get_option_remote_1(qcsapi_wifi_get_option_rpcdata *, qcsapi_wifi_get_option_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_option_remote_1_svc(qcsapi_wifi_get_option_rpcdata *, qcsapi_wifi_get_option_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_OPTION_REMOTE 2131
extern  enum clnt_stat qcsapi_wifi_set_option_remote_1(qcsapi_wifi_set_option_rpcdata *, qcsapi_wifi_set_option_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_option_remote_1_svc(qcsapi_wifi_set_option_rpcdata *, qcsapi_wifi_set_option_rpcdata *, struct svc_req *);
#define QCSAPI_GET_BOARD_PARAMETER_REMOTE 2141
extern  enum clnt_stat qcsapi_get_board_parameter_remote_1(qcsapi_get_board_parameter_rpcdata *, qcsapi_get_board_parameter_rpcdata *, CLIENT *);
extern  bool_t qcsapi_get_board_parameter_remote_1_svc(qcsapi_get_board_parameter_rpcdata *, qcsapi_get_board_parameter_rpcdata *, struct svc_req *);
#define QCSAPI_GET_SWFEAT_LIST_REMOTE 4451
extern  enum clnt_stat qcsapi_get_swfeat_list_remote_1(qcsapi_get_swfeat_list_rpcdata *, qcsapi_get_swfeat_list_rpcdata *, CLIENT *);
extern  bool_t qcsapi_get_swfeat_list_remote_1_svc(qcsapi_get_swfeat_list_rpcdata *, qcsapi_get_swfeat_list_rpcdata *, struct svc_req *);
#define QCSAPI_SSID_CREATE_SSID_REMOTE 2151
extern  enum clnt_stat qcsapi_ssid_create_ssid_remote_1(qcsapi_SSID_create_SSID_rpcdata *, qcsapi_SSID_create_SSID_rpcdata *, CLIENT *);
extern  bool_t qcsapi_ssid_create_ssid_remote_1_svc(qcsapi_SSID_create_SSID_rpcdata *, qcsapi_SSID_create_SSID_rpcdata *, struct svc_req *);
#define QCSAPI_SSID_REMOVE_SSID_REMOTE 2161
extern  enum clnt_stat qcsapi_ssid_remove_ssid_remote_1(qcsapi_SSID_remove_SSID_rpcdata *, qcsapi_SSID_remove_SSID_rpcdata *, CLIENT *);
extern  bool_t qcsapi_ssid_remove_ssid_remote_1_svc(qcsapi_SSID_remove_SSID_rpcdata *, qcsapi_SSID_remove_SSID_rpcdata *, struct svc_req *);
#define QCSAPI_SSID_VERIFY_SSID_REMOTE 2171
extern  enum clnt_stat qcsapi_ssid_verify_ssid_remote_1(qcsapi_SSID_verify_SSID_rpcdata *, qcsapi_SSID_verify_SSID_rpcdata *, CLIENT *);
extern  bool_t qcsapi_ssid_verify_ssid_remote_1_svc(qcsapi_SSID_verify_SSID_rpcdata *, qcsapi_SSID_verify_SSID_rpcdata *, struct svc_req *);
#define QCSAPI_SSID_RENAME_SSID_REMOTE 2181
extern  enum clnt_stat qcsapi_ssid_rename_ssid_remote_1(qcsapi_SSID_rename_SSID_rpcdata *, qcsapi_SSID_rename_SSID_rpcdata *, CLIENT *);
extern  bool_t qcsapi_ssid_rename_ssid_remote_1_svc(qcsapi_SSID_rename_SSID_rpcdata *, qcsapi_SSID_rename_SSID_rpcdata *, struct svc_req *);
#define QCSAPI_SSID_GET_SSID_LIST_REMOTE 2191
extern  enum clnt_stat qcsapi_ssid_get_ssid_list_remote_1(qcsapi_SSID_get_SSID_list_rpcdata *, qcsapi_SSID_get_SSID_list_rpcdata *, CLIENT *);
extern  bool_t qcsapi_ssid_get_ssid_list_remote_1_svc(qcsapi_SSID_get_SSID_list_rpcdata *, qcsapi_SSID_get_SSID_list_rpcdata *, struct svc_req *);
#define QCSAPI_SSID_SET_PROTOCOL_REMOTE 2201
extern  enum clnt_stat qcsapi_ssid_set_protocol_remote_1(qcsapi_SSID_set_protocol_rpcdata *, qcsapi_SSID_set_protocol_rpcdata *, CLIENT *);
extern  bool_t qcsapi_ssid_set_protocol_remote_1_svc(qcsapi_SSID_set_protocol_rpcdata *, qcsapi_SSID_set_protocol_rpcdata *, struct svc_req *);
#define QCSAPI_SSID_GET_PROTOCOL_REMOTE 2211
extern  enum clnt_stat qcsapi_ssid_get_protocol_remote_1(qcsapi_SSID_get_protocol_rpcdata *, qcsapi_SSID_get_protocol_rpcdata *, CLIENT *);
extern  bool_t qcsapi_ssid_get_protocol_remote_1_svc(qcsapi_SSID_get_protocol_rpcdata *, qcsapi_SSID_get_protocol_rpcdata *, struct svc_req *);
#define QCSAPI_SSID_GET_ENCRYPTION_MODES_REMOTE 2221
extern  enum clnt_stat qcsapi_ssid_get_encryption_modes_remote_1(qcsapi_SSID_get_encryption_modes_rpcdata *, qcsapi_SSID_get_encryption_modes_rpcdata *, CLIENT *);
extern  bool_t qcsapi_ssid_get_encryption_modes_remote_1_svc(qcsapi_SSID_get_encryption_modes_rpcdata *, qcsapi_SSID_get_encryption_modes_rpcdata *, struct svc_req *);
#define QCSAPI_SSID_SET_ENCRYPTION_MODES_REMOTE 2231
extern  enum clnt_stat qcsapi_ssid_set_encryption_modes_remote_1(qcsapi_SSID_set_encryption_modes_rpcdata *, qcsapi_SSID_set_encryption_modes_rpcdata *, CLIENT *);
extern  bool_t qcsapi_ssid_set_encryption_modes_remote_1_svc(qcsapi_SSID_set_encryption_modes_rpcdata *, qcsapi_SSID_set_encryption_modes_rpcdata *, struct svc_req *);
#define QCSAPI_SSID_GET_GROUP_ENCRYPTION_REMOTE 2241
extern  enum clnt_stat qcsapi_ssid_get_group_encryption_remote_1(qcsapi_SSID_get_group_encryption_rpcdata *, qcsapi_SSID_get_group_encryption_rpcdata *, CLIENT *);
extern  bool_t qcsapi_ssid_get_group_encryption_remote_1_svc(qcsapi_SSID_get_group_encryption_rpcdata *, qcsapi_SSID_get_group_encryption_rpcdata *, struct svc_req *);
#define QCSAPI_SSID_SET_GROUP_ENCRYPTION_REMOTE 2251
extern  enum clnt_stat qcsapi_ssid_set_group_encryption_remote_1(qcsapi_SSID_set_group_encryption_rpcdata *, qcsapi_SSID_set_group_encryption_rpcdata *, CLIENT *);
extern  bool_t qcsapi_ssid_set_group_encryption_remote_1_svc(qcsapi_SSID_set_group_encryption_rpcdata *, qcsapi_SSID_set_group_encryption_rpcdata *, struct svc_req *);
#define QCSAPI_SSID_GET_AUTHENTICATION_MODE_REMOTE 2261
extern  enum clnt_stat qcsapi_ssid_get_authentication_mode_remote_1(qcsapi_SSID_get_authentication_mode_rpcdata *, qcsapi_SSID_get_authentication_mode_rpcdata *, CLIENT *);
extern  bool_t qcsapi_ssid_get_authentication_mode_remote_1_svc(qcsapi_SSID_get_authentication_mode_rpcdata *, qcsapi_SSID_get_authentication_mode_rpcdata *, struct svc_req *);
#define QCSAPI_SSID_SET_AUTHENTICATION_MODE_REMOTE 2271
extern  enum clnt_stat qcsapi_ssid_set_authentication_mode_remote_1(qcsapi_SSID_set_authentication_mode_rpcdata *, qcsapi_SSID_set_authentication_mode_rpcdata *, CLIENT *);
extern  bool_t qcsapi_ssid_set_authentication_mode_remote_1_svc(qcsapi_SSID_set_authentication_mode_rpcdata *, qcsapi_SSID_set_authentication_mode_rpcdata *, struct svc_req *);
#define QCSAPI_SSID_GET_PRE_SHARED_KEY_REMOTE 2281
extern  enum clnt_stat qcsapi_ssid_get_pre_shared_key_remote_1(qcsapi_SSID_get_pre_shared_key_rpcdata *, qcsapi_SSID_get_pre_shared_key_rpcdata *, CLIENT *);
extern  bool_t qcsapi_ssid_get_pre_shared_key_remote_1_svc(qcsapi_SSID_get_pre_shared_key_rpcdata *, qcsapi_SSID_get_pre_shared_key_rpcdata *, struct svc_req *);
#define QCSAPI_SSID_SET_PRE_SHARED_KEY_REMOTE 2291
extern  enum clnt_stat qcsapi_ssid_set_pre_shared_key_remote_1(qcsapi_SSID_set_pre_shared_key_rpcdata *, qcsapi_SSID_set_pre_shared_key_rpcdata *, CLIENT *);
extern  bool_t qcsapi_ssid_set_pre_shared_key_remote_1_svc(qcsapi_SSID_set_pre_shared_key_rpcdata *, qcsapi_SSID_set_pre_shared_key_rpcdata *, struct svc_req *);
#define QCSAPI_SSID_GET_KEY_PASSPHRASE_REMOTE 2301
extern  enum clnt_stat qcsapi_ssid_get_key_passphrase_remote_1(qcsapi_SSID_get_key_passphrase_rpcdata *, qcsapi_SSID_get_key_passphrase_rpcdata *, CLIENT *);
extern  bool_t qcsapi_ssid_get_key_passphrase_remote_1_svc(qcsapi_SSID_get_key_passphrase_rpcdata *, qcsapi_SSID_get_key_passphrase_rpcdata *, struct svc_req *);
#define QCSAPI_SSID_SET_KEY_PASSPHRASE_REMOTE 2311
extern  enum clnt_stat qcsapi_ssid_set_key_passphrase_remote_1(qcsapi_SSID_set_key_passphrase_rpcdata *, qcsapi_SSID_set_key_passphrase_rpcdata *, CLIENT *);
extern  bool_t qcsapi_ssid_set_key_passphrase_remote_1_svc(qcsapi_SSID_set_key_passphrase_rpcdata *, qcsapi_SSID_set_key_passphrase_rpcdata *, struct svc_req *);
#define QCSAPI_SSID_GET_PMF_REMOTE 2321
extern  enum clnt_stat qcsapi_ssid_get_pmf_remote_1(qcsapi_SSID_get_pmf_rpcdata *, qcsapi_SSID_get_pmf_rpcdata *, CLIENT *);
extern  bool_t qcsapi_ssid_get_pmf_remote_1_svc(qcsapi_SSID_get_pmf_rpcdata *, qcsapi_SSID_get_pmf_rpcdata *, struct svc_req *);
#define QCSAPI_SSID_SET_PMF_REMOTE 2331
extern  enum clnt_stat qcsapi_ssid_set_pmf_remote_1(qcsapi_SSID_set_pmf_rpcdata *, qcsapi_SSID_set_pmf_rpcdata *, CLIENT *);
extern  bool_t qcsapi_ssid_set_pmf_remote_1_svc(qcsapi_SSID_set_pmf_rpcdata *, qcsapi_SSID_set_pmf_rpcdata *, struct svc_req *);
#define QCSAPI_SSID_GET_WPS_SSID_REMOTE 2341
extern  enum clnt_stat qcsapi_ssid_get_wps_ssid_remote_1(qcsapi_SSID_get_wps_SSID_rpcdata *, qcsapi_SSID_get_wps_SSID_rpcdata *, CLIENT *);
extern  bool_t qcsapi_ssid_get_wps_ssid_remote_1_svc(qcsapi_SSID_get_wps_SSID_rpcdata *, qcsapi_SSID_get_wps_SSID_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_VLAN_CONFIG_REMOTE 2351
extern  enum clnt_stat qcsapi_wifi_vlan_config_remote_1(qcsapi_wifi_vlan_config_rpcdata *, qcsapi_wifi_vlan_config_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_vlan_config_remote_1_svc(qcsapi_wifi_vlan_config_rpcdata *, qcsapi_wifi_vlan_config_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SHOW_VLAN_CONFIG_REMOTE 2361
extern  enum clnt_stat qcsapi_wifi_show_vlan_config_remote_1(qcsapi_wifi_show_vlan_config_rpcdata *, qcsapi_wifi_show_vlan_config_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_show_vlan_config_remote_1_svc(qcsapi_wifi_show_vlan_config_rpcdata *, qcsapi_wifi_show_vlan_config_rpcdata *, struct svc_req *);
#define QCSAPI_ENABLE_VLAN_PASS_THROUGH_REMOTE 2371
extern  enum clnt_stat qcsapi_enable_vlan_pass_through_remote_1(qcsapi_enable_vlan_pass_through_rpcdata *, qcsapi_enable_vlan_pass_through_rpcdata *, CLIENT *);
extern  bool_t qcsapi_enable_vlan_pass_through_remote_1_svc(qcsapi_enable_vlan_pass_through_rpcdata *, qcsapi_enable_vlan_pass_through_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_VLAN_PROMISC_REMOTE 2381
extern  enum clnt_stat qcsapi_wifi_set_vlan_promisc_remote_1(qcsapi_wifi_set_vlan_promisc_rpcdata *, qcsapi_wifi_set_vlan_promisc_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_vlan_promisc_remote_1_svc(qcsapi_wifi_set_vlan_promisc_rpcdata *, qcsapi_wifi_set_vlan_promisc_rpcdata *, struct svc_req *);
#define QCSAPI_WPS_REGISTRAR_REPORT_BUTTON_PRESS_REMOTE 2391
extern  enum clnt_stat qcsapi_wps_registrar_report_button_press_remote_1(qcsapi_wps_registrar_report_button_press_rpcdata *, qcsapi_wps_registrar_report_button_press_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wps_registrar_report_button_press_remote_1_svc(qcsapi_wps_registrar_report_button_press_rpcdata *, qcsapi_wps_registrar_report_button_press_rpcdata *, struct svc_req *);
#define QCSAPI_WPS_REGISTRAR_REPORT_PIN_REMOTE 2401
extern  enum clnt_stat qcsapi_wps_registrar_report_pin_remote_1(qcsapi_wps_registrar_report_pin_rpcdata *, qcsapi_wps_registrar_report_pin_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wps_registrar_report_pin_remote_1_svc(qcsapi_wps_registrar_report_pin_rpcdata *, qcsapi_wps_registrar_report_pin_rpcdata *, struct svc_req *);
#define QCSAPI_WPS_REGISTRAR_GET_PP_DEVNAME_REMOTE 2411
extern  enum clnt_stat qcsapi_wps_registrar_get_pp_devname_remote_1(qcsapi_wps_registrar_get_pp_devname_rpcdata *, qcsapi_wps_registrar_get_pp_devname_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wps_registrar_get_pp_devname_remote_1_svc(qcsapi_wps_registrar_get_pp_devname_rpcdata *, qcsapi_wps_registrar_get_pp_devname_rpcdata *, struct svc_req *);
#define QCSAPI_WPS_REGISTRAR_SET_PP_DEVNAME_REMOTE 2421
extern  enum clnt_stat qcsapi_wps_registrar_set_pp_devname_remote_1(qcsapi_wps_registrar_set_pp_devname_rpcdata *, qcsapi_wps_registrar_set_pp_devname_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wps_registrar_set_pp_devname_remote_1_svc(qcsapi_wps_registrar_set_pp_devname_rpcdata *, qcsapi_wps_registrar_set_pp_devname_rpcdata *, struct svc_req *);
#define QCSAPI_WPS_ENROLLEE_REPORT_BUTTON_PRESS_REMOTE 2431
extern  enum clnt_stat qcsapi_wps_enrollee_report_button_press_remote_1(qcsapi_wps_enrollee_report_button_press_rpcdata *, qcsapi_wps_enrollee_report_button_press_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wps_enrollee_report_button_press_remote_1_svc(qcsapi_wps_enrollee_report_button_press_rpcdata *, qcsapi_wps_enrollee_report_button_press_rpcdata *, struct svc_req *);
#define QCSAPI_WPS_ENROLLEE_REPORT_PIN_REMOTE 2441
extern  enum clnt_stat qcsapi_wps_enrollee_report_pin_remote_1(qcsapi_wps_enrollee_report_pin_rpcdata *, qcsapi_wps_enrollee_report_pin_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wps_enrollee_report_pin_remote_1_svc(qcsapi_wps_enrollee_report_pin_rpcdata *, qcsapi_wps_enrollee_report_pin_rpcdata *, struct svc_req *);
#define QCSAPI_WPS_ENROLLEE_GENERATE_PIN_REMOTE 2451
extern  enum clnt_stat qcsapi_wps_enrollee_generate_pin_remote_1(qcsapi_wps_enrollee_generate_pin_rpcdata *, qcsapi_wps_enrollee_generate_pin_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wps_enrollee_generate_pin_remote_1_svc(qcsapi_wps_enrollee_generate_pin_rpcdata *, qcsapi_wps_enrollee_generate_pin_rpcdata *, struct svc_req *);
#define QCSAPI_WPS_GET_AP_PIN_REMOTE 2461
extern  enum clnt_stat qcsapi_wps_get_ap_pin_remote_1(qcsapi_wps_get_ap_pin_rpcdata *, qcsapi_wps_get_ap_pin_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wps_get_ap_pin_remote_1_svc(qcsapi_wps_get_ap_pin_rpcdata *, qcsapi_wps_get_ap_pin_rpcdata *, struct svc_req *);
#define QCSAPI_WPS_SET_AP_PIN_REMOTE 2471
extern  enum clnt_stat qcsapi_wps_set_ap_pin_remote_1(qcsapi_wps_set_ap_pin_rpcdata *, qcsapi_wps_set_ap_pin_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wps_set_ap_pin_remote_1_svc(qcsapi_wps_set_ap_pin_rpcdata *, qcsapi_wps_set_ap_pin_rpcdata *, struct svc_req *);
#define QCSAPI_WPS_SAVE_AP_PIN_REMOTE 2481
extern  enum clnt_stat qcsapi_wps_save_ap_pin_remote_1(qcsapi_wps_save_ap_pin_rpcdata *, qcsapi_wps_save_ap_pin_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wps_save_ap_pin_remote_1_svc(qcsapi_wps_save_ap_pin_rpcdata *, qcsapi_wps_save_ap_pin_rpcdata *, struct svc_req *);
#define QCSAPI_WPS_ENABLE_AP_PIN_REMOTE 2491
extern  enum clnt_stat qcsapi_wps_enable_ap_pin_remote_1(qcsapi_wps_enable_ap_pin_rpcdata *, qcsapi_wps_enable_ap_pin_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wps_enable_ap_pin_remote_1_svc(qcsapi_wps_enable_ap_pin_rpcdata *, qcsapi_wps_enable_ap_pin_rpcdata *, struct svc_req *);
#define QCSAPI_WPS_GET_STA_PIN_REMOTE 2501
extern  enum clnt_stat qcsapi_wps_get_sta_pin_remote_1(qcsapi_wps_get_sta_pin_rpcdata *, qcsapi_wps_get_sta_pin_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wps_get_sta_pin_remote_1_svc(qcsapi_wps_get_sta_pin_rpcdata *, qcsapi_wps_get_sta_pin_rpcdata *, struct svc_req *);
#define QCSAPI_WPS_GET_STATE_REMOTE 2511
extern  enum clnt_stat qcsapi_wps_get_state_remote_1(qcsapi_wps_get_state_rpcdata *, qcsapi_wps_get_state_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wps_get_state_remote_1_svc(qcsapi_wps_get_state_rpcdata *, qcsapi_wps_get_state_rpcdata *, struct svc_req *);
#define QCSAPI_WPS_GET_CONFIGURED_STATE_REMOTE 2521
extern  enum clnt_stat qcsapi_wps_get_configured_state_remote_1(qcsapi_wps_get_configured_state_rpcdata *, qcsapi_wps_get_configured_state_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wps_get_configured_state_remote_1_svc(qcsapi_wps_get_configured_state_rpcdata *, qcsapi_wps_get_configured_state_rpcdata *, struct svc_req *);
#define QCSAPI_WPS_GET_RUNTIME_STATE_REMOTE 2531
extern  enum clnt_stat qcsapi_wps_get_runtime_state_remote_1(qcsapi_wps_get_runtime_state_rpcdata *, qcsapi_wps_get_runtime_state_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wps_get_runtime_state_remote_1_svc(qcsapi_wps_get_runtime_state_rpcdata *, qcsapi_wps_get_runtime_state_rpcdata *, struct svc_req *);
#define QCSAPI_WPS_SET_CONFIGURED_STATE_REMOTE 2541
extern  enum clnt_stat qcsapi_wps_set_configured_state_remote_1(qcsapi_wps_set_configured_state_rpcdata *, qcsapi_wps_set_configured_state_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wps_set_configured_state_remote_1_svc(qcsapi_wps_set_configured_state_rpcdata *, qcsapi_wps_set_configured_state_rpcdata *, struct svc_req *);
#define QCSAPI_WPS_GET_PARAM_REMOTE 2551
extern  enum clnt_stat qcsapi_wps_get_param_remote_1(qcsapi_wps_get_param_rpcdata *, qcsapi_wps_get_param_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wps_get_param_remote_1_svc(qcsapi_wps_get_param_rpcdata *, qcsapi_wps_get_param_rpcdata *, struct svc_req *);
#define QCSAPI_WPS_SET_TIMEOUT_REMOTE 2561
extern  enum clnt_stat qcsapi_wps_set_timeout_remote_1(qcsapi_wps_set_timeout_rpcdata *, qcsapi_wps_set_timeout_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wps_set_timeout_remote_1_svc(qcsapi_wps_set_timeout_rpcdata *, qcsapi_wps_set_timeout_rpcdata *, struct svc_req *);
#define QCSAPI_WPS_ON_HIDDEN_SSID_REMOTE 2571
extern  enum clnt_stat qcsapi_wps_on_hidden_ssid_remote_1(qcsapi_wps_on_hidden_ssid_rpcdata *, qcsapi_wps_on_hidden_ssid_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wps_on_hidden_ssid_remote_1_svc(qcsapi_wps_on_hidden_ssid_rpcdata *, qcsapi_wps_on_hidden_ssid_rpcdata *, struct svc_req *);
#define QCSAPI_WPS_ON_HIDDEN_SSID_STATUS_REMOTE 2581
extern  enum clnt_stat qcsapi_wps_on_hidden_ssid_status_remote_1(qcsapi_wps_on_hidden_ssid_status_rpcdata *, qcsapi_wps_on_hidden_ssid_status_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wps_on_hidden_ssid_status_remote_1_svc(qcsapi_wps_on_hidden_ssid_status_rpcdata *, qcsapi_wps_on_hidden_ssid_status_rpcdata *, struct svc_req *);
#define QCSAPI_WPS_UPNP_ENABLE_REMOTE 2591
extern  enum clnt_stat qcsapi_wps_upnp_enable_remote_1(qcsapi_wps_upnp_enable_rpcdata *, qcsapi_wps_upnp_enable_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wps_upnp_enable_remote_1_svc(qcsapi_wps_upnp_enable_rpcdata *, qcsapi_wps_upnp_enable_rpcdata *, struct svc_req *);
#define QCSAPI_WPS_UPNP_STATUS_REMOTE 2601
extern  enum clnt_stat qcsapi_wps_upnp_status_remote_1(qcsapi_wps_upnp_status_rpcdata *, qcsapi_wps_upnp_status_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wps_upnp_status_remote_1_svc(qcsapi_wps_upnp_status_rpcdata *, qcsapi_wps_upnp_status_rpcdata *, struct svc_req *);
#define QCSAPI_WPS_ALLOW_PBC_OVERLAP_REMOTE 2611
extern  enum clnt_stat qcsapi_wps_allow_pbc_overlap_remote_1(qcsapi_wps_allow_pbc_overlap_rpcdata *, qcsapi_wps_allow_pbc_overlap_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wps_allow_pbc_overlap_remote_1_svc(qcsapi_wps_allow_pbc_overlap_rpcdata *, qcsapi_wps_allow_pbc_overlap_rpcdata *, struct svc_req *);
#define QCSAPI_WPS_GET_ALLOW_PBC_OVERLAP_STATUS_REMOTE 2621
extern  enum clnt_stat qcsapi_wps_get_allow_pbc_overlap_status_remote_1(qcsapi_wps_get_allow_pbc_overlap_status_rpcdata *, qcsapi_wps_get_allow_pbc_overlap_status_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wps_get_allow_pbc_overlap_status_remote_1_svc(qcsapi_wps_get_allow_pbc_overlap_status_rpcdata *, qcsapi_wps_get_allow_pbc_overlap_status_rpcdata *, struct svc_req *);
#define QCSAPI_WPS_SET_ACCESS_CONTROL_REMOTE 2631
extern  enum clnt_stat qcsapi_wps_set_access_control_remote_1(qcsapi_wps_set_access_control_rpcdata *, qcsapi_wps_set_access_control_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wps_set_access_control_remote_1_svc(qcsapi_wps_set_access_control_rpcdata *, qcsapi_wps_set_access_control_rpcdata *, struct svc_req *);
#define QCSAPI_WPS_GET_ACCESS_CONTROL_REMOTE 2641
extern  enum clnt_stat qcsapi_wps_get_access_control_remote_1(qcsapi_wps_get_access_control_rpcdata *, qcsapi_wps_get_access_control_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wps_get_access_control_remote_1_svc(qcsapi_wps_get_access_control_rpcdata *, qcsapi_wps_get_access_control_rpcdata *, struct svc_req *);
#define QCSAPI_WPS_SET_PARAM_REMOTE 2651
extern  enum clnt_stat qcsapi_wps_set_param_remote_1(qcsapi_wps_set_param_rpcdata *, qcsapi_wps_set_param_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wps_set_param_remote_1_svc(qcsapi_wps_set_param_rpcdata *, qcsapi_wps_set_param_rpcdata *, struct svc_req *);
#define QCSAPI_WPS_CANCEL_REMOTE 2661
extern  enum clnt_stat qcsapi_wps_cancel_remote_1(qcsapi_wps_cancel_rpcdata *, qcsapi_wps_cancel_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wps_cancel_remote_1_svc(qcsapi_wps_cancel_rpcdata *, qcsapi_wps_cancel_rpcdata *, struct svc_req *);
#define QCSAPI_WPS_SET_PBC_IN_SRCM_REMOTE 2671
extern  enum clnt_stat qcsapi_wps_set_pbc_in_srcm_remote_1(qcsapi_wps_set_pbc_in_srcm_rpcdata *, qcsapi_wps_set_pbc_in_srcm_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wps_set_pbc_in_srcm_remote_1_svc(qcsapi_wps_set_pbc_in_srcm_rpcdata *, qcsapi_wps_set_pbc_in_srcm_rpcdata *, struct svc_req *);
#define QCSAPI_WPS_GET_PBC_IN_SRCM_REMOTE 2681
extern  enum clnt_stat qcsapi_wps_get_pbc_in_srcm_remote_1(qcsapi_wps_get_pbc_in_srcm_rpcdata *, qcsapi_wps_get_pbc_in_srcm_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wps_get_pbc_in_srcm_remote_1_svc(qcsapi_wps_get_pbc_in_srcm_rpcdata *, qcsapi_wps_get_pbc_in_srcm_rpcdata *, struct svc_req *);
#define QCSAPI_REGISTRAR_SET_DEFAULT_PBC_BSS_REMOTE 2691
extern  enum clnt_stat qcsapi_registrar_set_default_pbc_bss_remote_1(qcsapi_registrar_set_default_pbc_bss_rpcdata *, qcsapi_registrar_set_default_pbc_bss_rpcdata *, CLIENT *);
extern  bool_t qcsapi_registrar_set_default_pbc_bss_remote_1_svc(qcsapi_registrar_set_default_pbc_bss_rpcdata *, qcsapi_registrar_set_default_pbc_bss_rpcdata *, struct svc_req *);
#define QCSAPI_REGISTRAR_GET_DEFAULT_PBC_BSS_REMOTE 2701
extern  enum clnt_stat qcsapi_registrar_get_default_pbc_bss_remote_1(qcsapi_registrar_get_default_pbc_bss_rpcdata *, qcsapi_registrar_get_default_pbc_bss_rpcdata *, CLIENT *);
extern  bool_t qcsapi_registrar_get_default_pbc_bss_remote_1_svc(qcsapi_registrar_get_default_pbc_bss_rpcdata *, qcsapi_registrar_get_default_pbc_bss_rpcdata *, struct svc_req *);
#define QCSAPI_GPIO_SET_CONFIG_REMOTE 2711
extern  enum clnt_stat qcsapi_gpio_set_config_remote_1(qcsapi_gpio_set_config_rpcdata *, qcsapi_gpio_set_config_rpcdata *, CLIENT *);
extern  bool_t qcsapi_gpio_set_config_remote_1_svc(qcsapi_gpio_set_config_rpcdata *, qcsapi_gpio_set_config_rpcdata *, struct svc_req *);
#define QCSAPI_GPIO_GET_CONFIG_REMOTE 2721
extern  enum clnt_stat qcsapi_gpio_get_config_remote_1(qcsapi_gpio_get_config_rpcdata *, qcsapi_gpio_get_config_rpcdata *, CLIENT *);
extern  bool_t qcsapi_gpio_get_config_remote_1_svc(qcsapi_gpio_get_config_rpcdata *, qcsapi_gpio_get_config_rpcdata *, struct svc_req *);
#define QCSAPI_LED_GET_REMOTE 2731
extern  enum clnt_stat qcsapi_led_get_remote_1(qcsapi_led_get_rpcdata *, qcsapi_led_get_rpcdata *, CLIENT *);
extern  bool_t qcsapi_led_get_remote_1_svc(qcsapi_led_get_rpcdata *, qcsapi_led_get_rpcdata *, struct svc_req *);
#define QCSAPI_LED_SET_REMOTE 2741
extern  enum clnt_stat qcsapi_led_set_remote_1(qcsapi_led_set_rpcdata *, qcsapi_led_set_rpcdata *, CLIENT *);
extern  bool_t qcsapi_led_set_remote_1_svc(qcsapi_led_set_rpcdata *, qcsapi_led_set_rpcdata *, struct svc_req *);
#define QCSAPI_LED_PWM_ENABLE_REMOTE 2751
extern  enum clnt_stat qcsapi_led_pwm_enable_remote_1(qcsapi_led_pwm_enable_rpcdata *, qcsapi_led_pwm_enable_rpcdata *, CLIENT *);
extern  bool_t qcsapi_led_pwm_enable_remote_1_svc(qcsapi_led_pwm_enable_rpcdata *, qcsapi_led_pwm_enable_rpcdata *, struct svc_req *);
#define QCSAPI_LED_BRIGHTNESS_REMOTE 2761
extern  enum clnt_stat qcsapi_led_brightness_remote_1(qcsapi_led_brightness_rpcdata *, qcsapi_led_brightness_rpcdata *, CLIENT *);
extern  bool_t qcsapi_led_brightness_remote_1_svc(qcsapi_led_brightness_rpcdata *, qcsapi_led_brightness_rpcdata *, struct svc_req *);
#define QCSAPI_GPIO_ENABLE_WPS_PUSH_BUTTON_REMOTE 2781
extern  enum clnt_stat qcsapi_gpio_enable_wps_push_button_remote_1(qcsapi_gpio_enable_wps_push_button_rpcdata *, qcsapi_gpio_enable_wps_push_button_rpcdata *, CLIENT *);
extern  bool_t qcsapi_gpio_enable_wps_push_button_remote_1_svc(qcsapi_gpio_enable_wps_push_button_rpcdata *, qcsapi_gpio_enable_wps_push_button_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_COUNT_ASSOCIATIONS_REMOTE 2791
extern  enum clnt_stat qcsapi_wifi_get_count_associations_remote_1(qcsapi_wifi_get_count_associations_rpcdata *, qcsapi_wifi_get_count_associations_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_count_associations_remote_1_svc(qcsapi_wifi_get_count_associations_rpcdata *, qcsapi_wifi_get_count_associations_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_ASSOCIATED_DEVICE_MAC_ADDR_REMOTE 2801
extern  enum clnt_stat qcsapi_wifi_get_associated_device_mac_addr_remote_1(qcsapi_wifi_get_associated_device_mac_addr_rpcdata *, qcsapi_wifi_get_associated_device_mac_addr_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_associated_device_mac_addr_remote_1_svc(qcsapi_wifi_get_associated_device_mac_addr_rpcdata *, qcsapi_wifi_get_associated_device_mac_addr_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_ASSOCIATED_DEVICE_IP_ADDR_REMOTE 2811
extern  enum clnt_stat qcsapi_wifi_get_associated_device_ip_addr_remote_1(qcsapi_wifi_get_associated_device_ip_addr_rpcdata *, qcsapi_wifi_get_associated_device_ip_addr_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_associated_device_ip_addr_remote_1_svc(qcsapi_wifi_get_associated_device_ip_addr_rpcdata *, qcsapi_wifi_get_associated_device_ip_addr_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_LINK_QUALITY_REMOTE 2821
extern  enum clnt_stat qcsapi_wifi_get_link_quality_remote_1(qcsapi_wifi_get_link_quality_rpcdata *, qcsapi_wifi_get_link_quality_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_link_quality_remote_1_svc(qcsapi_wifi_get_link_quality_rpcdata *, qcsapi_wifi_get_link_quality_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_LINK_QUALITY_MAX_REMOTE 5851
extern  enum clnt_stat qcsapi_wifi_get_link_quality_max_remote_1(qcsapi_wifi_get_link_quality_max_rpcdata *, qcsapi_wifi_get_link_quality_max_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_link_quality_max_remote_1_svc(qcsapi_wifi_get_link_quality_max_rpcdata *, qcsapi_wifi_get_link_quality_max_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_RX_BYTES_PER_ASSOCIATION_REMOTE 2831
extern  enum clnt_stat qcsapi_wifi_get_rx_bytes_per_association_remote_1(qcsapi_wifi_get_rx_bytes_per_association_rpcdata *, qcsapi_wifi_get_rx_bytes_per_association_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_rx_bytes_per_association_remote_1_svc(qcsapi_wifi_get_rx_bytes_per_association_rpcdata *, qcsapi_wifi_get_rx_bytes_per_association_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_TX_BYTES_PER_ASSOCIATION_REMOTE 2841
extern  enum clnt_stat qcsapi_wifi_get_tx_bytes_per_association_remote_1(qcsapi_wifi_get_tx_bytes_per_association_rpcdata *, qcsapi_wifi_get_tx_bytes_per_association_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_tx_bytes_per_association_remote_1_svc(qcsapi_wifi_get_tx_bytes_per_association_rpcdata *, qcsapi_wifi_get_tx_bytes_per_association_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_RX_PACKETS_PER_ASSOCIATION_REMOTE 2851
extern  enum clnt_stat qcsapi_wifi_get_rx_packets_per_association_remote_1(qcsapi_wifi_get_rx_packets_per_association_rpcdata *, qcsapi_wifi_get_rx_packets_per_association_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_rx_packets_per_association_remote_1_svc(qcsapi_wifi_get_rx_packets_per_association_rpcdata *, qcsapi_wifi_get_rx_packets_per_association_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_TX_PACKETS_PER_ASSOCIATION_REMOTE 2861
extern  enum clnt_stat qcsapi_wifi_get_tx_packets_per_association_remote_1(qcsapi_wifi_get_tx_packets_per_association_rpcdata *, qcsapi_wifi_get_tx_packets_per_association_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_tx_packets_per_association_remote_1_svc(qcsapi_wifi_get_tx_packets_per_association_rpcdata *, qcsapi_wifi_get_tx_packets_per_association_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_TX_ERR_PACKETS_PER_ASSOCIATION_REMOTE 2871
extern  enum clnt_stat qcsapi_wifi_get_tx_err_packets_per_association_remote_1(qcsapi_wifi_get_tx_err_packets_per_association_rpcdata *, qcsapi_wifi_get_tx_err_packets_per_association_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_tx_err_packets_per_association_remote_1_svc(qcsapi_wifi_get_tx_err_packets_per_association_rpcdata *, qcsapi_wifi_get_tx_err_packets_per_association_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_RSSI_PER_ASSOCIATION_REMOTE 2881
extern  enum clnt_stat qcsapi_wifi_get_rssi_per_association_remote_1(qcsapi_wifi_get_rssi_per_association_rpcdata *, qcsapi_wifi_get_rssi_per_association_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_rssi_per_association_remote_1_svc(qcsapi_wifi_get_rssi_per_association_rpcdata *, qcsapi_wifi_get_rssi_per_association_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_RSSI_IN_DBM_PER_ASSOCIATION_REMOTE 2891
extern  enum clnt_stat qcsapi_wifi_get_rssi_in_dbm_per_association_remote_1(qcsapi_wifi_get_rssi_in_dbm_per_association_rpcdata *, qcsapi_wifi_get_rssi_in_dbm_per_association_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_rssi_in_dbm_per_association_remote_1_svc(qcsapi_wifi_get_rssi_in_dbm_per_association_rpcdata *, qcsapi_wifi_get_rssi_in_dbm_per_association_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_BW_PER_ASSOCIATION_REMOTE 2901
extern  enum clnt_stat qcsapi_wifi_get_bw_per_association_remote_1(qcsapi_wifi_get_bw_per_association_rpcdata *, qcsapi_wifi_get_bw_per_association_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_bw_per_association_remote_1_svc(qcsapi_wifi_get_bw_per_association_rpcdata *, qcsapi_wifi_get_bw_per_association_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_TX_PHY_RATE_PER_ASSOCIATION_REMOTE 2911
extern  enum clnt_stat qcsapi_wifi_get_tx_phy_rate_per_association_remote_1(qcsapi_wifi_get_tx_phy_rate_per_association_rpcdata *, qcsapi_wifi_get_tx_phy_rate_per_association_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_tx_phy_rate_per_association_remote_1_svc(qcsapi_wifi_get_tx_phy_rate_per_association_rpcdata *, qcsapi_wifi_get_tx_phy_rate_per_association_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_RX_PHY_RATE_PER_ASSOCIATION_REMOTE 2921
extern  enum clnt_stat qcsapi_wifi_get_rx_phy_rate_per_association_remote_1(qcsapi_wifi_get_rx_phy_rate_per_association_rpcdata *, qcsapi_wifi_get_rx_phy_rate_per_association_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_rx_phy_rate_per_association_remote_1_svc(qcsapi_wifi_get_rx_phy_rate_per_association_rpcdata *, qcsapi_wifi_get_rx_phy_rate_per_association_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_TX_MCS_PER_ASSOCIATION_REMOTE 2931
extern  enum clnt_stat qcsapi_wifi_get_tx_mcs_per_association_remote_1(qcsapi_wifi_get_tx_mcs_per_association_rpcdata *, qcsapi_wifi_get_tx_mcs_per_association_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_tx_mcs_per_association_remote_1_svc(qcsapi_wifi_get_tx_mcs_per_association_rpcdata *, qcsapi_wifi_get_tx_mcs_per_association_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_RX_MCS_PER_ASSOCIATION_REMOTE 2941
extern  enum clnt_stat qcsapi_wifi_get_rx_mcs_per_association_remote_1(qcsapi_wifi_get_rx_mcs_per_association_rpcdata *, qcsapi_wifi_get_rx_mcs_per_association_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_rx_mcs_per_association_remote_1_svc(qcsapi_wifi_get_rx_mcs_per_association_rpcdata *, qcsapi_wifi_get_rx_mcs_per_association_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_ACHIEVABLE_TX_PHY_RATE_PER_ASSOCIATION_REMOTE 2951
extern  enum clnt_stat qcsapi_wifi_get_achievable_tx_phy_rate_per_association_remote_1(qcsapi_wifi_get_achievable_tx_phy_rate_per_association_rpcdata *, qcsapi_wifi_get_achievable_tx_phy_rate_per_association_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_achievable_tx_phy_rate_per_association_remote_1_svc(qcsapi_wifi_get_achievable_tx_phy_rate_per_association_rpcdata *, qcsapi_wifi_get_achievable_tx_phy_rate_per_association_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_ACHIEVABLE_RX_PHY_RATE_PER_ASSOCIATION_REMOTE 2961
extern  enum clnt_stat qcsapi_wifi_get_achievable_rx_phy_rate_per_association_remote_1(qcsapi_wifi_get_achievable_rx_phy_rate_per_association_rpcdata *, qcsapi_wifi_get_achievable_rx_phy_rate_per_association_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_achievable_rx_phy_rate_per_association_remote_1_svc(qcsapi_wifi_get_achievable_rx_phy_rate_per_association_rpcdata *, qcsapi_wifi_get_achievable_rx_phy_rate_per_association_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_AUTH_ENC_PER_ASSOCIATION_REMOTE 2971
extern  enum clnt_stat qcsapi_wifi_get_auth_enc_per_association_remote_1(qcsapi_wifi_get_auth_enc_per_association_rpcdata *, qcsapi_wifi_get_auth_enc_per_association_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_auth_enc_per_association_remote_1_svc(qcsapi_wifi_get_auth_enc_per_association_rpcdata *, qcsapi_wifi_get_auth_enc_per_association_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_TPUT_CAPS_REMOTE 2981
extern  enum clnt_stat qcsapi_wifi_get_tput_caps_remote_1(qcsapi_wifi_get_tput_caps_rpcdata *, qcsapi_wifi_get_tput_caps_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_tput_caps_remote_1_svc(qcsapi_wifi_get_tput_caps_rpcdata *, qcsapi_wifi_get_tput_caps_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_CONNECTION_MODE_REMOTE 2991
extern  enum clnt_stat qcsapi_wifi_get_connection_mode_remote_1(qcsapi_wifi_get_connection_mode_rpcdata *, qcsapi_wifi_get_connection_mode_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_connection_mode_remote_1_svc(qcsapi_wifi_get_connection_mode_rpcdata *, qcsapi_wifi_get_connection_mode_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_VENDOR_PER_ASSOCIATION_REMOTE 3001
extern  enum clnt_stat qcsapi_wifi_get_vendor_per_association_remote_1(qcsapi_wifi_get_vendor_per_association_rpcdata *, qcsapi_wifi_get_vendor_per_association_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_vendor_per_association_remote_1_svc(qcsapi_wifi_get_vendor_per_association_rpcdata *, qcsapi_wifi_get_vendor_per_association_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_MAX_MIMO_REMOTE 4461
extern  enum clnt_stat qcsapi_wifi_get_max_mimo_remote_1(qcsapi_wifi_get_max_mimo_rpcdata *, qcsapi_wifi_get_max_mimo_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_max_mimo_remote_1_svc(qcsapi_wifi_get_max_mimo_rpcdata *, qcsapi_wifi_get_max_mimo_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_SNR_PER_ASSOCIATION_REMOTE 3011
extern  enum clnt_stat qcsapi_wifi_get_snr_per_association_remote_1(qcsapi_wifi_get_snr_per_association_rpcdata *, qcsapi_wifi_get_snr_per_association_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_snr_per_association_remote_1_svc(qcsapi_wifi_get_snr_per_association_rpcdata *, qcsapi_wifi_get_snr_per_association_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_TIME_ASSOCIATED_PER_ASSOCIATION_REMOTE 3021
extern  enum clnt_stat qcsapi_wifi_get_time_associated_per_association_remote_1(qcsapi_wifi_get_time_associated_per_association_rpcdata *, qcsapi_wifi_get_time_associated_per_association_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_time_associated_per_association_remote_1_svc(qcsapi_wifi_get_time_associated_per_association_rpcdata *, qcsapi_wifi_get_time_associated_per_association_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_NODE_PARAM_REMOTE 3031
extern  enum clnt_stat qcsapi_wifi_get_node_param_remote_1(qcsapi_wifi_get_node_param_rpcdata *, qcsapi_wifi_get_node_param_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_node_param_remote_1_svc(qcsapi_wifi_get_node_param_rpcdata *, qcsapi_wifi_get_node_param_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_NODE_COUNTER_REMOTE 3041
extern  enum clnt_stat qcsapi_wifi_get_node_counter_remote_1(qcsapi_wifi_get_node_counter_rpcdata *, qcsapi_wifi_get_node_counter_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_node_counter_remote_1_svc(qcsapi_wifi_get_node_counter_rpcdata *, qcsapi_wifi_get_node_counter_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_NODE_STATS_REMOTE 3051
extern  enum clnt_stat qcsapi_wifi_get_node_stats_remote_1(qcsapi_wifi_get_node_stats_rpcdata *, qcsapi_wifi_get_node_stats_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_node_stats_remote_1_svc(qcsapi_wifi_get_node_stats_rpcdata *, qcsapi_wifi_get_node_stats_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_MAX_QUEUED_REMOTE 3061
extern  enum clnt_stat qcsapi_wifi_get_max_queued_remote_1(qcsapi_wifi_get_max_queued_rpcdata *, qcsapi_wifi_get_max_queued_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_max_queued_remote_1_svc(qcsapi_wifi_get_max_queued_rpcdata *, qcsapi_wifi_get_max_queued_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_HW_NOISE_PER_ASSOCIATION_REMOTE 3071
extern  enum clnt_stat qcsapi_wifi_get_hw_noise_per_association_remote_1(qcsapi_wifi_get_hw_noise_per_association_rpcdata *, qcsapi_wifi_get_hw_noise_per_association_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_hw_noise_per_association_remote_1_svc(qcsapi_wifi_get_hw_noise_per_association_rpcdata *, qcsapi_wifi_get_hw_noise_per_association_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_MLME_STATS_PER_MAC_REMOTE 3591
extern  enum clnt_stat qcsapi_wifi_get_mlme_stats_per_mac_remote_1(qcsapi_wifi_get_mlme_stats_per_mac_rpcdata *, qcsapi_wifi_get_mlme_stats_per_mac_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_mlme_stats_per_mac_remote_1_svc(qcsapi_wifi_get_mlme_stats_per_mac_rpcdata *, qcsapi_wifi_get_mlme_stats_per_mac_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_MLME_STATS_PER_ASSOCIATION_REMOTE 3601
extern  enum clnt_stat qcsapi_wifi_get_mlme_stats_per_association_remote_1(qcsapi_wifi_get_mlme_stats_per_association_rpcdata *, qcsapi_wifi_get_mlme_stats_per_association_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_mlme_stats_per_association_remote_1_svc(qcsapi_wifi_get_mlme_stats_per_association_rpcdata *, qcsapi_wifi_get_mlme_stats_per_association_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_MLME_STATS_MACS_LIST_REMOTE 3611
extern  enum clnt_stat qcsapi_wifi_get_mlme_stats_macs_list_remote_1(qcsapi_wifi_get_mlme_stats_macs_list_rpcdata *, qcsapi_wifi_get_mlme_stats_macs_list_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_mlme_stats_macs_list_remote_1_svc(qcsapi_wifi_get_mlme_stats_macs_list_rpcdata *, qcsapi_wifi_get_mlme_stats_macs_list_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_LIST_REGULATORY_REGIONS_REMOTE 3081
extern  enum clnt_stat qcsapi_wifi_get_list_regulatory_regions_remote_1(qcsapi_wifi_get_list_regulatory_regions_rpcdata *, qcsapi_wifi_get_list_regulatory_regions_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_list_regulatory_regions_remote_1_svc(qcsapi_wifi_get_list_regulatory_regions_rpcdata *, qcsapi_wifi_get_list_regulatory_regions_rpcdata *, struct svc_req *);
#define QCSAPI_REGULATORY_GET_LIST_REGULATORY_REGIONS_REMOTE 3091
extern  enum clnt_stat qcsapi_regulatory_get_list_regulatory_regions_remote_1(qcsapi_regulatory_get_list_regulatory_regions_rpcdata *, qcsapi_regulatory_get_list_regulatory_regions_rpcdata *, CLIENT *);
extern  bool_t qcsapi_regulatory_get_list_regulatory_regions_remote_1_svc(qcsapi_regulatory_get_list_regulatory_regions_rpcdata *, qcsapi_regulatory_get_list_regulatory_regions_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_LIST_REGULATORY_CHANNELS_REMOTE 3101
extern  enum clnt_stat qcsapi_wifi_get_list_regulatory_channels_remote_1(qcsapi_wifi_get_list_regulatory_channels_rpcdata *, qcsapi_wifi_get_list_regulatory_channels_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_list_regulatory_channels_remote_1_svc(qcsapi_wifi_get_list_regulatory_channels_rpcdata *, qcsapi_wifi_get_list_regulatory_channels_rpcdata *, struct svc_req *);
#define QCSAPI_REGULATORY_GET_LIST_REGULATORY_CHANNELS_REMOTE 3111
extern  enum clnt_stat qcsapi_regulatory_get_list_regulatory_channels_remote_1(qcsapi_regulatory_get_list_regulatory_channels_rpcdata *, qcsapi_regulatory_get_list_regulatory_channels_rpcdata *, CLIENT *);
extern  bool_t qcsapi_regulatory_get_list_regulatory_channels_remote_1_svc(qcsapi_regulatory_get_list_regulatory_channels_rpcdata *, qcsapi_regulatory_get_list_regulatory_channels_rpcdata *, struct svc_req *);
#define QCSAPI_REGULATORY_GET_LIST_REGULATORY_BANDS_REMOTE 3121
extern  enum clnt_stat qcsapi_regulatory_get_list_regulatory_bands_remote_1(qcsapi_regulatory_get_list_regulatory_bands_rpcdata *, qcsapi_regulatory_get_list_regulatory_bands_rpcdata *, CLIENT *);
extern  bool_t qcsapi_regulatory_get_list_regulatory_bands_remote_1_svc(qcsapi_regulatory_get_list_regulatory_bands_rpcdata *, qcsapi_regulatory_get_list_regulatory_bands_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_REGULATORY_TX_POWER_REMOTE 3131
extern  enum clnt_stat qcsapi_wifi_get_regulatory_tx_power_remote_1(qcsapi_wifi_get_regulatory_tx_power_rpcdata *, qcsapi_wifi_get_regulatory_tx_power_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_regulatory_tx_power_remote_1_svc(qcsapi_wifi_get_regulatory_tx_power_rpcdata *, qcsapi_wifi_get_regulatory_tx_power_rpcdata *, struct svc_req *);
#define QCSAPI_REGULATORY_GET_REGULATORY_TX_POWER_REMOTE 3141
extern  enum clnt_stat qcsapi_regulatory_get_regulatory_tx_power_remote_1(qcsapi_regulatory_get_regulatory_tx_power_rpcdata *, qcsapi_regulatory_get_regulatory_tx_power_rpcdata *, CLIENT *);
extern  bool_t qcsapi_regulatory_get_regulatory_tx_power_remote_1_svc(qcsapi_regulatory_get_regulatory_tx_power_rpcdata *, qcsapi_regulatory_get_regulatory_tx_power_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_CONFIGURED_TX_POWER_REMOTE 3151
extern  enum clnt_stat qcsapi_wifi_get_configured_tx_power_remote_1(qcsapi_wifi_get_configured_tx_power_rpcdata *, qcsapi_wifi_get_configured_tx_power_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_configured_tx_power_remote_1_svc(qcsapi_wifi_get_configured_tx_power_rpcdata *, qcsapi_wifi_get_configured_tx_power_rpcdata *, struct svc_req *);
#define QCSAPI_REGULATORY_GET_CONFIGURED_TX_POWER_REMOTE 3161
extern  enum clnt_stat qcsapi_regulatory_get_configured_tx_power_remote_1(qcsapi_regulatory_get_configured_tx_power_rpcdata *, qcsapi_regulatory_get_configured_tx_power_rpcdata *, CLIENT *);
extern  bool_t qcsapi_regulatory_get_configured_tx_power_remote_1_svc(qcsapi_regulatory_get_configured_tx_power_rpcdata *, qcsapi_regulatory_get_configured_tx_power_rpcdata *, struct svc_req *);
#define QCSAPI_REGULATORY_GET_CONFIGURED_TX_POWER_EXT_REMOTE 5681
extern  enum clnt_stat qcsapi_regulatory_get_configured_tx_power_ext_remote_1(qcsapi_regulatory_get_configured_tx_power_ext_rpcdata *, qcsapi_regulatory_get_configured_tx_power_ext_rpcdata *, CLIENT *);
extern  bool_t qcsapi_regulatory_get_configured_tx_power_ext_remote_1_svc(qcsapi_regulatory_get_configured_tx_power_ext_rpcdata *, qcsapi_regulatory_get_configured_tx_power_ext_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_REGULATORY_REGION_REMOTE 3171
extern  enum clnt_stat qcsapi_wifi_set_regulatory_region_remote_1(qcsapi_wifi_set_regulatory_region_rpcdata *, qcsapi_wifi_set_regulatory_region_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_regulatory_region_remote_1_svc(qcsapi_wifi_set_regulatory_region_rpcdata *, qcsapi_wifi_set_regulatory_region_rpcdata *, struct svc_req *);
#define QCSAPI_REGULATORY_SET_REGULATORY_REGION_REMOTE 3181
extern  enum clnt_stat qcsapi_regulatory_set_regulatory_region_remote_1(qcsapi_regulatory_set_regulatory_region_rpcdata *, qcsapi_regulatory_set_regulatory_region_rpcdata *, CLIENT *);
extern  bool_t qcsapi_regulatory_set_regulatory_region_remote_1_svc(qcsapi_regulatory_set_regulatory_region_rpcdata *, qcsapi_regulatory_set_regulatory_region_rpcdata *, struct svc_req *);
#define QCSAPI_REGULATORY_RESTORE_REGULATORY_TX_POWER_REMOTE 3191
extern  enum clnt_stat qcsapi_regulatory_restore_regulatory_tx_power_remote_1(qcsapi_regulatory_restore_regulatory_tx_power_rpcdata *, qcsapi_regulatory_restore_regulatory_tx_power_rpcdata *, CLIENT *);
extern  bool_t qcsapi_regulatory_restore_regulatory_tx_power_remote_1_svc(qcsapi_regulatory_restore_regulatory_tx_power_rpcdata *, qcsapi_regulatory_restore_regulatory_tx_power_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_REGULATORY_REGION_REMOTE 3201
extern  enum clnt_stat qcsapi_wifi_get_regulatory_region_remote_1(qcsapi_wifi_get_regulatory_region_rpcdata *, qcsapi_wifi_get_regulatory_region_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_regulatory_region_remote_1_svc(qcsapi_wifi_get_regulatory_region_rpcdata *, qcsapi_wifi_get_regulatory_region_rpcdata *, struct svc_req *);
#define QCSAPI_REGULATORY_OVERWRITE_COUNTRY_CODE_REMOTE 3211
extern  enum clnt_stat qcsapi_regulatory_overwrite_country_code_remote_1(qcsapi_regulatory_overwrite_country_code_rpcdata *, qcsapi_regulatory_overwrite_country_code_rpcdata *, CLIENT *);
extern  bool_t qcsapi_regulatory_overwrite_country_code_remote_1_svc(qcsapi_regulatory_overwrite_country_code_rpcdata *, qcsapi_regulatory_overwrite_country_code_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_REGULATORY_CHANNEL_REMOTE 3221
extern  enum clnt_stat qcsapi_wifi_set_regulatory_channel_remote_1(qcsapi_wifi_set_regulatory_channel_rpcdata *, qcsapi_wifi_set_regulatory_channel_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_regulatory_channel_remote_1_svc(qcsapi_wifi_set_regulatory_channel_rpcdata *, qcsapi_wifi_set_regulatory_channel_rpcdata *, struct svc_req *);
#define QCSAPI_REGULATORY_SET_REGULATORY_CHANNEL_REMOTE 3231
extern  enum clnt_stat qcsapi_regulatory_set_regulatory_channel_remote_1(qcsapi_regulatory_set_regulatory_channel_rpcdata *, qcsapi_regulatory_set_regulatory_channel_rpcdata *, CLIENT *);
extern  bool_t qcsapi_regulatory_set_regulatory_channel_remote_1_svc(qcsapi_regulatory_set_regulatory_channel_rpcdata *, qcsapi_regulatory_set_regulatory_channel_rpcdata *, struct svc_req *);
#define QCSAPI_REGULATORY_GET_DB_VERSION_REMOTE 3241
extern  enum clnt_stat qcsapi_regulatory_get_db_version_remote_1(qcsapi_regulatory_get_db_version_rpcdata *, qcsapi_regulatory_get_db_version_rpcdata *, CLIENT *);
extern  bool_t qcsapi_regulatory_get_db_version_remote_1_svc(qcsapi_regulatory_get_db_version_rpcdata *, qcsapi_regulatory_get_db_version_rpcdata *, struct svc_req *);
#define QCSAPI_REGULATORY_APPLY_TX_POWER_CAP_REMOTE 3251
extern  enum clnt_stat qcsapi_regulatory_apply_tx_power_cap_remote_1(qcsapi_regulatory_apply_tx_power_cap_rpcdata *, qcsapi_regulatory_apply_tx_power_cap_rpcdata *, CLIENT *);
extern  bool_t qcsapi_regulatory_apply_tx_power_cap_remote_1_svc(qcsapi_regulatory_apply_tx_power_cap_rpcdata *, qcsapi_regulatory_apply_tx_power_cap_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_LIST_DFS_CHANNELS_REMOTE 3261
extern  enum clnt_stat qcsapi_wifi_get_list_dfs_channels_remote_1(qcsapi_wifi_get_list_DFS_channels_rpcdata *, qcsapi_wifi_get_list_DFS_channels_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_list_dfs_channels_remote_1_svc(qcsapi_wifi_get_list_DFS_channels_rpcdata *, qcsapi_wifi_get_list_DFS_channels_rpcdata *, struct svc_req *);
#define QCSAPI_REGULATORY_GET_LIST_DFS_CHANNELS_REMOTE 3271
extern  enum clnt_stat qcsapi_regulatory_get_list_dfs_channels_remote_1(qcsapi_regulatory_get_list_DFS_channels_rpcdata *, qcsapi_regulatory_get_list_DFS_channels_rpcdata *, CLIENT *);
extern  bool_t qcsapi_regulatory_get_list_dfs_channels_remote_1_svc(qcsapi_regulatory_get_list_DFS_channels_rpcdata *, qcsapi_regulatory_get_list_DFS_channels_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_IS_CHANNEL_DFS_REMOTE 3281
extern  enum clnt_stat qcsapi_wifi_is_channel_dfs_remote_1(qcsapi_wifi_is_channel_DFS_rpcdata *, qcsapi_wifi_is_channel_DFS_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_is_channel_dfs_remote_1_svc(qcsapi_wifi_is_channel_DFS_rpcdata *, qcsapi_wifi_is_channel_DFS_rpcdata *, struct svc_req *);
#define QCSAPI_REGULATORY_IS_CHANNEL_DFS_REMOTE 3291
extern  enum clnt_stat qcsapi_regulatory_is_channel_dfs_remote_1(qcsapi_regulatory_is_channel_DFS_rpcdata *, qcsapi_regulatory_is_channel_DFS_rpcdata *, CLIENT *);
extern  bool_t qcsapi_regulatory_is_channel_dfs_remote_1_svc(qcsapi_regulatory_is_channel_DFS_rpcdata *, qcsapi_regulatory_is_channel_DFS_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_DFS_CCE_CHANNELS_REMOTE 3301
extern  enum clnt_stat qcsapi_wifi_get_dfs_cce_channels_remote_1(qcsapi_wifi_get_dfs_cce_channels_rpcdata *, qcsapi_wifi_get_dfs_cce_channels_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_dfs_cce_channels_remote_1_svc(qcsapi_wifi_get_dfs_cce_channels_rpcdata *, qcsapi_wifi_get_dfs_cce_channels_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_DFS_ALT_CHANNEL_REMOTE 3311
extern  enum clnt_stat qcsapi_wifi_get_dfs_alt_channel_remote_1(qcsapi_wifi_get_DFS_alt_channel_rpcdata *, qcsapi_wifi_get_DFS_alt_channel_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_dfs_alt_channel_remote_1_svc(qcsapi_wifi_get_DFS_alt_channel_rpcdata *, qcsapi_wifi_get_DFS_alt_channel_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_DFS_ALT_CHANNEL_REMOTE 3321
extern  enum clnt_stat qcsapi_wifi_set_dfs_alt_channel_remote_1(qcsapi_wifi_set_DFS_alt_channel_rpcdata *, qcsapi_wifi_set_DFS_alt_channel_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_dfs_alt_channel_remote_1_svc(qcsapi_wifi_set_DFS_alt_channel_rpcdata *, qcsapi_wifi_set_DFS_alt_channel_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_START_DFS_REENTRY_REMOTE 3331
extern  enum clnt_stat qcsapi_wifi_start_dfs_reentry_remote_1(qcsapi_wifi_start_dfs_reentry_rpcdata *, qcsapi_wifi_start_dfs_reentry_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_start_dfs_reentry_remote_1_svc(qcsapi_wifi_start_dfs_reentry_rpcdata *, qcsapi_wifi_start_dfs_reentry_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_START_SCAN_EXT_REMOTE 3341
extern  enum clnt_stat qcsapi_wifi_start_scan_ext_remote_1(qcsapi_wifi_start_scan_ext_rpcdata *, qcsapi_wifi_start_scan_ext_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_start_scan_ext_remote_1_svc(qcsapi_wifi_start_scan_ext_rpcdata *, qcsapi_wifi_start_scan_ext_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_CSW_RECORDS_REMOTE 3351
extern  enum clnt_stat qcsapi_wifi_get_csw_records_remote_1(qcsapi_wifi_get_csw_records_rpcdata *, qcsapi_wifi_get_csw_records_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_csw_records_remote_1_svc(qcsapi_wifi_get_csw_records_rpcdata *, qcsapi_wifi_get_csw_records_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_RADAR_STATUS_REMOTE 3361
extern  enum clnt_stat qcsapi_wifi_get_radar_status_remote_1(qcsapi_wifi_get_radar_status_rpcdata *, qcsapi_wifi_get_radar_status_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_radar_status_remote_1_svc(qcsapi_wifi_get_radar_status_rpcdata *, qcsapi_wifi_get_radar_status_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_CAC_STATUS_REMOTE 1191
extern  enum clnt_stat qcsapi_wifi_get_cac_status_remote_1(qcsapi_wifi_get_cac_status_rpcdata *, qcsapi_wifi_get_cac_status_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_cac_status_remote_1_svc(qcsapi_wifi_get_cac_status_rpcdata *, qcsapi_wifi_get_cac_status_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_RESULTS_AP_SCAN_REMOTE 3371
extern  enum clnt_stat qcsapi_wifi_get_results_ap_scan_remote_1(qcsapi_wifi_get_results_AP_scan_rpcdata *, qcsapi_wifi_get_results_AP_scan_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_results_ap_scan_remote_1_svc(qcsapi_wifi_get_results_AP_scan_rpcdata *, qcsapi_wifi_get_results_AP_scan_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_COUNT_APS_SCANNED_REMOTE 3381
extern  enum clnt_stat qcsapi_wifi_get_count_aps_scanned_remote_1(qcsapi_wifi_get_count_APs_scanned_rpcdata *, qcsapi_wifi_get_count_APs_scanned_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_count_aps_scanned_remote_1_svc(qcsapi_wifi_get_count_APs_scanned_rpcdata *, qcsapi_wifi_get_count_APs_scanned_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_PROPERTIES_AP_REMOTE 3391
extern  enum clnt_stat qcsapi_wifi_get_properties_ap_remote_1(qcsapi_wifi_get_properties_AP_rpcdata *, qcsapi_wifi_get_properties_AP_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_properties_ap_remote_1_svc(qcsapi_wifi_get_properties_AP_rpcdata *, qcsapi_wifi_get_properties_AP_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_SCAN_CHK_INV_REMOTE 4491
extern  enum clnt_stat qcsapi_wifi_set_scan_chk_inv_remote_1(qcsapi_wifi_set_scan_chk_inv_rpcdata *, qcsapi_wifi_set_scan_chk_inv_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_scan_chk_inv_remote_1_svc(qcsapi_wifi_set_scan_chk_inv_rpcdata *, qcsapi_wifi_set_scan_chk_inv_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_SCAN_CHK_INV_REMOTE 4501
extern  enum clnt_stat qcsapi_wifi_get_scan_chk_inv_remote_1(qcsapi_wifi_get_scan_chk_inv_rpcdata *, qcsapi_wifi_get_scan_chk_inv_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_scan_chk_inv_remote_1_svc(qcsapi_wifi_get_scan_chk_inv_rpcdata *, qcsapi_wifi_get_scan_chk_inv_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_SCAN_BUF_MAX_SIZE_REMOTE 4301
extern  enum clnt_stat qcsapi_wifi_set_scan_buf_max_size_remote_1(qcsapi_wifi_set_scan_buf_max_size_rpcdata *, qcsapi_wifi_set_scan_buf_max_size_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_scan_buf_max_size_remote_1_svc(qcsapi_wifi_set_scan_buf_max_size_rpcdata *, qcsapi_wifi_set_scan_buf_max_size_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_SCAN_BUF_MAX_SIZE_REMOTE 4311
extern  enum clnt_stat qcsapi_wifi_get_scan_buf_max_size_remote_1(qcsapi_wifi_get_scan_buf_max_size_rpcdata *, qcsapi_wifi_get_scan_buf_max_size_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_scan_buf_max_size_remote_1_svc(qcsapi_wifi_get_scan_buf_max_size_rpcdata *, qcsapi_wifi_get_scan_buf_max_size_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_SCAN_TABLE_MAX_LEN_REMOTE 4321
extern  enum clnt_stat qcsapi_wifi_set_scan_table_max_len_remote_1(qcsapi_wifi_set_scan_table_max_len_rpcdata *, qcsapi_wifi_set_scan_table_max_len_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_scan_table_max_len_remote_1_svc(qcsapi_wifi_set_scan_table_max_len_rpcdata *, qcsapi_wifi_set_scan_table_max_len_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_SCAN_TABLE_MAX_LEN_REMOTE 4331
extern  enum clnt_stat qcsapi_wifi_get_scan_table_max_len_remote_1(qcsapi_wifi_get_scan_table_max_len_rpcdata *, qcsapi_wifi_get_scan_table_max_len_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_scan_table_max_len_remote_1_svc(qcsapi_wifi_get_scan_table_max_len_rpcdata *, qcsapi_wifi_get_scan_table_max_len_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_DWELL_TIMES_REMOTE 1121
extern  enum clnt_stat qcsapi_wifi_set_dwell_times_remote_1(qcsapi_wifi_set_dwell_times_rpcdata *, qcsapi_wifi_set_dwell_times_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_dwell_times_remote_1_svc(qcsapi_wifi_set_dwell_times_rpcdata *, qcsapi_wifi_set_dwell_times_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_DWELL_TIMES_REMOTE 1131
extern  enum clnt_stat qcsapi_wifi_get_dwell_times_remote_1(qcsapi_wifi_get_dwell_times_rpcdata *, qcsapi_wifi_get_dwell_times_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_dwell_times_remote_1_svc(qcsapi_wifi_get_dwell_times_rpcdata *, qcsapi_wifi_get_dwell_times_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_BGSCAN_DWELL_TIMES_REMOTE 1141
extern  enum clnt_stat qcsapi_wifi_set_bgscan_dwell_times_remote_1(qcsapi_wifi_set_bgscan_dwell_times_rpcdata *, qcsapi_wifi_set_bgscan_dwell_times_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_bgscan_dwell_times_remote_1_svc(qcsapi_wifi_set_bgscan_dwell_times_rpcdata *, qcsapi_wifi_set_bgscan_dwell_times_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_BGSCAN_DWELL_TIMES_REMOTE 1151
extern  enum clnt_stat qcsapi_wifi_get_bgscan_dwell_times_remote_1(qcsapi_wifi_get_bgscan_dwell_times_rpcdata *, qcsapi_wifi_get_bgscan_dwell_times_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_bgscan_dwell_times_remote_1_svc(qcsapi_wifi_get_bgscan_dwell_times_rpcdata *, qcsapi_wifi_get_bgscan_dwell_times_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_START_SCAN_REMOTE 1161
extern  enum clnt_stat qcsapi_wifi_start_scan_remote_1(qcsapi_wifi_start_scan_rpcdata *, qcsapi_wifi_start_scan_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_start_scan_remote_1_svc(qcsapi_wifi_start_scan_rpcdata *, qcsapi_wifi_start_scan_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_CANCEL_SCAN_REMOTE 1171
extern  enum clnt_stat qcsapi_wifi_cancel_scan_remote_1(qcsapi_wifi_cancel_scan_rpcdata *, qcsapi_wifi_cancel_scan_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_cancel_scan_remote_1_svc(qcsapi_wifi_cancel_scan_rpcdata *, qcsapi_wifi_cancel_scan_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_SCAN_STATUS_REMOTE 1181
extern  enum clnt_stat qcsapi_wifi_get_scan_status_remote_1(qcsapi_wifi_get_scan_status_rpcdata *, qcsapi_wifi_get_scan_status_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_scan_status_remote_1_svc(qcsapi_wifi_get_scan_status_rpcdata *, qcsapi_wifi_get_scan_status_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_ENABLE_BGSCAN_REMOTE 1561
extern  enum clnt_stat qcsapi_wifi_enable_bgscan_remote_1(qcsapi_wifi_enable_bgscan_rpcdata *, qcsapi_wifi_enable_bgscan_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_enable_bgscan_remote_1_svc(qcsapi_wifi_enable_bgscan_rpcdata *, qcsapi_wifi_enable_bgscan_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_BGSCAN_STATUS_REMOTE 1571
extern  enum clnt_stat qcsapi_wifi_get_bgscan_status_remote_1(qcsapi_wifi_get_bgscan_status_rpcdata *, qcsapi_wifi_get_bgscan_status_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_bgscan_status_remote_1_svc(qcsapi_wifi_get_bgscan_status_rpcdata *, qcsapi_wifi_get_bgscan_status_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_WAIT_SCAN_COMPLETES_REMOTE 1201
extern  enum clnt_stat qcsapi_wifi_wait_scan_completes_remote_1(qcsapi_wifi_wait_scan_completes_rpcdata *, qcsapi_wifi_wait_scan_completes_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_wait_scan_completes_remote_1_svc(qcsapi_wifi_wait_scan_completes_rpcdata *, qcsapi_wifi_wait_scan_completes_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_BACKOFF_FAIL_MAX_REMOTE 3401
extern  enum clnt_stat qcsapi_wifi_backoff_fail_max_remote_1(qcsapi_wifi_backoff_fail_max_rpcdata *, qcsapi_wifi_backoff_fail_max_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_backoff_fail_max_remote_1_svc(qcsapi_wifi_backoff_fail_max_rpcdata *, qcsapi_wifi_backoff_fail_max_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_BACKOFF_TIMEOUT_REMOTE 3411
extern  enum clnt_stat qcsapi_wifi_backoff_timeout_remote_1(qcsapi_wifi_backoff_timeout_rpcdata *, qcsapi_wifi_backoff_timeout_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_backoff_timeout_remote_1_svc(qcsapi_wifi_backoff_timeout_rpcdata *, qcsapi_wifi_backoff_timeout_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_MCS_RATE_REMOTE 3421
extern  enum clnt_stat qcsapi_wifi_get_mcs_rate_remote_1(qcsapi_wifi_get_mcs_rate_rpcdata *, qcsapi_wifi_get_mcs_rate_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_mcs_rate_remote_1_svc(qcsapi_wifi_get_mcs_rate_rpcdata *, qcsapi_wifi_get_mcs_rate_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_MCS_RATE_REMOTE 3431
extern  enum clnt_stat qcsapi_wifi_set_mcs_rate_remote_1(qcsapi_wifi_set_mcs_rate_rpcdata *, qcsapi_wifi_set_mcs_rate_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_mcs_rate_remote_1_svc(qcsapi_wifi_set_mcs_rate_rpcdata *, qcsapi_wifi_set_mcs_rate_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_PAIRING_ID_REMOTE 3441
extern  enum clnt_stat qcsapi_wifi_set_pairing_id_remote_1(qcsapi_wifi_set_pairing_id_rpcdata *, qcsapi_wifi_set_pairing_id_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_pairing_id_remote_1_svc(qcsapi_wifi_set_pairing_id_rpcdata *, qcsapi_wifi_set_pairing_id_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_PAIRING_ID_REMOTE 3451
extern  enum clnt_stat qcsapi_wifi_get_pairing_id_remote_1(qcsapi_wifi_get_pairing_id_rpcdata *, qcsapi_wifi_get_pairing_id_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_pairing_id_remote_1_svc(qcsapi_wifi_get_pairing_id_rpcdata *, qcsapi_wifi_get_pairing_id_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_PAIRING_ENABLE_REMOTE 3461
extern  enum clnt_stat qcsapi_wifi_set_pairing_enable_remote_1(qcsapi_wifi_set_pairing_enable_rpcdata *, qcsapi_wifi_set_pairing_enable_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_pairing_enable_remote_1_svc(qcsapi_wifi_set_pairing_enable_rpcdata *, qcsapi_wifi_set_pairing_enable_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_PAIRING_ENABLE_REMOTE 3471
extern  enum clnt_stat qcsapi_wifi_get_pairing_enable_remote_1(qcsapi_wifi_get_pairing_enable_rpcdata *, qcsapi_wifi_get_pairing_enable_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_pairing_enable_remote_1_svc(qcsapi_wifi_get_pairing_enable_rpcdata *, qcsapi_wifi_get_pairing_enable_rpcdata *, struct svc_req *);
#define QCSAPI_NON_WPS_SET_PP_ENABLE_REMOTE 3481
extern  enum clnt_stat qcsapi_non_wps_set_pp_enable_remote_1(qcsapi_non_wps_set_pp_enable_rpcdata *, qcsapi_non_wps_set_pp_enable_rpcdata *, CLIENT *);
extern  bool_t qcsapi_non_wps_set_pp_enable_remote_1_svc(qcsapi_non_wps_set_pp_enable_rpcdata *, qcsapi_non_wps_set_pp_enable_rpcdata *, struct svc_req *);
#define QCSAPI_NON_WPS_GET_PP_ENABLE_REMOTE 3491
extern  enum clnt_stat qcsapi_non_wps_get_pp_enable_remote_1(qcsapi_non_wps_get_pp_enable_rpcdata *, qcsapi_non_wps_get_pp_enable_rpcdata *, CLIENT *);
extern  bool_t qcsapi_non_wps_get_pp_enable_remote_1_svc(qcsapi_non_wps_get_pp_enable_rpcdata *, qcsapi_non_wps_get_pp_enable_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_VENDOR_FIX_REMOTE 3501
extern  enum clnt_stat qcsapi_wifi_set_vendor_fix_remote_1(qcsapi_wifi_set_vendor_fix_rpcdata *, qcsapi_wifi_set_vendor_fix_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_vendor_fix_remote_1_svc(qcsapi_wifi_set_vendor_fix_rpcdata *, qcsapi_wifi_set_vendor_fix_rpcdata *, struct svc_req *);
#define QCSAPI_ERRNO_GET_MESSAGE_REMOTE 3511
extern  enum clnt_stat qcsapi_errno_get_message_remote_1(qcsapi_errno_get_message_rpcdata *, qcsapi_errno_get_message_rpcdata *, CLIENT *);
extern  bool_t qcsapi_errno_get_message_remote_1_svc(qcsapi_errno_get_message_rpcdata *, qcsapi_errno_get_message_rpcdata *, struct svc_req *);
#define QCSAPI_GET_INTERFACE_STATS_REMOTE 3521
extern  enum clnt_stat qcsapi_get_interface_stats_remote_1(qcsapi_get_interface_stats_rpcdata *, qcsapi_get_interface_stats_rpcdata *, CLIENT *);
extern  bool_t qcsapi_get_interface_stats_remote_1_svc(qcsapi_get_interface_stats_rpcdata *, qcsapi_get_interface_stats_rpcdata *, struct svc_req *);
#define QCSAPI_GET_PHY_STATS_REMOTE 3531
extern  enum clnt_stat qcsapi_get_phy_stats_remote_1(qcsapi_get_phy_stats_rpcdata *, qcsapi_get_phy_stats_rpcdata *, CLIENT *);
extern  bool_t qcsapi_get_phy_stats_remote_1_svc(qcsapi_get_phy_stats_rpcdata *, qcsapi_get_phy_stats_rpcdata *, struct svc_req *);
#define QCSAPI_RESET_ALL_COUNTERS_REMOTE 3541
extern  enum clnt_stat qcsapi_reset_all_counters_remote_1(qcsapi_reset_all_counters_rpcdata *, qcsapi_reset_all_counters_rpcdata *, CLIENT *);
extern  bool_t qcsapi_reset_all_counters_remote_1_svc(qcsapi_reset_all_counters_rpcdata *, qcsapi_reset_all_counters_rpcdata *, struct svc_req *);
#define QCSAPI_GET_UBOOT_INFO_REMOTE 661
extern  enum clnt_stat qcsapi_get_uboot_info_remote_1(qcsapi_get_uboot_info_rpcdata *, qcsapi_get_uboot_info_rpcdata *, CLIENT *);
extern  bool_t qcsapi_get_uboot_info_remote_1_svc(qcsapi_get_uboot_info_rpcdata *, qcsapi_get_uboot_info_rpcdata *, struct svc_req *);
#define QCSAPI_FIRMWARE_GET_VERSION_REMOTE 3551
extern  enum clnt_stat qcsapi_firmware_get_version_remote_1(qcsapi_firmware_get_version_rpcdata *, qcsapi_firmware_get_version_rpcdata *, CLIENT *);
extern  bool_t qcsapi_firmware_get_version_remote_1_svc(qcsapi_firmware_get_version_rpcdata *, qcsapi_firmware_get_version_rpcdata *, struct svc_req *);
#define QCSAPI_FLASH_IMAGE_UPDATE_REMOTE 3561
extern  enum clnt_stat qcsapi_flash_image_update_remote_1(qcsapi_flash_image_update_rpcdata *, qcsapi_flash_image_update_rpcdata *, CLIENT *);
extern  bool_t qcsapi_flash_image_update_remote_1_svc(qcsapi_flash_image_update_rpcdata *, qcsapi_flash_image_update_rpcdata *, struct svc_req *);
#define QCSAPI_SEND_FILE_REMOTE 5961
extern  enum clnt_stat qcsapi_send_file_remote_1(qcsapi_send_file_rpcdata *, qcsapi_send_file_rpcdata *, CLIENT *);
extern  bool_t qcsapi_send_file_remote_1_svc(qcsapi_send_file_rpcdata *, qcsapi_send_file_rpcdata *, struct svc_req *);
#define QCSAPI_PM_SET_MODE_REMOTE 3621
extern  enum clnt_stat qcsapi_pm_set_mode_remote_1(qcsapi_pm_set_mode_rpcdata *, qcsapi_pm_set_mode_rpcdata *, CLIENT *);
extern  bool_t qcsapi_pm_set_mode_remote_1_svc(qcsapi_pm_set_mode_rpcdata *, qcsapi_pm_set_mode_rpcdata *, struct svc_req *);
#define QCSAPI_PM_GET_MODE_REMOTE 3631
extern  enum clnt_stat qcsapi_pm_get_mode_remote_1(qcsapi_pm_get_mode_rpcdata *, qcsapi_pm_get_mode_rpcdata *, CLIENT *);
extern  bool_t qcsapi_pm_get_mode_remote_1_svc(qcsapi_pm_get_mode_rpcdata *, qcsapi_pm_get_mode_rpcdata *, struct svc_req *);
#define QCSAPI_GET_QPM_LEVEL_REMOTE 3641
extern  enum clnt_stat qcsapi_get_qpm_level_remote_1(qcsapi_get_qpm_level_rpcdata *, qcsapi_get_qpm_level_rpcdata *, CLIENT *);
extern  bool_t qcsapi_get_qpm_level_remote_1_svc(qcsapi_get_qpm_level_rpcdata *, qcsapi_get_qpm_level_rpcdata *, struct svc_req *);
#define QCSAPI_SET_HOST_STATE_REMOTE 4151
extern  enum clnt_stat qcsapi_set_host_state_remote_1(qcsapi_set_host_state_rpcdata *, qcsapi_set_host_state_rpcdata *, CLIENT *);
extern  bool_t qcsapi_set_host_state_remote_1_svc(qcsapi_set_host_state_rpcdata *, qcsapi_set_host_state_rpcdata *, struct svc_req *);
#define QCSAPI_QTM_GET_STATE_REMOTE 3651
extern  enum clnt_stat qcsapi_qtm_get_state_remote_1(qcsapi_qtm_get_state_rpcdata *, qcsapi_qtm_get_state_rpcdata *, CLIENT *);
extern  bool_t qcsapi_qtm_get_state_remote_1_svc(qcsapi_qtm_get_state_rpcdata *, qcsapi_qtm_get_state_rpcdata *, struct svc_req *);
#define QCSAPI_QTM_GET_STATE_ALL_REMOTE 3661
extern  enum clnt_stat qcsapi_qtm_get_state_all_remote_1(qcsapi_qtm_get_state_all_rpcdata *, qcsapi_qtm_get_state_all_rpcdata *, CLIENT *);
extern  bool_t qcsapi_qtm_get_state_all_remote_1_svc(qcsapi_qtm_get_state_all_rpcdata *, qcsapi_qtm_get_state_all_rpcdata *, struct svc_req *);
#define QCSAPI_QTM_SET_STATE_REMOTE 3671
extern  enum clnt_stat qcsapi_qtm_set_state_remote_1(qcsapi_qtm_set_state_rpcdata *, qcsapi_qtm_set_state_rpcdata *, CLIENT *);
extern  bool_t qcsapi_qtm_set_state_remote_1_svc(qcsapi_qtm_set_state_rpcdata *, qcsapi_qtm_set_state_rpcdata *, struct svc_req *);
#define QCSAPI_QTM_GET_CONFIG_REMOTE 3681
extern  enum clnt_stat qcsapi_qtm_get_config_remote_1(qcsapi_qtm_get_config_rpcdata *, qcsapi_qtm_get_config_rpcdata *, CLIENT *);
extern  bool_t qcsapi_qtm_get_config_remote_1_svc(qcsapi_qtm_get_config_rpcdata *, qcsapi_qtm_get_config_rpcdata *, struct svc_req *);
#define QCSAPI_QTM_GET_CONFIG_ALL_REMOTE 3691
extern  enum clnt_stat qcsapi_qtm_get_config_all_remote_1(qcsapi_qtm_get_config_all_rpcdata *, qcsapi_qtm_get_config_all_rpcdata *, CLIENT *);
extern  bool_t qcsapi_qtm_get_config_all_remote_1_svc(qcsapi_qtm_get_config_all_rpcdata *, qcsapi_qtm_get_config_all_rpcdata *, struct svc_req *);
#define QCSAPI_QTM_SET_CONFIG_REMOTE 3701
extern  enum clnt_stat qcsapi_qtm_set_config_remote_1(qcsapi_qtm_set_config_rpcdata *, qcsapi_qtm_set_config_rpcdata *, CLIENT *);
extern  bool_t qcsapi_qtm_set_config_remote_1_svc(qcsapi_qtm_set_config_rpcdata *, qcsapi_qtm_set_config_rpcdata *, struct svc_req *);
#define QCSAPI_QTM_ADD_RULE_REMOTE 3751
extern  enum clnt_stat qcsapi_qtm_add_rule_remote_1(qcsapi_qtm_add_rule_rpcdata *, qcsapi_qtm_add_rule_rpcdata *, CLIENT *);
extern  bool_t qcsapi_qtm_add_rule_remote_1_svc(qcsapi_qtm_add_rule_rpcdata *, qcsapi_qtm_add_rule_rpcdata *, struct svc_req *);
#define QCSAPI_QTM_DEL_RULE_REMOTE 3761
extern  enum clnt_stat qcsapi_qtm_del_rule_remote_1(qcsapi_qtm_del_rule_rpcdata *, qcsapi_qtm_del_rule_rpcdata *, CLIENT *);
extern  bool_t qcsapi_qtm_del_rule_remote_1_svc(qcsapi_qtm_del_rule_rpcdata *, qcsapi_qtm_del_rule_rpcdata *, struct svc_req *);
#define QCSAPI_QTM_DEL_RULE_INDEX_REMOTE 3771
extern  enum clnt_stat qcsapi_qtm_del_rule_index_remote_1(qcsapi_qtm_del_rule_index_rpcdata *, qcsapi_qtm_del_rule_index_rpcdata *, CLIENT *);
extern  bool_t qcsapi_qtm_del_rule_index_remote_1_svc(qcsapi_qtm_del_rule_index_rpcdata *, qcsapi_qtm_del_rule_index_rpcdata *, struct svc_req *);
#define QCSAPI_QTM_GET_RULE_REMOTE 3781
extern  enum clnt_stat qcsapi_qtm_get_rule_remote_1(qcsapi_qtm_get_rule_rpcdata *, qcsapi_qtm_get_rule_rpcdata *, CLIENT *);
extern  bool_t qcsapi_qtm_get_rule_remote_1_svc(qcsapi_qtm_get_rule_rpcdata *, qcsapi_qtm_get_rule_rpcdata *, struct svc_req *);
#define QCSAPI_QTM_GET_STRM_REMOTE 3791
extern  enum clnt_stat qcsapi_qtm_get_strm_remote_1(qcsapi_qtm_get_strm_rpcdata *, qcsapi_qtm_get_strm_rpcdata *, CLIENT *);
extern  bool_t qcsapi_qtm_get_strm_remote_1_svc(qcsapi_qtm_get_strm_rpcdata *, qcsapi_qtm_get_strm_rpcdata *, struct svc_req *);
#define QCSAPI_QTM_GET_STATS_REMOTE 3801
extern  enum clnt_stat qcsapi_qtm_get_stats_remote_1(qcsapi_qtm_get_stats_rpcdata *, qcsapi_qtm_get_stats_rpcdata *, CLIENT *);
extern  bool_t qcsapi_qtm_get_stats_remote_1_svc(qcsapi_qtm_get_stats_rpcdata *, qcsapi_qtm_get_stats_rpcdata *, struct svc_req *);
#define QCSAPI_QTM_GET_INACTIVE_FLAGS_REMOTE 3811
extern  enum clnt_stat qcsapi_qtm_get_inactive_flags_remote_1(qcsapi_qtm_get_inactive_flags_rpcdata *, qcsapi_qtm_get_inactive_flags_rpcdata *, CLIENT *);
extern  bool_t qcsapi_qtm_get_inactive_flags_remote_1_svc(qcsapi_qtm_get_inactive_flags_rpcdata *, qcsapi_qtm_get_inactive_flags_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_RUN_SCRIPT_REMOTE 3821
extern  enum clnt_stat qcsapi_wifi_run_script_remote_1(qcsapi_wifi_run_script_rpcdata *, qcsapi_wifi_run_script_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_run_script_remote_1_svc(qcsapi_wifi_run_script_rpcdata *, qcsapi_wifi_run_script_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_TEST_TRAFFIC_REMOTE 3831
extern  enum clnt_stat qcsapi_wifi_test_traffic_remote_1(qcsapi_wifi_test_traffic_rpcdata *, qcsapi_wifi_test_traffic_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_test_traffic_remote_1_svc(qcsapi_wifi_test_traffic_rpcdata *, qcsapi_wifi_test_traffic_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_ADD_IPFF_REMOTE 3841
extern  enum clnt_stat qcsapi_wifi_add_ipff_remote_1(qcsapi_wifi_add_ipff_rpcdata *, qcsapi_wifi_add_ipff_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_add_ipff_remote_1_svc(qcsapi_wifi_add_ipff_rpcdata *, qcsapi_wifi_add_ipff_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_DEL_IPFF_REMOTE 3851
extern  enum clnt_stat qcsapi_wifi_del_ipff_remote_1(qcsapi_wifi_del_ipff_rpcdata *, qcsapi_wifi_del_ipff_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_del_ipff_remote_1_svc(qcsapi_wifi_del_ipff_rpcdata *, qcsapi_wifi_del_ipff_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_IPFF_REMOTE 3861
extern  enum clnt_stat qcsapi_wifi_get_ipff_remote_1(qcsapi_wifi_get_ipff_rpcdata *, qcsapi_wifi_get_ipff_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_ipff_remote_1_svc(qcsapi_wifi_get_ipff_rpcdata *, qcsapi_wifi_get_ipff_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_RTS_THRESHOLD_REMOTE 3871
extern  enum clnt_stat qcsapi_wifi_get_rts_threshold_remote_1(qcsapi_wifi_get_rts_threshold_rpcdata *, qcsapi_wifi_get_rts_threshold_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_rts_threshold_remote_1_svc(qcsapi_wifi_get_rts_threshold_rpcdata *, qcsapi_wifi_get_rts_threshold_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_RTS_THRESHOLD_REMOTE 3881
extern  enum clnt_stat qcsapi_wifi_set_rts_threshold_remote_1(qcsapi_wifi_set_rts_threshold_rpcdata *, qcsapi_wifi_set_rts_threshold_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_rts_threshold_remote_1_svc(qcsapi_wifi_set_rts_threshold_rpcdata *, qcsapi_wifi_set_rts_threshold_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_NSS_CAP_REMOTE 4131
extern  enum clnt_stat qcsapi_wifi_set_nss_cap_remote_1(qcsapi_wifi_set_nss_cap_rpcdata *, qcsapi_wifi_set_nss_cap_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_nss_cap_remote_1_svc(qcsapi_wifi_set_nss_cap_rpcdata *, qcsapi_wifi_set_nss_cap_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_NSS_CAP_REMOTE 4141
extern  enum clnt_stat qcsapi_wifi_get_nss_cap_remote_1(qcsapi_wifi_get_nss_cap_rpcdata *, qcsapi_wifi_get_nss_cap_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_nss_cap_remote_1_svc(qcsapi_wifi_get_nss_cap_rpcdata *, qcsapi_wifi_get_nss_cap_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_TX_AMSDU_REMOTE 4171
extern  enum clnt_stat qcsapi_wifi_get_tx_amsdu_remote_1(qcsapi_wifi_get_tx_amsdu_rpcdata *, qcsapi_wifi_get_tx_amsdu_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_tx_amsdu_remote_1_svc(qcsapi_wifi_get_tx_amsdu_rpcdata *, qcsapi_wifi_get_tx_amsdu_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_TX_AMSDU_REMOTE 4181
extern  enum clnt_stat qcsapi_wifi_set_tx_amsdu_remote_1(qcsapi_wifi_set_tx_amsdu_rpcdata *, qcsapi_wifi_set_tx_amsdu_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_tx_amsdu_remote_1_svc(qcsapi_wifi_set_tx_amsdu_rpcdata *, qcsapi_wifi_set_tx_amsdu_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_DISASSOC_REASON_REMOTE 4271
extern  enum clnt_stat qcsapi_wifi_get_disassoc_reason_remote_1(qcsapi_wifi_get_disassoc_reason_rpcdata *, qcsapi_wifi_get_disassoc_reason_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_disassoc_reason_remote_1_svc(qcsapi_wifi_get_disassoc_reason_rpcdata *, qcsapi_wifi_get_disassoc_reason_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_BLOCK_BSS_REMOTE 6201
extern  enum clnt_stat qcsapi_wifi_block_bss_remote_1(qcsapi_wifi_block_bss_rpcdata *, qcsapi_wifi_block_bss_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_block_bss_remote_1_svc(qcsapi_wifi_block_bss_rpcdata *, qcsapi_wifi_block_bss_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_VERIFY_REPEATER_MODE_REMOTE 6171
extern  enum clnt_stat qcsapi_wifi_verify_repeater_mode_remote_1(qcsapi_wifi_verify_repeater_mode_rpcdata *, qcsapi_wifi_verify_repeater_mode_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_verify_repeater_mode_remote_1_svc(qcsapi_wifi_verify_repeater_mode_rpcdata *, qcsapi_wifi_verify_repeater_mode_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_AP_INTERFACE_NAME_REMOTE 6181
extern  enum clnt_stat qcsapi_wifi_set_ap_interface_name_remote_1(qcsapi_wifi_set_ap_interface_name_rpcdata *, qcsapi_wifi_set_ap_interface_name_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_ap_interface_name_remote_1_svc(qcsapi_wifi_set_ap_interface_name_rpcdata *, qcsapi_wifi_set_ap_interface_name_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_AP_INTERFACE_NAME_REMOTE 6191
extern  enum clnt_stat qcsapi_wifi_get_ap_interface_name_remote_1(qcsapi_wifi_get_ap_interface_name_rpcdata *, qcsapi_wifi_get_ap_interface_name_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_ap_interface_name_remote_1_svc(qcsapi_wifi_get_ap_interface_name_rpcdata *, qcsapi_wifi_get_ap_interface_name_rpcdata *, struct svc_req *);
#define QCSAPI_GET_TEMPERATURE_INFO_REMOTE 3892
extern  enum clnt_stat qcsapi_get_temperature_info_remote_1(qcsapi_get_temperature_info_rpcdata *, qcsapi_get_temperature_info_rpcdata *, CLIENT *);
extern  bool_t qcsapi_get_temperature_info_remote_1_svc(qcsapi_get_temperature_info_rpcdata *, qcsapi_get_temperature_info_rpcdata *, struct svc_req *);
#define QCSAPI_CALCMD_SET_TEST_MODE_REMOTE 3901
extern  enum clnt_stat qcsapi_calcmd_set_test_mode_remote_1(qcsapi_calcmd_set_test_mode_rpcdata *, qcsapi_calcmd_set_test_mode_rpcdata *, CLIENT *);
extern  bool_t qcsapi_calcmd_set_test_mode_remote_1_svc(qcsapi_calcmd_set_test_mode_rpcdata *, qcsapi_calcmd_set_test_mode_rpcdata *, struct svc_req *);
#define QCSAPI_CALCMD_SHOW_TEST_PACKET_REMOTE 3911
extern  enum clnt_stat qcsapi_calcmd_show_test_packet_remote_1(qcsapi_calcmd_show_test_packet_rpcdata *, qcsapi_calcmd_show_test_packet_rpcdata *, CLIENT *);
extern  bool_t qcsapi_calcmd_show_test_packet_remote_1_svc(qcsapi_calcmd_show_test_packet_rpcdata *, qcsapi_calcmd_show_test_packet_rpcdata *, struct svc_req *);
#define QCSAPI_CALCMD_SEND_TEST_PACKET_REMOTE 3921
extern  enum clnt_stat qcsapi_calcmd_send_test_packet_remote_1(qcsapi_calcmd_send_test_packet_rpcdata *, qcsapi_calcmd_send_test_packet_rpcdata *, CLIENT *);
extern  bool_t qcsapi_calcmd_send_test_packet_remote_1_svc(qcsapi_calcmd_send_test_packet_rpcdata *, qcsapi_calcmd_send_test_packet_rpcdata *, struct svc_req *);
#define QCSAPI_CALCMD_STOP_TEST_PACKET_REMOTE 3931
extern  enum clnt_stat qcsapi_calcmd_stop_test_packet_remote_1(qcsapi_calcmd_stop_test_packet_rpcdata *, qcsapi_calcmd_stop_test_packet_rpcdata *, CLIENT *);
extern  bool_t qcsapi_calcmd_stop_test_packet_remote_1_svc(qcsapi_calcmd_stop_test_packet_rpcdata *, qcsapi_calcmd_stop_test_packet_rpcdata *, struct svc_req *);
#define QCSAPI_CALCMD_SEND_DC_CW_SIGNAL_REMOTE 3941
extern  enum clnt_stat qcsapi_calcmd_send_dc_cw_signal_remote_1(qcsapi_calcmd_send_dc_cw_signal_rpcdata *, qcsapi_calcmd_send_dc_cw_signal_rpcdata *, CLIENT *);
extern  bool_t qcsapi_calcmd_send_dc_cw_signal_remote_1_svc(qcsapi_calcmd_send_dc_cw_signal_rpcdata *, qcsapi_calcmd_send_dc_cw_signal_rpcdata *, struct svc_req *);
#define QCSAPI_CALCMD_STOP_DC_CW_SIGNAL_REMOTE 3951
extern  enum clnt_stat qcsapi_calcmd_stop_dc_cw_signal_remote_1(qcsapi_calcmd_stop_dc_cw_signal_rpcdata *, qcsapi_calcmd_stop_dc_cw_signal_rpcdata *, CLIENT *);
extern  bool_t qcsapi_calcmd_stop_dc_cw_signal_remote_1_svc(qcsapi_calcmd_stop_dc_cw_signal_rpcdata *, qcsapi_calcmd_stop_dc_cw_signal_rpcdata *, struct svc_req *);
#define QCSAPI_CALCMD_GET_TEST_MODE_ANTENNA_SEL_REMOTE 3961
extern  enum clnt_stat qcsapi_calcmd_get_test_mode_antenna_sel_remote_1(qcsapi_calcmd_get_test_mode_antenna_sel_rpcdata *, qcsapi_calcmd_get_test_mode_antenna_sel_rpcdata *, CLIENT *);
extern  bool_t qcsapi_calcmd_get_test_mode_antenna_sel_remote_1_svc(qcsapi_calcmd_get_test_mode_antenna_sel_rpcdata *, qcsapi_calcmd_get_test_mode_antenna_sel_rpcdata *, struct svc_req *);
#define QCSAPI_CALCMD_GET_TEST_MODE_MCS_REMOTE 3971
extern  enum clnt_stat qcsapi_calcmd_get_test_mode_mcs_remote_1(qcsapi_calcmd_get_test_mode_mcs_rpcdata *, qcsapi_calcmd_get_test_mode_mcs_rpcdata *, CLIENT *);
extern  bool_t qcsapi_calcmd_get_test_mode_mcs_remote_1_svc(qcsapi_calcmd_get_test_mode_mcs_rpcdata *, qcsapi_calcmd_get_test_mode_mcs_rpcdata *, struct svc_req *);
#define QCSAPI_CALCMD_GET_TEST_MODE_BW_REMOTE 3981
extern  enum clnt_stat qcsapi_calcmd_get_test_mode_bw_remote_1(qcsapi_calcmd_get_test_mode_bw_rpcdata *, qcsapi_calcmd_get_test_mode_bw_rpcdata *, CLIENT *);
extern  bool_t qcsapi_calcmd_get_test_mode_bw_remote_1_svc(qcsapi_calcmd_get_test_mode_bw_rpcdata *, qcsapi_calcmd_get_test_mode_bw_rpcdata *, struct svc_req *);
#define QCSAPI_CALCMD_GET_TX_POWER_REMOTE 3991
extern  enum clnt_stat qcsapi_calcmd_get_tx_power_remote_1(qcsapi_calcmd_get_tx_power_rpcdata *, qcsapi_calcmd_get_tx_power_rpcdata *, CLIENT *);
extern  bool_t qcsapi_calcmd_get_tx_power_remote_1_svc(qcsapi_calcmd_get_tx_power_rpcdata *, qcsapi_calcmd_get_tx_power_rpcdata *, struct svc_req *);
#define QCSAPI_CALCMD_SET_TX_POWER_REMOTE 4001
extern  enum clnt_stat qcsapi_calcmd_set_tx_power_remote_1(qcsapi_calcmd_set_tx_power_rpcdata *, qcsapi_calcmd_set_tx_power_rpcdata *, CLIENT *);
extern  bool_t qcsapi_calcmd_set_tx_power_remote_1_svc(qcsapi_calcmd_set_tx_power_rpcdata *, qcsapi_calcmd_set_tx_power_rpcdata *, struct svc_req *);
#define QCSAPI_CALCMD_GET_TEST_MODE_RSSI_REMOTE 4011
extern  enum clnt_stat qcsapi_calcmd_get_test_mode_rssi_remote_1(qcsapi_calcmd_get_test_mode_rssi_rpcdata *, qcsapi_calcmd_get_test_mode_rssi_rpcdata *, CLIENT *);
extern  bool_t qcsapi_calcmd_get_test_mode_rssi_remote_1_svc(qcsapi_calcmd_get_test_mode_rssi_rpcdata *, qcsapi_calcmd_get_test_mode_rssi_rpcdata *, struct svc_req *);
#define QCSAPI_CALCMD_SET_MAC_FILTER_REMOTE 4021
extern  enum clnt_stat qcsapi_calcmd_set_mac_filter_remote_1(qcsapi_calcmd_set_mac_filter_rpcdata *, qcsapi_calcmd_set_mac_filter_rpcdata *, CLIENT *);
extern  bool_t qcsapi_calcmd_set_mac_filter_remote_1_svc(qcsapi_calcmd_set_mac_filter_rpcdata *, qcsapi_calcmd_set_mac_filter_rpcdata *, struct svc_req *);
#define QCSAPI_CALCMD_GET_ANTENNA_COUNT_REMOTE 4031
extern  enum clnt_stat qcsapi_calcmd_get_antenna_count_remote_1(qcsapi_calcmd_get_antenna_count_rpcdata *, qcsapi_calcmd_get_antenna_count_rpcdata *, CLIENT *);
extern  bool_t qcsapi_calcmd_get_antenna_count_remote_1_svc(qcsapi_calcmd_get_antenna_count_rpcdata *, qcsapi_calcmd_get_antenna_count_rpcdata *, struct svc_req *);
#define QCSAPI_CALCMD_CLEAR_COUNTER_REMOTE 4041
extern  enum clnt_stat qcsapi_calcmd_clear_counter_remote_1(qcsapi_calcmd_clear_counter_rpcdata *, qcsapi_calcmd_clear_counter_rpcdata *, CLIENT *);
extern  bool_t qcsapi_calcmd_clear_counter_remote_1_svc(qcsapi_calcmd_clear_counter_rpcdata *, qcsapi_calcmd_clear_counter_rpcdata *, struct svc_req *);
#define QCSAPI_CALCMD_GET_INFO_REMOTE 4051
extern  enum clnt_stat qcsapi_calcmd_get_info_remote_1(qcsapi_calcmd_get_info_rpcdata *, qcsapi_calcmd_get_info_rpcdata *, CLIENT *);
extern  bool_t qcsapi_calcmd_get_info_remote_1_svc(qcsapi_calcmd_get_info_rpcdata *, qcsapi_calcmd_get_info_rpcdata *, struct svc_req *);
#define QCSAPI_WOWLAN_SET_MATCH_TYPE_REMOTE 4161
extern  enum clnt_stat qcsapi_wowlan_set_match_type_remote_1(qcsapi_wowlan_set_match_type_rpcdata *, qcsapi_wowlan_set_match_type_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wowlan_set_match_type_remote_1_svc(qcsapi_wowlan_set_match_type_rpcdata *, qcsapi_wowlan_set_match_type_rpcdata *, struct svc_req *);
#define QCSAPI_WOWLAN_SET_L2_TYPE_REMOTE 4191
extern  enum clnt_stat qcsapi_wowlan_set_l2_type_remote_1(qcsapi_wowlan_set_L2_type_rpcdata *, qcsapi_wowlan_set_L2_type_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wowlan_set_l2_type_remote_1_svc(qcsapi_wowlan_set_L2_type_rpcdata *, qcsapi_wowlan_set_L2_type_rpcdata *, struct svc_req *);
#define QCSAPI_WOWLAN_SET_UDP_PORT_REMOTE 4201
extern  enum clnt_stat qcsapi_wowlan_set_udp_port_remote_1(qcsapi_wowlan_set_udp_port_rpcdata *, qcsapi_wowlan_set_udp_port_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wowlan_set_udp_port_remote_1_svc(qcsapi_wowlan_set_udp_port_rpcdata *, qcsapi_wowlan_set_udp_port_rpcdata *, struct svc_req *);
#define QCSAPI_WOWLAN_SET_MAGIC_PATTERN_REMOTE 4211
extern  enum clnt_stat qcsapi_wowlan_set_magic_pattern_remote_1(qcsapi_wowlan_set_magic_pattern_rpcdata *, qcsapi_wowlan_set_magic_pattern_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wowlan_set_magic_pattern_remote_1_svc(qcsapi_wowlan_set_magic_pattern_rpcdata *, qcsapi_wowlan_set_magic_pattern_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_WOWLAN_GET_HOST_STATE_REMOTE 4221
extern  enum clnt_stat qcsapi_wifi_wowlan_get_host_state_remote_1(qcsapi_wifi_wowlan_get_host_state_rpcdata *, qcsapi_wifi_wowlan_get_host_state_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_wowlan_get_host_state_remote_1_svc(qcsapi_wifi_wowlan_get_host_state_rpcdata *, qcsapi_wifi_wowlan_get_host_state_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_WOWLAN_GET_MATCH_TYPE_REMOTE 4231
extern  enum clnt_stat qcsapi_wifi_wowlan_get_match_type_remote_1(qcsapi_wifi_wowlan_get_match_type_rpcdata *, qcsapi_wifi_wowlan_get_match_type_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_wowlan_get_match_type_remote_1_svc(qcsapi_wifi_wowlan_get_match_type_rpcdata *, qcsapi_wifi_wowlan_get_match_type_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_WOWLAN_GET_L2_TYPE_REMOTE 4241
extern  enum clnt_stat qcsapi_wifi_wowlan_get_l2_type_remote_1(qcsapi_wifi_wowlan_get_l2_type_rpcdata *, qcsapi_wifi_wowlan_get_l2_type_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_wowlan_get_l2_type_remote_1_svc(qcsapi_wifi_wowlan_get_l2_type_rpcdata *, qcsapi_wifi_wowlan_get_l2_type_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_WOWLAN_GET_UDP_PORT_REMOTE 4251
extern  enum clnt_stat qcsapi_wifi_wowlan_get_udp_port_remote_1(qcsapi_wifi_wowlan_get_udp_port_rpcdata *, qcsapi_wifi_wowlan_get_udp_port_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_wowlan_get_udp_port_remote_1_svc(qcsapi_wifi_wowlan_get_udp_port_rpcdata *, qcsapi_wifi_wowlan_get_udp_port_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_WOWLAN_GET_MAGIC_PATTERN_REMOTE 4261
extern  enum clnt_stat qcsapi_wifi_wowlan_get_magic_pattern_remote_1(qcsapi_wifi_wowlan_get_magic_pattern_rpcdata *, qcsapi_wifi_wowlan_get_magic_pattern_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_wowlan_get_magic_pattern_remote_1_svc(qcsapi_wifi_wowlan_get_magic_pattern_rpcdata *, qcsapi_wifi_wowlan_get_magic_pattern_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_ENABLE_MU_REMOTE 5861
extern  enum clnt_stat qcsapi_wifi_set_enable_mu_remote_1(qcsapi_wifi_set_enable_mu_rpcdata *, qcsapi_wifi_set_enable_mu_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_enable_mu_remote_1_svc(qcsapi_wifi_set_enable_mu_rpcdata *, qcsapi_wifi_set_enable_mu_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_ENABLE_MU_REMOTE 5871
extern  enum clnt_stat qcsapi_wifi_get_enable_mu_remote_1(qcsapi_wifi_get_enable_mu_rpcdata *, qcsapi_wifi_get_enable_mu_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_enable_mu_remote_1_svc(qcsapi_wifi_get_enable_mu_rpcdata *, qcsapi_wifi_get_enable_mu_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_MU_USE_PRECODE_REMOTE 5881
extern  enum clnt_stat qcsapi_wifi_set_mu_use_precode_remote_1(qcsapi_wifi_set_mu_use_precode_rpcdata *, qcsapi_wifi_set_mu_use_precode_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_mu_use_precode_remote_1_svc(qcsapi_wifi_set_mu_use_precode_rpcdata *, qcsapi_wifi_set_mu_use_precode_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_MU_USE_PRECODE_REMOTE 5891
extern  enum clnt_stat qcsapi_wifi_get_mu_use_precode_remote_1(qcsapi_wifi_get_mu_use_precode_rpcdata *, qcsapi_wifi_get_mu_use_precode_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_mu_use_precode_remote_1_svc(qcsapi_wifi_get_mu_use_precode_rpcdata *, qcsapi_wifi_get_mu_use_precode_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_MU_USE_EQ_REMOTE 5901
extern  enum clnt_stat qcsapi_wifi_set_mu_use_eq_remote_1(qcsapi_wifi_set_mu_use_eq_rpcdata *, qcsapi_wifi_set_mu_use_eq_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_mu_use_eq_remote_1_svc(qcsapi_wifi_set_mu_use_eq_rpcdata *, qcsapi_wifi_set_mu_use_eq_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_MU_USE_EQ_REMOTE 5911
extern  enum clnt_stat qcsapi_wifi_get_mu_use_eq_remote_1(qcsapi_wifi_get_mu_use_eq_rpcdata *, qcsapi_wifi_get_mu_use_eq_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_mu_use_eq_remote_1_svc(qcsapi_wifi_get_mu_use_eq_rpcdata *, qcsapi_wifi_get_mu_use_eq_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_MU_GROUPS_REMOTE 5921
extern  enum clnt_stat qcsapi_wifi_get_mu_groups_remote_1(qcsapi_wifi_get_mu_groups_rpcdata *, qcsapi_wifi_get_mu_groups_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_mu_groups_remote_1_svc(qcsapi_wifi_get_mu_groups_rpcdata *, qcsapi_wifi_get_mu_groups_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_ENABLE_TDLS_REMOTE 4111
extern  enum clnt_stat qcsapi_wifi_enable_tdls_remote_1(qcsapi_wifi_enable_tdls_rpcdata *, qcsapi_wifi_enable_tdls_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_enable_tdls_remote_1_svc(qcsapi_wifi_enable_tdls_rpcdata *, qcsapi_wifi_enable_tdls_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_ENABLE_TDLS_OVER_QHOP_REMOTE 4381
extern  enum clnt_stat qcsapi_wifi_enable_tdls_over_qhop_remote_1(qcsapi_wifi_enable_tdls_over_qhop_rpcdata *, qcsapi_wifi_enable_tdls_over_qhop_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_enable_tdls_over_qhop_remote_1_svc(qcsapi_wifi_enable_tdls_over_qhop_rpcdata *, qcsapi_wifi_enable_tdls_over_qhop_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_TDLS_STATUS_REMOTE 4341
extern  enum clnt_stat qcsapi_wifi_get_tdls_status_remote_1(qcsapi_wifi_get_tdls_status_rpcdata *, qcsapi_wifi_get_tdls_status_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_tdls_status_remote_1_svc(qcsapi_wifi_get_tdls_status_rpcdata *, qcsapi_wifi_get_tdls_status_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_SET_TDLS_PARAMS_REMOTE 4351
extern  enum clnt_stat qcsapi_wifi_set_tdls_params_remote_1(qcsapi_wifi_set_tdls_params_rpcdata *, qcsapi_wifi_set_tdls_params_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_set_tdls_params_remote_1_svc(qcsapi_wifi_set_tdls_params_rpcdata *, qcsapi_wifi_set_tdls_params_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_GET_TDLS_PARAMS_REMOTE 4361
extern  enum clnt_stat qcsapi_wifi_get_tdls_params_remote_1(qcsapi_wifi_get_tdls_params_rpcdata *, qcsapi_wifi_get_tdls_params_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_get_tdls_params_remote_1_svc(qcsapi_wifi_get_tdls_params_rpcdata *, qcsapi_wifi_get_tdls_params_rpcdata *, struct svc_req *);
#define QCSAPI_WIFI_TDLS_OPERATE_REMOTE 4371
extern  enum clnt_stat qcsapi_wifi_tdls_operate_remote_1(qcsapi_wifi_tdls_operate_rpcdata *, qcsapi_wifi_tdls_operate_rpcdata *, CLIENT *);
extern  bool_t qcsapi_wifi_tdls_operate_remote_1_svc(qcsapi_wifi_tdls_operate_rpcdata *, qcsapi_wifi_tdls_operate_rpcdata *, struct svc_req *);
extern int qcsapi_prog_1_freeresult (SVCXPRT *, xdrproc_t, caddr_t);

#else /* K&R C */
#define QCSAPI_BOOTCFG_GET_PARAMETER_REMOTE 1
extern  enum clnt_stat qcsapi_bootcfg_get_parameter_remote_1();
extern  bool_t qcsapi_bootcfg_get_parameter_remote_1_svc();
#define QCSAPI_BOOTCFG_UPDATE_PARAMETER_REMOTE 11
extern  enum clnt_stat qcsapi_bootcfg_update_parameter_remote_1();
extern  bool_t qcsapi_bootcfg_update_parameter_remote_1_svc();
#define QCSAPI_BOOTCFG_COMMIT_REMOTE 21
extern  enum clnt_stat qcsapi_bootcfg_commit_remote_1();
extern  bool_t qcsapi_bootcfg_commit_remote_1_svc();
#define QCSAPI_TELNET_ENABLE_REMOTE 31
extern  enum clnt_stat qcsapi_telnet_enable_remote_1();
extern  bool_t qcsapi_telnet_enable_remote_1_svc();
#define QCSAPI_GET_SERVICE_NAME_ENUM_REMOTE 5651
extern  enum clnt_stat qcsapi_get_service_name_enum_remote_1();
extern  bool_t qcsapi_get_service_name_enum_remote_1_svc();
#define QCSAPI_GET_SERVICE_ACTION_ENUM_REMOTE 5661
extern  enum clnt_stat qcsapi_get_service_action_enum_remote_1();
extern  bool_t qcsapi_get_service_action_enum_remote_1_svc();
#define QCSAPI_SERVICE_CONTROL_REMOTE 5671
extern  enum clnt_stat qcsapi_service_control_remote_1();
extern  bool_t qcsapi_service_control_remote_1_svc();
#define QCSAPI_WFA_CERT_MODE_ENABLE_REMOTE 5931
extern  enum clnt_stat qcsapi_wfa_cert_mode_enable_remote_1();
extern  bool_t qcsapi_wfa_cert_mode_enable_remote_1_svc();
#define QCSAPI_WIFI_GET_SCS_CCE_CHANNELS_REMOTE 41
extern  enum clnt_stat qcsapi_wifi_get_scs_cce_channels_remote_1();
extern  bool_t qcsapi_wifi_get_scs_cce_channels_remote_1_svc();
#define QCSAPI_WIFI_SCS_ENABLE_REMOTE 51
extern  enum clnt_stat qcsapi_wifi_scs_enable_remote_1();
extern  bool_t qcsapi_wifi_scs_enable_remote_1_svc();
#define QCSAPI_WIFI_SCS_SWITCH_CHANNEL_REMOTE 61
extern  enum clnt_stat qcsapi_wifi_scs_switch_channel_remote_1();
extern  bool_t qcsapi_wifi_scs_switch_channel_remote_1_svc();
#define QCSAPI_WIFI_SET_SCS_VERBOSE_REMOTE 71
extern  enum clnt_stat qcsapi_wifi_set_scs_verbose_remote_1();
extern  bool_t qcsapi_wifi_set_scs_verbose_remote_1_svc();
#define QCSAPI_WIFI_GET_SCS_STATUS_REMOTE 81
extern  enum clnt_stat qcsapi_wifi_get_scs_status_remote_1();
extern  bool_t qcsapi_wifi_get_scs_status_remote_1_svc();
#define QCSAPI_WIFI_SET_SCS_SMPL_ENABLE_REMOTE 91
extern  enum clnt_stat qcsapi_wifi_set_scs_smpl_enable_remote_1();
extern  bool_t qcsapi_wifi_set_scs_smpl_enable_remote_1_svc();
#define QCSAPI_WIFI_SET_SCS_SMPL_DWELL_TIME_REMOTE 101
extern  enum clnt_stat qcsapi_wifi_set_scs_smpl_dwell_time_remote_1();
extern  bool_t qcsapi_wifi_set_scs_smpl_dwell_time_remote_1_svc();
#define QCSAPI_WIFI_SET_SCS_SAMPLE_INTV_REMOTE 111
extern  enum clnt_stat qcsapi_wifi_set_scs_sample_intv_remote_1();
extern  bool_t qcsapi_wifi_set_scs_sample_intv_remote_1_svc();
#define QCSAPI_WIFI_SET_SCS_INTF_DETECT_INTV_REMOTE 121
extern  enum clnt_stat qcsapi_wifi_set_scs_intf_detect_intv_remote_1();
extern  bool_t qcsapi_wifi_set_scs_intf_detect_intv_remote_1_svc();
#define QCSAPI_WIFI_SET_SCS_THRSHLD_REMOTE 131
extern  enum clnt_stat qcsapi_wifi_set_scs_thrshld_remote_1();
extern  bool_t qcsapi_wifi_set_scs_thrshld_remote_1_svc();
#define QCSAPI_WIFI_SET_SCS_REPORT_ONLY_REMOTE 141
extern  enum clnt_stat qcsapi_wifi_set_scs_report_only_remote_1();
extern  bool_t qcsapi_wifi_set_scs_report_only_remote_1_svc();
#define QCSAPI_WIFI_GET_SCS_STAT_REPORT_REMOTE 151
extern  enum clnt_stat qcsapi_wifi_get_scs_stat_report_remote_1();
extern  bool_t qcsapi_wifi_get_scs_stat_report_remote_1_svc();
#define QCSAPI_WIFI_GET_SCS_SCORE_REPORT_REMOTE 152
extern  enum clnt_stat qcsapi_wifi_get_scs_score_report_remote_1();
extern  bool_t qcsapi_wifi_get_scs_score_report_remote_1_svc();
#define QCSAPI_WIFI_GET_SCS_CURRCHAN_REPORT_REMOTE 161
extern  enum clnt_stat qcsapi_wifi_get_scs_currchan_report_remote_1();
extern  bool_t qcsapi_wifi_get_scs_currchan_report_remote_1_svc();
#define QCSAPI_WIFI_SET_SCS_STATS_REMOTE 171
extern  enum clnt_stat qcsapi_wifi_set_scs_stats_remote_1();
extern  bool_t qcsapi_wifi_set_scs_stats_remote_1_svc();
#define QCSAPI_WIFI_GET_AUTOCHAN_REPORT_REMOTE 181
extern  enum clnt_stat qcsapi_wifi_get_autochan_report_remote_1();
extern  bool_t qcsapi_wifi_get_autochan_report_remote_1_svc();
#define QCSAPI_WIFI_SET_SCS_CCA_INTF_SMTH_FCTR_REMOTE 191
extern  enum clnt_stat qcsapi_wifi_set_scs_cca_intf_smth_fctr_remote_1();
extern  bool_t qcsapi_wifi_set_scs_cca_intf_smth_fctr_remote_1_svc();
#define QCSAPI_WIFI_SET_SCS_CHAN_MTRC_MRGN_REMOTE 201
extern  enum clnt_stat qcsapi_wifi_set_scs_chan_mtrc_mrgn_remote_1();
extern  bool_t qcsapi_wifi_set_scs_chan_mtrc_mrgn_remote_1_svc();
#define QCSAPI_WIFI_GET_SCS_CCA_INTF_REMOTE 211
extern  enum clnt_stat qcsapi_wifi_get_scs_cca_intf_remote_1();
extern  bool_t qcsapi_wifi_get_scs_cca_intf_remote_1_svc();
#define QCSAPI_WIFI_GET_SCS_PARAM_REPORT_REMOTE 221
extern  enum clnt_stat qcsapi_wifi_get_scs_param_report_remote_1();
extern  bool_t qcsapi_wifi_get_scs_param_report_remote_1_svc();
#define QCSAPI_WIFI_GET_SCS_DFS_REENTRY_REQUEST_REMOTE 231
extern  enum clnt_stat qcsapi_wifi_get_scs_dfs_reentry_request_remote_1();
extern  bool_t qcsapi_wifi_get_scs_dfs_reentry_request_remote_1_svc();
#define QCSAPI_WIFI_START_OCAC_REMOTE 241
extern  enum clnt_stat qcsapi_wifi_start_ocac_remote_1();
extern  bool_t qcsapi_wifi_start_ocac_remote_1_svc();
#define QCSAPI_WIFI_STOP_OCAC_REMOTE 251
extern  enum clnt_stat qcsapi_wifi_stop_ocac_remote_1();
extern  bool_t qcsapi_wifi_stop_ocac_remote_1_svc();
#define QCSAPI_WIFI_GET_OCAC_STATUS_REMOTE 261
extern  enum clnt_stat qcsapi_wifi_get_ocac_status_remote_1();
extern  bool_t qcsapi_wifi_get_ocac_status_remote_1_svc();
#define QCSAPI_WIFI_SET_OCAC_DWELL_TIME_REMOTE 271
extern  enum clnt_stat qcsapi_wifi_set_ocac_dwell_time_remote_1();
extern  bool_t qcsapi_wifi_set_ocac_dwell_time_remote_1_svc();
#define QCSAPI_WIFI_SET_OCAC_DURATION_REMOTE 281
extern  enum clnt_stat qcsapi_wifi_set_ocac_duration_remote_1();
extern  bool_t qcsapi_wifi_set_ocac_duration_remote_1_svc();
#define QCSAPI_WIFI_SET_OCAC_CAC_TIME_REMOTE 291
extern  enum clnt_stat qcsapi_wifi_set_ocac_cac_time_remote_1();
extern  bool_t qcsapi_wifi_set_ocac_cac_time_remote_1_svc();
#define QCSAPI_WIFI_SET_OCAC_REPORT_ONLY_REMOTE 301
extern  enum clnt_stat qcsapi_wifi_set_ocac_report_only_remote_1();
extern  bool_t qcsapi_wifi_set_ocac_report_only_remote_1_svc();
#define QCSAPI_WIFI_SET_OCAC_THRSHLD_REMOTE 311
extern  enum clnt_stat qcsapi_wifi_set_ocac_thrshld_remote_1();
extern  bool_t qcsapi_wifi_set_ocac_thrshld_remote_1_svc();
#define QCSAPI_WIFI_START_DFS_S_RADIO_REMOTE 242
extern  enum clnt_stat qcsapi_wifi_start_dfs_s_radio_remote_1();
extern  bool_t qcsapi_wifi_start_dfs_s_radio_remote_1_svc();
#define QCSAPI_WIFI_STOP_DFS_S_RADIO_REMOTE 252
extern  enum clnt_stat qcsapi_wifi_stop_dfs_s_radio_remote_1();
extern  bool_t qcsapi_wifi_stop_dfs_s_radio_remote_1_svc();
#define QCSAPI_WIFI_GET_DFS_S_RADIO_STATUS_REMOTE 262
extern  enum clnt_stat qcsapi_wifi_get_dfs_s_radio_status_remote_1();
extern  bool_t qcsapi_wifi_get_dfs_s_radio_status_remote_1_svc();
#define QCSAPI_WIFI_GET_DFS_S_RADIO_AVAILABILITY_REMOTE 263
extern  enum clnt_stat qcsapi_wifi_get_dfs_s_radio_availability_remote_1();
extern  bool_t qcsapi_wifi_get_dfs_s_radio_availability_remote_1_svc();
#define QCSAPI_WIFI_SET_DFS_S_RADIO_DWELL_TIME_REMOTE 272
extern  enum clnt_stat qcsapi_wifi_set_dfs_s_radio_dwell_time_remote_1();
extern  bool_t qcsapi_wifi_set_dfs_s_radio_dwell_time_remote_1_svc();
#define QCSAPI_WIFI_SET_DFS_S_RADIO_DURATION_REMOTE 282
extern  enum clnt_stat qcsapi_wifi_set_dfs_s_radio_duration_remote_1();
extern  bool_t qcsapi_wifi_set_dfs_s_radio_duration_remote_1_svc();
#define QCSAPI_WIFI_SET_DFS_S_RADIO_WEA_DURATION_REMOTE 283
extern  enum clnt_stat qcsapi_wifi_set_dfs_s_radio_wea_duration_remote_1();
extern  bool_t qcsapi_wifi_set_dfs_s_radio_wea_duration_remote_1_svc();
#define QCSAPI_WIFI_SET_DFS_S_RADIO_CAC_TIME_REMOTE 292
extern  enum clnt_stat qcsapi_wifi_set_dfs_s_radio_cac_time_remote_1();
extern  bool_t qcsapi_wifi_set_dfs_s_radio_cac_time_remote_1_svc();
#define QCSAPI_WIFI_SET_DFS_S_RADIO_WEA_CAC_TIME_REMOTE 293
extern  enum clnt_stat qcsapi_wifi_set_dfs_s_radio_wea_cac_time_remote_1();
extern  bool_t qcsapi_wifi_set_dfs_s_radio_wea_cac_time_remote_1_svc();
#define QCSAPI_WIFI_SET_DFS_S_RADIO_REPORT_ONLY_REMOTE 302
extern  enum clnt_stat qcsapi_wifi_set_dfs_s_radio_report_only_remote_1();
extern  bool_t qcsapi_wifi_set_dfs_s_radio_report_only_remote_1_svc();
#define QCSAPI_WIFI_SET_DFS_S_RADIO_THRSHLD_REMOTE 312
extern  enum clnt_stat qcsapi_wifi_set_dfs_s_radio_thrshld_remote_1();
extern  bool_t qcsapi_wifi_set_dfs_s_radio_thrshld_remote_1_svc();
#define QCSAPI_INIT_REMOTE 321
extern  enum clnt_stat qcsapi_init_remote_1();
extern  bool_t qcsapi_init_remote_1_svc();
#define QCSAPI_CONSOLE_DISCONNECT_REMOTE 331
extern  enum clnt_stat qcsapi_console_disconnect_remote_1();
extern  bool_t qcsapi_console_disconnect_remote_1_svc();
#define QCSAPI_WIFI_STARTPROD_REMOTE 611
extern  enum clnt_stat qcsapi_wifi_startprod_remote_1();
extern  bool_t qcsapi_wifi_startprod_remote_1_svc();
#define QCSAPI_IS_STARTPROD_DONE_REMOTE 621
extern  enum clnt_stat qcsapi_is_startprod_done_remote_1();
extern  bool_t qcsapi_is_startprod_done_remote_1_svc();
#define QCSAPI_SYSTEM_GET_TIME_SINCE_START_REMOTE 341
extern  enum clnt_stat qcsapi_system_get_time_since_start_remote_1();
extern  bool_t qcsapi_system_get_time_since_start_remote_1_svc();
#define QCSAPI_GET_SYSTEM_STATUS_REMOTE 351
extern  enum clnt_stat qcsapi_get_system_status_remote_1();
extern  bool_t qcsapi_get_system_status_remote_1_svc();
#define QCSAPI_GET_RANDOM_SEED_REMOTE 5831
extern  enum clnt_stat qcsapi_get_random_seed_remote_1();
extern  bool_t qcsapi_get_random_seed_remote_1_svc();
#define QCSAPI_SET_RANDOM_SEED_REMOTE 5841
extern  enum clnt_stat qcsapi_set_random_seed_remote_1();
extern  bool_t qcsapi_set_random_seed_remote_1_svc();
#define QCSAPI_GET_CARRIER_ID_REMOTE 4071
extern  enum clnt_stat qcsapi_get_carrier_id_remote_1();
extern  bool_t qcsapi_get_carrier_id_remote_1_svc();
#define QCSAPI_SET_CARRIER_ID_REMOTE 4081
extern  enum clnt_stat qcsapi_set_carrier_id_remote_1();
extern  bool_t qcsapi_set_carrier_id_remote_1_svc();
#define QCSAPI_WIFI_GET_SPINOR_JEDECID_REMOTE 4121
extern  enum clnt_stat qcsapi_wifi_get_spinor_jedecid_remote_1();
extern  bool_t qcsapi_wifi_get_spinor_jedecid_remote_1_svc();
#define QCSAPI_WIFI_GET_BB_PARAM_REMOTE 4281
extern  enum clnt_stat qcsapi_wifi_get_bb_param_remote_1();
extern  bool_t qcsapi_wifi_get_bb_param_remote_1_svc();
#define QCSAPI_WIFI_SET_BB_PARAM_REMOTE 4291
extern  enum clnt_stat qcsapi_wifi_set_bb_param_remote_1();
extern  bool_t qcsapi_wifi_set_bb_param_remote_1_svc();
#define QCSAPI_WIFI_SET_OPTIM_STATS_REMOTE 6001
extern  enum clnt_stat qcsapi_wifi_set_optim_stats_remote_1();
extern  bool_t qcsapi_wifi_set_optim_stats_remote_1_svc();
#define QCSAPI_WIFI_SET_SYS_TIME_REMOTE 6101
extern  enum clnt_stat qcsapi_wifi_set_sys_time_remote_1();
extern  bool_t qcsapi_wifi_set_sys_time_remote_1_svc();
#define QCSAPI_WIFI_GET_SYS_TIME_REMOTE 6111
extern  enum clnt_stat qcsapi_wifi_get_sys_time_remote_1();
extern  bool_t qcsapi_wifi_get_sys_time_remote_1_svc();
#define QCSAPI_SET_SOC_MAC_ADDR_REMOTE 3571
extern  enum clnt_stat qcsapi_set_soc_mac_addr_remote_1();
extern  bool_t qcsapi_set_soc_mac_addr_remote_1_svc();
#define QCSAPI_GET_CUSTOM_VALUE_REMOTE 3581
extern  enum clnt_stat qcsapi_get_custom_value_remote_1();
extern  bool_t qcsapi_get_custom_value_remote_1_svc();
#define QCSAPI_CONFIG_GET_PARAMETER_REMOTE 361
extern  enum clnt_stat qcsapi_config_get_parameter_remote_1();
extern  bool_t qcsapi_config_get_parameter_remote_1_svc();
#define QCSAPI_CONFIG_UPDATE_PARAMETER_REMOTE 371
extern  enum clnt_stat qcsapi_config_update_parameter_remote_1();
extern  bool_t qcsapi_config_update_parameter_remote_1_svc();
#define QCSAPI_CONFIG_GET_SSID_PARAMETER_REMOTE 381
extern  enum clnt_stat qcsapi_config_get_ssid_parameter_remote_1();
extern  bool_t qcsapi_config_get_ssid_parameter_remote_1_svc();
#define QCSAPI_CONFIG_UPDATE_SSID_PARAMETER_REMOTE 391
extern  enum clnt_stat qcsapi_config_update_ssid_parameter_remote_1();
extern  bool_t qcsapi_config_update_ssid_parameter_remote_1_svc();
#define QCSAPI_FILE_PATH_GET_CONFIG_REMOTE 401
extern  enum clnt_stat qcsapi_file_path_get_config_remote_1();
extern  bool_t qcsapi_file_path_get_config_remote_1_svc();
#define QCSAPI_FILE_PATH_SET_CONFIG_REMOTE 411
extern  enum clnt_stat qcsapi_file_path_set_config_remote_1();
extern  bool_t qcsapi_file_path_set_config_remote_1_svc();
#define QCSAPI_RESTORE_DEFAULT_CONFIG_REMOTE 421
extern  enum clnt_stat qcsapi_restore_default_config_remote_1();
extern  bool_t qcsapi_restore_default_config_remote_1_svc();
#define QCSAPI_STORE_IPADDR_REMOTE 431
extern  enum clnt_stat qcsapi_store_ipaddr_remote_1();
extern  bool_t qcsapi_store_ipaddr_remote_1_svc();
#define QCSAPI_INTERFACE_ENABLE_REMOTE 441
extern  enum clnt_stat qcsapi_interface_enable_remote_1();
extern  bool_t qcsapi_interface_enable_remote_1_svc();
#define QCSAPI_INTERFACE_GET_STATUS_REMOTE 451
extern  enum clnt_stat qcsapi_interface_get_status_remote_1();
extern  bool_t qcsapi_interface_get_status_remote_1_svc();
#define QCSAPI_INTERFACE_SET_IP4_REMOTE 5691
extern  enum clnt_stat qcsapi_interface_set_ip4_remote_1();
extern  bool_t qcsapi_interface_set_ip4_remote_1_svc();
#define QCSAPI_INTERFACE_GET_IP4_REMOTE 5701
extern  enum clnt_stat qcsapi_interface_get_ip4_remote_1();
extern  bool_t qcsapi_interface_get_ip4_remote_1_svc();
#define QCSAPI_INTERFACE_GET_COUNTER_REMOTE 461
extern  enum clnt_stat qcsapi_interface_get_counter_remote_1();
extern  bool_t qcsapi_interface_get_counter_remote_1_svc();
#define QCSAPI_INTERFACE_GET_COUNTER64_REMOTE 471
extern  enum clnt_stat qcsapi_interface_get_counter64_remote_1();
extern  bool_t qcsapi_interface_get_counter64_remote_1_svc();
#define QCSAPI_INTERFACE_GET_MAC_ADDR_REMOTE 481
extern  enum clnt_stat qcsapi_interface_get_mac_addr_remote_1();
extern  bool_t qcsapi_interface_get_mac_addr_remote_1_svc();
#define QCSAPI_INTERFACE_SET_MAC_ADDR_REMOTE 491
extern  enum clnt_stat qcsapi_interface_set_mac_addr_remote_1();
extern  bool_t qcsapi_interface_set_mac_addr_remote_1_svc();
#define QCSAPI_PM_GET_COUNTER_REMOTE 501
extern  enum clnt_stat qcsapi_pm_get_counter_remote_1();
extern  bool_t qcsapi_pm_get_counter_remote_1_svc();
#define QCSAPI_SET_ASPM_L1_REMOTE 511
extern  enum clnt_stat qcsapi_set_aspm_l1_remote_1();
extern  bool_t qcsapi_set_aspm_l1_remote_1_svc();
#define QCSAPI_SET_L1_REMOTE 521
extern  enum clnt_stat qcsapi_set_l1_remote_1();
extern  bool_t qcsapi_set_l1_remote_1_svc();
#define QCSAPI_PM_GET_ELAPSED_TIME_REMOTE 531
extern  enum clnt_stat qcsapi_pm_get_elapsed_time_remote_1();
extern  bool_t qcsapi_pm_get_elapsed_time_remote_1_svc();
#define QCSAPI_ETH_PHY_POWER_CONTROL_REMOTE 541
extern  enum clnt_stat qcsapi_eth_phy_power_control_remote_1();
extern  bool_t qcsapi_eth_phy_power_control_remote_1_svc();
#define QCSAPI_GET_EMAC_SWITCH_REMOTE 5971
extern  enum clnt_stat qcsapi_get_emac_switch_remote_1();
extern  bool_t qcsapi_get_emac_switch_remote_1_svc();
#define QCSAPI_SET_EMAC_SWITCH_REMOTE 5981
extern  enum clnt_stat qcsapi_set_emac_switch_remote_1();
extern  bool_t qcsapi_set_emac_switch_remote_1_svc();
#define QCSAPI_ETH_DSCP_MAP_REMOTE 5991
extern  enum clnt_stat qcsapi_eth_dscp_map_remote_1();
extern  bool_t qcsapi_eth_dscp_map_remote_1_svc();
#define QCSAPI_GET_ETH_INFO_REMOTE 6121
extern  enum clnt_stat qcsapi_get_eth_info_remote_1();
extern  bool_t qcsapi_get_eth_info_remote_1_svc();
#define QCSAPI_WIFI_GET_MODE_REMOTE 551
extern  enum clnt_stat qcsapi_wifi_get_mode_remote_1();
extern  bool_t qcsapi_wifi_get_mode_remote_1_svc();
#define QCSAPI_WIFI_SET_MODE_REMOTE 561
extern  enum clnt_stat qcsapi_wifi_set_mode_remote_1();
extern  bool_t qcsapi_wifi_set_mode_remote_1_svc();
#define QCSAPI_WIFI_GET_PHY_MODE_REMOTE 571
extern  enum clnt_stat qcsapi_wifi_get_phy_mode_remote_1();
extern  bool_t qcsapi_wifi_get_phy_mode_remote_1_svc();
#define QCSAPI_WIFI_SET_PHY_MODE_REMOTE 581
extern  enum clnt_stat qcsapi_wifi_set_phy_mode_remote_1();
extern  bool_t qcsapi_wifi_set_phy_mode_remote_1_svc();
#define QCSAPI_WIFI_RELOAD_IN_MODE_REMOTE 591
extern  enum clnt_stat qcsapi_wifi_reload_in_mode_remote_1();
extern  bool_t qcsapi_wifi_reload_in_mode_remote_1_svc();
#define QCSAPI_WIFI_RFENABLE_REMOTE 601
extern  enum clnt_stat qcsapi_wifi_rfenable_remote_1();
extern  bool_t qcsapi_wifi_rfenable_remote_1_svc();
#define QCSAPI_WIFI_RFSTATUS_REMOTE 631
extern  enum clnt_stat qcsapi_wifi_rfstatus_remote_1();
extern  bool_t qcsapi_wifi_rfstatus_remote_1_svc();
#define QCSAPI_WIFI_GET_BW_REMOTE 641
extern  enum clnt_stat qcsapi_wifi_get_bw_remote_1();
extern  bool_t qcsapi_wifi_get_bw_remote_1_svc();
#define QCSAPI_WIFI_SET_BW_REMOTE 651
extern  enum clnt_stat qcsapi_wifi_set_bw_remote_1();
extern  bool_t qcsapi_wifi_set_bw_remote_1_svc();
#define QCSAPI_WIFI_SET_VHT_REMOTE 4091
extern  enum clnt_stat qcsapi_wifi_set_vht_remote_1();
extern  bool_t qcsapi_wifi_set_vht_remote_1_svc();
#define QCSAPI_WIFI_GET_VHT_REMOTE 4101
extern  enum clnt_stat qcsapi_wifi_get_vht_remote_1();
extern  bool_t qcsapi_wifi_get_vht_remote_1_svc();
#define QCSAPI_WIFI_GET_CHANNEL_REMOTE 671
extern  enum clnt_stat qcsapi_wifi_get_channel_remote_1();
extern  bool_t qcsapi_wifi_get_channel_remote_1_svc();
#define QCSAPI_WIFI_SET_CHANNEL_REMOTE 681
extern  enum clnt_stat qcsapi_wifi_set_channel_remote_1();
extern  bool_t qcsapi_wifi_set_channel_remote_1_svc();
#define QCSAPI_WIFI_SET_CHAN_PRI_INACTIVE_REMOTE 691
extern  enum clnt_stat qcsapi_wifi_set_chan_pri_inactive_remote_1();
extern  bool_t qcsapi_wifi_set_chan_pri_inactive_remote_1_svc();
#define QCSAPI_WIFI_CHAN_CONTROL_REMOTE 6211
extern  enum clnt_stat qcsapi_wifi_chan_control_remote_1();
extern  bool_t qcsapi_wifi_chan_control_remote_1_svc();
#define QCSAPI_WIFI_GET_CHAN_DISABLED_REMOTE 6221
extern  enum clnt_stat qcsapi_wifi_get_chan_disabled_remote_1();
extern  bool_t qcsapi_wifi_get_chan_disabled_remote_1_svc();
#define QCSAPI_WIFI_GET_BEACON_INTERVAL_REMOTE 701
extern  enum clnt_stat qcsapi_wifi_get_beacon_interval_remote_1();
extern  bool_t qcsapi_wifi_get_beacon_interval_remote_1_svc();
#define QCSAPI_WIFI_SET_BEACON_INTERVAL_REMOTE 711
extern  enum clnt_stat qcsapi_wifi_set_beacon_interval_remote_1();
extern  bool_t qcsapi_wifi_set_beacon_interval_remote_1_svc();
#define QCSAPI_WIFI_GET_DTIM_REMOTE 721
extern  enum clnt_stat qcsapi_wifi_get_dtim_remote_1();
extern  bool_t qcsapi_wifi_get_dtim_remote_1_svc();
#define QCSAPI_WIFI_SET_DTIM_REMOTE 731
extern  enum clnt_stat qcsapi_wifi_set_dtim_remote_1();
extern  bool_t qcsapi_wifi_set_dtim_remote_1_svc();
#define QCSAPI_WIFI_GET_ASSOC_LIMIT_REMOTE 741
extern  enum clnt_stat qcsapi_wifi_get_assoc_limit_remote_1();
extern  bool_t qcsapi_wifi_get_assoc_limit_remote_1_svc();
#define QCSAPI_WIFI_GET_BSS_ASSOC_LIMIT_REMOTE 5721
extern  enum clnt_stat qcsapi_wifi_get_bss_assoc_limit_remote_1();
extern  bool_t qcsapi_wifi_get_bss_assoc_limit_remote_1_svc();
#define QCSAPI_WIFI_SET_ASSOC_LIMIT_REMOTE 751
extern  enum clnt_stat qcsapi_wifi_set_assoc_limit_remote_1();
extern  bool_t qcsapi_wifi_set_assoc_limit_remote_1_svc();
#define QCSAPI_WIFI_SET_BSS_ASSOC_LIMIT_REMOTE 5711
extern  enum clnt_stat qcsapi_wifi_set_bss_assoc_limit_remote_1();
extern  bool_t qcsapi_wifi_set_bss_assoc_limit_remote_1_svc();
#define QCSAPI_WIFI_GET_BSSID_REMOTE 761
extern  enum clnt_stat qcsapi_wifi_get_bssid_remote_1();
extern  bool_t qcsapi_wifi_get_bssid_remote_1_svc();
#define QCSAPI_WIFI_GET_CONFIG_BSSID_REMOTE 771
extern  enum clnt_stat qcsapi_wifi_get_config_bssid_remote_1();
extern  bool_t qcsapi_wifi_get_config_bssid_remote_1_svc();
#define QCSAPI_WIFI_SSID_GET_BSSID_REMOTE 6131
extern  enum clnt_stat qcsapi_wifi_ssid_get_bssid_remote_1();
extern  bool_t qcsapi_wifi_ssid_get_bssid_remote_1_svc();
#define QCSAPI_WIFI_SSID_SET_BSSID_REMOTE 6141
extern  enum clnt_stat qcsapi_wifi_ssid_set_bssid_remote_1();
extern  bool_t qcsapi_wifi_ssid_set_bssid_remote_1_svc();
#define QCSAPI_WIFI_GET_SSID_REMOTE 781
extern  enum clnt_stat qcsapi_wifi_get_ssid_remote_1();
extern  bool_t qcsapi_wifi_get_ssid_remote_1_svc();
#define QCSAPI_WIFI_SET_SSID_REMOTE 791
extern  enum clnt_stat qcsapi_wifi_set_ssid_remote_1();
extern  bool_t qcsapi_wifi_set_ssid_remote_1_svc();
#define QCSAPI_WIFI_GET_IEEE_802_11_STANDARD_REMOTE 801
extern  enum clnt_stat qcsapi_wifi_get_ieee_802_11_standard_remote_1();
extern  bool_t qcsapi_wifi_get_ieee_802_11_standard_remote_1_svc();
#define QCSAPI_WIFI_GET_LIST_CHANNELS_REMOTE 811
extern  enum clnt_stat qcsapi_wifi_get_list_channels_remote_1();
extern  bool_t qcsapi_wifi_get_list_channels_remote_1_svc();
#define QCSAPI_WIFI_GET_MODE_SWITCH_REMOTE 821
extern  enum clnt_stat qcsapi_wifi_get_mode_switch_remote_1();
extern  bool_t qcsapi_wifi_get_mode_switch_remote_1_svc();
#define QCSAPI_WIFI_DISASSOCIATE_REMOTE 831
extern  enum clnt_stat qcsapi_wifi_disassociate_remote_1();
extern  bool_t qcsapi_wifi_disassociate_remote_1_svc();
#define QCSAPI_WIFI_DISASSOCIATE_STA_REMOTE 841
extern  enum clnt_stat qcsapi_wifi_disassociate_sta_remote_1();
extern  bool_t qcsapi_wifi_disassociate_sta_remote_1_svc();
#define QCSAPI_WIFI_REASSOCIATE_REMOTE 4441
extern  enum clnt_stat qcsapi_wifi_reassociate_remote_1();
extern  bool_t qcsapi_wifi_reassociate_remote_1_svc();
#define QCSAPI_WIFI_GET_DISCONN_INFO_REMOTE 851
extern  enum clnt_stat qcsapi_wifi_get_disconn_info_remote_1();
extern  bool_t qcsapi_wifi_get_disconn_info_remote_1_svc();
#define QCSAPI_WIFI_DISABLE_WPS_REMOTE 861
extern  enum clnt_stat qcsapi_wifi_disable_wps_remote_1();
extern  bool_t qcsapi_wifi_disable_wps_remote_1_svc();
#define QCSAPI_WIFI_ASSOCIATE_REMOTE 871
extern  enum clnt_stat qcsapi_wifi_associate_remote_1();
extern  bool_t qcsapi_wifi_associate_remote_1_svc();
#define QCSAPI_WIFI_START_CCA_REMOTE 881
extern  enum clnt_stat qcsapi_wifi_start_cca_remote_1();
extern  bool_t qcsapi_wifi_start_cca_remote_1_svc();
#define QCSAPI_WIFI_GET_NOISE_REMOTE 891
extern  enum clnt_stat qcsapi_wifi_get_noise_remote_1();
extern  bool_t qcsapi_wifi_get_noise_remote_1_svc();
#define QCSAPI_WIFI_GET_RSSI_BY_CHAIN_REMOTE 901
extern  enum clnt_stat qcsapi_wifi_get_rssi_by_chain_remote_1();
extern  bool_t qcsapi_wifi_get_rssi_by_chain_remote_1_svc();
#define QCSAPI_WIFI_GET_AVG_SNR_REMOTE 911
extern  enum clnt_stat qcsapi_wifi_get_avg_snr_remote_1();
extern  bool_t qcsapi_wifi_get_avg_snr_remote_1_svc();
#define QCSAPI_GET_PRIMARY_INTERFACE_REMOTE 921
extern  enum clnt_stat qcsapi_get_primary_interface_remote_1();
extern  bool_t qcsapi_get_primary_interface_remote_1_svc();
#define QCSAPI_GET_INTERFACE_BY_INDEX_REMOTE 931
extern  enum clnt_stat qcsapi_get_interface_by_index_remote_1();
extern  bool_t qcsapi_get_interface_by_index_remote_1_svc();
#define QCSAPI_WIFI_SET_WIFI_MACADDR_REMOTE 941
extern  enum clnt_stat qcsapi_wifi_set_wifi_macaddr_remote_1();
extern  bool_t qcsapi_wifi_set_wifi_macaddr_remote_1_svc();
#define QCSAPI_INTERFACE_GET_BSSID_REMOTE 951
extern  enum clnt_stat qcsapi_interface_get_bssid_remote_1();
extern  bool_t qcsapi_interface_get_bssid_remote_1_svc();
#define QCSAPI_WIFI_GET_RATES_REMOTE 961
extern  enum clnt_stat qcsapi_wifi_get_rates_remote_1();
extern  bool_t qcsapi_wifi_get_rates_remote_1_svc();
#define QCSAPI_WIFI_SET_RATES_REMOTE 971
extern  enum clnt_stat qcsapi_wifi_set_rates_remote_1();
extern  bool_t qcsapi_wifi_set_rates_remote_1_svc();
#define QCSAPI_GET_MAX_BITRATE_REMOTE 981
extern  enum clnt_stat qcsapi_get_max_bitrate_remote_1();
extern  bool_t qcsapi_get_max_bitrate_remote_1_svc();
#define QCSAPI_SET_MAX_BITRATE_REMOTE 991
extern  enum clnt_stat qcsapi_set_max_bitrate_remote_1();
extern  bool_t qcsapi_set_max_bitrate_remote_1_svc();
#define QCSAPI_WIFI_QOS_GET_PARAM_REMOTE 1001
extern  enum clnt_stat qcsapi_wifi_qos_get_param_remote_1();
extern  bool_t qcsapi_wifi_qos_get_param_remote_1_svc();
#define QCSAPI_WIFI_QOS_SET_PARAM_REMOTE 1011
extern  enum clnt_stat qcsapi_wifi_qos_set_param_remote_1();
extern  bool_t qcsapi_wifi_qos_set_param_remote_1_svc();
#define QCSAPI_WIFI_GET_WMM_AC_MAP_REMOTE 1021
extern  enum clnt_stat qcsapi_wifi_get_wmm_ac_map_remote_1();
extern  bool_t qcsapi_wifi_get_wmm_ac_map_remote_1_svc();
#define QCSAPI_WIFI_SET_WMM_AC_MAP_REMOTE 1031
extern  enum clnt_stat qcsapi_wifi_set_wmm_ac_map_remote_1();
extern  bool_t qcsapi_wifi_set_wmm_ac_map_remote_1_svc();
#define QCSAPI_WIFI_GET_DSCP_8021P_MAP_REMOTE 1041
extern  enum clnt_stat qcsapi_wifi_get_dscp_8021p_map_remote_1();
extern  bool_t qcsapi_wifi_get_dscp_8021p_map_remote_1_svc();
#define QCSAPI_WIFI_GET_DSCP_AC_MAP_REMOTE 1051
extern  enum clnt_stat qcsapi_wifi_get_dscp_ac_map_remote_1();
extern  bool_t qcsapi_wifi_get_dscp_ac_map_remote_1_svc();
#define QCSAPI_WIFI_SET_DSCP_8021P_MAP_REMOTE 1061
extern  enum clnt_stat qcsapi_wifi_set_dscp_8021p_map_remote_1();
extern  bool_t qcsapi_wifi_set_dscp_8021p_map_remote_1_svc();
#define QCSAPI_WIFI_SET_DSCP_AC_MAP_REMOTE 1071
extern  enum clnt_stat qcsapi_wifi_set_dscp_ac_map_remote_1();
extern  bool_t qcsapi_wifi_set_dscp_ac_map_remote_1_svc();
#define QCSAPI_WIFI_GET_PRIORITY_REMOTE 1081
extern  enum clnt_stat qcsapi_wifi_get_priority_remote_1();
extern  bool_t qcsapi_wifi_get_priority_remote_1_svc();
#define QCSAPI_WIFI_SET_PRIORITY_REMOTE 1091
extern  enum clnt_stat qcsapi_wifi_set_priority_remote_1();
extern  bool_t qcsapi_wifi_set_priority_remote_1_svc();
#define QCSAPI_WIFI_GET_AIRFAIR_REMOTE 1101
extern  enum clnt_stat qcsapi_wifi_get_airfair_remote_1();
extern  bool_t qcsapi_wifi_get_airfair_remote_1_svc();
#define QCSAPI_WIFI_SET_AIRFAIR_REMOTE 1111
extern  enum clnt_stat qcsapi_wifi_set_airfair_remote_1();
extern  bool_t qcsapi_wifi_set_airfair_remote_1_svc();
#define QCSAPI_WIFI_GET_TX_POWER_REMOTE 1211
extern  enum clnt_stat qcsapi_wifi_get_tx_power_remote_1();
extern  bool_t qcsapi_wifi_get_tx_power_remote_1_svc();
#define QCSAPI_WIFI_SET_TX_POWER_REMOTE 1221
extern  enum clnt_stat qcsapi_wifi_set_tx_power_remote_1();
extern  bool_t qcsapi_wifi_set_tx_power_remote_1_svc();
#define QCSAPI_WIFI_GET_BW_POWER_REMOTE 1231
extern  enum clnt_stat qcsapi_wifi_get_bw_power_remote_1();
extern  bool_t qcsapi_wifi_get_bw_power_remote_1_svc();
#define QCSAPI_WIFI_SET_BW_POWER_REMOTE 1241
extern  enum clnt_stat qcsapi_wifi_set_bw_power_remote_1();
extern  bool_t qcsapi_wifi_set_bw_power_remote_1_svc();
#define QCSAPI_WIFI_GET_BF_POWER_REMOTE 1261
extern  enum clnt_stat qcsapi_wifi_get_bf_power_remote_1();
extern  bool_t qcsapi_wifi_get_bf_power_remote_1_svc();
#define QCSAPI_WIFI_SET_BF_POWER_REMOTE 1271
extern  enum clnt_stat qcsapi_wifi_set_bf_power_remote_1();
extern  bool_t qcsapi_wifi_set_bf_power_remote_1_svc();
#define QCSAPI_WIFI_GET_TX_POWER_EXT_REMOTE 4541
extern  enum clnt_stat qcsapi_wifi_get_tx_power_ext_remote_1();
extern  bool_t qcsapi_wifi_get_tx_power_ext_remote_1_svc();
#define QCSAPI_WIFI_SET_TX_POWER_EXT_REMOTE 4551
extern  enum clnt_stat qcsapi_wifi_set_tx_power_ext_remote_1();
extern  bool_t qcsapi_wifi_set_tx_power_ext_remote_1_svc();
#define QCSAPI_WIFI_GET_CHAN_POWER_TABLE_REMOTE 6151
extern  enum clnt_stat qcsapi_wifi_get_chan_power_table_remote_1();
extern  bool_t qcsapi_wifi_get_chan_power_table_remote_1_svc();
#define QCSAPI_WIFI_SET_CHAN_POWER_TABLE_REMOTE 6161
extern  enum clnt_stat qcsapi_wifi_set_chan_power_table_remote_1();
extern  bool_t qcsapi_wifi_set_chan_power_table_remote_1_svc();
#define QCSAPI_WIFI_GET_POWER_SELECTION_REMOTE 4471
extern  enum clnt_stat qcsapi_wifi_get_power_selection_remote_1();
extern  bool_t qcsapi_wifi_get_power_selection_remote_1_svc();
#define QCSAPI_WIFI_SET_POWER_SELECTION_REMOTE 4481
extern  enum clnt_stat qcsapi_wifi_set_power_selection_remote_1();
extern  bool_t qcsapi_wifi_set_power_selection_remote_1_svc();
#define QCSAPI_WIFI_GET_CARRIER_INTERFERENCE_REMOTE 1291
extern  enum clnt_stat qcsapi_wifi_get_carrier_interference_remote_1();
extern  bool_t qcsapi_wifi_get_carrier_interference_remote_1_svc();
#define QCSAPI_WIFI_GET_CONGESTION_INDEX_REMOTE 1301
extern  enum clnt_stat qcsapi_wifi_get_congestion_index_remote_1();
extern  bool_t qcsapi_wifi_get_congestion_index_remote_1_svc();
#define QCSAPI_WIFI_GET_SUPPORTED_TX_POWER_LEVELS_REMOTE 1311
extern  enum clnt_stat qcsapi_wifi_get_supported_tx_power_levels_remote_1();
extern  bool_t qcsapi_wifi_get_supported_tx_power_levels_remote_1_svc();
#define QCSAPI_WIFI_GET_CURRENT_TX_POWER_LEVEL_REMOTE 1321
extern  enum clnt_stat qcsapi_wifi_get_current_tx_power_level_remote_1();
extern  bool_t qcsapi_wifi_get_current_tx_power_level_remote_1_svc();
#define QCSAPI_WIFI_SET_POWER_CONSTRAINT_REMOTE 1331
extern  enum clnt_stat qcsapi_wifi_set_power_constraint_remote_1();
extern  bool_t qcsapi_wifi_set_power_constraint_remote_1_svc();
#define QCSAPI_WIFI_GET_POWER_CONSTRAINT_REMOTE 1341
extern  enum clnt_stat qcsapi_wifi_get_power_constraint_remote_1();
extern  bool_t qcsapi_wifi_get_power_constraint_remote_1_svc();
#define QCSAPI_WIFI_SET_TPC_INTERVAL_REMOTE 1351
extern  enum clnt_stat qcsapi_wifi_set_tpc_interval_remote_1();
extern  bool_t qcsapi_wifi_set_tpc_interval_remote_1_svc();
#define QCSAPI_WIFI_GET_TPC_INTERVAL_REMOTE 1361
extern  enum clnt_stat qcsapi_wifi_get_tpc_interval_remote_1();
extern  bool_t qcsapi_wifi_get_tpc_interval_remote_1_svc();
#define QCSAPI_WIFI_GET_ASSOC_RECORDS_REMOTE 1371
extern  enum clnt_stat qcsapi_wifi_get_assoc_records_remote_1();
extern  bool_t qcsapi_wifi_get_assoc_records_remote_1_svc();
#define QCSAPI_WIFI_GET_AP_ISOLATE_REMOTE 1381
extern  enum clnt_stat qcsapi_wifi_get_ap_isolate_remote_1();
extern  bool_t qcsapi_wifi_get_ap_isolate_remote_1_svc();
#define QCSAPI_WIFI_SET_AP_ISOLATE_REMOTE 1391
extern  enum clnt_stat qcsapi_wifi_set_ap_isolate_remote_1();
extern  bool_t qcsapi_wifi_set_ap_isolate_remote_1_svc();
#define QCSAPI_WIFI_GET_INTRA_BSS_ISOLATE_REMOTE 1401
extern  enum clnt_stat qcsapi_wifi_get_intra_bss_isolate_remote_1();
extern  bool_t qcsapi_wifi_get_intra_bss_isolate_remote_1_svc();
#define QCSAPI_WIFI_SET_INTRA_BSS_ISOLATE_REMOTE 1411
extern  enum clnt_stat qcsapi_wifi_set_intra_bss_isolate_remote_1();
extern  bool_t qcsapi_wifi_set_intra_bss_isolate_remote_1_svc();
#define QCSAPI_WIFI_GET_BSS_ISOLATE_REMOTE 1421
extern  enum clnt_stat qcsapi_wifi_get_bss_isolate_remote_1();
extern  bool_t qcsapi_wifi_get_bss_isolate_remote_1_svc();
#define QCSAPI_WIFI_SET_BSS_ISOLATE_REMOTE 1431
extern  enum clnt_stat qcsapi_wifi_set_bss_isolate_remote_1();
extern  bool_t qcsapi_wifi_set_bss_isolate_remote_1_svc();
#define QCSAPI_WIFI_DISABLE_DFS_CHANNELS_REMOTE 4061
extern  enum clnt_stat qcsapi_wifi_disable_dfs_channels_remote_1();
extern  bool_t qcsapi_wifi_disable_dfs_channels_remote_1_svc();
#define QCSAPI_WIFI_CREATE_RESTRICTED_BSS_REMOTE 1441
extern  enum clnt_stat qcsapi_wifi_create_restricted_bss_remote_1();
extern  bool_t qcsapi_wifi_create_restricted_bss_remote_1_svc();
#define QCSAPI_WIFI_CREATE_BSS_REMOTE 1451
extern  enum clnt_stat qcsapi_wifi_create_bss_remote_1();
extern  bool_t qcsapi_wifi_create_bss_remote_1_svc();
#define QCSAPI_WIFI_REMOVE_BSS_REMOTE 1461
extern  enum clnt_stat qcsapi_wifi_remove_bss_remote_1();
extern  bool_t qcsapi_wifi_remove_bss_remote_1_svc();
#define QCSAPI_WDS_ADD_PEER_REMOTE 1471
extern  enum clnt_stat qcsapi_wds_add_peer_remote_1();
extern  bool_t qcsapi_wds_add_peer_remote_1_svc();
#define QCSAPI_WDS_ADD_PEER_ENCRYPT_REMOTE 1481
extern  enum clnt_stat qcsapi_wds_add_peer_encrypt_remote_1();
extern  bool_t qcsapi_wds_add_peer_encrypt_remote_1_svc();
#define QCSAPI_WDS_REMOVE_PEER_REMOTE 1491
extern  enum clnt_stat qcsapi_wds_remove_peer_remote_1();
extern  bool_t qcsapi_wds_remove_peer_remote_1_svc();
#define QCSAPI_WDS_GET_PEER_ADDRESS_REMOTE 1501
extern  enum clnt_stat qcsapi_wds_get_peer_address_remote_1();
extern  bool_t qcsapi_wds_get_peer_address_remote_1_svc();
#define QCSAPI_WDS_SET_PSK_REMOTE 1511
extern  enum clnt_stat qcsapi_wds_set_psk_remote_1();
extern  bool_t qcsapi_wds_set_psk_remote_1_svc();
#define QCSAPI_WDS_SET_MODE_REMOTE 1521
extern  enum clnt_stat qcsapi_wds_set_mode_remote_1();
extern  bool_t qcsapi_wds_set_mode_remote_1_svc();
#define QCSAPI_WDS_GET_MODE_REMOTE 1531
extern  enum clnt_stat qcsapi_wds_get_mode_remote_1();
extern  bool_t qcsapi_wds_get_mode_remote_1_svc();
#define QCSAPI_WIFI_SET_EXTENDER_PARAMS_REMOTE 1541
extern  enum clnt_stat qcsapi_wifi_set_extender_params_remote_1();
extern  bool_t qcsapi_wifi_set_extender_params_remote_1_svc();
#define QCSAPI_WIFI_GET_EXTENDER_PARAMS_REMOTE 1551
extern  enum clnt_stat qcsapi_wifi_get_extender_params_remote_1();
extern  bool_t qcsapi_wifi_get_extender_params_remote_1_svc();
#define QCSAPI_WIFI_GET_BEACON_TYPE_REMOTE 1581
extern  enum clnt_stat qcsapi_wifi_get_beacon_type_remote_1();
extern  bool_t qcsapi_wifi_get_beacon_type_remote_1_svc();
#define QCSAPI_WIFI_SET_BEACON_TYPE_REMOTE 1591
extern  enum clnt_stat qcsapi_wifi_set_beacon_type_remote_1();
extern  bool_t qcsapi_wifi_set_beacon_type_remote_1_svc();
#define QCSAPI_WIFI_GET_WEP_KEY_INDEX_REMOTE 1601
extern  enum clnt_stat qcsapi_wifi_get_wep_key_index_remote_1();
extern  bool_t qcsapi_wifi_get_wep_key_index_remote_1_svc();
#define QCSAPI_WIFI_SET_WEP_KEY_INDEX_REMOTE 1611
extern  enum clnt_stat qcsapi_wifi_set_wep_key_index_remote_1();
extern  bool_t qcsapi_wifi_set_wep_key_index_remote_1_svc();
#define QCSAPI_WIFI_GET_WEP_KEY_PASSPHRASE_REMOTE 1621
extern  enum clnt_stat qcsapi_wifi_get_wep_key_passphrase_remote_1();
extern  bool_t qcsapi_wifi_get_wep_key_passphrase_remote_1_svc();
#define QCSAPI_WIFI_SET_WEP_KEY_PASSPHRASE_REMOTE 1631
extern  enum clnt_stat qcsapi_wifi_set_wep_key_passphrase_remote_1();
extern  bool_t qcsapi_wifi_set_wep_key_passphrase_remote_1_svc();
#define QCSAPI_WIFI_GET_WEP_ENCRYPTION_LEVEL_REMOTE 1641
extern  enum clnt_stat qcsapi_wifi_get_wep_encryption_level_remote_1();
extern  bool_t qcsapi_wifi_get_wep_encryption_level_remote_1_svc();
#define QCSAPI_WIFI_GET_BASIC_ENCRYPTION_MODES_REMOTE 1651
extern  enum clnt_stat qcsapi_wifi_get_basic_encryption_modes_remote_1();
extern  bool_t qcsapi_wifi_get_basic_encryption_modes_remote_1_svc();
#define QCSAPI_WIFI_SET_BASIC_ENCRYPTION_MODES_REMOTE 1661
extern  enum clnt_stat qcsapi_wifi_set_basic_encryption_modes_remote_1();
extern  bool_t qcsapi_wifi_set_basic_encryption_modes_remote_1_svc();
#define QCSAPI_WIFI_GET_BASIC_AUTHENTICATION_MODE_REMOTE 1671
extern  enum clnt_stat qcsapi_wifi_get_basic_authentication_mode_remote_1();
extern  bool_t qcsapi_wifi_get_basic_authentication_mode_remote_1_svc();
#define QCSAPI_WIFI_SET_BASIC_AUTHENTICATION_MODE_REMOTE 1681
extern  enum clnt_stat qcsapi_wifi_set_basic_authentication_mode_remote_1();
extern  bool_t qcsapi_wifi_set_basic_authentication_mode_remote_1_svc();
#define QCSAPI_WIFI_GET_WEP_KEY_REMOTE 1691
extern  enum clnt_stat qcsapi_wifi_get_wep_key_remote_1();
extern  bool_t qcsapi_wifi_get_wep_key_remote_1_svc();
#define QCSAPI_WIFI_SET_WEP_KEY_REMOTE 1701
extern  enum clnt_stat qcsapi_wifi_set_wep_key_remote_1();
extern  bool_t qcsapi_wifi_set_wep_key_remote_1_svc();
#define QCSAPI_WIFI_GET_WPA_ENCRYPTION_MODES_REMOTE 1711
extern  enum clnt_stat qcsapi_wifi_get_wpa_encryption_modes_remote_1();
extern  bool_t qcsapi_wifi_get_wpa_encryption_modes_remote_1_svc();
#define QCSAPI_WIFI_SET_WPA_ENCRYPTION_MODES_REMOTE 1721
extern  enum clnt_stat qcsapi_wifi_set_wpa_encryption_modes_remote_1();
extern  bool_t qcsapi_wifi_set_wpa_encryption_modes_remote_1_svc();
#define QCSAPI_WIFI_GET_WPA_AUTHENTICATION_MODE_REMOTE 1731
extern  enum clnt_stat qcsapi_wifi_get_wpa_authentication_mode_remote_1();
extern  bool_t qcsapi_wifi_get_wpa_authentication_mode_remote_1_svc();
#define QCSAPI_WIFI_SET_WPA_AUTHENTICATION_MODE_REMOTE 1741
extern  enum clnt_stat qcsapi_wifi_set_wpa_authentication_mode_remote_1();
extern  bool_t qcsapi_wifi_set_wpa_authentication_mode_remote_1_svc();
#define QCSAPI_WIFI_GET_INTERWORKING_REMOTE 5451
extern  enum clnt_stat qcsapi_wifi_get_interworking_remote_1();
extern  bool_t qcsapi_wifi_get_interworking_remote_1_svc();
#define QCSAPI_WIFI_SET_INTERWORKING_REMOTE 5461
extern  enum clnt_stat qcsapi_wifi_set_interworking_remote_1();
extern  bool_t qcsapi_wifi_set_interworking_remote_1_svc();
#define QCSAPI_WIFI_GET_80211U_PARAMS_REMOTE 5471
extern  enum clnt_stat qcsapi_wifi_get_80211u_params_remote_1();
extern  bool_t qcsapi_wifi_get_80211u_params_remote_1_svc();
#define QCSAPI_WIFI_SET_80211U_PARAMS_REMOTE 5481
extern  enum clnt_stat qcsapi_wifi_set_80211u_params_remote_1();
extern  bool_t qcsapi_wifi_set_80211u_params_remote_1_svc();
#define QCSAPI_SECURITY_GET_NAI_REALMS_REMOTE 5491
extern  enum clnt_stat qcsapi_security_get_nai_realms_remote_1();
extern  bool_t qcsapi_security_get_nai_realms_remote_1_svc();
#define QCSAPI_SECURITY_ADD_NAI_REALM_REMOTE 5501
extern  enum clnt_stat qcsapi_security_add_nai_realm_remote_1();
extern  bool_t qcsapi_security_add_nai_realm_remote_1_svc();
#define QCSAPI_SECURITY_DEL_NAI_REALM_REMOTE 5511
extern  enum clnt_stat qcsapi_security_del_nai_realm_remote_1();
extern  bool_t qcsapi_security_del_nai_realm_remote_1_svc();
#define QCSAPI_SECURITY_GET_ROAMING_CONSORTIUM_REMOTE 5521
extern  enum clnt_stat qcsapi_security_get_roaming_consortium_remote_1();
extern  bool_t qcsapi_security_get_roaming_consortium_remote_1_svc();
#define QCSAPI_SECURITY_ADD_ROAMING_CONSORTIUM_REMOTE 5531
extern  enum clnt_stat qcsapi_security_add_roaming_consortium_remote_1();
extern  bool_t qcsapi_security_add_roaming_consortium_remote_1_svc();
#define QCSAPI_SECURITY_DEL_ROAMING_CONSORTIUM_REMOTE 5541
extern  enum clnt_stat qcsapi_security_del_roaming_consortium_remote_1();
extern  bool_t qcsapi_security_del_roaming_consortium_remote_1_svc();
#define QCSAPI_SECURITY_GET_VENUE_NAME_REMOTE 5551
extern  enum clnt_stat qcsapi_security_get_venue_name_remote_1();
extern  bool_t qcsapi_security_get_venue_name_remote_1_svc();
#define QCSAPI_SECURITY_ADD_VENUE_NAME_REMOTE 5561
extern  enum clnt_stat qcsapi_security_add_venue_name_remote_1();
extern  bool_t qcsapi_security_add_venue_name_remote_1_svc();
#define QCSAPI_SECURITY_DEL_VENUE_NAME_REMOTE 5731
extern  enum clnt_stat qcsapi_security_del_venue_name_remote_1();
extern  bool_t qcsapi_security_del_venue_name_remote_1_svc();
#define QCSAPI_SECURITY_GET_OPER_FRIENDLY_NAME_REMOTE 5741
extern  enum clnt_stat qcsapi_security_get_oper_friendly_name_remote_1();
extern  bool_t qcsapi_security_get_oper_friendly_name_remote_1_svc();
#define QCSAPI_SECURITY_ADD_OPER_FRIENDLY_NAME_REMOTE 5751
extern  enum clnt_stat qcsapi_security_add_oper_friendly_name_remote_1();
extern  bool_t qcsapi_security_add_oper_friendly_name_remote_1_svc();
#define QCSAPI_SECURITY_DEL_OPER_FRIENDLY_NAME_REMOTE 5761
extern  enum clnt_stat qcsapi_security_del_oper_friendly_name_remote_1();
extern  bool_t qcsapi_security_del_oper_friendly_name_remote_1_svc();
#define QCSAPI_SECURITY_GET_HS20_CONN_CAPAB_REMOTE 5771
extern  enum clnt_stat qcsapi_security_get_hs20_conn_capab_remote_1();
extern  bool_t qcsapi_security_get_hs20_conn_capab_remote_1_svc();
#define QCSAPI_SECURITY_ADD_HS20_CONN_CAPAB_REMOTE 5781
extern  enum clnt_stat qcsapi_security_add_hs20_conn_capab_remote_1();
extern  bool_t qcsapi_security_add_hs20_conn_capab_remote_1_svc();
#define QCSAPI_SECURITY_DEL_HS20_CONN_CAPAB_REMOTE 5791
extern  enum clnt_stat qcsapi_security_del_hs20_conn_capab_remote_1();
extern  bool_t qcsapi_security_del_hs20_conn_capab_remote_1_svc();
#define QCSAPI_WIFI_GET_HS20_STATUS_REMOTE 5571
extern  enum clnt_stat qcsapi_wifi_get_hs20_status_remote_1();
extern  bool_t qcsapi_wifi_get_hs20_status_remote_1_svc();
#define QCSAPI_WIFI_SET_HS20_STATUS_REMOTE 5581
extern  enum clnt_stat qcsapi_wifi_set_hs20_status_remote_1();
extern  bool_t qcsapi_wifi_set_hs20_status_remote_1_svc();
#define QCSAPI_WIFI_GET_PROXY_ARP_REMOTE 5641
extern  enum clnt_stat qcsapi_wifi_get_proxy_arp_remote_1();
extern  bool_t qcsapi_wifi_get_proxy_arp_remote_1_svc();
#define QCSAPI_WIFI_SET_PROXY_ARP_REMOTE 5631
extern  enum clnt_stat qcsapi_wifi_set_proxy_arp_remote_1();
extern  bool_t qcsapi_wifi_set_proxy_arp_remote_1_svc();
#define QCSAPI_WIFI_GET_L2_EXT_FILTER_REMOTE 5941
extern  enum clnt_stat qcsapi_wifi_get_l2_ext_filter_remote_1();
extern  bool_t qcsapi_wifi_get_l2_ext_filter_remote_1_svc();
#define QCSAPI_WIFI_SET_L2_EXT_FILTER_REMOTE 5951
extern  enum clnt_stat qcsapi_wifi_set_l2_ext_filter_remote_1();
extern  bool_t qcsapi_wifi_set_l2_ext_filter_remote_1_svc();
#define QCSAPI_WIFI_GET_HS20_PARAMS_REMOTE 5591
extern  enum clnt_stat qcsapi_wifi_get_hs20_params_remote_1();
extern  bool_t qcsapi_wifi_get_hs20_params_remote_1_svc();
#define QCSAPI_WIFI_SET_HS20_PARAMS_REMOTE 5601
extern  enum clnt_stat qcsapi_wifi_set_hs20_params_remote_1();
extern  bool_t qcsapi_wifi_set_hs20_params_remote_1_svc();
#define QCSAPI_REMOVE_11U_PARAM_REMOTE 5611
extern  enum clnt_stat qcsapi_remove_11u_param_remote_1();
extern  bool_t qcsapi_remove_11u_param_remote_1_svc();
#define QCSAPI_REMOVE_HS20_PARAM_REMOTE 5621
extern  enum clnt_stat qcsapi_remove_hs20_param_remote_1();
extern  bool_t qcsapi_remove_hs20_param_remote_1_svc();
#define QCSAPI_WIFI_GET_IEEE11I_ENCRYPTION_MODES_REMOTE 1751
extern  enum clnt_stat qcsapi_wifi_get_ieee11i_encryption_modes_remote_1();
extern  bool_t qcsapi_wifi_get_ieee11i_encryption_modes_remote_1_svc();
#define QCSAPI_WIFI_SET_IEEE11I_ENCRYPTION_MODES_REMOTE 1761
extern  enum clnt_stat qcsapi_wifi_set_ieee11i_encryption_modes_remote_1();
extern  bool_t qcsapi_wifi_set_ieee11i_encryption_modes_remote_1_svc();
#define QCSAPI_WIFI_GET_IEEE11I_AUTHENTICATION_MODE_REMOTE 1771
extern  enum clnt_stat qcsapi_wifi_get_ieee11i_authentication_mode_remote_1();
extern  bool_t qcsapi_wifi_get_ieee11i_authentication_mode_remote_1_svc();
#define QCSAPI_WIFI_SET_IEEE11I_AUTHENTICATION_MODE_REMOTE 1781
extern  enum clnt_stat qcsapi_wifi_set_ieee11i_authentication_mode_remote_1();
extern  bool_t qcsapi_wifi_set_ieee11i_authentication_mode_remote_1_svc();
#define QCSAPI_WIFI_GET_MICHAEL_ERRCNT_REMOTE 1791
extern  enum clnt_stat qcsapi_wifi_get_michael_errcnt_remote_1();
extern  bool_t qcsapi_wifi_get_michael_errcnt_remote_1_svc();
#define QCSAPI_WIFI_GET_PRE_SHARED_KEY_REMOTE 1801
extern  enum clnt_stat qcsapi_wifi_get_pre_shared_key_remote_1();
extern  bool_t qcsapi_wifi_get_pre_shared_key_remote_1_svc();
#define QCSAPI_WIFI_SET_PRE_SHARED_KEY_REMOTE 1811
extern  enum clnt_stat qcsapi_wifi_set_pre_shared_key_remote_1();
extern  bool_t qcsapi_wifi_set_pre_shared_key_remote_1_svc();
#define QCSAPI_WIFI_ADD_RADIUS_AUTH_SERVER_CFG_REMOTE 5801
extern  enum clnt_stat qcsapi_wifi_add_radius_auth_server_cfg_remote_1();
extern  bool_t qcsapi_wifi_add_radius_auth_server_cfg_remote_1_svc();
#define QCSAPI_WIFI_DEL_RADIUS_AUTH_SERVER_CFG_REMOTE 5811
extern  enum clnt_stat qcsapi_wifi_del_radius_auth_server_cfg_remote_1();
extern  bool_t qcsapi_wifi_del_radius_auth_server_cfg_remote_1_svc();
#define QCSAPI_WIFI_GET_RADIUS_AUTH_SERVER_CFG_REMOTE 5821
extern  enum clnt_stat qcsapi_wifi_get_radius_auth_server_cfg_remote_1();
extern  bool_t qcsapi_wifi_get_radius_auth_server_cfg_remote_1_svc();
#define QCSAPI_WIFI_SET_OWN_IP_ADDR_REMOTE 1881
extern  enum clnt_stat qcsapi_wifi_set_own_ip_addr_remote_1();
extern  bool_t qcsapi_wifi_set_own_ip_addr_remote_1_svc();
#define QCSAPI_WIFI_GET_KEY_PASSPHRASE_REMOTE 1891
extern  enum clnt_stat qcsapi_wifi_get_key_passphrase_remote_1();
extern  bool_t qcsapi_wifi_get_key_passphrase_remote_1_svc();
#define QCSAPI_WIFI_SET_KEY_PASSPHRASE_REMOTE 1901
extern  enum clnt_stat qcsapi_wifi_set_key_passphrase_remote_1();
extern  bool_t qcsapi_wifi_set_key_passphrase_remote_1_svc();
#define QCSAPI_WIFI_GET_GROUP_KEY_INTERVAL_REMOTE 1911
extern  enum clnt_stat qcsapi_wifi_get_group_key_interval_remote_1();
extern  bool_t qcsapi_wifi_get_group_key_interval_remote_1_svc();
#define QCSAPI_WIFI_SET_GROUP_KEY_INTERVAL_REMOTE 1921
extern  enum clnt_stat qcsapi_wifi_set_group_key_interval_remote_1();
extern  bool_t qcsapi_wifi_set_group_key_interval_remote_1_svc();
#define QCSAPI_WIFI_GET_PMF_REMOTE 1931
extern  enum clnt_stat qcsapi_wifi_get_pmf_remote_1();
extern  bool_t qcsapi_wifi_get_pmf_remote_1_svc();
#define QCSAPI_WIFI_SET_PMF_REMOTE 1941
extern  enum clnt_stat qcsapi_wifi_set_pmf_remote_1();
extern  bool_t qcsapi_wifi_set_pmf_remote_1_svc();
#define QCSAPI_WIFI_GET_WPA_STATUS_REMOTE 1951
extern  enum clnt_stat qcsapi_wifi_get_wpa_status_remote_1();
extern  bool_t qcsapi_wifi_get_wpa_status_remote_1_svc();
#define QCSAPI_WIFI_GET_PSK_AUTH_FAILURES_REMOTE 1961
extern  enum clnt_stat qcsapi_wifi_get_psk_auth_failures_remote_1();
extern  bool_t qcsapi_wifi_get_psk_auth_failures_remote_1_svc();
#define QCSAPI_WIFI_GET_AUTH_STATE_REMOTE 1971
extern  enum clnt_stat qcsapi_wifi_get_auth_state_remote_1();
extern  bool_t qcsapi_wifi_get_auth_state_remote_1_svc();
#define QCSAPI_WIFI_SET_SECURITY_DEFER_MODE_REMOTE 1981
extern  enum clnt_stat qcsapi_wifi_set_security_defer_mode_remote_1();
extern  bool_t qcsapi_wifi_set_security_defer_mode_remote_1_svc();
#define QCSAPI_WIFI_GET_SECURITY_DEFER_MODE_REMOTE 1991
extern  enum clnt_stat qcsapi_wifi_get_security_defer_mode_remote_1();
extern  bool_t qcsapi_wifi_get_security_defer_mode_remote_1_svc();
#define QCSAPI_WIFI_APPLY_SECURITY_CONFIG_REMOTE 2001
extern  enum clnt_stat qcsapi_wifi_apply_security_config_remote_1();
extern  bool_t qcsapi_wifi_apply_security_config_remote_1_svc();
#define QCSAPI_WIFI_SET_MAC_ADDRESS_FILTERING_REMOTE 2011
extern  enum clnt_stat qcsapi_wifi_set_mac_address_filtering_remote_1();
extern  bool_t qcsapi_wifi_set_mac_address_filtering_remote_1_svc();
#define QCSAPI_WIFI_GET_MAC_ADDRESS_FILTERING_REMOTE 2021
extern  enum clnt_stat qcsapi_wifi_get_mac_address_filtering_remote_1();
extern  bool_t qcsapi_wifi_get_mac_address_filtering_remote_1_svc();
#define QCSAPI_WIFI_AUTHORIZE_MAC_ADDRESS_REMOTE 2031
extern  enum clnt_stat qcsapi_wifi_authorize_mac_address_remote_1();
extern  bool_t qcsapi_wifi_authorize_mac_address_remote_1_svc();
#define QCSAPI_WIFI_DENY_MAC_ADDRESS_REMOTE 2041
extern  enum clnt_stat qcsapi_wifi_deny_mac_address_remote_1();
extern  bool_t qcsapi_wifi_deny_mac_address_remote_1_svc();
#define QCSAPI_WIFI_REMOVE_MAC_ADDRESS_REMOTE 2051
extern  enum clnt_stat qcsapi_wifi_remove_mac_address_remote_1();
extern  bool_t qcsapi_wifi_remove_mac_address_remote_1_svc();
#define QCSAPI_WIFI_IS_MAC_ADDRESS_AUTHORIZED_REMOTE 2061
extern  enum clnt_stat qcsapi_wifi_is_mac_address_authorized_remote_1();
extern  bool_t qcsapi_wifi_is_mac_address_authorized_remote_1_svc();
#define QCSAPI_WIFI_GET_AUTHORIZED_MAC_ADDRESSES_REMOTE 2071
extern  enum clnt_stat qcsapi_wifi_get_authorized_mac_addresses_remote_1();
extern  bool_t qcsapi_wifi_get_authorized_mac_addresses_remote_1_svc();
#define QCSAPI_WIFI_GET_DENIED_MAC_ADDRESSES_REMOTE 2081
extern  enum clnt_stat qcsapi_wifi_get_denied_mac_addresses_remote_1();
extern  bool_t qcsapi_wifi_get_denied_mac_addresses_remote_1_svc();
#define QCSAPI_WIFI_SET_ACCEPT_OUI_FILTER_REMOTE 2091
extern  enum clnt_stat qcsapi_wifi_set_accept_oui_filter_remote_1();
extern  bool_t qcsapi_wifi_set_accept_oui_filter_remote_1_svc();
#define QCSAPI_WIFI_GET_ACCEPT_OUI_FILTER_REMOTE 2101
extern  enum clnt_stat qcsapi_wifi_get_accept_oui_filter_remote_1();
extern  bool_t qcsapi_wifi_get_accept_oui_filter_remote_1_svc();
#define QCSAPI_WIFI_CLEAR_MAC_ADDRESS_FILTERS_REMOTE 2111
extern  enum clnt_stat qcsapi_wifi_clear_mac_address_filters_remote_1();
extern  bool_t qcsapi_wifi_clear_mac_address_filters_remote_1_svc();
#define QCSAPI_WIFI_SET_MAC_ADDRESS_RESERVE_REMOTE 6011
extern  enum clnt_stat qcsapi_wifi_set_mac_address_reserve_remote_1();
extern  bool_t qcsapi_wifi_set_mac_address_reserve_remote_1_svc();
#define QCSAPI_WIFI_GET_MAC_ADDRESS_RESERVE_REMOTE 6021
extern  enum clnt_stat qcsapi_wifi_get_mac_address_reserve_remote_1();
extern  bool_t qcsapi_wifi_get_mac_address_reserve_remote_1_svc();
#define QCSAPI_WIFI_CLEAR_MAC_ADDRESS_RESERVE_REMOTE 6031
extern  enum clnt_stat qcsapi_wifi_clear_mac_address_reserve_remote_1();
extern  bool_t qcsapi_wifi_clear_mac_address_reserve_remote_1_svc();
#define QCSAPI_WIFI_GET_OPTION_REMOTE 2121
extern  enum clnt_stat qcsapi_wifi_get_option_remote_1();
extern  bool_t qcsapi_wifi_get_option_remote_1_svc();
#define QCSAPI_WIFI_SET_OPTION_REMOTE 2131
extern  enum clnt_stat qcsapi_wifi_set_option_remote_1();
extern  bool_t qcsapi_wifi_set_option_remote_1_svc();
#define QCSAPI_GET_BOARD_PARAMETER_REMOTE 2141
extern  enum clnt_stat qcsapi_get_board_parameter_remote_1();
extern  bool_t qcsapi_get_board_parameter_remote_1_svc();
#define QCSAPI_GET_SWFEAT_LIST_REMOTE 4451
extern  enum clnt_stat qcsapi_get_swfeat_list_remote_1();
extern  bool_t qcsapi_get_swfeat_list_remote_1_svc();
#define QCSAPI_SSID_CREATE_SSID_REMOTE 2151
extern  enum clnt_stat qcsapi_ssid_create_ssid_remote_1();
extern  bool_t qcsapi_ssid_create_ssid_remote_1_svc();
#define QCSAPI_SSID_REMOVE_SSID_REMOTE 2161
extern  enum clnt_stat qcsapi_ssid_remove_ssid_remote_1();
extern  bool_t qcsapi_ssid_remove_ssid_remote_1_svc();
#define QCSAPI_SSID_VERIFY_SSID_REMOTE 2171
extern  enum clnt_stat qcsapi_ssid_verify_ssid_remote_1();
extern  bool_t qcsapi_ssid_verify_ssid_remote_1_svc();
#define QCSAPI_SSID_RENAME_SSID_REMOTE 2181
extern  enum clnt_stat qcsapi_ssid_rename_ssid_remote_1();
extern  bool_t qcsapi_ssid_rename_ssid_remote_1_svc();
#define QCSAPI_SSID_GET_SSID_LIST_REMOTE 2191
extern  enum clnt_stat qcsapi_ssid_get_ssid_list_remote_1();
extern  bool_t qcsapi_ssid_get_ssid_list_remote_1_svc();
#define QCSAPI_SSID_SET_PROTOCOL_REMOTE 2201
extern  enum clnt_stat qcsapi_ssid_set_protocol_remote_1();
extern  bool_t qcsapi_ssid_set_protocol_remote_1_svc();
#define QCSAPI_SSID_GET_PROTOCOL_REMOTE 2211
extern  enum clnt_stat qcsapi_ssid_get_protocol_remote_1();
extern  bool_t qcsapi_ssid_get_protocol_remote_1_svc();
#define QCSAPI_SSID_GET_ENCRYPTION_MODES_REMOTE 2221
extern  enum clnt_stat qcsapi_ssid_get_encryption_modes_remote_1();
extern  bool_t qcsapi_ssid_get_encryption_modes_remote_1_svc();
#define QCSAPI_SSID_SET_ENCRYPTION_MODES_REMOTE 2231
extern  enum clnt_stat qcsapi_ssid_set_encryption_modes_remote_1();
extern  bool_t qcsapi_ssid_set_encryption_modes_remote_1_svc();
#define QCSAPI_SSID_GET_GROUP_ENCRYPTION_REMOTE 2241
extern  enum clnt_stat qcsapi_ssid_get_group_encryption_remote_1();
extern  bool_t qcsapi_ssid_get_group_encryption_remote_1_svc();
#define QCSAPI_SSID_SET_GROUP_ENCRYPTION_REMOTE 2251
extern  enum clnt_stat qcsapi_ssid_set_group_encryption_remote_1();
extern  bool_t qcsapi_ssid_set_group_encryption_remote_1_svc();
#define QCSAPI_SSID_GET_AUTHENTICATION_MODE_REMOTE 2261
extern  enum clnt_stat qcsapi_ssid_get_authentication_mode_remote_1();
extern  bool_t qcsapi_ssid_get_authentication_mode_remote_1_svc();
#define QCSAPI_SSID_SET_AUTHENTICATION_MODE_REMOTE 2271
extern  enum clnt_stat qcsapi_ssid_set_authentication_mode_remote_1();
extern  bool_t qcsapi_ssid_set_authentication_mode_remote_1_svc();
#define QCSAPI_SSID_GET_PRE_SHARED_KEY_REMOTE 2281
extern  enum clnt_stat qcsapi_ssid_get_pre_shared_key_remote_1();
extern  bool_t qcsapi_ssid_get_pre_shared_key_remote_1_svc();
#define QCSAPI_SSID_SET_PRE_SHARED_KEY_REMOTE 2291
extern  enum clnt_stat qcsapi_ssid_set_pre_shared_key_remote_1();
extern  bool_t qcsapi_ssid_set_pre_shared_key_remote_1_svc();
#define QCSAPI_SSID_GET_KEY_PASSPHRASE_REMOTE 2301
extern  enum clnt_stat qcsapi_ssid_get_key_passphrase_remote_1();
extern  bool_t qcsapi_ssid_get_key_passphrase_remote_1_svc();
#define QCSAPI_SSID_SET_KEY_PASSPHRASE_REMOTE 2311
extern  enum clnt_stat qcsapi_ssid_set_key_passphrase_remote_1();
extern  bool_t qcsapi_ssid_set_key_passphrase_remote_1_svc();
#define QCSAPI_SSID_GET_PMF_REMOTE 2321
extern  enum clnt_stat qcsapi_ssid_get_pmf_remote_1();
extern  bool_t qcsapi_ssid_get_pmf_remote_1_svc();
#define QCSAPI_SSID_SET_PMF_REMOTE 2331
extern  enum clnt_stat qcsapi_ssid_set_pmf_remote_1();
extern  bool_t qcsapi_ssid_set_pmf_remote_1_svc();
#define QCSAPI_SSID_GET_WPS_SSID_REMOTE 2341
extern  enum clnt_stat qcsapi_ssid_get_wps_ssid_remote_1();
extern  bool_t qcsapi_ssid_get_wps_ssid_remote_1_svc();
#define QCSAPI_WIFI_VLAN_CONFIG_REMOTE 2351
extern  enum clnt_stat qcsapi_wifi_vlan_config_remote_1();
extern  bool_t qcsapi_wifi_vlan_config_remote_1_svc();
#define QCSAPI_WIFI_SHOW_VLAN_CONFIG_REMOTE 2361
extern  enum clnt_stat qcsapi_wifi_show_vlan_config_remote_1();
extern  bool_t qcsapi_wifi_show_vlan_config_remote_1_svc();
#define QCSAPI_ENABLE_VLAN_PASS_THROUGH_REMOTE 2371
extern  enum clnt_stat qcsapi_enable_vlan_pass_through_remote_1();
extern  bool_t qcsapi_enable_vlan_pass_through_remote_1_svc();
#define QCSAPI_WIFI_SET_VLAN_PROMISC_REMOTE 2381
extern  enum clnt_stat qcsapi_wifi_set_vlan_promisc_remote_1();
extern  bool_t qcsapi_wifi_set_vlan_promisc_remote_1_svc();
#define QCSAPI_WPS_REGISTRAR_REPORT_BUTTON_PRESS_REMOTE 2391
extern  enum clnt_stat qcsapi_wps_registrar_report_button_press_remote_1();
extern  bool_t qcsapi_wps_registrar_report_button_press_remote_1_svc();
#define QCSAPI_WPS_REGISTRAR_REPORT_PIN_REMOTE 2401
extern  enum clnt_stat qcsapi_wps_registrar_report_pin_remote_1();
extern  bool_t qcsapi_wps_registrar_report_pin_remote_1_svc();
#define QCSAPI_WPS_REGISTRAR_GET_PP_DEVNAME_REMOTE 2411
extern  enum clnt_stat qcsapi_wps_registrar_get_pp_devname_remote_1();
extern  bool_t qcsapi_wps_registrar_get_pp_devname_remote_1_svc();
#define QCSAPI_WPS_REGISTRAR_SET_PP_DEVNAME_REMOTE 2421
extern  enum clnt_stat qcsapi_wps_registrar_set_pp_devname_remote_1();
extern  bool_t qcsapi_wps_registrar_set_pp_devname_remote_1_svc();
#define QCSAPI_WPS_ENROLLEE_REPORT_BUTTON_PRESS_REMOTE 2431
extern  enum clnt_stat qcsapi_wps_enrollee_report_button_press_remote_1();
extern  bool_t qcsapi_wps_enrollee_report_button_press_remote_1_svc();
#define QCSAPI_WPS_ENROLLEE_REPORT_PIN_REMOTE 2441
extern  enum clnt_stat qcsapi_wps_enrollee_report_pin_remote_1();
extern  bool_t qcsapi_wps_enrollee_report_pin_remote_1_svc();
#define QCSAPI_WPS_ENROLLEE_GENERATE_PIN_REMOTE 2451
extern  enum clnt_stat qcsapi_wps_enrollee_generate_pin_remote_1();
extern  bool_t qcsapi_wps_enrollee_generate_pin_remote_1_svc();
#define QCSAPI_WPS_GET_AP_PIN_REMOTE 2461
extern  enum clnt_stat qcsapi_wps_get_ap_pin_remote_1();
extern  bool_t qcsapi_wps_get_ap_pin_remote_1_svc();
#define QCSAPI_WPS_SET_AP_PIN_REMOTE 2471
extern  enum clnt_stat qcsapi_wps_set_ap_pin_remote_1();
extern  bool_t qcsapi_wps_set_ap_pin_remote_1_svc();
#define QCSAPI_WPS_SAVE_AP_PIN_REMOTE 2481
extern  enum clnt_stat qcsapi_wps_save_ap_pin_remote_1();
extern  bool_t qcsapi_wps_save_ap_pin_remote_1_svc();
#define QCSAPI_WPS_ENABLE_AP_PIN_REMOTE 2491
extern  enum clnt_stat qcsapi_wps_enable_ap_pin_remote_1();
extern  bool_t qcsapi_wps_enable_ap_pin_remote_1_svc();
#define QCSAPI_WPS_GET_STA_PIN_REMOTE 2501
extern  enum clnt_stat qcsapi_wps_get_sta_pin_remote_1();
extern  bool_t qcsapi_wps_get_sta_pin_remote_1_svc();
#define QCSAPI_WPS_GET_STATE_REMOTE 2511
extern  enum clnt_stat qcsapi_wps_get_state_remote_1();
extern  bool_t qcsapi_wps_get_state_remote_1_svc();
#define QCSAPI_WPS_GET_CONFIGURED_STATE_REMOTE 2521
extern  enum clnt_stat qcsapi_wps_get_configured_state_remote_1();
extern  bool_t qcsapi_wps_get_configured_state_remote_1_svc();
#define QCSAPI_WPS_GET_RUNTIME_STATE_REMOTE 2531
extern  enum clnt_stat qcsapi_wps_get_runtime_state_remote_1();
extern  bool_t qcsapi_wps_get_runtime_state_remote_1_svc();
#define QCSAPI_WPS_SET_CONFIGURED_STATE_REMOTE 2541
extern  enum clnt_stat qcsapi_wps_set_configured_state_remote_1();
extern  bool_t qcsapi_wps_set_configured_state_remote_1_svc();
#define QCSAPI_WPS_GET_PARAM_REMOTE 2551
extern  enum clnt_stat qcsapi_wps_get_param_remote_1();
extern  bool_t qcsapi_wps_get_param_remote_1_svc();
#define QCSAPI_WPS_SET_TIMEOUT_REMOTE 2561
extern  enum clnt_stat qcsapi_wps_set_timeout_remote_1();
extern  bool_t qcsapi_wps_set_timeout_remote_1_svc();
#define QCSAPI_WPS_ON_HIDDEN_SSID_REMOTE 2571
extern  enum clnt_stat qcsapi_wps_on_hidden_ssid_remote_1();
extern  bool_t qcsapi_wps_on_hidden_ssid_remote_1_svc();
#define QCSAPI_WPS_ON_HIDDEN_SSID_STATUS_REMOTE 2581
extern  enum clnt_stat qcsapi_wps_on_hidden_ssid_status_remote_1();
extern  bool_t qcsapi_wps_on_hidden_ssid_status_remote_1_svc();
#define QCSAPI_WPS_UPNP_ENABLE_REMOTE 2591
extern  enum clnt_stat qcsapi_wps_upnp_enable_remote_1();
extern  bool_t qcsapi_wps_upnp_enable_remote_1_svc();
#define QCSAPI_WPS_UPNP_STATUS_REMOTE 2601
extern  enum clnt_stat qcsapi_wps_upnp_status_remote_1();
extern  bool_t qcsapi_wps_upnp_status_remote_1_svc();
#define QCSAPI_WPS_ALLOW_PBC_OVERLAP_REMOTE 2611
extern  enum clnt_stat qcsapi_wps_allow_pbc_overlap_remote_1();
extern  bool_t qcsapi_wps_allow_pbc_overlap_remote_1_svc();
#define QCSAPI_WPS_GET_ALLOW_PBC_OVERLAP_STATUS_REMOTE 2621
extern  enum clnt_stat qcsapi_wps_get_allow_pbc_overlap_status_remote_1();
extern  bool_t qcsapi_wps_get_allow_pbc_overlap_status_remote_1_svc();
#define QCSAPI_WPS_SET_ACCESS_CONTROL_REMOTE 2631
extern  enum clnt_stat qcsapi_wps_set_access_control_remote_1();
extern  bool_t qcsapi_wps_set_access_control_remote_1_svc();
#define QCSAPI_WPS_GET_ACCESS_CONTROL_REMOTE 2641
extern  enum clnt_stat qcsapi_wps_get_access_control_remote_1();
extern  bool_t qcsapi_wps_get_access_control_remote_1_svc();
#define QCSAPI_WPS_SET_PARAM_REMOTE 2651
extern  enum clnt_stat qcsapi_wps_set_param_remote_1();
extern  bool_t qcsapi_wps_set_param_remote_1_svc();
#define QCSAPI_WPS_CANCEL_REMOTE 2661
extern  enum clnt_stat qcsapi_wps_cancel_remote_1();
extern  bool_t qcsapi_wps_cancel_remote_1_svc();
#define QCSAPI_WPS_SET_PBC_IN_SRCM_REMOTE 2671
extern  enum clnt_stat qcsapi_wps_set_pbc_in_srcm_remote_1();
extern  bool_t qcsapi_wps_set_pbc_in_srcm_remote_1_svc();
#define QCSAPI_WPS_GET_PBC_IN_SRCM_REMOTE 2681
extern  enum clnt_stat qcsapi_wps_get_pbc_in_srcm_remote_1();
extern  bool_t qcsapi_wps_get_pbc_in_srcm_remote_1_svc();
#define QCSAPI_REGISTRAR_SET_DEFAULT_PBC_BSS_REMOTE 2691
extern  enum clnt_stat qcsapi_registrar_set_default_pbc_bss_remote_1();
extern  bool_t qcsapi_registrar_set_default_pbc_bss_remote_1_svc();
#define QCSAPI_REGISTRAR_GET_DEFAULT_PBC_BSS_REMOTE 2701
extern  enum clnt_stat qcsapi_registrar_get_default_pbc_bss_remote_1();
extern  bool_t qcsapi_registrar_get_default_pbc_bss_remote_1_svc();
#define QCSAPI_GPIO_SET_CONFIG_REMOTE 2711
extern  enum clnt_stat qcsapi_gpio_set_config_remote_1();
extern  bool_t qcsapi_gpio_set_config_remote_1_svc();
#define QCSAPI_GPIO_GET_CONFIG_REMOTE 2721
extern  enum clnt_stat qcsapi_gpio_get_config_remote_1();
extern  bool_t qcsapi_gpio_get_config_remote_1_svc();
#define QCSAPI_LED_GET_REMOTE 2731
extern  enum clnt_stat qcsapi_led_get_remote_1();
extern  bool_t qcsapi_led_get_remote_1_svc();
#define QCSAPI_LED_SET_REMOTE 2741
extern  enum clnt_stat qcsapi_led_set_remote_1();
extern  bool_t qcsapi_led_set_remote_1_svc();
#define QCSAPI_LED_PWM_ENABLE_REMOTE 2751
extern  enum clnt_stat qcsapi_led_pwm_enable_remote_1();
extern  bool_t qcsapi_led_pwm_enable_remote_1_svc();
#define QCSAPI_LED_BRIGHTNESS_REMOTE 2761
extern  enum clnt_stat qcsapi_led_brightness_remote_1();
extern  bool_t qcsapi_led_brightness_remote_1_svc();
#define QCSAPI_GPIO_ENABLE_WPS_PUSH_BUTTON_REMOTE 2781
extern  enum clnt_stat qcsapi_gpio_enable_wps_push_button_remote_1();
extern  bool_t qcsapi_gpio_enable_wps_push_button_remote_1_svc();
#define QCSAPI_WIFI_GET_COUNT_ASSOCIATIONS_REMOTE 2791
extern  enum clnt_stat qcsapi_wifi_get_count_associations_remote_1();
extern  bool_t qcsapi_wifi_get_count_associations_remote_1_svc();
#define QCSAPI_WIFI_GET_ASSOCIATED_DEVICE_MAC_ADDR_REMOTE 2801
extern  enum clnt_stat qcsapi_wifi_get_associated_device_mac_addr_remote_1();
extern  bool_t qcsapi_wifi_get_associated_device_mac_addr_remote_1_svc();
#define QCSAPI_WIFI_GET_ASSOCIATED_DEVICE_IP_ADDR_REMOTE 2811
extern  enum clnt_stat qcsapi_wifi_get_associated_device_ip_addr_remote_1();
extern  bool_t qcsapi_wifi_get_associated_device_ip_addr_remote_1_svc();
#define QCSAPI_WIFI_GET_LINK_QUALITY_REMOTE 2821
extern  enum clnt_stat qcsapi_wifi_get_link_quality_remote_1();
extern  bool_t qcsapi_wifi_get_link_quality_remote_1_svc();
#define QCSAPI_WIFI_GET_LINK_QUALITY_MAX_REMOTE 5851
extern  enum clnt_stat qcsapi_wifi_get_link_quality_max_remote_1();
extern  bool_t qcsapi_wifi_get_link_quality_max_remote_1_svc();
#define QCSAPI_WIFI_GET_RX_BYTES_PER_ASSOCIATION_REMOTE 2831
extern  enum clnt_stat qcsapi_wifi_get_rx_bytes_per_association_remote_1();
extern  bool_t qcsapi_wifi_get_rx_bytes_per_association_remote_1_svc();
#define QCSAPI_WIFI_GET_TX_BYTES_PER_ASSOCIATION_REMOTE 2841
extern  enum clnt_stat qcsapi_wifi_get_tx_bytes_per_association_remote_1();
extern  bool_t qcsapi_wifi_get_tx_bytes_per_association_remote_1_svc();
#define QCSAPI_WIFI_GET_RX_PACKETS_PER_ASSOCIATION_REMOTE 2851
extern  enum clnt_stat qcsapi_wifi_get_rx_packets_per_association_remote_1();
extern  bool_t qcsapi_wifi_get_rx_packets_per_association_remote_1_svc();
#define QCSAPI_WIFI_GET_TX_PACKETS_PER_ASSOCIATION_REMOTE 2861
extern  enum clnt_stat qcsapi_wifi_get_tx_packets_per_association_remote_1();
extern  bool_t qcsapi_wifi_get_tx_packets_per_association_remote_1_svc();
#define QCSAPI_WIFI_GET_TX_ERR_PACKETS_PER_ASSOCIATION_REMOTE 2871
extern  enum clnt_stat qcsapi_wifi_get_tx_err_packets_per_association_remote_1();
extern  bool_t qcsapi_wifi_get_tx_err_packets_per_association_remote_1_svc();
#define QCSAPI_WIFI_GET_RSSI_PER_ASSOCIATION_REMOTE 2881
extern  enum clnt_stat qcsapi_wifi_get_rssi_per_association_remote_1();
extern  bool_t qcsapi_wifi_get_rssi_per_association_remote_1_svc();
#define QCSAPI_WIFI_GET_RSSI_IN_DBM_PER_ASSOCIATION_REMOTE 2891
extern  enum clnt_stat qcsapi_wifi_get_rssi_in_dbm_per_association_remote_1();
extern  bool_t qcsapi_wifi_get_rssi_in_dbm_per_association_remote_1_svc();
#define QCSAPI_WIFI_GET_BW_PER_ASSOCIATION_REMOTE 2901
extern  enum clnt_stat qcsapi_wifi_get_bw_per_association_remote_1();
extern  bool_t qcsapi_wifi_get_bw_per_association_remote_1_svc();
#define QCSAPI_WIFI_GET_TX_PHY_RATE_PER_ASSOCIATION_REMOTE 2911
extern  enum clnt_stat qcsapi_wifi_get_tx_phy_rate_per_association_remote_1();
extern  bool_t qcsapi_wifi_get_tx_phy_rate_per_association_remote_1_svc();
#define QCSAPI_WIFI_GET_RX_PHY_RATE_PER_ASSOCIATION_REMOTE 2921
extern  enum clnt_stat qcsapi_wifi_get_rx_phy_rate_per_association_remote_1();
extern  bool_t qcsapi_wifi_get_rx_phy_rate_per_association_remote_1_svc();
#define QCSAPI_WIFI_GET_TX_MCS_PER_ASSOCIATION_REMOTE 2931
extern  enum clnt_stat qcsapi_wifi_get_tx_mcs_per_association_remote_1();
extern  bool_t qcsapi_wifi_get_tx_mcs_per_association_remote_1_svc();
#define QCSAPI_WIFI_GET_RX_MCS_PER_ASSOCIATION_REMOTE 2941
extern  enum clnt_stat qcsapi_wifi_get_rx_mcs_per_association_remote_1();
extern  bool_t qcsapi_wifi_get_rx_mcs_per_association_remote_1_svc();
#define QCSAPI_WIFI_GET_ACHIEVABLE_TX_PHY_RATE_PER_ASSOCIATION_REMOTE 2951
extern  enum clnt_stat qcsapi_wifi_get_achievable_tx_phy_rate_per_association_remote_1();
extern  bool_t qcsapi_wifi_get_achievable_tx_phy_rate_per_association_remote_1_svc();
#define QCSAPI_WIFI_GET_ACHIEVABLE_RX_PHY_RATE_PER_ASSOCIATION_REMOTE 2961
extern  enum clnt_stat qcsapi_wifi_get_achievable_rx_phy_rate_per_association_remote_1();
extern  bool_t qcsapi_wifi_get_achievable_rx_phy_rate_per_association_remote_1_svc();
#define QCSAPI_WIFI_GET_AUTH_ENC_PER_ASSOCIATION_REMOTE 2971
extern  enum clnt_stat qcsapi_wifi_get_auth_enc_per_association_remote_1();
extern  bool_t qcsapi_wifi_get_auth_enc_per_association_remote_1_svc();
#define QCSAPI_WIFI_GET_TPUT_CAPS_REMOTE 2981
extern  enum clnt_stat qcsapi_wifi_get_tput_caps_remote_1();
extern  bool_t qcsapi_wifi_get_tput_caps_remote_1_svc();
#define QCSAPI_WIFI_GET_CONNECTION_MODE_REMOTE 2991
extern  enum clnt_stat qcsapi_wifi_get_connection_mode_remote_1();
extern  bool_t qcsapi_wifi_get_connection_mode_remote_1_svc();
#define QCSAPI_WIFI_GET_VENDOR_PER_ASSOCIATION_REMOTE 3001
extern  enum clnt_stat qcsapi_wifi_get_vendor_per_association_remote_1();
extern  bool_t qcsapi_wifi_get_vendor_per_association_remote_1_svc();
#define QCSAPI_WIFI_GET_MAX_MIMO_REMOTE 4461
extern  enum clnt_stat qcsapi_wifi_get_max_mimo_remote_1();
extern  bool_t qcsapi_wifi_get_max_mimo_remote_1_svc();
#define QCSAPI_WIFI_GET_SNR_PER_ASSOCIATION_REMOTE 3011
extern  enum clnt_stat qcsapi_wifi_get_snr_per_association_remote_1();
extern  bool_t qcsapi_wifi_get_snr_per_association_remote_1_svc();
#define QCSAPI_WIFI_GET_TIME_ASSOCIATED_PER_ASSOCIATION_REMOTE 3021
extern  enum clnt_stat qcsapi_wifi_get_time_associated_per_association_remote_1();
extern  bool_t qcsapi_wifi_get_time_associated_per_association_remote_1_svc();
#define QCSAPI_WIFI_GET_NODE_PARAM_REMOTE 3031
extern  enum clnt_stat qcsapi_wifi_get_node_param_remote_1();
extern  bool_t qcsapi_wifi_get_node_param_remote_1_svc();
#define QCSAPI_WIFI_GET_NODE_COUNTER_REMOTE 3041
extern  enum clnt_stat qcsapi_wifi_get_node_counter_remote_1();
extern  bool_t qcsapi_wifi_get_node_counter_remote_1_svc();
#define QCSAPI_WIFI_GET_NODE_STATS_REMOTE 3051
extern  enum clnt_stat qcsapi_wifi_get_node_stats_remote_1();
extern  bool_t qcsapi_wifi_get_node_stats_remote_1_svc();
#define QCSAPI_WIFI_GET_MAX_QUEUED_REMOTE 3061
extern  enum clnt_stat qcsapi_wifi_get_max_queued_remote_1();
extern  bool_t qcsapi_wifi_get_max_queued_remote_1_svc();
#define QCSAPI_WIFI_GET_HW_NOISE_PER_ASSOCIATION_REMOTE 3071
extern  enum clnt_stat qcsapi_wifi_get_hw_noise_per_association_remote_1();
extern  bool_t qcsapi_wifi_get_hw_noise_per_association_remote_1_svc();
#define QCSAPI_WIFI_GET_MLME_STATS_PER_MAC_REMOTE 3591
extern  enum clnt_stat qcsapi_wifi_get_mlme_stats_per_mac_remote_1();
extern  bool_t qcsapi_wifi_get_mlme_stats_per_mac_remote_1_svc();
#define QCSAPI_WIFI_GET_MLME_STATS_PER_ASSOCIATION_REMOTE 3601
extern  enum clnt_stat qcsapi_wifi_get_mlme_stats_per_association_remote_1();
extern  bool_t qcsapi_wifi_get_mlme_stats_per_association_remote_1_svc();
#define QCSAPI_WIFI_GET_MLME_STATS_MACS_LIST_REMOTE 3611
extern  enum clnt_stat qcsapi_wifi_get_mlme_stats_macs_list_remote_1();
extern  bool_t qcsapi_wifi_get_mlme_stats_macs_list_remote_1_svc();
#define QCSAPI_WIFI_GET_LIST_REGULATORY_REGIONS_REMOTE 3081
extern  enum clnt_stat qcsapi_wifi_get_list_regulatory_regions_remote_1();
extern  bool_t qcsapi_wifi_get_list_regulatory_regions_remote_1_svc();
#define QCSAPI_REGULATORY_GET_LIST_REGULATORY_REGIONS_REMOTE 3091
extern  enum clnt_stat qcsapi_regulatory_get_list_regulatory_regions_remote_1();
extern  bool_t qcsapi_regulatory_get_list_regulatory_regions_remote_1_svc();
#define QCSAPI_WIFI_GET_LIST_REGULATORY_CHANNELS_REMOTE 3101
extern  enum clnt_stat qcsapi_wifi_get_list_regulatory_channels_remote_1();
extern  bool_t qcsapi_wifi_get_list_regulatory_channels_remote_1_svc();
#define QCSAPI_REGULATORY_GET_LIST_REGULATORY_CHANNELS_REMOTE 3111
extern  enum clnt_stat qcsapi_regulatory_get_list_regulatory_channels_remote_1();
extern  bool_t qcsapi_regulatory_get_list_regulatory_channels_remote_1_svc();
#define QCSAPI_REGULATORY_GET_LIST_REGULATORY_BANDS_REMOTE 3121
extern  enum clnt_stat qcsapi_regulatory_get_list_regulatory_bands_remote_1();
extern  bool_t qcsapi_regulatory_get_list_regulatory_bands_remote_1_svc();
#define QCSAPI_WIFI_GET_REGULATORY_TX_POWER_REMOTE 3131
extern  enum clnt_stat qcsapi_wifi_get_regulatory_tx_power_remote_1();
extern  bool_t qcsapi_wifi_get_regulatory_tx_power_remote_1_svc();
#define QCSAPI_REGULATORY_GET_REGULATORY_TX_POWER_REMOTE 3141
extern  enum clnt_stat qcsapi_regulatory_get_regulatory_tx_power_remote_1();
extern  bool_t qcsapi_regulatory_get_regulatory_tx_power_remote_1_svc();
#define QCSAPI_WIFI_GET_CONFIGURED_TX_POWER_REMOTE 3151
extern  enum clnt_stat qcsapi_wifi_get_configured_tx_power_remote_1();
extern  bool_t qcsapi_wifi_get_configured_tx_power_remote_1_svc();
#define QCSAPI_REGULATORY_GET_CONFIGURED_TX_POWER_REMOTE 3161
extern  enum clnt_stat qcsapi_regulatory_get_configured_tx_power_remote_1();
extern  bool_t qcsapi_regulatory_get_configured_tx_power_remote_1_svc();
#define QCSAPI_REGULATORY_GET_CONFIGURED_TX_POWER_EXT_REMOTE 5681
extern  enum clnt_stat qcsapi_regulatory_get_configured_tx_power_ext_remote_1();
extern  bool_t qcsapi_regulatory_get_configured_tx_power_ext_remote_1_svc();
#define QCSAPI_WIFI_SET_REGULATORY_REGION_REMOTE 3171
extern  enum clnt_stat qcsapi_wifi_set_regulatory_region_remote_1();
extern  bool_t qcsapi_wifi_set_regulatory_region_remote_1_svc();
#define QCSAPI_REGULATORY_SET_REGULATORY_REGION_REMOTE 3181
extern  enum clnt_stat qcsapi_regulatory_set_regulatory_region_remote_1();
extern  bool_t qcsapi_regulatory_set_regulatory_region_remote_1_svc();
#define QCSAPI_REGULATORY_RESTORE_REGULATORY_TX_POWER_REMOTE 3191
extern  enum clnt_stat qcsapi_regulatory_restore_regulatory_tx_power_remote_1();
extern  bool_t qcsapi_regulatory_restore_regulatory_tx_power_remote_1_svc();
#define QCSAPI_WIFI_GET_REGULATORY_REGION_REMOTE 3201
extern  enum clnt_stat qcsapi_wifi_get_regulatory_region_remote_1();
extern  bool_t qcsapi_wifi_get_regulatory_region_remote_1_svc();
#define QCSAPI_REGULATORY_OVERWRITE_COUNTRY_CODE_REMOTE 3211
extern  enum clnt_stat qcsapi_regulatory_overwrite_country_code_remote_1();
extern  bool_t qcsapi_regulatory_overwrite_country_code_remote_1_svc();
#define QCSAPI_WIFI_SET_REGULATORY_CHANNEL_REMOTE 3221
extern  enum clnt_stat qcsapi_wifi_set_regulatory_channel_remote_1();
extern  bool_t qcsapi_wifi_set_regulatory_channel_remote_1_svc();
#define QCSAPI_REGULATORY_SET_REGULATORY_CHANNEL_REMOTE 3231
extern  enum clnt_stat qcsapi_regulatory_set_regulatory_channel_remote_1();
extern  bool_t qcsapi_regulatory_set_regulatory_channel_remote_1_svc();
#define QCSAPI_REGULATORY_GET_DB_VERSION_REMOTE 3241
extern  enum clnt_stat qcsapi_regulatory_get_db_version_remote_1();
extern  bool_t qcsapi_regulatory_get_db_version_remote_1_svc();
#define QCSAPI_REGULATORY_APPLY_TX_POWER_CAP_REMOTE 3251
extern  enum clnt_stat qcsapi_regulatory_apply_tx_power_cap_remote_1();
extern  bool_t qcsapi_regulatory_apply_tx_power_cap_remote_1_svc();
#define QCSAPI_WIFI_GET_LIST_DFS_CHANNELS_REMOTE 3261
extern  enum clnt_stat qcsapi_wifi_get_list_dfs_channels_remote_1();
extern  bool_t qcsapi_wifi_get_list_dfs_channels_remote_1_svc();
#define QCSAPI_REGULATORY_GET_LIST_DFS_CHANNELS_REMOTE 3271
extern  enum clnt_stat qcsapi_regulatory_get_list_dfs_channels_remote_1();
extern  bool_t qcsapi_regulatory_get_list_dfs_channels_remote_1_svc();
#define QCSAPI_WIFI_IS_CHANNEL_DFS_REMOTE 3281
extern  enum clnt_stat qcsapi_wifi_is_channel_dfs_remote_1();
extern  bool_t qcsapi_wifi_is_channel_dfs_remote_1_svc();
#define QCSAPI_REGULATORY_IS_CHANNEL_DFS_REMOTE 3291
extern  enum clnt_stat qcsapi_regulatory_is_channel_dfs_remote_1();
extern  bool_t qcsapi_regulatory_is_channel_dfs_remote_1_svc();
#define QCSAPI_WIFI_GET_DFS_CCE_CHANNELS_REMOTE 3301
extern  enum clnt_stat qcsapi_wifi_get_dfs_cce_channels_remote_1();
extern  bool_t qcsapi_wifi_get_dfs_cce_channels_remote_1_svc();
#define QCSAPI_WIFI_GET_DFS_ALT_CHANNEL_REMOTE 3311
extern  enum clnt_stat qcsapi_wifi_get_dfs_alt_channel_remote_1();
extern  bool_t qcsapi_wifi_get_dfs_alt_channel_remote_1_svc();
#define QCSAPI_WIFI_SET_DFS_ALT_CHANNEL_REMOTE 3321
extern  enum clnt_stat qcsapi_wifi_set_dfs_alt_channel_remote_1();
extern  bool_t qcsapi_wifi_set_dfs_alt_channel_remote_1_svc();
#define QCSAPI_WIFI_START_DFS_REENTRY_REMOTE 3331
extern  enum clnt_stat qcsapi_wifi_start_dfs_reentry_remote_1();
extern  bool_t qcsapi_wifi_start_dfs_reentry_remote_1_svc();
#define QCSAPI_WIFI_START_SCAN_EXT_REMOTE 3341
extern  enum clnt_stat qcsapi_wifi_start_scan_ext_remote_1();
extern  bool_t qcsapi_wifi_start_scan_ext_remote_1_svc();
#define QCSAPI_WIFI_GET_CSW_RECORDS_REMOTE 3351
extern  enum clnt_stat qcsapi_wifi_get_csw_records_remote_1();
extern  bool_t qcsapi_wifi_get_csw_records_remote_1_svc();
#define QCSAPI_WIFI_GET_RADAR_STATUS_REMOTE 3361
extern  enum clnt_stat qcsapi_wifi_get_radar_status_remote_1();
extern  bool_t qcsapi_wifi_get_radar_status_remote_1_svc();
#define QCSAPI_WIFI_GET_CAC_STATUS_REMOTE 1191
extern  enum clnt_stat qcsapi_wifi_get_cac_status_remote_1();
extern  bool_t qcsapi_wifi_get_cac_status_remote_1_svc();
#define QCSAPI_WIFI_GET_RESULTS_AP_SCAN_REMOTE 3371
extern  enum clnt_stat qcsapi_wifi_get_results_ap_scan_remote_1();
extern  bool_t qcsapi_wifi_get_results_ap_scan_remote_1_svc();
#define QCSAPI_WIFI_GET_COUNT_APS_SCANNED_REMOTE 3381
extern  enum clnt_stat qcsapi_wifi_get_count_aps_scanned_remote_1();
extern  bool_t qcsapi_wifi_get_count_aps_scanned_remote_1_svc();
#define QCSAPI_WIFI_GET_PROPERTIES_AP_REMOTE 3391
extern  enum clnt_stat qcsapi_wifi_get_properties_ap_remote_1();
extern  bool_t qcsapi_wifi_get_properties_ap_remote_1_svc();
#define QCSAPI_WIFI_SET_SCAN_CHK_INV_REMOTE 4491
extern  enum clnt_stat qcsapi_wifi_set_scan_chk_inv_remote_1();
extern  bool_t qcsapi_wifi_set_scan_chk_inv_remote_1_svc();
#define QCSAPI_WIFI_GET_SCAN_CHK_INV_REMOTE 4501
extern  enum clnt_stat qcsapi_wifi_get_scan_chk_inv_remote_1();
extern  bool_t qcsapi_wifi_get_scan_chk_inv_remote_1_svc();
#define QCSAPI_WIFI_SET_SCAN_BUF_MAX_SIZE_REMOTE 4301
extern  enum clnt_stat qcsapi_wifi_set_scan_buf_max_size_remote_1();
extern  bool_t qcsapi_wifi_set_scan_buf_max_size_remote_1_svc();
#define QCSAPI_WIFI_GET_SCAN_BUF_MAX_SIZE_REMOTE 4311
extern  enum clnt_stat qcsapi_wifi_get_scan_buf_max_size_remote_1();
extern  bool_t qcsapi_wifi_get_scan_buf_max_size_remote_1_svc();
#define QCSAPI_WIFI_SET_SCAN_TABLE_MAX_LEN_REMOTE 4321
extern  enum clnt_stat qcsapi_wifi_set_scan_table_max_len_remote_1();
extern  bool_t qcsapi_wifi_set_scan_table_max_len_remote_1_svc();
#define QCSAPI_WIFI_GET_SCAN_TABLE_MAX_LEN_REMOTE 4331
extern  enum clnt_stat qcsapi_wifi_get_scan_table_max_len_remote_1();
extern  bool_t qcsapi_wifi_get_scan_table_max_len_remote_1_svc();
#define QCSAPI_WIFI_SET_DWELL_TIMES_REMOTE 1121
extern  enum clnt_stat qcsapi_wifi_set_dwell_times_remote_1();
extern  bool_t qcsapi_wifi_set_dwell_times_remote_1_svc();
#define QCSAPI_WIFI_GET_DWELL_TIMES_REMOTE 1131
extern  enum clnt_stat qcsapi_wifi_get_dwell_times_remote_1();
extern  bool_t qcsapi_wifi_get_dwell_times_remote_1_svc();
#define QCSAPI_WIFI_SET_BGSCAN_DWELL_TIMES_REMOTE 1141
extern  enum clnt_stat qcsapi_wifi_set_bgscan_dwell_times_remote_1();
extern  bool_t qcsapi_wifi_set_bgscan_dwell_times_remote_1_svc();
#define QCSAPI_WIFI_GET_BGSCAN_DWELL_TIMES_REMOTE 1151
extern  enum clnt_stat qcsapi_wifi_get_bgscan_dwell_times_remote_1();
extern  bool_t qcsapi_wifi_get_bgscan_dwell_times_remote_1_svc();
#define QCSAPI_WIFI_START_SCAN_REMOTE 1161
extern  enum clnt_stat qcsapi_wifi_start_scan_remote_1();
extern  bool_t qcsapi_wifi_start_scan_remote_1_svc();
#define QCSAPI_WIFI_CANCEL_SCAN_REMOTE 1171
extern  enum clnt_stat qcsapi_wifi_cancel_scan_remote_1();
extern  bool_t qcsapi_wifi_cancel_scan_remote_1_svc();
#define QCSAPI_WIFI_GET_SCAN_STATUS_REMOTE 1181
extern  enum clnt_stat qcsapi_wifi_get_scan_status_remote_1();
extern  bool_t qcsapi_wifi_get_scan_status_remote_1_svc();
#define QCSAPI_WIFI_ENABLE_BGSCAN_REMOTE 1561
extern  enum clnt_stat qcsapi_wifi_enable_bgscan_remote_1();
extern  bool_t qcsapi_wifi_enable_bgscan_remote_1_svc();
#define QCSAPI_WIFI_GET_BGSCAN_STATUS_REMOTE 1571
extern  enum clnt_stat qcsapi_wifi_get_bgscan_status_remote_1();
extern  bool_t qcsapi_wifi_get_bgscan_status_remote_1_svc();
#define QCSAPI_WIFI_WAIT_SCAN_COMPLETES_REMOTE 1201
extern  enum clnt_stat qcsapi_wifi_wait_scan_completes_remote_1();
extern  bool_t qcsapi_wifi_wait_scan_completes_remote_1_svc();
#define QCSAPI_WIFI_BACKOFF_FAIL_MAX_REMOTE 3401
extern  enum clnt_stat qcsapi_wifi_backoff_fail_max_remote_1();
extern  bool_t qcsapi_wifi_backoff_fail_max_remote_1_svc();
#define QCSAPI_WIFI_BACKOFF_TIMEOUT_REMOTE 3411
extern  enum clnt_stat qcsapi_wifi_backoff_timeout_remote_1();
extern  bool_t qcsapi_wifi_backoff_timeout_remote_1_svc();
#define QCSAPI_WIFI_GET_MCS_RATE_REMOTE 3421
extern  enum clnt_stat qcsapi_wifi_get_mcs_rate_remote_1();
extern  bool_t qcsapi_wifi_get_mcs_rate_remote_1_svc();
#define QCSAPI_WIFI_SET_MCS_RATE_REMOTE 3431
extern  enum clnt_stat qcsapi_wifi_set_mcs_rate_remote_1();
extern  bool_t qcsapi_wifi_set_mcs_rate_remote_1_svc();
#define QCSAPI_WIFI_SET_PAIRING_ID_REMOTE 3441
extern  enum clnt_stat qcsapi_wifi_set_pairing_id_remote_1();
extern  bool_t qcsapi_wifi_set_pairing_id_remote_1_svc();
#define QCSAPI_WIFI_GET_PAIRING_ID_REMOTE 3451
extern  enum clnt_stat qcsapi_wifi_get_pairing_id_remote_1();
extern  bool_t qcsapi_wifi_get_pairing_id_remote_1_svc();
#define QCSAPI_WIFI_SET_PAIRING_ENABLE_REMOTE 3461
extern  enum clnt_stat qcsapi_wifi_set_pairing_enable_remote_1();
extern  bool_t qcsapi_wifi_set_pairing_enable_remote_1_svc();
#define QCSAPI_WIFI_GET_PAIRING_ENABLE_REMOTE 3471
extern  enum clnt_stat qcsapi_wifi_get_pairing_enable_remote_1();
extern  bool_t qcsapi_wifi_get_pairing_enable_remote_1_svc();
#define QCSAPI_NON_WPS_SET_PP_ENABLE_REMOTE 3481
extern  enum clnt_stat qcsapi_non_wps_set_pp_enable_remote_1();
extern  bool_t qcsapi_non_wps_set_pp_enable_remote_1_svc();
#define QCSAPI_NON_WPS_GET_PP_ENABLE_REMOTE 3491
extern  enum clnt_stat qcsapi_non_wps_get_pp_enable_remote_1();
extern  bool_t qcsapi_non_wps_get_pp_enable_remote_1_svc();
#define QCSAPI_WIFI_SET_VENDOR_FIX_REMOTE 3501
extern  enum clnt_stat qcsapi_wifi_set_vendor_fix_remote_1();
extern  bool_t qcsapi_wifi_set_vendor_fix_remote_1_svc();
#define QCSAPI_ERRNO_GET_MESSAGE_REMOTE 3511
extern  enum clnt_stat qcsapi_errno_get_message_remote_1();
extern  bool_t qcsapi_errno_get_message_remote_1_svc();
#define QCSAPI_GET_INTERFACE_STATS_REMOTE 3521
extern  enum clnt_stat qcsapi_get_interface_stats_remote_1();
extern  bool_t qcsapi_get_interface_stats_remote_1_svc();
#define QCSAPI_GET_PHY_STATS_REMOTE 3531
extern  enum clnt_stat qcsapi_get_phy_stats_remote_1();
extern  bool_t qcsapi_get_phy_stats_remote_1_svc();
#define QCSAPI_RESET_ALL_COUNTERS_REMOTE 3541
extern  enum clnt_stat qcsapi_reset_all_counters_remote_1();
extern  bool_t qcsapi_reset_all_counters_remote_1_svc();
#define QCSAPI_GET_UBOOT_INFO_REMOTE 661
extern  enum clnt_stat qcsapi_get_uboot_info_remote_1();
extern  bool_t qcsapi_get_uboot_info_remote_1_svc();
#define QCSAPI_FIRMWARE_GET_VERSION_REMOTE 3551
extern  enum clnt_stat qcsapi_firmware_get_version_remote_1();
extern  bool_t qcsapi_firmware_get_version_remote_1_svc();
#define QCSAPI_FLASH_IMAGE_UPDATE_REMOTE 3561
extern  enum clnt_stat qcsapi_flash_image_update_remote_1();
extern  bool_t qcsapi_flash_image_update_remote_1_svc();
#define QCSAPI_SEND_FILE_REMOTE 5961
extern  enum clnt_stat qcsapi_send_file_remote_1();
extern  bool_t qcsapi_send_file_remote_1_svc();
#define QCSAPI_PM_SET_MODE_REMOTE 3621
extern  enum clnt_stat qcsapi_pm_set_mode_remote_1();
extern  bool_t qcsapi_pm_set_mode_remote_1_svc();
#define QCSAPI_PM_GET_MODE_REMOTE 3631
extern  enum clnt_stat qcsapi_pm_get_mode_remote_1();
extern  bool_t qcsapi_pm_get_mode_remote_1_svc();
#define QCSAPI_GET_QPM_LEVEL_REMOTE 3641
extern  enum clnt_stat qcsapi_get_qpm_level_remote_1();
extern  bool_t qcsapi_get_qpm_level_remote_1_svc();
#define QCSAPI_SET_HOST_STATE_REMOTE 4151
extern  enum clnt_stat qcsapi_set_host_state_remote_1();
extern  bool_t qcsapi_set_host_state_remote_1_svc();
#define QCSAPI_QTM_GET_STATE_REMOTE 3651
extern  enum clnt_stat qcsapi_qtm_get_state_remote_1();
extern  bool_t qcsapi_qtm_get_state_remote_1_svc();
#define QCSAPI_QTM_GET_STATE_ALL_REMOTE 3661
extern  enum clnt_stat qcsapi_qtm_get_state_all_remote_1();
extern  bool_t qcsapi_qtm_get_state_all_remote_1_svc();
#define QCSAPI_QTM_SET_STATE_REMOTE 3671
extern  enum clnt_stat qcsapi_qtm_set_state_remote_1();
extern  bool_t qcsapi_qtm_set_state_remote_1_svc();
#define QCSAPI_QTM_GET_CONFIG_REMOTE 3681
extern  enum clnt_stat qcsapi_qtm_get_config_remote_1();
extern  bool_t qcsapi_qtm_get_config_remote_1_svc();
#define QCSAPI_QTM_GET_CONFIG_ALL_REMOTE 3691
extern  enum clnt_stat qcsapi_qtm_get_config_all_remote_1();
extern  bool_t qcsapi_qtm_get_config_all_remote_1_svc();
#define QCSAPI_QTM_SET_CONFIG_REMOTE 3701
extern  enum clnt_stat qcsapi_qtm_set_config_remote_1();
extern  bool_t qcsapi_qtm_set_config_remote_1_svc();
#define QCSAPI_QTM_ADD_RULE_REMOTE 3751
extern  enum clnt_stat qcsapi_qtm_add_rule_remote_1();
extern  bool_t qcsapi_qtm_add_rule_remote_1_svc();
#define QCSAPI_QTM_DEL_RULE_REMOTE 3761
extern  enum clnt_stat qcsapi_qtm_del_rule_remote_1();
extern  bool_t qcsapi_qtm_del_rule_remote_1_svc();
#define QCSAPI_QTM_DEL_RULE_INDEX_REMOTE 3771
extern  enum clnt_stat qcsapi_qtm_del_rule_index_remote_1();
extern  bool_t qcsapi_qtm_del_rule_index_remote_1_svc();
#define QCSAPI_QTM_GET_RULE_REMOTE 3781
extern  enum clnt_stat qcsapi_qtm_get_rule_remote_1();
extern  bool_t qcsapi_qtm_get_rule_remote_1_svc();
#define QCSAPI_QTM_GET_STRM_REMOTE 3791
extern  enum clnt_stat qcsapi_qtm_get_strm_remote_1();
extern  bool_t qcsapi_qtm_get_strm_remote_1_svc();
#define QCSAPI_QTM_GET_STATS_REMOTE 3801
extern  enum clnt_stat qcsapi_qtm_get_stats_remote_1();
extern  bool_t qcsapi_qtm_get_stats_remote_1_svc();
#define QCSAPI_QTM_GET_INACTIVE_FLAGS_REMOTE 3811
extern  enum clnt_stat qcsapi_qtm_get_inactive_flags_remote_1();
extern  bool_t qcsapi_qtm_get_inactive_flags_remote_1_svc();
#define QCSAPI_WIFI_RUN_SCRIPT_REMOTE 3821
extern  enum clnt_stat qcsapi_wifi_run_script_remote_1();
extern  bool_t qcsapi_wifi_run_script_remote_1_svc();
#define QCSAPI_WIFI_TEST_TRAFFIC_REMOTE 3831
extern  enum clnt_stat qcsapi_wifi_test_traffic_remote_1();
extern  bool_t qcsapi_wifi_test_traffic_remote_1_svc();
#define QCSAPI_WIFI_ADD_IPFF_REMOTE 3841
extern  enum clnt_stat qcsapi_wifi_add_ipff_remote_1();
extern  bool_t qcsapi_wifi_add_ipff_remote_1_svc();
#define QCSAPI_WIFI_DEL_IPFF_REMOTE 3851
extern  enum clnt_stat qcsapi_wifi_del_ipff_remote_1();
extern  bool_t qcsapi_wifi_del_ipff_remote_1_svc();
#define QCSAPI_WIFI_GET_IPFF_REMOTE 3861
extern  enum clnt_stat qcsapi_wifi_get_ipff_remote_1();
extern  bool_t qcsapi_wifi_get_ipff_remote_1_svc();
#define QCSAPI_WIFI_GET_RTS_THRESHOLD_REMOTE 3871
extern  enum clnt_stat qcsapi_wifi_get_rts_threshold_remote_1();
extern  bool_t qcsapi_wifi_get_rts_threshold_remote_1_svc();
#define QCSAPI_WIFI_SET_RTS_THRESHOLD_REMOTE 3881
extern  enum clnt_stat qcsapi_wifi_set_rts_threshold_remote_1();
extern  bool_t qcsapi_wifi_set_rts_threshold_remote_1_svc();
#define QCSAPI_WIFI_SET_NSS_CAP_REMOTE 4131
extern  enum clnt_stat qcsapi_wifi_set_nss_cap_remote_1();
extern  bool_t qcsapi_wifi_set_nss_cap_remote_1_svc();
#define QCSAPI_WIFI_GET_NSS_CAP_REMOTE 4141
extern  enum clnt_stat qcsapi_wifi_get_nss_cap_remote_1();
extern  bool_t qcsapi_wifi_get_nss_cap_remote_1_svc();
#define QCSAPI_WIFI_GET_TX_AMSDU_REMOTE 4171
extern  enum clnt_stat qcsapi_wifi_get_tx_amsdu_remote_1();
extern  bool_t qcsapi_wifi_get_tx_amsdu_remote_1_svc();
#define QCSAPI_WIFI_SET_TX_AMSDU_REMOTE 4181
extern  enum clnt_stat qcsapi_wifi_set_tx_amsdu_remote_1();
extern  bool_t qcsapi_wifi_set_tx_amsdu_remote_1_svc();
#define QCSAPI_WIFI_GET_DISASSOC_REASON_REMOTE 4271
extern  enum clnt_stat qcsapi_wifi_get_disassoc_reason_remote_1();
extern  bool_t qcsapi_wifi_get_disassoc_reason_remote_1_svc();
#define QCSAPI_WIFI_BLOCK_BSS_REMOTE 6201
extern  enum clnt_stat qcsapi_wifi_block_bss_remote_1();
extern  bool_t qcsapi_wifi_block_bss_remote_1_svc();
#define QCSAPI_WIFI_VERIFY_REPEATER_MODE_REMOTE 6171
extern  enum clnt_stat qcsapi_wifi_verify_repeater_mode_remote_1();
extern  bool_t qcsapi_wifi_verify_repeater_mode_remote_1_svc();
#define QCSAPI_WIFI_SET_AP_INTERFACE_NAME_REMOTE 6181
extern  enum clnt_stat qcsapi_wifi_set_ap_interface_name_remote_1();
extern  bool_t qcsapi_wifi_set_ap_interface_name_remote_1_svc();
#define QCSAPI_WIFI_GET_AP_INTERFACE_NAME_REMOTE 6191
extern  enum clnt_stat qcsapi_wifi_get_ap_interface_name_remote_1();
extern  bool_t qcsapi_wifi_get_ap_interface_name_remote_1_svc();
#define QCSAPI_GET_TEMPERATURE_INFO_REMOTE 3892
extern  enum clnt_stat qcsapi_get_temperature_info_remote_1();
extern  bool_t qcsapi_get_temperature_info_remote_1_svc();
#define QCSAPI_CALCMD_SET_TEST_MODE_REMOTE 3901
extern  enum clnt_stat qcsapi_calcmd_set_test_mode_remote_1();
extern  bool_t qcsapi_calcmd_set_test_mode_remote_1_svc();
#define QCSAPI_CALCMD_SHOW_TEST_PACKET_REMOTE 3911
extern  enum clnt_stat qcsapi_calcmd_show_test_packet_remote_1();
extern  bool_t qcsapi_calcmd_show_test_packet_remote_1_svc();
#define QCSAPI_CALCMD_SEND_TEST_PACKET_REMOTE 3921
extern  enum clnt_stat qcsapi_calcmd_send_test_packet_remote_1();
extern  bool_t qcsapi_calcmd_send_test_packet_remote_1_svc();
#define QCSAPI_CALCMD_STOP_TEST_PACKET_REMOTE 3931
extern  enum clnt_stat qcsapi_calcmd_stop_test_packet_remote_1();
extern  bool_t qcsapi_calcmd_stop_test_packet_remote_1_svc();
#define QCSAPI_CALCMD_SEND_DC_CW_SIGNAL_REMOTE 3941
extern  enum clnt_stat qcsapi_calcmd_send_dc_cw_signal_remote_1();
extern  bool_t qcsapi_calcmd_send_dc_cw_signal_remote_1_svc();
#define QCSAPI_CALCMD_STOP_DC_CW_SIGNAL_REMOTE 3951
extern  enum clnt_stat qcsapi_calcmd_stop_dc_cw_signal_remote_1();
extern  bool_t qcsapi_calcmd_stop_dc_cw_signal_remote_1_svc();
#define QCSAPI_CALCMD_GET_TEST_MODE_ANTENNA_SEL_REMOTE 3961
extern  enum clnt_stat qcsapi_calcmd_get_test_mode_antenna_sel_remote_1();
extern  bool_t qcsapi_calcmd_get_test_mode_antenna_sel_remote_1_svc();
#define QCSAPI_CALCMD_GET_TEST_MODE_MCS_REMOTE 3971
extern  enum clnt_stat qcsapi_calcmd_get_test_mode_mcs_remote_1();
extern  bool_t qcsapi_calcmd_get_test_mode_mcs_remote_1_svc();
#define QCSAPI_CALCMD_GET_TEST_MODE_BW_REMOTE 3981
extern  enum clnt_stat qcsapi_calcmd_get_test_mode_bw_remote_1();
extern  bool_t qcsapi_calcmd_get_test_mode_bw_remote_1_svc();
#define QCSAPI_CALCMD_GET_TX_POWER_REMOTE 3991
extern  enum clnt_stat qcsapi_calcmd_get_tx_power_remote_1();
extern  bool_t qcsapi_calcmd_get_tx_power_remote_1_svc();
#define QCSAPI_CALCMD_SET_TX_POWER_REMOTE 4001
extern  enum clnt_stat qcsapi_calcmd_set_tx_power_remote_1();
extern  bool_t qcsapi_calcmd_set_tx_power_remote_1_svc();
#define QCSAPI_CALCMD_GET_TEST_MODE_RSSI_REMOTE 4011
extern  enum clnt_stat qcsapi_calcmd_get_test_mode_rssi_remote_1();
extern  bool_t qcsapi_calcmd_get_test_mode_rssi_remote_1_svc();
#define QCSAPI_CALCMD_SET_MAC_FILTER_REMOTE 4021
extern  enum clnt_stat qcsapi_calcmd_set_mac_filter_remote_1();
extern  bool_t qcsapi_calcmd_set_mac_filter_remote_1_svc();
#define QCSAPI_CALCMD_GET_ANTENNA_COUNT_REMOTE 4031
extern  enum clnt_stat qcsapi_calcmd_get_antenna_count_remote_1();
extern  bool_t qcsapi_calcmd_get_antenna_count_remote_1_svc();
#define QCSAPI_CALCMD_CLEAR_COUNTER_REMOTE 4041
extern  enum clnt_stat qcsapi_calcmd_clear_counter_remote_1();
extern  bool_t qcsapi_calcmd_clear_counter_remote_1_svc();
#define QCSAPI_CALCMD_GET_INFO_REMOTE 4051
extern  enum clnt_stat qcsapi_calcmd_get_info_remote_1();
extern  bool_t qcsapi_calcmd_get_info_remote_1_svc();
#define QCSAPI_WOWLAN_SET_MATCH_TYPE_REMOTE 4161
extern  enum clnt_stat qcsapi_wowlan_set_match_type_remote_1();
extern  bool_t qcsapi_wowlan_set_match_type_remote_1_svc();
#define QCSAPI_WOWLAN_SET_L2_TYPE_REMOTE 4191
extern  enum clnt_stat qcsapi_wowlan_set_l2_type_remote_1();
extern  bool_t qcsapi_wowlan_set_l2_type_remote_1_svc();
#define QCSAPI_WOWLAN_SET_UDP_PORT_REMOTE 4201
extern  enum clnt_stat qcsapi_wowlan_set_udp_port_remote_1();
extern  bool_t qcsapi_wowlan_set_udp_port_remote_1_svc();
#define QCSAPI_WOWLAN_SET_MAGIC_PATTERN_REMOTE 4211
extern  enum clnt_stat qcsapi_wowlan_set_magic_pattern_remote_1();
extern  bool_t qcsapi_wowlan_set_magic_pattern_remote_1_svc();
#define QCSAPI_WIFI_WOWLAN_GET_HOST_STATE_REMOTE 4221
extern  enum clnt_stat qcsapi_wifi_wowlan_get_host_state_remote_1();
extern  bool_t qcsapi_wifi_wowlan_get_host_state_remote_1_svc();
#define QCSAPI_WIFI_WOWLAN_GET_MATCH_TYPE_REMOTE 4231
extern  enum clnt_stat qcsapi_wifi_wowlan_get_match_type_remote_1();
extern  bool_t qcsapi_wifi_wowlan_get_match_type_remote_1_svc();
#define QCSAPI_WIFI_WOWLAN_GET_L2_TYPE_REMOTE 4241
extern  enum clnt_stat qcsapi_wifi_wowlan_get_l2_type_remote_1();
extern  bool_t qcsapi_wifi_wowlan_get_l2_type_remote_1_svc();
#define QCSAPI_WIFI_WOWLAN_GET_UDP_PORT_REMOTE 4251
extern  enum clnt_stat qcsapi_wifi_wowlan_get_udp_port_remote_1();
extern  bool_t qcsapi_wifi_wowlan_get_udp_port_remote_1_svc();
#define QCSAPI_WIFI_WOWLAN_GET_MAGIC_PATTERN_REMOTE 4261
extern  enum clnt_stat qcsapi_wifi_wowlan_get_magic_pattern_remote_1();
extern  bool_t qcsapi_wifi_wowlan_get_magic_pattern_remote_1_svc();
#define QCSAPI_WIFI_SET_ENABLE_MU_REMOTE 5861
extern  enum clnt_stat qcsapi_wifi_set_enable_mu_remote_1();
extern  bool_t qcsapi_wifi_set_enable_mu_remote_1_svc();
#define QCSAPI_WIFI_GET_ENABLE_MU_REMOTE 5871
extern  enum clnt_stat qcsapi_wifi_get_enable_mu_remote_1();
extern  bool_t qcsapi_wifi_get_enable_mu_remote_1_svc();
#define QCSAPI_WIFI_SET_MU_USE_PRECODE_REMOTE 5881
extern  enum clnt_stat qcsapi_wifi_set_mu_use_precode_remote_1();
extern  bool_t qcsapi_wifi_set_mu_use_precode_remote_1_svc();
#define QCSAPI_WIFI_GET_MU_USE_PRECODE_REMOTE 5891
extern  enum clnt_stat qcsapi_wifi_get_mu_use_precode_remote_1();
extern  bool_t qcsapi_wifi_get_mu_use_precode_remote_1_svc();
#define QCSAPI_WIFI_SET_MU_USE_EQ_REMOTE 5901
extern  enum clnt_stat qcsapi_wifi_set_mu_use_eq_remote_1();
extern  bool_t qcsapi_wifi_set_mu_use_eq_remote_1_svc();
#define QCSAPI_WIFI_GET_MU_USE_EQ_REMOTE 5911
extern  enum clnt_stat qcsapi_wifi_get_mu_use_eq_remote_1();
extern  bool_t qcsapi_wifi_get_mu_use_eq_remote_1_svc();
#define QCSAPI_WIFI_GET_MU_GROUPS_REMOTE 5921
extern  enum clnt_stat qcsapi_wifi_get_mu_groups_remote_1();
extern  bool_t qcsapi_wifi_get_mu_groups_remote_1_svc();
#define QCSAPI_WIFI_ENABLE_TDLS_REMOTE 4111
extern  enum clnt_stat qcsapi_wifi_enable_tdls_remote_1();
extern  bool_t qcsapi_wifi_enable_tdls_remote_1_svc();
#define QCSAPI_WIFI_ENABLE_TDLS_OVER_QHOP_REMOTE 4381
extern  enum clnt_stat qcsapi_wifi_enable_tdls_over_qhop_remote_1();
extern  bool_t qcsapi_wifi_enable_tdls_over_qhop_remote_1_svc();
#define QCSAPI_WIFI_GET_TDLS_STATUS_REMOTE 4341
extern  enum clnt_stat qcsapi_wifi_get_tdls_status_remote_1();
extern  bool_t qcsapi_wifi_get_tdls_status_remote_1_svc();
#define QCSAPI_WIFI_SET_TDLS_PARAMS_REMOTE 4351
extern  enum clnt_stat qcsapi_wifi_set_tdls_params_remote_1();
extern  bool_t qcsapi_wifi_set_tdls_params_remote_1_svc();
#define QCSAPI_WIFI_GET_TDLS_PARAMS_REMOTE 4361
extern  enum clnt_stat qcsapi_wifi_get_tdls_params_remote_1();
extern  bool_t qcsapi_wifi_get_tdls_params_remote_1_svc();
#define QCSAPI_WIFI_TDLS_OPERATE_REMOTE 4371
extern  enum clnt_stat qcsapi_wifi_tdls_operate_remote_1();
extern  bool_t qcsapi_wifi_tdls_operate_remote_1_svc();
extern int qcsapi_prog_1_freeresult ();
#endif /* K&R C */

/* the xdr functions */

#if defined(__STDC__) || defined(__cplusplus)
extern  bool_t xdr_str (XDR *, str*);
extern  bool_t xdr___rpc_string (XDR *, __rpc_string*);
extern  bool_t xdr___rpc_string_p (XDR *, __rpc_string_p*);
extern  bool_t xdr___rpc_qcsapi_mac_addr (XDR *, __rpc_qcsapi_mac_addr*);
extern  bool_t xdr___rpc_qcsapi_mac_addr_p (XDR *, __rpc_qcsapi_mac_addr_p*);
extern  bool_t xdr___rpc_qcsapi_int_a32 (XDR *, __rpc_qcsapi_int_a32*);
extern  bool_t xdr___rpc_qcsapi_int_a32_p (XDR *, __rpc_qcsapi_int_a32_p*);
extern  bool_t xdr___rpc_qcsapi_SSID (XDR *, __rpc_qcsapi_SSID*);
extern  bool_t xdr___rpc_qcsapi_scs_ranking_rpt (XDR *, __rpc_qcsapi_scs_ranking_rpt*);
extern  bool_t xdr___rpc_qcsapi_scs_score_rpt (XDR *, __rpc_qcsapi_scs_score_rpt*);
extern  bool_t xdr___rpc_qcsapi_scs_currchan_rpt (XDR *, __rpc_qcsapi_scs_currchan_rpt*);
extern  bool_t xdr___rpc_qcsapi_autochan_rpt (XDR *, __rpc_qcsapi_autochan_rpt*);
extern  bool_t xdr___rpc_qcsapi_scs_param_rpt (XDR *, __rpc_qcsapi_scs_param_rpt*);
extern  bool_t xdr___rpc_qcsapi_data_512bytes (XDR *, __rpc_qcsapi_data_512bytes*);
extern  bool_t xdr___rpc_qcsapi_data_256bytes (XDR *, __rpc_qcsapi_data_256bytes*);
extern  bool_t xdr___rpc_qcsapi_disconn_info (XDR *, __rpc_qcsapi_disconn_info*);
extern  bool_t xdr___rpc_qcsapi_data_64bytes (XDR *, __rpc_qcsapi_data_64bytes*);
extern  bool_t xdr___rpc_qcsapi_channel_power_table (XDR *, __rpc_qcsapi_channel_power_table*);
extern  bool_t xdr___rpc_qcsapi_assoc_records (XDR *, __rpc_qcsapi_assoc_records*);
extern  bool_t xdr___rpc_ieee8011req_sta_tput_caps (XDR *, __rpc_ieee8011req_sta_tput_caps*);
extern  bool_t xdr___rpc_qcsapi_measure_report_result (XDR *, __rpc_qcsapi_measure_report_result*);
extern  bool_t xdr___rpc_qcsapi_node_stats (XDR *, __rpc_qcsapi_node_stats*);
extern  bool_t xdr___rpc_qcsapi_mlme_stats (XDR *, __rpc_qcsapi_mlme_stats*);
extern  bool_t xdr___rpc_qcsapi_mlme_stats_macs (XDR *, __rpc_qcsapi_mlme_stats_macs*);
extern  bool_t xdr___rpc_qcsapi_csw_record (XDR *, __rpc_qcsapi_csw_record*);
extern  bool_t xdr___rpc_qcsapi_radar_status (XDR *, __rpc_qcsapi_radar_status*);
extern  bool_t xdr___rpc_qcsapi_ap_properties (XDR *, __rpc_qcsapi_ap_properties*);
extern  bool_t xdr___rpc_qcsapi_interface_stats (XDR *, __rpc_qcsapi_interface_stats*);
extern  bool_t xdr___rpc_qcsapi_phy_stats (XDR *, __rpc_qcsapi_phy_stats*);
extern  bool_t xdr___rpc_early_flash_config (XDR *, __rpc_early_flash_config*);
extern  bool_t xdr___rpc_qcsapi_data_128bytes (XDR *, __rpc_qcsapi_data_128bytes*);
extern  bool_t xdr___rpc_qcsapi_data_1Kbytes (XDR *, __rpc_qcsapi_data_1Kbytes*);
extern  bool_t xdr___rpc_qcsapi_data_3Kbytes (XDR *, __rpc_qcsapi_data_3Kbytes*);
extern  bool_t xdr___rpc_qcsapi_data_4Kbytes (XDR *, __rpc_qcsapi_data_4Kbytes*);
extern  bool_t xdr___rpc_qcsapi_calcmd_tx_power_rsp (XDR *, __rpc_qcsapi_calcmd_tx_power_rsp*);
extern  bool_t xdr___rpc_qcsapi_calcmd_rssi_rsp (XDR *, __rpc_qcsapi_calcmd_rssi_rsp*);
extern  bool_t xdr_qcsapi_bootcfg_get_parameter_rpcdata (XDR *, qcsapi_bootcfg_get_parameter_rpcdata*);
extern  bool_t xdr_qcsapi_bootcfg_update_parameter_rpcdata (XDR *, qcsapi_bootcfg_update_parameter_rpcdata*);
extern  bool_t xdr_qcsapi_bootcfg_commit_rpcdata (XDR *, qcsapi_bootcfg_commit_rpcdata*);
extern  bool_t xdr_qcsapi_telnet_enable_rpcdata (XDR *, qcsapi_telnet_enable_rpcdata*);
extern  bool_t xdr_qcsapi_get_service_name_enum_rpcdata (XDR *, qcsapi_get_service_name_enum_rpcdata*);
extern  bool_t xdr_qcsapi_get_service_action_enum_rpcdata (XDR *, qcsapi_get_service_action_enum_rpcdata*);
extern  bool_t xdr_qcsapi_service_control_rpcdata (XDR *, qcsapi_service_control_rpcdata*);
extern  bool_t xdr_qcsapi_wfa_cert_mode_enable_rpcdata (XDR *, qcsapi_wfa_cert_mode_enable_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_scs_cce_channels_rpcdata (XDR *, qcsapi_wifi_get_scs_cce_channels_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_scs_enable_rpcdata (XDR *, qcsapi_wifi_scs_enable_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_scs_switch_channel_rpcdata (XDR *, qcsapi_wifi_scs_switch_channel_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_scs_verbose_rpcdata (XDR *, qcsapi_wifi_set_scs_verbose_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_scs_status_rpcdata (XDR *, qcsapi_wifi_get_scs_status_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_scs_smpl_enable_rpcdata (XDR *, qcsapi_wifi_set_scs_smpl_enable_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_scs_smpl_dwell_time_rpcdata (XDR *, qcsapi_wifi_set_scs_smpl_dwell_time_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_scs_sample_intv_rpcdata (XDR *, qcsapi_wifi_set_scs_sample_intv_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_scs_intf_detect_intv_rpcdata (XDR *, qcsapi_wifi_set_scs_intf_detect_intv_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_scs_thrshld_rpcdata (XDR *, qcsapi_wifi_set_scs_thrshld_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_scs_report_only_rpcdata (XDR *, qcsapi_wifi_set_scs_report_only_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_scs_stat_report_rpcdata (XDR *, qcsapi_wifi_get_scs_stat_report_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_scs_score_report_rpcdata (XDR *, qcsapi_wifi_get_scs_score_report_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_scs_currchan_report_rpcdata (XDR *, qcsapi_wifi_get_scs_currchan_report_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_scs_stats_rpcdata (XDR *, qcsapi_wifi_set_scs_stats_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_autochan_report_rpcdata (XDR *, qcsapi_wifi_get_autochan_report_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_scs_cca_intf_smth_fctr_rpcdata (XDR *, qcsapi_wifi_set_scs_cca_intf_smth_fctr_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_scs_chan_mtrc_mrgn_rpcdata (XDR *, qcsapi_wifi_set_scs_chan_mtrc_mrgn_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_scs_cca_intf_rpcdata (XDR *, qcsapi_wifi_get_scs_cca_intf_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_scs_param_report_rpcdata (XDR *, qcsapi_wifi_get_scs_param_report_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_scs_dfs_reentry_request_rpcdata (XDR *, qcsapi_wifi_get_scs_dfs_reentry_request_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_start_ocac_rpcdata (XDR *, qcsapi_wifi_start_ocac_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_stop_ocac_rpcdata (XDR *, qcsapi_wifi_stop_ocac_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_ocac_status_rpcdata (XDR *, qcsapi_wifi_get_ocac_status_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_ocac_dwell_time_rpcdata (XDR *, qcsapi_wifi_set_ocac_dwell_time_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_ocac_duration_rpcdata (XDR *, qcsapi_wifi_set_ocac_duration_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_ocac_cac_time_rpcdata (XDR *, qcsapi_wifi_set_ocac_cac_time_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_ocac_report_only_rpcdata (XDR *, qcsapi_wifi_set_ocac_report_only_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_ocac_thrshld_rpcdata (XDR *, qcsapi_wifi_set_ocac_thrshld_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_start_dfs_s_radio_rpcdata (XDR *, qcsapi_wifi_start_dfs_s_radio_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_stop_dfs_s_radio_rpcdata (XDR *, qcsapi_wifi_stop_dfs_s_radio_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_dfs_s_radio_status_rpcdata (XDR *, qcsapi_wifi_get_dfs_s_radio_status_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_dfs_s_radio_availability_rpcdata (XDR *, qcsapi_wifi_get_dfs_s_radio_availability_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_dfs_s_radio_dwell_time_rpcdata (XDR *, qcsapi_wifi_set_dfs_s_radio_dwell_time_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_dfs_s_radio_duration_rpcdata (XDR *, qcsapi_wifi_set_dfs_s_radio_duration_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_dfs_s_radio_wea_duration_rpcdata (XDR *, qcsapi_wifi_set_dfs_s_radio_wea_duration_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_dfs_s_radio_cac_time_rpcdata (XDR *, qcsapi_wifi_set_dfs_s_radio_cac_time_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_dfs_s_radio_wea_cac_time_rpcdata (XDR *, qcsapi_wifi_set_dfs_s_radio_wea_cac_time_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_dfs_s_radio_report_only_rpcdata (XDR *, qcsapi_wifi_set_dfs_s_radio_report_only_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_dfs_s_radio_thrshld_rpcdata (XDR *, qcsapi_wifi_set_dfs_s_radio_thrshld_rpcdata*);
extern  bool_t xdr_qcsapi_init_rpcdata (XDR *, qcsapi_init_rpcdata*);
extern  bool_t xdr_qcsapi_console_disconnect_rpcdata (XDR *, qcsapi_console_disconnect_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_startprod_rpcdata (XDR *, qcsapi_wifi_startprod_rpcdata*);
extern  bool_t xdr_qcsapi_is_startprod_done_rpcdata (XDR *, qcsapi_is_startprod_done_rpcdata*);
extern  bool_t xdr_qcsapi_system_get_time_since_start_rpcdata (XDR *, qcsapi_system_get_time_since_start_rpcdata*);
extern  bool_t xdr_qcsapi_get_system_status_rpcdata (XDR *, qcsapi_get_system_status_rpcdata*);
extern  bool_t xdr_qcsapi_get_random_seed_rpcdata (XDR *, qcsapi_get_random_seed_rpcdata*);
extern  bool_t xdr_qcsapi_set_random_seed_rpcdata (XDR *, qcsapi_set_random_seed_rpcdata*);
extern  bool_t xdr_qcsapi_get_carrier_id_rpcdata (XDR *, qcsapi_get_carrier_id_rpcdata*);
extern  bool_t xdr_qcsapi_set_carrier_id_rpcdata (XDR *, qcsapi_set_carrier_id_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_spinor_jedecid_rpcdata (XDR *, qcsapi_wifi_get_spinor_jedecid_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_bb_param_rpcdata (XDR *, qcsapi_wifi_get_bb_param_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_bb_param_rpcdata (XDR *, qcsapi_wifi_set_bb_param_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_optim_stats_rpcdata (XDR *, qcsapi_wifi_set_optim_stats_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_sys_time_rpcdata (XDR *, qcsapi_wifi_set_sys_time_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_sys_time_rpcdata (XDR *, qcsapi_wifi_get_sys_time_rpcdata*);
extern  bool_t xdr_qcsapi_set_soc_mac_addr_rpcdata (XDR *, qcsapi_set_soc_mac_addr_rpcdata*);
extern  bool_t xdr_qcsapi_get_custom_value_rpcdata (XDR *, qcsapi_get_custom_value_rpcdata*);
extern  bool_t xdr_qcsapi_config_get_parameter_rpcdata (XDR *, qcsapi_config_get_parameter_rpcdata*);
extern  bool_t xdr_qcsapi_config_update_parameter_rpcdata (XDR *, qcsapi_config_update_parameter_rpcdata*);
extern  bool_t xdr_qcsapi_config_get_ssid_parameter_rpcdata (XDR *, qcsapi_config_get_ssid_parameter_rpcdata*);
extern  bool_t xdr_qcsapi_config_update_ssid_parameter_rpcdata (XDR *, qcsapi_config_update_ssid_parameter_rpcdata*);
extern  bool_t xdr_qcsapi_file_path_get_config_rpcdata (XDR *, qcsapi_file_path_get_config_rpcdata*);
extern  bool_t xdr_qcsapi_file_path_set_config_rpcdata (XDR *, qcsapi_file_path_set_config_rpcdata*);
extern  bool_t xdr_qcsapi_restore_default_config_rpcdata (XDR *, qcsapi_restore_default_config_rpcdata*);
extern  bool_t xdr_qcsapi_store_ipaddr_rpcdata (XDR *, qcsapi_store_ipaddr_rpcdata*);
extern  bool_t xdr_qcsapi_interface_enable_rpcdata (XDR *, qcsapi_interface_enable_rpcdata*);
extern  bool_t xdr_qcsapi_interface_get_status_rpcdata (XDR *, qcsapi_interface_get_status_rpcdata*);
extern  bool_t xdr_qcsapi_interface_set_ip4_rpcdata (XDR *, qcsapi_interface_set_ip4_rpcdata*);
extern  bool_t xdr_qcsapi_interface_get_ip4_rpcdata (XDR *, qcsapi_interface_get_ip4_rpcdata*);
extern  bool_t xdr_qcsapi_interface_get_counter_rpcdata (XDR *, qcsapi_interface_get_counter_rpcdata*);
extern  bool_t xdr_qcsapi_interface_get_counter64_rpcdata (XDR *, qcsapi_interface_get_counter64_rpcdata*);
extern  bool_t xdr_qcsapi_interface_get_mac_addr_rpcdata (XDR *, qcsapi_interface_get_mac_addr_rpcdata*);
extern  bool_t xdr_qcsapi_interface_set_mac_addr_rpcdata (XDR *, qcsapi_interface_set_mac_addr_rpcdata*);
extern  bool_t xdr_qcsapi_pm_get_counter_rpcdata (XDR *, qcsapi_pm_get_counter_rpcdata*);
extern  bool_t xdr_qcsapi_set_aspm_l1_rpcdata (XDR *, qcsapi_set_aspm_l1_rpcdata*);
extern  bool_t xdr_qcsapi_set_l1_rpcdata (XDR *, qcsapi_set_l1_rpcdata*);
extern  bool_t xdr_qcsapi_pm_get_elapsed_time_rpcdata (XDR *, qcsapi_pm_get_elapsed_time_rpcdata*);
extern  bool_t xdr_qcsapi_eth_phy_power_control_rpcdata (XDR *, qcsapi_eth_phy_power_control_rpcdata*);
extern  bool_t xdr_qcsapi_get_emac_switch_rpcdata (XDR *, qcsapi_get_emac_switch_rpcdata*);
extern  bool_t xdr_qcsapi_set_emac_switch_rpcdata (XDR *, qcsapi_set_emac_switch_rpcdata*);
extern  bool_t xdr_qcsapi_eth_dscp_map_rpcdata (XDR *, qcsapi_eth_dscp_map_rpcdata*);
extern  bool_t xdr_qcsapi_get_eth_info_rpcdata (XDR *, qcsapi_get_eth_info_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_mode_rpcdata (XDR *, qcsapi_wifi_get_mode_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_mode_rpcdata (XDR *, qcsapi_wifi_set_mode_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_phy_mode_rpcdata (XDR *, qcsapi_wifi_get_phy_mode_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_phy_mode_rpcdata (XDR *, qcsapi_wifi_set_phy_mode_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_reload_in_mode_rpcdata (XDR *, qcsapi_wifi_reload_in_mode_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_rfenable_rpcdata (XDR *, qcsapi_wifi_rfenable_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_rfstatus_rpcdata (XDR *, qcsapi_wifi_rfstatus_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_bw_rpcdata (XDR *, qcsapi_wifi_get_bw_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_bw_rpcdata (XDR *, qcsapi_wifi_set_bw_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_vht_rpcdata (XDR *, qcsapi_wifi_set_vht_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_vht_rpcdata (XDR *, qcsapi_wifi_get_vht_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_channel_rpcdata (XDR *, qcsapi_wifi_get_channel_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_channel_rpcdata (XDR *, qcsapi_wifi_set_channel_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_chan_pri_inactive_rpcdata (XDR *, qcsapi_wifi_set_chan_pri_inactive_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_chan_control_rpcdata (XDR *, qcsapi_wifi_chan_control_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_chan_disabled_rpcdata (XDR *, qcsapi_wifi_get_chan_disabled_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_beacon_interval_rpcdata (XDR *, qcsapi_wifi_get_beacon_interval_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_beacon_interval_rpcdata (XDR *, qcsapi_wifi_set_beacon_interval_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_dtim_rpcdata (XDR *, qcsapi_wifi_get_dtim_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_dtim_rpcdata (XDR *, qcsapi_wifi_set_dtim_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_assoc_limit_rpcdata (XDR *, qcsapi_wifi_get_assoc_limit_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_bss_assoc_limit_rpcdata (XDR *, qcsapi_wifi_get_bss_assoc_limit_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_assoc_limit_rpcdata (XDR *, qcsapi_wifi_set_assoc_limit_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_bss_assoc_limit_rpcdata (XDR *, qcsapi_wifi_set_bss_assoc_limit_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_BSSID_rpcdata (XDR *, qcsapi_wifi_get_BSSID_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_config_BSSID_rpcdata (XDR *, qcsapi_wifi_get_config_BSSID_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_ssid_get_bssid_rpcdata (XDR *, qcsapi_wifi_ssid_get_bssid_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_ssid_set_bssid_rpcdata (XDR *, qcsapi_wifi_ssid_set_bssid_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_SSID_rpcdata (XDR *, qcsapi_wifi_get_SSID_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_SSID_rpcdata (XDR *, qcsapi_wifi_set_SSID_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_IEEE_802_11_standard_rpcdata (XDR *, qcsapi_wifi_get_IEEE_802_11_standard_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_list_channels_rpcdata (XDR *, qcsapi_wifi_get_list_channels_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_mode_switch_rpcdata (XDR *, qcsapi_wifi_get_mode_switch_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_disassociate_rpcdata (XDR *, qcsapi_wifi_disassociate_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_disassociate_sta_rpcdata (XDR *, qcsapi_wifi_disassociate_sta_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_reassociate_rpcdata (XDR *, qcsapi_wifi_reassociate_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_disconn_info_rpcdata (XDR *, qcsapi_wifi_get_disconn_info_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_disable_wps_rpcdata (XDR *, qcsapi_wifi_disable_wps_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_associate_rpcdata (XDR *, qcsapi_wifi_associate_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_start_cca_rpcdata (XDR *, qcsapi_wifi_start_cca_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_noise_rpcdata (XDR *, qcsapi_wifi_get_noise_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_rssi_by_chain_rpcdata (XDR *, qcsapi_wifi_get_rssi_by_chain_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_avg_snr_rpcdata (XDR *, qcsapi_wifi_get_avg_snr_rpcdata*);
extern  bool_t xdr_qcsapi_get_primary_interface_rpcdata (XDR *, qcsapi_get_primary_interface_rpcdata*);
extern  bool_t xdr_qcsapi_get_interface_by_index_rpcdata (XDR *, qcsapi_get_interface_by_index_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_wifi_macaddr_rpcdata (XDR *, qcsapi_wifi_set_wifi_macaddr_rpcdata*);
extern  bool_t xdr_qcsapi_interface_get_BSSID_rpcdata (XDR *, qcsapi_interface_get_BSSID_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_rates_rpcdata (XDR *, qcsapi_wifi_get_rates_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_rates_rpcdata (XDR *, qcsapi_wifi_set_rates_rpcdata*);
extern  bool_t xdr_qcsapi_get_max_bitrate_rpcdata (XDR *, qcsapi_get_max_bitrate_rpcdata*);
extern  bool_t xdr_qcsapi_set_max_bitrate_rpcdata (XDR *, qcsapi_set_max_bitrate_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_qos_get_param_rpcdata (XDR *, qcsapi_wifi_qos_get_param_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_qos_set_param_rpcdata (XDR *, qcsapi_wifi_qos_set_param_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_wmm_ac_map_rpcdata (XDR *, qcsapi_wifi_get_wmm_ac_map_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_wmm_ac_map_rpcdata (XDR *, qcsapi_wifi_set_wmm_ac_map_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_dscp_8021p_map_rpcdata (XDR *, qcsapi_wifi_get_dscp_8021p_map_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_dscp_ac_map_rpcdata (XDR *, qcsapi_wifi_get_dscp_ac_map_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_dscp_8021p_map_rpcdata (XDR *, qcsapi_wifi_set_dscp_8021p_map_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_dscp_ac_map_rpcdata (XDR *, qcsapi_wifi_set_dscp_ac_map_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_priority_rpcdata (XDR *, qcsapi_wifi_get_priority_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_priority_rpcdata (XDR *, qcsapi_wifi_set_priority_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_airfair_rpcdata (XDR *, qcsapi_wifi_get_airfair_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_airfair_rpcdata (XDR *, qcsapi_wifi_set_airfair_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_tx_power_rpcdata (XDR *, qcsapi_wifi_get_tx_power_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_tx_power_rpcdata (XDR *, qcsapi_wifi_set_tx_power_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_bw_power_rpcdata (XDR *, qcsapi_wifi_get_bw_power_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_bw_power_rpcdata (XDR *, qcsapi_wifi_set_bw_power_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_bf_power_rpcdata (XDR *, qcsapi_wifi_get_bf_power_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_bf_power_rpcdata (XDR *, qcsapi_wifi_set_bf_power_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_tx_power_ext_rpcdata (XDR *, qcsapi_wifi_get_tx_power_ext_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_tx_power_ext_rpcdata (XDR *, qcsapi_wifi_set_tx_power_ext_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_chan_power_table_rpcdata (XDR *, qcsapi_wifi_get_chan_power_table_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_chan_power_table_rpcdata (XDR *, qcsapi_wifi_set_chan_power_table_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_power_selection_rpcdata (XDR *, qcsapi_wifi_get_power_selection_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_power_selection_rpcdata (XDR *, qcsapi_wifi_set_power_selection_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_carrier_interference_rpcdata (XDR *, qcsapi_wifi_get_carrier_interference_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_congestion_index_rpcdata (XDR *, qcsapi_wifi_get_congestion_index_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_supported_tx_power_levels_rpcdata (XDR *, qcsapi_wifi_get_supported_tx_power_levels_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_current_tx_power_level_rpcdata (XDR *, qcsapi_wifi_get_current_tx_power_level_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_power_constraint_rpcdata (XDR *, qcsapi_wifi_set_power_constraint_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_power_constraint_rpcdata (XDR *, qcsapi_wifi_get_power_constraint_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_tpc_interval_rpcdata (XDR *, qcsapi_wifi_set_tpc_interval_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_tpc_interval_rpcdata (XDR *, qcsapi_wifi_get_tpc_interval_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_assoc_records_rpcdata (XDR *, qcsapi_wifi_get_assoc_records_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_ap_isolate_rpcdata (XDR *, qcsapi_wifi_get_ap_isolate_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_ap_isolate_rpcdata (XDR *, qcsapi_wifi_set_ap_isolate_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_intra_bss_isolate_rpcdata (XDR *, qcsapi_wifi_get_intra_bss_isolate_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_intra_bss_isolate_rpcdata (XDR *, qcsapi_wifi_set_intra_bss_isolate_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_bss_isolate_rpcdata (XDR *, qcsapi_wifi_get_bss_isolate_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_bss_isolate_rpcdata (XDR *, qcsapi_wifi_set_bss_isolate_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_disable_dfs_channels_rpcdata (XDR *, qcsapi_wifi_disable_dfs_channels_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_create_restricted_bss_rpcdata (XDR *, qcsapi_wifi_create_restricted_bss_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_create_bss_rpcdata (XDR *, qcsapi_wifi_create_bss_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_remove_bss_rpcdata (XDR *, qcsapi_wifi_remove_bss_rpcdata*);
extern  bool_t xdr_qcsapi_wds_add_peer_rpcdata (XDR *, qcsapi_wds_add_peer_rpcdata*);
extern  bool_t xdr_qcsapi_wds_add_peer_encrypt_rpcdata (XDR *, qcsapi_wds_add_peer_encrypt_rpcdata*);
extern  bool_t xdr_qcsapi_wds_remove_peer_rpcdata (XDR *, qcsapi_wds_remove_peer_rpcdata*);
extern  bool_t xdr_qcsapi_wds_get_peer_address_rpcdata (XDR *, qcsapi_wds_get_peer_address_rpcdata*);
extern  bool_t xdr_qcsapi_wds_set_psk_rpcdata (XDR *, qcsapi_wds_set_psk_rpcdata*);
extern  bool_t xdr_qcsapi_wds_set_mode_rpcdata (XDR *, qcsapi_wds_set_mode_rpcdata*);
extern  bool_t xdr_qcsapi_wds_get_mode_rpcdata (XDR *, qcsapi_wds_get_mode_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_extender_params_rpcdata (XDR *, qcsapi_wifi_set_extender_params_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_extender_params_rpcdata (XDR *, qcsapi_wifi_get_extender_params_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_beacon_type_rpcdata (XDR *, qcsapi_wifi_get_beacon_type_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_beacon_type_rpcdata (XDR *, qcsapi_wifi_set_beacon_type_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_WEP_key_index_rpcdata (XDR *, qcsapi_wifi_get_WEP_key_index_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_WEP_key_index_rpcdata (XDR *, qcsapi_wifi_set_WEP_key_index_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_WEP_key_passphrase_rpcdata (XDR *, qcsapi_wifi_get_WEP_key_passphrase_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_WEP_key_passphrase_rpcdata (XDR *, qcsapi_wifi_set_WEP_key_passphrase_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_WEP_encryption_level_rpcdata (XDR *, qcsapi_wifi_get_WEP_encryption_level_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_basic_encryption_modes_rpcdata (XDR *, qcsapi_wifi_get_basic_encryption_modes_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_basic_encryption_modes_rpcdata (XDR *, qcsapi_wifi_set_basic_encryption_modes_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_basic_authentication_mode_rpcdata (XDR *, qcsapi_wifi_get_basic_authentication_mode_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_basic_authentication_mode_rpcdata (XDR *, qcsapi_wifi_set_basic_authentication_mode_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_WEP_key_rpcdata (XDR *, qcsapi_wifi_get_WEP_key_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_WEP_key_rpcdata (XDR *, qcsapi_wifi_set_WEP_key_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_WPA_encryption_modes_rpcdata (XDR *, qcsapi_wifi_get_WPA_encryption_modes_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_WPA_encryption_modes_rpcdata (XDR *, qcsapi_wifi_set_WPA_encryption_modes_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_WPA_authentication_mode_rpcdata (XDR *, qcsapi_wifi_get_WPA_authentication_mode_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_WPA_authentication_mode_rpcdata (XDR *, qcsapi_wifi_set_WPA_authentication_mode_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_interworking_rpcdata (XDR *, qcsapi_wifi_get_interworking_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_interworking_rpcdata (XDR *, qcsapi_wifi_set_interworking_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_80211u_params_rpcdata (XDR *, qcsapi_wifi_get_80211u_params_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_80211u_params_rpcdata (XDR *, qcsapi_wifi_set_80211u_params_rpcdata*);
extern  bool_t xdr_qcsapi_security_get_nai_realms_rpcdata (XDR *, qcsapi_security_get_nai_realms_rpcdata*);
extern  bool_t xdr_qcsapi_security_add_nai_realm_rpcdata (XDR *, qcsapi_security_add_nai_realm_rpcdata*);
extern  bool_t xdr_qcsapi_security_del_nai_realm_rpcdata (XDR *, qcsapi_security_del_nai_realm_rpcdata*);
extern  bool_t xdr_qcsapi_security_get_roaming_consortium_rpcdata (XDR *, qcsapi_security_get_roaming_consortium_rpcdata*);
extern  bool_t xdr_qcsapi_security_add_roaming_consortium_rpcdata (XDR *, qcsapi_security_add_roaming_consortium_rpcdata*);
extern  bool_t xdr_qcsapi_security_del_roaming_consortium_rpcdata (XDR *, qcsapi_security_del_roaming_consortium_rpcdata*);
extern  bool_t xdr_qcsapi_security_get_venue_name_rpcdata (XDR *, qcsapi_security_get_venue_name_rpcdata*);
extern  bool_t xdr_qcsapi_security_add_venue_name_rpcdata (XDR *, qcsapi_security_add_venue_name_rpcdata*);
extern  bool_t xdr_qcsapi_security_del_venue_name_rpcdata (XDR *, qcsapi_security_del_venue_name_rpcdata*);
extern  bool_t xdr_qcsapi_security_get_oper_friendly_name_rpcdata (XDR *, qcsapi_security_get_oper_friendly_name_rpcdata*);
extern  bool_t xdr_qcsapi_security_add_oper_friendly_name_rpcdata (XDR *, qcsapi_security_add_oper_friendly_name_rpcdata*);
extern  bool_t xdr_qcsapi_security_del_oper_friendly_name_rpcdata (XDR *, qcsapi_security_del_oper_friendly_name_rpcdata*);
extern  bool_t xdr_qcsapi_security_get_hs20_conn_capab_rpcdata (XDR *, qcsapi_security_get_hs20_conn_capab_rpcdata*);
extern  bool_t xdr_qcsapi_security_add_hs20_conn_capab_rpcdata (XDR *, qcsapi_security_add_hs20_conn_capab_rpcdata*);
extern  bool_t xdr_qcsapi_security_del_hs20_conn_capab_rpcdata (XDR *, qcsapi_security_del_hs20_conn_capab_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_hs20_status_rpcdata (XDR *, qcsapi_wifi_get_hs20_status_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_hs20_status_rpcdata (XDR *, qcsapi_wifi_set_hs20_status_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_proxy_arp_rpcdata (XDR *, qcsapi_wifi_get_proxy_arp_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_proxy_arp_rpcdata (XDR *, qcsapi_wifi_set_proxy_arp_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_l2_ext_filter_rpcdata (XDR *, qcsapi_wifi_get_l2_ext_filter_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_l2_ext_filter_rpcdata (XDR *, qcsapi_wifi_set_l2_ext_filter_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_hs20_params_rpcdata (XDR *, qcsapi_wifi_get_hs20_params_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_hs20_params_rpcdata (XDR *, qcsapi_wifi_set_hs20_params_rpcdata*);
extern  bool_t xdr_qcsapi_remove_11u_param_rpcdata (XDR *, qcsapi_remove_11u_param_rpcdata*);
extern  bool_t xdr_qcsapi_remove_hs20_param_rpcdata (XDR *, qcsapi_remove_hs20_param_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_IEEE11i_encryption_modes_rpcdata (XDR *, qcsapi_wifi_get_IEEE11i_encryption_modes_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_IEEE11i_encryption_modes_rpcdata (XDR *, qcsapi_wifi_set_IEEE11i_encryption_modes_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_IEEE11i_authentication_mode_rpcdata (XDR *, qcsapi_wifi_get_IEEE11i_authentication_mode_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_IEEE11i_authentication_mode_rpcdata (XDR *, qcsapi_wifi_set_IEEE11i_authentication_mode_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_michael_errcnt_rpcdata (XDR *, qcsapi_wifi_get_michael_errcnt_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_pre_shared_key_rpcdata (XDR *, qcsapi_wifi_get_pre_shared_key_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_pre_shared_key_rpcdata (XDR *, qcsapi_wifi_set_pre_shared_key_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_add_radius_auth_server_cfg_rpcdata (XDR *, qcsapi_wifi_add_radius_auth_server_cfg_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_del_radius_auth_server_cfg_rpcdata (XDR *, qcsapi_wifi_del_radius_auth_server_cfg_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_radius_auth_server_cfg_rpcdata (XDR *, qcsapi_wifi_get_radius_auth_server_cfg_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_own_ip_addr_rpcdata (XDR *, qcsapi_wifi_set_own_ip_addr_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_key_passphrase_rpcdata (XDR *, qcsapi_wifi_get_key_passphrase_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_key_passphrase_rpcdata (XDR *, qcsapi_wifi_set_key_passphrase_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_group_key_interval_rpcdata (XDR *, qcsapi_wifi_get_group_key_interval_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_group_key_interval_rpcdata (XDR *, qcsapi_wifi_set_group_key_interval_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_pmf_rpcdata (XDR *, qcsapi_wifi_get_pmf_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_pmf_rpcdata (XDR *, qcsapi_wifi_set_pmf_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_wpa_status_rpcdata (XDR *, qcsapi_wifi_get_wpa_status_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_psk_auth_failures_rpcdata (XDR *, qcsapi_wifi_get_psk_auth_failures_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_auth_state_rpcdata (XDR *, qcsapi_wifi_get_auth_state_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_security_defer_mode_rpcdata (XDR *, qcsapi_wifi_set_security_defer_mode_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_security_defer_mode_rpcdata (XDR *, qcsapi_wifi_get_security_defer_mode_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_apply_security_config_rpcdata (XDR *, qcsapi_wifi_apply_security_config_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_mac_address_filtering_rpcdata (XDR *, qcsapi_wifi_set_mac_address_filtering_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_mac_address_filtering_rpcdata (XDR *, qcsapi_wifi_get_mac_address_filtering_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_authorize_mac_address_rpcdata (XDR *, qcsapi_wifi_authorize_mac_address_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_deny_mac_address_rpcdata (XDR *, qcsapi_wifi_deny_mac_address_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_remove_mac_address_rpcdata (XDR *, qcsapi_wifi_remove_mac_address_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_is_mac_address_authorized_rpcdata (XDR *, qcsapi_wifi_is_mac_address_authorized_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_authorized_mac_addresses_rpcdata (XDR *, qcsapi_wifi_get_authorized_mac_addresses_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_denied_mac_addresses_rpcdata (XDR *, qcsapi_wifi_get_denied_mac_addresses_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_accept_oui_filter_rpcdata (XDR *, qcsapi_wifi_set_accept_oui_filter_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_accept_oui_filter_rpcdata (XDR *, qcsapi_wifi_get_accept_oui_filter_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_clear_mac_address_filters_rpcdata (XDR *, qcsapi_wifi_clear_mac_address_filters_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_mac_address_reserve_rpcdata (XDR *, qcsapi_wifi_set_mac_address_reserve_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_mac_address_reserve_rpcdata (XDR *, qcsapi_wifi_get_mac_address_reserve_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_clear_mac_address_reserve_rpcdata (XDR *, qcsapi_wifi_clear_mac_address_reserve_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_option_rpcdata (XDR *, qcsapi_wifi_get_option_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_option_rpcdata (XDR *, qcsapi_wifi_set_option_rpcdata*);
extern  bool_t xdr_qcsapi_get_board_parameter_rpcdata (XDR *, qcsapi_get_board_parameter_rpcdata*);
extern  bool_t xdr_qcsapi_get_swfeat_list_rpcdata (XDR *, qcsapi_get_swfeat_list_rpcdata*);
extern  bool_t xdr_qcsapi_SSID_create_SSID_rpcdata (XDR *, qcsapi_SSID_create_SSID_rpcdata*);
extern  bool_t xdr_qcsapi_SSID_remove_SSID_rpcdata (XDR *, qcsapi_SSID_remove_SSID_rpcdata*);
extern  bool_t xdr_qcsapi_SSID_verify_SSID_rpcdata (XDR *, qcsapi_SSID_verify_SSID_rpcdata*);
extern  bool_t xdr_qcsapi_SSID_rename_SSID_rpcdata (XDR *, qcsapi_SSID_rename_SSID_rpcdata*);
extern  bool_t xdr_qcsapi_SSID_get_SSID_list_rpcdata (XDR *, qcsapi_SSID_get_SSID_list_rpcdata*);
extern  bool_t xdr_qcsapi_SSID_set_protocol_rpcdata (XDR *, qcsapi_SSID_set_protocol_rpcdata*);
extern  bool_t xdr_qcsapi_SSID_get_protocol_rpcdata (XDR *, qcsapi_SSID_get_protocol_rpcdata*);
extern  bool_t xdr_qcsapi_SSID_get_encryption_modes_rpcdata (XDR *, qcsapi_SSID_get_encryption_modes_rpcdata*);
extern  bool_t xdr_qcsapi_SSID_set_encryption_modes_rpcdata (XDR *, qcsapi_SSID_set_encryption_modes_rpcdata*);
extern  bool_t xdr_qcsapi_SSID_get_group_encryption_rpcdata (XDR *, qcsapi_SSID_get_group_encryption_rpcdata*);
extern  bool_t xdr_qcsapi_SSID_set_group_encryption_rpcdata (XDR *, qcsapi_SSID_set_group_encryption_rpcdata*);
extern  bool_t xdr_qcsapi_SSID_get_authentication_mode_rpcdata (XDR *, qcsapi_SSID_get_authentication_mode_rpcdata*);
extern  bool_t xdr_qcsapi_SSID_set_authentication_mode_rpcdata (XDR *, qcsapi_SSID_set_authentication_mode_rpcdata*);
extern  bool_t xdr_qcsapi_SSID_get_pre_shared_key_rpcdata (XDR *, qcsapi_SSID_get_pre_shared_key_rpcdata*);
extern  bool_t xdr_qcsapi_SSID_set_pre_shared_key_rpcdata (XDR *, qcsapi_SSID_set_pre_shared_key_rpcdata*);
extern  bool_t xdr_qcsapi_SSID_get_key_passphrase_rpcdata (XDR *, qcsapi_SSID_get_key_passphrase_rpcdata*);
extern  bool_t xdr_qcsapi_SSID_set_key_passphrase_rpcdata (XDR *, qcsapi_SSID_set_key_passphrase_rpcdata*);
extern  bool_t xdr_qcsapi_SSID_get_pmf_rpcdata (XDR *, qcsapi_SSID_get_pmf_rpcdata*);
extern  bool_t xdr_qcsapi_SSID_set_pmf_rpcdata (XDR *, qcsapi_SSID_set_pmf_rpcdata*);
extern  bool_t xdr_qcsapi_SSID_get_wps_SSID_rpcdata (XDR *, qcsapi_SSID_get_wps_SSID_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_vlan_config_rpcdata (XDR *, qcsapi_wifi_vlan_config_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_show_vlan_config_rpcdata (XDR *, qcsapi_wifi_show_vlan_config_rpcdata*);
extern  bool_t xdr_qcsapi_enable_vlan_pass_through_rpcdata (XDR *, qcsapi_enable_vlan_pass_through_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_vlan_promisc_rpcdata (XDR *, qcsapi_wifi_set_vlan_promisc_rpcdata*);
extern  bool_t xdr_qcsapi_wps_registrar_report_button_press_rpcdata (XDR *, qcsapi_wps_registrar_report_button_press_rpcdata*);
extern  bool_t xdr_qcsapi_wps_registrar_report_pin_rpcdata (XDR *, qcsapi_wps_registrar_report_pin_rpcdata*);
extern  bool_t xdr_qcsapi_wps_registrar_get_pp_devname_rpcdata (XDR *, qcsapi_wps_registrar_get_pp_devname_rpcdata*);
extern  bool_t xdr_qcsapi_wps_registrar_set_pp_devname_rpcdata (XDR *, qcsapi_wps_registrar_set_pp_devname_rpcdata*);
extern  bool_t xdr_qcsapi_wps_enrollee_report_button_press_rpcdata (XDR *, qcsapi_wps_enrollee_report_button_press_rpcdata*);
extern  bool_t xdr_qcsapi_wps_enrollee_report_pin_rpcdata (XDR *, qcsapi_wps_enrollee_report_pin_rpcdata*);
extern  bool_t xdr_qcsapi_wps_enrollee_generate_pin_rpcdata (XDR *, qcsapi_wps_enrollee_generate_pin_rpcdata*);
extern  bool_t xdr_qcsapi_wps_get_ap_pin_rpcdata (XDR *, qcsapi_wps_get_ap_pin_rpcdata*);
extern  bool_t xdr_qcsapi_wps_set_ap_pin_rpcdata (XDR *, qcsapi_wps_set_ap_pin_rpcdata*);
extern  bool_t xdr_qcsapi_wps_save_ap_pin_rpcdata (XDR *, qcsapi_wps_save_ap_pin_rpcdata*);
extern  bool_t xdr_qcsapi_wps_enable_ap_pin_rpcdata (XDR *, qcsapi_wps_enable_ap_pin_rpcdata*);
extern  bool_t xdr_qcsapi_wps_get_sta_pin_rpcdata (XDR *, qcsapi_wps_get_sta_pin_rpcdata*);
extern  bool_t xdr_qcsapi_wps_get_state_rpcdata (XDR *, qcsapi_wps_get_state_rpcdata*);
extern  bool_t xdr_qcsapi_wps_get_configured_state_rpcdata (XDR *, qcsapi_wps_get_configured_state_rpcdata*);
extern  bool_t xdr_qcsapi_wps_get_runtime_state_rpcdata (XDR *, qcsapi_wps_get_runtime_state_rpcdata*);
extern  bool_t xdr_qcsapi_wps_set_configured_state_rpcdata (XDR *, qcsapi_wps_set_configured_state_rpcdata*);
extern  bool_t xdr_qcsapi_wps_get_param_rpcdata (XDR *, qcsapi_wps_get_param_rpcdata*);
extern  bool_t xdr_qcsapi_wps_set_timeout_rpcdata (XDR *, qcsapi_wps_set_timeout_rpcdata*);
extern  bool_t xdr_qcsapi_wps_on_hidden_ssid_rpcdata (XDR *, qcsapi_wps_on_hidden_ssid_rpcdata*);
extern  bool_t xdr_qcsapi_wps_on_hidden_ssid_status_rpcdata (XDR *, qcsapi_wps_on_hidden_ssid_status_rpcdata*);
extern  bool_t xdr_qcsapi_wps_upnp_enable_rpcdata (XDR *, qcsapi_wps_upnp_enable_rpcdata*);
extern  bool_t xdr_qcsapi_wps_upnp_status_rpcdata (XDR *, qcsapi_wps_upnp_status_rpcdata*);
extern  bool_t xdr_qcsapi_wps_allow_pbc_overlap_rpcdata (XDR *, qcsapi_wps_allow_pbc_overlap_rpcdata*);
extern  bool_t xdr_qcsapi_wps_get_allow_pbc_overlap_status_rpcdata (XDR *, qcsapi_wps_get_allow_pbc_overlap_status_rpcdata*);
extern  bool_t xdr_qcsapi_wps_set_access_control_rpcdata (XDR *, qcsapi_wps_set_access_control_rpcdata*);
extern  bool_t xdr_qcsapi_wps_get_access_control_rpcdata (XDR *, qcsapi_wps_get_access_control_rpcdata*);
extern  bool_t xdr_qcsapi_wps_set_param_rpcdata (XDR *, qcsapi_wps_set_param_rpcdata*);
extern  bool_t xdr_qcsapi_wps_cancel_rpcdata (XDR *, qcsapi_wps_cancel_rpcdata*);
extern  bool_t xdr_qcsapi_wps_set_pbc_in_srcm_rpcdata (XDR *, qcsapi_wps_set_pbc_in_srcm_rpcdata*);
extern  bool_t xdr_qcsapi_wps_get_pbc_in_srcm_rpcdata (XDR *, qcsapi_wps_get_pbc_in_srcm_rpcdata*);
extern  bool_t xdr_qcsapi_registrar_set_default_pbc_bss_rpcdata (XDR *, qcsapi_registrar_set_default_pbc_bss_rpcdata*);
extern  bool_t xdr_qcsapi_registrar_get_default_pbc_bss_rpcdata (XDR *, qcsapi_registrar_get_default_pbc_bss_rpcdata*);
extern  bool_t xdr_qcsapi_gpio_set_config_rpcdata (XDR *, qcsapi_gpio_set_config_rpcdata*);
extern  bool_t xdr_qcsapi_gpio_get_config_rpcdata (XDR *, qcsapi_gpio_get_config_rpcdata*);
extern  bool_t xdr_qcsapi_led_get_rpcdata (XDR *, qcsapi_led_get_rpcdata*);
extern  bool_t xdr_qcsapi_led_set_rpcdata (XDR *, qcsapi_led_set_rpcdata*);
extern  bool_t xdr_qcsapi_led_pwm_enable_rpcdata (XDR *, qcsapi_led_pwm_enable_rpcdata*);
extern  bool_t xdr_qcsapi_led_brightness_rpcdata (XDR *, qcsapi_led_brightness_rpcdata*);
extern  bool_t xdr_qcsapi_gpio_enable_wps_push_button_rpcdata (XDR *, qcsapi_gpio_enable_wps_push_button_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_count_associations_rpcdata (XDR *, qcsapi_wifi_get_count_associations_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_associated_device_mac_addr_rpcdata (XDR *, qcsapi_wifi_get_associated_device_mac_addr_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_associated_device_ip_addr_rpcdata (XDR *, qcsapi_wifi_get_associated_device_ip_addr_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_link_quality_rpcdata (XDR *, qcsapi_wifi_get_link_quality_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_link_quality_max_rpcdata (XDR *, qcsapi_wifi_get_link_quality_max_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_rx_bytes_per_association_rpcdata (XDR *, qcsapi_wifi_get_rx_bytes_per_association_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_tx_bytes_per_association_rpcdata (XDR *, qcsapi_wifi_get_tx_bytes_per_association_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_rx_packets_per_association_rpcdata (XDR *, qcsapi_wifi_get_rx_packets_per_association_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_tx_packets_per_association_rpcdata (XDR *, qcsapi_wifi_get_tx_packets_per_association_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_tx_err_packets_per_association_rpcdata (XDR *, qcsapi_wifi_get_tx_err_packets_per_association_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_rssi_per_association_rpcdata (XDR *, qcsapi_wifi_get_rssi_per_association_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_rssi_in_dbm_per_association_rpcdata (XDR *, qcsapi_wifi_get_rssi_in_dbm_per_association_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_bw_per_association_rpcdata (XDR *, qcsapi_wifi_get_bw_per_association_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_tx_phy_rate_per_association_rpcdata (XDR *, qcsapi_wifi_get_tx_phy_rate_per_association_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_rx_phy_rate_per_association_rpcdata (XDR *, qcsapi_wifi_get_rx_phy_rate_per_association_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_tx_mcs_per_association_rpcdata (XDR *, qcsapi_wifi_get_tx_mcs_per_association_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_rx_mcs_per_association_rpcdata (XDR *, qcsapi_wifi_get_rx_mcs_per_association_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_achievable_tx_phy_rate_per_association_rpcdata (XDR *, qcsapi_wifi_get_achievable_tx_phy_rate_per_association_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_achievable_rx_phy_rate_per_association_rpcdata (XDR *, qcsapi_wifi_get_achievable_rx_phy_rate_per_association_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_auth_enc_per_association_rpcdata (XDR *, qcsapi_wifi_get_auth_enc_per_association_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_tput_caps_rpcdata (XDR *, qcsapi_wifi_get_tput_caps_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_connection_mode_rpcdata (XDR *, qcsapi_wifi_get_connection_mode_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_vendor_per_association_rpcdata (XDR *, qcsapi_wifi_get_vendor_per_association_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_max_mimo_rpcdata (XDR *, qcsapi_wifi_get_max_mimo_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_snr_per_association_rpcdata (XDR *, qcsapi_wifi_get_snr_per_association_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_time_associated_per_association_rpcdata (XDR *, qcsapi_wifi_get_time_associated_per_association_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_node_param_rpcdata (XDR *, qcsapi_wifi_get_node_param_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_node_counter_rpcdata (XDR *, qcsapi_wifi_get_node_counter_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_node_stats_rpcdata (XDR *, qcsapi_wifi_get_node_stats_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_max_queued_rpcdata (XDR *, qcsapi_wifi_get_max_queued_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_hw_noise_per_association_rpcdata (XDR *, qcsapi_wifi_get_hw_noise_per_association_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_mlme_stats_per_mac_rpcdata (XDR *, qcsapi_wifi_get_mlme_stats_per_mac_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_mlme_stats_per_association_rpcdata (XDR *, qcsapi_wifi_get_mlme_stats_per_association_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_mlme_stats_macs_list_rpcdata (XDR *, qcsapi_wifi_get_mlme_stats_macs_list_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_list_regulatory_regions_rpcdata (XDR *, qcsapi_wifi_get_list_regulatory_regions_rpcdata*);
extern  bool_t xdr_qcsapi_regulatory_get_list_regulatory_regions_rpcdata (XDR *, qcsapi_regulatory_get_list_regulatory_regions_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_list_regulatory_channels_rpcdata (XDR *, qcsapi_wifi_get_list_regulatory_channels_rpcdata*);
extern  bool_t xdr_qcsapi_regulatory_get_list_regulatory_channels_rpcdata (XDR *, qcsapi_regulatory_get_list_regulatory_channels_rpcdata*);
extern  bool_t xdr_qcsapi_regulatory_get_list_regulatory_bands_rpcdata (XDR *, qcsapi_regulatory_get_list_regulatory_bands_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_regulatory_tx_power_rpcdata (XDR *, qcsapi_wifi_get_regulatory_tx_power_rpcdata*);
extern  bool_t xdr_qcsapi_regulatory_get_regulatory_tx_power_rpcdata (XDR *, qcsapi_regulatory_get_regulatory_tx_power_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_configured_tx_power_rpcdata (XDR *, qcsapi_wifi_get_configured_tx_power_rpcdata*);
extern  bool_t xdr_qcsapi_regulatory_get_configured_tx_power_rpcdata (XDR *, qcsapi_regulatory_get_configured_tx_power_rpcdata*);
extern  bool_t xdr_qcsapi_regulatory_get_configured_tx_power_ext_rpcdata (XDR *, qcsapi_regulatory_get_configured_tx_power_ext_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_regulatory_region_rpcdata (XDR *, qcsapi_wifi_set_regulatory_region_rpcdata*);
extern  bool_t xdr_qcsapi_regulatory_set_regulatory_region_rpcdata (XDR *, qcsapi_regulatory_set_regulatory_region_rpcdata*);
extern  bool_t xdr_qcsapi_regulatory_restore_regulatory_tx_power_rpcdata (XDR *, qcsapi_regulatory_restore_regulatory_tx_power_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_regulatory_region_rpcdata (XDR *, qcsapi_wifi_get_regulatory_region_rpcdata*);
extern  bool_t xdr_qcsapi_regulatory_overwrite_country_code_rpcdata (XDR *, qcsapi_regulatory_overwrite_country_code_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_regulatory_channel_rpcdata (XDR *, qcsapi_wifi_set_regulatory_channel_rpcdata*);
extern  bool_t xdr_qcsapi_regulatory_set_regulatory_channel_rpcdata (XDR *, qcsapi_regulatory_set_regulatory_channel_rpcdata*);
extern  bool_t xdr_qcsapi_regulatory_get_db_version_rpcdata (XDR *, qcsapi_regulatory_get_db_version_rpcdata*);
extern  bool_t xdr_qcsapi_regulatory_apply_tx_power_cap_rpcdata (XDR *, qcsapi_regulatory_apply_tx_power_cap_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_list_DFS_channels_rpcdata (XDR *, qcsapi_wifi_get_list_DFS_channels_rpcdata*);
extern  bool_t xdr_qcsapi_regulatory_get_list_DFS_channels_rpcdata (XDR *, qcsapi_regulatory_get_list_DFS_channels_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_is_channel_DFS_rpcdata (XDR *, qcsapi_wifi_is_channel_DFS_rpcdata*);
extern  bool_t xdr_qcsapi_regulatory_is_channel_DFS_rpcdata (XDR *, qcsapi_regulatory_is_channel_DFS_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_dfs_cce_channels_rpcdata (XDR *, qcsapi_wifi_get_dfs_cce_channels_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_DFS_alt_channel_rpcdata (XDR *, qcsapi_wifi_get_DFS_alt_channel_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_DFS_alt_channel_rpcdata (XDR *, qcsapi_wifi_set_DFS_alt_channel_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_start_dfs_reentry_rpcdata (XDR *, qcsapi_wifi_start_dfs_reentry_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_start_scan_ext_rpcdata (XDR *, qcsapi_wifi_start_scan_ext_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_csw_records_rpcdata (XDR *, qcsapi_wifi_get_csw_records_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_radar_status_rpcdata (XDR *, qcsapi_wifi_get_radar_status_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_cac_status_rpcdata (XDR *, qcsapi_wifi_get_cac_status_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_results_AP_scan_rpcdata (XDR *, qcsapi_wifi_get_results_AP_scan_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_count_APs_scanned_rpcdata (XDR *, qcsapi_wifi_get_count_APs_scanned_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_properties_AP_rpcdata (XDR *, qcsapi_wifi_get_properties_AP_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_scan_chk_inv_rpcdata (XDR *, qcsapi_wifi_set_scan_chk_inv_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_scan_chk_inv_rpcdata (XDR *, qcsapi_wifi_get_scan_chk_inv_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_scan_buf_max_size_rpcdata (XDR *, qcsapi_wifi_set_scan_buf_max_size_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_scan_buf_max_size_rpcdata (XDR *, qcsapi_wifi_get_scan_buf_max_size_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_scan_table_max_len_rpcdata (XDR *, qcsapi_wifi_set_scan_table_max_len_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_scan_table_max_len_rpcdata (XDR *, qcsapi_wifi_get_scan_table_max_len_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_dwell_times_rpcdata (XDR *, qcsapi_wifi_set_dwell_times_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_dwell_times_rpcdata (XDR *, qcsapi_wifi_get_dwell_times_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_bgscan_dwell_times_rpcdata (XDR *, qcsapi_wifi_set_bgscan_dwell_times_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_bgscan_dwell_times_rpcdata (XDR *, qcsapi_wifi_get_bgscan_dwell_times_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_start_scan_rpcdata (XDR *, qcsapi_wifi_start_scan_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_cancel_scan_rpcdata (XDR *, qcsapi_wifi_cancel_scan_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_scan_status_rpcdata (XDR *, qcsapi_wifi_get_scan_status_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_enable_bgscan_rpcdata (XDR *, qcsapi_wifi_enable_bgscan_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_bgscan_status_rpcdata (XDR *, qcsapi_wifi_get_bgscan_status_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_wait_scan_completes_rpcdata (XDR *, qcsapi_wifi_wait_scan_completes_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_backoff_fail_max_rpcdata (XDR *, qcsapi_wifi_backoff_fail_max_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_backoff_timeout_rpcdata (XDR *, qcsapi_wifi_backoff_timeout_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_mcs_rate_rpcdata (XDR *, qcsapi_wifi_get_mcs_rate_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_mcs_rate_rpcdata (XDR *, qcsapi_wifi_set_mcs_rate_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_pairing_id_rpcdata (XDR *, qcsapi_wifi_set_pairing_id_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_pairing_id_rpcdata (XDR *, qcsapi_wifi_get_pairing_id_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_pairing_enable_rpcdata (XDR *, qcsapi_wifi_set_pairing_enable_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_pairing_enable_rpcdata (XDR *, qcsapi_wifi_get_pairing_enable_rpcdata*);
extern  bool_t xdr_qcsapi_non_wps_set_pp_enable_rpcdata (XDR *, qcsapi_non_wps_set_pp_enable_rpcdata*);
extern  bool_t xdr_qcsapi_non_wps_get_pp_enable_rpcdata (XDR *, qcsapi_non_wps_get_pp_enable_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_vendor_fix_rpcdata (XDR *, qcsapi_wifi_set_vendor_fix_rpcdata*);
extern  bool_t xdr_qcsapi_errno_get_message_rpcdata (XDR *, qcsapi_errno_get_message_rpcdata*);
extern  bool_t xdr_qcsapi_get_interface_stats_rpcdata (XDR *, qcsapi_get_interface_stats_rpcdata*);
extern  bool_t xdr_qcsapi_get_phy_stats_rpcdata (XDR *, qcsapi_get_phy_stats_rpcdata*);
extern  bool_t xdr_qcsapi_reset_all_counters_rpcdata (XDR *, qcsapi_reset_all_counters_rpcdata*);
extern  bool_t xdr_qcsapi_get_uboot_info_rpcdata (XDR *, qcsapi_get_uboot_info_rpcdata*);
extern  bool_t xdr_qcsapi_firmware_get_version_rpcdata (XDR *, qcsapi_firmware_get_version_rpcdata*);
extern  bool_t xdr_qcsapi_flash_image_update_rpcdata (XDR *, qcsapi_flash_image_update_rpcdata*);
extern  bool_t xdr_qcsapi_send_file_rpcdata (XDR *, qcsapi_send_file_rpcdata*);
extern  bool_t xdr_qcsapi_pm_set_mode_rpcdata (XDR *, qcsapi_pm_set_mode_rpcdata*);
extern  bool_t xdr_qcsapi_pm_get_mode_rpcdata (XDR *, qcsapi_pm_get_mode_rpcdata*);
extern  bool_t xdr_qcsapi_get_qpm_level_rpcdata (XDR *, qcsapi_get_qpm_level_rpcdata*);
extern  bool_t xdr_qcsapi_set_host_state_rpcdata (XDR *, qcsapi_set_host_state_rpcdata*);
extern  bool_t xdr_qcsapi_qtm_get_state_rpcdata (XDR *, qcsapi_qtm_get_state_rpcdata*);
extern  bool_t xdr_qcsapi_qtm_get_state_all_rpcdata (XDR *, qcsapi_qtm_get_state_all_rpcdata*);
extern  bool_t xdr_qcsapi_qtm_set_state_rpcdata (XDR *, qcsapi_qtm_set_state_rpcdata*);
extern  bool_t xdr_qcsapi_qtm_get_config_rpcdata (XDR *, qcsapi_qtm_get_config_rpcdata*);
extern  bool_t xdr_qcsapi_qtm_get_config_all_rpcdata (XDR *, qcsapi_qtm_get_config_all_rpcdata*);
extern  bool_t xdr_qcsapi_qtm_set_config_rpcdata (XDR *, qcsapi_qtm_set_config_rpcdata*);
extern  bool_t xdr_qcsapi_qtm_add_rule_rpcdata (XDR *, qcsapi_qtm_add_rule_rpcdata*);
extern  bool_t xdr_qcsapi_qtm_del_rule_rpcdata (XDR *, qcsapi_qtm_del_rule_rpcdata*);
extern  bool_t xdr_qcsapi_qtm_del_rule_index_rpcdata (XDR *, qcsapi_qtm_del_rule_index_rpcdata*);
extern  bool_t xdr_qcsapi_qtm_get_rule_rpcdata (XDR *, qcsapi_qtm_get_rule_rpcdata*);
extern  bool_t xdr_qcsapi_qtm_get_strm_rpcdata (XDR *, qcsapi_qtm_get_strm_rpcdata*);
extern  bool_t xdr_qcsapi_qtm_get_stats_rpcdata (XDR *, qcsapi_qtm_get_stats_rpcdata*);
extern  bool_t xdr_qcsapi_qtm_get_inactive_flags_rpcdata (XDR *, qcsapi_qtm_get_inactive_flags_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_run_script_rpcdata (XDR *, qcsapi_wifi_run_script_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_test_traffic_rpcdata (XDR *, qcsapi_wifi_test_traffic_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_add_ipff_rpcdata (XDR *, qcsapi_wifi_add_ipff_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_del_ipff_rpcdata (XDR *, qcsapi_wifi_del_ipff_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_ipff_rpcdata (XDR *, qcsapi_wifi_get_ipff_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_rts_threshold_rpcdata (XDR *, qcsapi_wifi_get_rts_threshold_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_rts_threshold_rpcdata (XDR *, qcsapi_wifi_set_rts_threshold_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_nss_cap_rpcdata (XDR *, qcsapi_wifi_set_nss_cap_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_nss_cap_rpcdata (XDR *, qcsapi_wifi_get_nss_cap_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_tx_amsdu_rpcdata (XDR *, qcsapi_wifi_get_tx_amsdu_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_tx_amsdu_rpcdata (XDR *, qcsapi_wifi_set_tx_amsdu_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_disassoc_reason_rpcdata (XDR *, qcsapi_wifi_get_disassoc_reason_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_block_bss_rpcdata (XDR *, qcsapi_wifi_block_bss_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_verify_repeater_mode_rpcdata (XDR *, qcsapi_wifi_verify_repeater_mode_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_ap_interface_name_rpcdata (XDR *, qcsapi_wifi_set_ap_interface_name_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_ap_interface_name_rpcdata (XDR *, qcsapi_wifi_get_ap_interface_name_rpcdata*);
extern  bool_t xdr_qcsapi_get_temperature_info_rpcdata (XDR *, qcsapi_get_temperature_info_rpcdata*);
extern  bool_t xdr_qcsapi_calcmd_set_test_mode_rpcdata (XDR *, qcsapi_calcmd_set_test_mode_rpcdata*);
extern  bool_t xdr_qcsapi_calcmd_show_test_packet_rpcdata (XDR *, qcsapi_calcmd_show_test_packet_rpcdata*);
extern  bool_t xdr_qcsapi_calcmd_send_test_packet_rpcdata (XDR *, qcsapi_calcmd_send_test_packet_rpcdata*);
extern  bool_t xdr_qcsapi_calcmd_stop_test_packet_rpcdata (XDR *, qcsapi_calcmd_stop_test_packet_rpcdata*);
extern  bool_t xdr_qcsapi_calcmd_send_dc_cw_signal_rpcdata (XDR *, qcsapi_calcmd_send_dc_cw_signal_rpcdata*);
extern  bool_t xdr_qcsapi_calcmd_stop_dc_cw_signal_rpcdata (XDR *, qcsapi_calcmd_stop_dc_cw_signal_rpcdata*);
extern  bool_t xdr_qcsapi_calcmd_get_test_mode_antenna_sel_rpcdata (XDR *, qcsapi_calcmd_get_test_mode_antenna_sel_rpcdata*);
extern  bool_t xdr_qcsapi_calcmd_get_test_mode_mcs_rpcdata (XDR *, qcsapi_calcmd_get_test_mode_mcs_rpcdata*);
extern  bool_t xdr_qcsapi_calcmd_get_test_mode_bw_rpcdata (XDR *, qcsapi_calcmd_get_test_mode_bw_rpcdata*);
extern  bool_t xdr_qcsapi_calcmd_get_tx_power_rpcdata (XDR *, qcsapi_calcmd_get_tx_power_rpcdata*);
extern  bool_t xdr_qcsapi_calcmd_set_tx_power_rpcdata (XDR *, qcsapi_calcmd_set_tx_power_rpcdata*);
extern  bool_t xdr_qcsapi_calcmd_get_test_mode_rssi_rpcdata (XDR *, qcsapi_calcmd_get_test_mode_rssi_rpcdata*);
extern  bool_t xdr_qcsapi_calcmd_set_mac_filter_rpcdata (XDR *, qcsapi_calcmd_set_mac_filter_rpcdata*);
extern  bool_t xdr_qcsapi_calcmd_get_antenna_count_rpcdata (XDR *, qcsapi_calcmd_get_antenna_count_rpcdata*);
extern  bool_t xdr_qcsapi_calcmd_clear_counter_rpcdata (XDR *, qcsapi_calcmd_clear_counter_rpcdata*);
extern  bool_t xdr_qcsapi_calcmd_get_info_rpcdata (XDR *, qcsapi_calcmd_get_info_rpcdata*);
extern  bool_t xdr_qcsapi_wowlan_set_match_type_rpcdata (XDR *, qcsapi_wowlan_set_match_type_rpcdata*);
extern  bool_t xdr_qcsapi_wowlan_set_L2_type_rpcdata (XDR *, qcsapi_wowlan_set_L2_type_rpcdata*);
extern  bool_t xdr_qcsapi_wowlan_set_udp_port_rpcdata (XDR *, qcsapi_wowlan_set_udp_port_rpcdata*);
extern  bool_t xdr_qcsapi_wowlan_set_magic_pattern_rpcdata (XDR *, qcsapi_wowlan_set_magic_pattern_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_wowlan_get_host_state_rpcdata (XDR *, qcsapi_wifi_wowlan_get_host_state_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_wowlan_get_match_type_rpcdata (XDR *, qcsapi_wifi_wowlan_get_match_type_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_wowlan_get_l2_type_rpcdata (XDR *, qcsapi_wifi_wowlan_get_l2_type_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_wowlan_get_udp_port_rpcdata (XDR *, qcsapi_wifi_wowlan_get_udp_port_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_wowlan_get_magic_pattern_rpcdata (XDR *, qcsapi_wifi_wowlan_get_magic_pattern_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_enable_mu_rpcdata (XDR *, qcsapi_wifi_set_enable_mu_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_enable_mu_rpcdata (XDR *, qcsapi_wifi_get_enable_mu_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_mu_use_precode_rpcdata (XDR *, qcsapi_wifi_set_mu_use_precode_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_mu_use_precode_rpcdata (XDR *, qcsapi_wifi_get_mu_use_precode_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_mu_use_eq_rpcdata (XDR *, qcsapi_wifi_set_mu_use_eq_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_mu_use_eq_rpcdata (XDR *, qcsapi_wifi_get_mu_use_eq_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_mu_groups_rpcdata (XDR *, qcsapi_wifi_get_mu_groups_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_enable_tdls_rpcdata (XDR *, qcsapi_wifi_enable_tdls_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_enable_tdls_over_qhop_rpcdata (XDR *, qcsapi_wifi_enable_tdls_over_qhop_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_tdls_status_rpcdata (XDR *, qcsapi_wifi_get_tdls_status_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_set_tdls_params_rpcdata (XDR *, qcsapi_wifi_set_tdls_params_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_get_tdls_params_rpcdata (XDR *, qcsapi_wifi_get_tdls_params_rpcdata*);
extern  bool_t xdr_qcsapi_wifi_tdls_operate_rpcdata (XDR *, qcsapi_wifi_tdls_operate_rpcdata*);

#else /* K&R C */
extern bool_t xdr_str ();
extern bool_t xdr___rpc_string ();
extern bool_t xdr___rpc_string_p ();
extern bool_t xdr___rpc_qcsapi_mac_addr ();
extern bool_t xdr___rpc_qcsapi_mac_addr_p ();
extern bool_t xdr___rpc_qcsapi_int_a32 ();
extern bool_t xdr___rpc_qcsapi_int_a32_p ();
extern bool_t xdr___rpc_qcsapi_SSID ();
extern bool_t xdr___rpc_qcsapi_scs_ranking_rpt ();
extern bool_t xdr___rpc_qcsapi_scs_score_rpt ();
extern bool_t xdr___rpc_qcsapi_scs_currchan_rpt ();
extern bool_t xdr___rpc_qcsapi_autochan_rpt ();
extern bool_t xdr___rpc_qcsapi_scs_param_rpt ();
extern bool_t xdr___rpc_qcsapi_data_512bytes ();
extern bool_t xdr___rpc_qcsapi_data_256bytes ();
extern bool_t xdr___rpc_qcsapi_disconn_info ();
extern bool_t xdr___rpc_qcsapi_data_64bytes ();
extern bool_t xdr___rpc_qcsapi_channel_power_table ();
extern bool_t xdr___rpc_qcsapi_assoc_records ();
extern bool_t xdr___rpc_ieee8011req_sta_tput_caps ();
extern bool_t xdr___rpc_qcsapi_measure_report_result ();
extern bool_t xdr___rpc_qcsapi_node_stats ();
extern bool_t xdr___rpc_qcsapi_mlme_stats ();
extern bool_t xdr___rpc_qcsapi_mlme_stats_macs ();
extern bool_t xdr___rpc_qcsapi_csw_record ();
extern bool_t xdr___rpc_qcsapi_radar_status ();
extern bool_t xdr___rpc_qcsapi_ap_properties ();
extern bool_t xdr___rpc_qcsapi_interface_stats ();
extern bool_t xdr___rpc_qcsapi_phy_stats ();
extern bool_t xdr___rpc_early_flash_config ();
extern bool_t xdr___rpc_qcsapi_data_128bytes ();
extern bool_t xdr___rpc_qcsapi_data_1Kbytes ();
extern bool_t xdr___rpc_qcsapi_data_3Kbytes ();
extern bool_t xdr___rpc_qcsapi_data_4Kbytes ();
extern bool_t xdr___rpc_qcsapi_calcmd_tx_power_rsp ();
extern bool_t xdr___rpc_qcsapi_calcmd_rssi_rsp ();
extern bool_t xdr_qcsapi_bootcfg_get_parameter_rpcdata ();
extern bool_t xdr_qcsapi_bootcfg_update_parameter_rpcdata ();
extern bool_t xdr_qcsapi_bootcfg_commit_rpcdata ();
extern bool_t xdr_qcsapi_telnet_enable_rpcdata ();
extern bool_t xdr_qcsapi_get_service_name_enum_rpcdata ();
extern bool_t xdr_qcsapi_get_service_action_enum_rpcdata ();
extern bool_t xdr_qcsapi_service_control_rpcdata ();
extern bool_t xdr_qcsapi_wfa_cert_mode_enable_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_scs_cce_channels_rpcdata ();
extern bool_t xdr_qcsapi_wifi_scs_enable_rpcdata ();
extern bool_t xdr_qcsapi_wifi_scs_switch_channel_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_scs_verbose_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_scs_status_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_scs_smpl_enable_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_scs_smpl_dwell_time_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_scs_sample_intv_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_scs_intf_detect_intv_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_scs_thrshld_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_scs_report_only_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_scs_stat_report_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_scs_score_report_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_scs_currchan_report_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_scs_stats_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_autochan_report_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_scs_cca_intf_smth_fctr_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_scs_chan_mtrc_mrgn_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_scs_cca_intf_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_scs_param_report_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_scs_dfs_reentry_request_rpcdata ();
extern bool_t xdr_qcsapi_wifi_start_ocac_rpcdata ();
extern bool_t xdr_qcsapi_wifi_stop_ocac_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_ocac_status_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_ocac_dwell_time_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_ocac_duration_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_ocac_cac_time_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_ocac_report_only_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_ocac_thrshld_rpcdata ();
extern bool_t xdr_qcsapi_wifi_start_dfs_s_radio_rpcdata ();
extern bool_t xdr_qcsapi_wifi_stop_dfs_s_radio_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_dfs_s_radio_status_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_dfs_s_radio_availability_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_dfs_s_radio_dwell_time_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_dfs_s_radio_duration_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_dfs_s_radio_wea_duration_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_dfs_s_radio_cac_time_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_dfs_s_radio_wea_cac_time_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_dfs_s_radio_report_only_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_dfs_s_radio_thrshld_rpcdata ();
extern bool_t xdr_qcsapi_init_rpcdata ();
extern bool_t xdr_qcsapi_console_disconnect_rpcdata ();
extern bool_t xdr_qcsapi_wifi_startprod_rpcdata ();
extern bool_t xdr_qcsapi_is_startprod_done_rpcdata ();
extern bool_t xdr_qcsapi_system_get_time_since_start_rpcdata ();
extern bool_t xdr_qcsapi_get_system_status_rpcdata ();
extern bool_t xdr_qcsapi_get_random_seed_rpcdata ();
extern bool_t xdr_qcsapi_set_random_seed_rpcdata ();
extern bool_t xdr_qcsapi_get_carrier_id_rpcdata ();
extern bool_t xdr_qcsapi_set_carrier_id_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_spinor_jedecid_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_bb_param_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_bb_param_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_optim_stats_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_sys_time_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_sys_time_rpcdata ();
extern bool_t xdr_qcsapi_set_soc_mac_addr_rpcdata ();
extern bool_t xdr_qcsapi_get_custom_value_rpcdata ();
extern bool_t xdr_qcsapi_config_get_parameter_rpcdata ();
extern bool_t xdr_qcsapi_config_update_parameter_rpcdata ();
extern bool_t xdr_qcsapi_config_get_ssid_parameter_rpcdata ();
extern bool_t xdr_qcsapi_config_update_ssid_parameter_rpcdata ();
extern bool_t xdr_qcsapi_file_path_get_config_rpcdata ();
extern bool_t xdr_qcsapi_file_path_set_config_rpcdata ();
extern bool_t xdr_qcsapi_restore_default_config_rpcdata ();
extern bool_t xdr_qcsapi_store_ipaddr_rpcdata ();
extern bool_t xdr_qcsapi_interface_enable_rpcdata ();
extern bool_t xdr_qcsapi_interface_get_status_rpcdata ();
extern bool_t xdr_qcsapi_interface_set_ip4_rpcdata ();
extern bool_t xdr_qcsapi_interface_get_ip4_rpcdata ();
extern bool_t xdr_qcsapi_interface_get_counter_rpcdata ();
extern bool_t xdr_qcsapi_interface_get_counter64_rpcdata ();
extern bool_t xdr_qcsapi_interface_get_mac_addr_rpcdata ();
extern bool_t xdr_qcsapi_interface_set_mac_addr_rpcdata ();
extern bool_t xdr_qcsapi_pm_get_counter_rpcdata ();
extern bool_t xdr_qcsapi_set_aspm_l1_rpcdata ();
extern bool_t xdr_qcsapi_set_l1_rpcdata ();
extern bool_t xdr_qcsapi_pm_get_elapsed_time_rpcdata ();
extern bool_t xdr_qcsapi_eth_phy_power_control_rpcdata ();
extern bool_t xdr_qcsapi_get_emac_switch_rpcdata ();
extern bool_t xdr_qcsapi_set_emac_switch_rpcdata ();
extern bool_t xdr_qcsapi_eth_dscp_map_rpcdata ();
extern bool_t xdr_qcsapi_get_eth_info_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_mode_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_mode_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_phy_mode_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_phy_mode_rpcdata ();
extern bool_t xdr_qcsapi_wifi_reload_in_mode_rpcdata ();
extern bool_t xdr_qcsapi_wifi_rfenable_rpcdata ();
extern bool_t xdr_qcsapi_wifi_rfstatus_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_bw_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_bw_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_vht_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_vht_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_channel_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_channel_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_chan_pri_inactive_rpcdata ();
extern bool_t xdr_qcsapi_wifi_chan_control_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_chan_disabled_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_beacon_interval_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_beacon_interval_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_dtim_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_dtim_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_assoc_limit_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_bss_assoc_limit_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_assoc_limit_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_bss_assoc_limit_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_BSSID_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_config_BSSID_rpcdata ();
extern bool_t xdr_qcsapi_wifi_ssid_get_bssid_rpcdata ();
extern bool_t xdr_qcsapi_wifi_ssid_set_bssid_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_SSID_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_SSID_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_IEEE_802_11_standard_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_list_channels_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_mode_switch_rpcdata ();
extern bool_t xdr_qcsapi_wifi_disassociate_rpcdata ();
extern bool_t xdr_qcsapi_wifi_disassociate_sta_rpcdata ();
extern bool_t xdr_qcsapi_wifi_reassociate_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_disconn_info_rpcdata ();
extern bool_t xdr_qcsapi_wifi_disable_wps_rpcdata ();
extern bool_t xdr_qcsapi_wifi_associate_rpcdata ();
extern bool_t xdr_qcsapi_wifi_start_cca_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_noise_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_rssi_by_chain_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_avg_snr_rpcdata ();
extern bool_t xdr_qcsapi_get_primary_interface_rpcdata ();
extern bool_t xdr_qcsapi_get_interface_by_index_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_wifi_macaddr_rpcdata ();
extern bool_t xdr_qcsapi_interface_get_BSSID_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_rates_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_rates_rpcdata ();
extern bool_t xdr_qcsapi_get_max_bitrate_rpcdata ();
extern bool_t xdr_qcsapi_set_max_bitrate_rpcdata ();
extern bool_t xdr_qcsapi_wifi_qos_get_param_rpcdata ();
extern bool_t xdr_qcsapi_wifi_qos_set_param_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_wmm_ac_map_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_wmm_ac_map_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_dscp_8021p_map_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_dscp_ac_map_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_dscp_8021p_map_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_dscp_ac_map_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_priority_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_priority_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_airfair_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_airfair_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_tx_power_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_tx_power_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_bw_power_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_bw_power_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_bf_power_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_bf_power_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_tx_power_ext_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_tx_power_ext_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_chan_power_table_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_chan_power_table_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_power_selection_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_power_selection_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_carrier_interference_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_congestion_index_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_supported_tx_power_levels_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_current_tx_power_level_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_power_constraint_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_power_constraint_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_tpc_interval_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_tpc_interval_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_assoc_records_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_ap_isolate_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_ap_isolate_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_intra_bss_isolate_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_intra_bss_isolate_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_bss_isolate_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_bss_isolate_rpcdata ();
extern bool_t xdr_qcsapi_wifi_disable_dfs_channels_rpcdata ();
extern bool_t xdr_qcsapi_wifi_create_restricted_bss_rpcdata ();
extern bool_t xdr_qcsapi_wifi_create_bss_rpcdata ();
extern bool_t xdr_qcsapi_wifi_remove_bss_rpcdata ();
extern bool_t xdr_qcsapi_wds_add_peer_rpcdata ();
extern bool_t xdr_qcsapi_wds_add_peer_encrypt_rpcdata ();
extern bool_t xdr_qcsapi_wds_remove_peer_rpcdata ();
extern bool_t xdr_qcsapi_wds_get_peer_address_rpcdata ();
extern bool_t xdr_qcsapi_wds_set_psk_rpcdata ();
extern bool_t xdr_qcsapi_wds_set_mode_rpcdata ();
extern bool_t xdr_qcsapi_wds_get_mode_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_extender_params_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_extender_params_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_beacon_type_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_beacon_type_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_WEP_key_index_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_WEP_key_index_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_WEP_key_passphrase_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_WEP_key_passphrase_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_WEP_encryption_level_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_basic_encryption_modes_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_basic_encryption_modes_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_basic_authentication_mode_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_basic_authentication_mode_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_WEP_key_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_WEP_key_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_WPA_encryption_modes_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_WPA_encryption_modes_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_WPA_authentication_mode_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_WPA_authentication_mode_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_interworking_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_interworking_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_80211u_params_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_80211u_params_rpcdata ();
extern bool_t xdr_qcsapi_security_get_nai_realms_rpcdata ();
extern bool_t xdr_qcsapi_security_add_nai_realm_rpcdata ();
extern bool_t xdr_qcsapi_security_del_nai_realm_rpcdata ();
extern bool_t xdr_qcsapi_security_get_roaming_consortium_rpcdata ();
extern bool_t xdr_qcsapi_security_add_roaming_consortium_rpcdata ();
extern bool_t xdr_qcsapi_security_del_roaming_consortium_rpcdata ();
extern bool_t xdr_qcsapi_security_get_venue_name_rpcdata ();
extern bool_t xdr_qcsapi_security_add_venue_name_rpcdata ();
extern bool_t xdr_qcsapi_security_del_venue_name_rpcdata ();
extern bool_t xdr_qcsapi_security_get_oper_friendly_name_rpcdata ();
extern bool_t xdr_qcsapi_security_add_oper_friendly_name_rpcdata ();
extern bool_t xdr_qcsapi_security_del_oper_friendly_name_rpcdata ();
extern bool_t xdr_qcsapi_security_get_hs20_conn_capab_rpcdata ();
extern bool_t xdr_qcsapi_security_add_hs20_conn_capab_rpcdata ();
extern bool_t xdr_qcsapi_security_del_hs20_conn_capab_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_hs20_status_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_hs20_status_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_proxy_arp_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_proxy_arp_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_l2_ext_filter_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_l2_ext_filter_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_hs20_params_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_hs20_params_rpcdata ();
extern bool_t xdr_qcsapi_remove_11u_param_rpcdata ();
extern bool_t xdr_qcsapi_remove_hs20_param_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_IEEE11i_encryption_modes_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_IEEE11i_encryption_modes_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_IEEE11i_authentication_mode_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_IEEE11i_authentication_mode_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_michael_errcnt_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_pre_shared_key_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_pre_shared_key_rpcdata ();
extern bool_t xdr_qcsapi_wifi_add_radius_auth_server_cfg_rpcdata ();
extern bool_t xdr_qcsapi_wifi_del_radius_auth_server_cfg_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_radius_auth_server_cfg_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_own_ip_addr_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_key_passphrase_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_key_passphrase_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_group_key_interval_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_group_key_interval_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_pmf_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_pmf_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_wpa_status_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_psk_auth_failures_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_auth_state_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_security_defer_mode_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_security_defer_mode_rpcdata ();
extern bool_t xdr_qcsapi_wifi_apply_security_config_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_mac_address_filtering_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_mac_address_filtering_rpcdata ();
extern bool_t xdr_qcsapi_wifi_authorize_mac_address_rpcdata ();
extern bool_t xdr_qcsapi_wifi_deny_mac_address_rpcdata ();
extern bool_t xdr_qcsapi_wifi_remove_mac_address_rpcdata ();
extern bool_t xdr_qcsapi_wifi_is_mac_address_authorized_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_authorized_mac_addresses_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_denied_mac_addresses_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_accept_oui_filter_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_accept_oui_filter_rpcdata ();
extern bool_t xdr_qcsapi_wifi_clear_mac_address_filters_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_mac_address_reserve_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_mac_address_reserve_rpcdata ();
extern bool_t xdr_qcsapi_wifi_clear_mac_address_reserve_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_option_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_option_rpcdata ();
extern bool_t xdr_qcsapi_get_board_parameter_rpcdata ();
extern bool_t xdr_qcsapi_get_swfeat_list_rpcdata ();
extern bool_t xdr_qcsapi_SSID_create_SSID_rpcdata ();
extern bool_t xdr_qcsapi_SSID_remove_SSID_rpcdata ();
extern bool_t xdr_qcsapi_SSID_verify_SSID_rpcdata ();
extern bool_t xdr_qcsapi_SSID_rename_SSID_rpcdata ();
extern bool_t xdr_qcsapi_SSID_get_SSID_list_rpcdata ();
extern bool_t xdr_qcsapi_SSID_set_protocol_rpcdata ();
extern bool_t xdr_qcsapi_SSID_get_protocol_rpcdata ();
extern bool_t xdr_qcsapi_SSID_get_encryption_modes_rpcdata ();
extern bool_t xdr_qcsapi_SSID_set_encryption_modes_rpcdata ();
extern bool_t xdr_qcsapi_SSID_get_group_encryption_rpcdata ();
extern bool_t xdr_qcsapi_SSID_set_group_encryption_rpcdata ();
extern bool_t xdr_qcsapi_SSID_get_authentication_mode_rpcdata ();
extern bool_t xdr_qcsapi_SSID_set_authentication_mode_rpcdata ();
extern bool_t xdr_qcsapi_SSID_get_pre_shared_key_rpcdata ();
extern bool_t xdr_qcsapi_SSID_set_pre_shared_key_rpcdata ();
extern bool_t xdr_qcsapi_SSID_get_key_passphrase_rpcdata ();
extern bool_t xdr_qcsapi_SSID_set_key_passphrase_rpcdata ();
extern bool_t xdr_qcsapi_SSID_get_pmf_rpcdata ();
extern bool_t xdr_qcsapi_SSID_set_pmf_rpcdata ();
extern bool_t xdr_qcsapi_SSID_get_wps_SSID_rpcdata ();
extern bool_t xdr_qcsapi_wifi_vlan_config_rpcdata ();
extern bool_t xdr_qcsapi_wifi_show_vlan_config_rpcdata ();
extern bool_t xdr_qcsapi_enable_vlan_pass_through_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_vlan_promisc_rpcdata ();
extern bool_t xdr_qcsapi_wps_registrar_report_button_press_rpcdata ();
extern bool_t xdr_qcsapi_wps_registrar_report_pin_rpcdata ();
extern bool_t xdr_qcsapi_wps_registrar_get_pp_devname_rpcdata ();
extern bool_t xdr_qcsapi_wps_registrar_set_pp_devname_rpcdata ();
extern bool_t xdr_qcsapi_wps_enrollee_report_button_press_rpcdata ();
extern bool_t xdr_qcsapi_wps_enrollee_report_pin_rpcdata ();
extern bool_t xdr_qcsapi_wps_enrollee_generate_pin_rpcdata ();
extern bool_t xdr_qcsapi_wps_get_ap_pin_rpcdata ();
extern bool_t xdr_qcsapi_wps_set_ap_pin_rpcdata ();
extern bool_t xdr_qcsapi_wps_save_ap_pin_rpcdata ();
extern bool_t xdr_qcsapi_wps_enable_ap_pin_rpcdata ();
extern bool_t xdr_qcsapi_wps_get_sta_pin_rpcdata ();
extern bool_t xdr_qcsapi_wps_get_state_rpcdata ();
extern bool_t xdr_qcsapi_wps_get_configured_state_rpcdata ();
extern bool_t xdr_qcsapi_wps_get_runtime_state_rpcdata ();
extern bool_t xdr_qcsapi_wps_set_configured_state_rpcdata ();
extern bool_t xdr_qcsapi_wps_get_param_rpcdata ();
extern bool_t xdr_qcsapi_wps_set_timeout_rpcdata ();
extern bool_t xdr_qcsapi_wps_on_hidden_ssid_rpcdata ();
extern bool_t xdr_qcsapi_wps_on_hidden_ssid_status_rpcdata ();
extern bool_t xdr_qcsapi_wps_upnp_enable_rpcdata ();
extern bool_t xdr_qcsapi_wps_upnp_status_rpcdata ();
extern bool_t xdr_qcsapi_wps_allow_pbc_overlap_rpcdata ();
extern bool_t xdr_qcsapi_wps_get_allow_pbc_overlap_status_rpcdata ();
extern bool_t xdr_qcsapi_wps_set_access_control_rpcdata ();
extern bool_t xdr_qcsapi_wps_get_access_control_rpcdata ();
extern bool_t xdr_qcsapi_wps_set_param_rpcdata ();
extern bool_t xdr_qcsapi_wps_cancel_rpcdata ();
extern bool_t xdr_qcsapi_wps_set_pbc_in_srcm_rpcdata ();
extern bool_t xdr_qcsapi_wps_get_pbc_in_srcm_rpcdata ();
extern bool_t xdr_qcsapi_registrar_set_default_pbc_bss_rpcdata ();
extern bool_t xdr_qcsapi_registrar_get_default_pbc_bss_rpcdata ();
extern bool_t xdr_qcsapi_gpio_set_config_rpcdata ();
extern bool_t xdr_qcsapi_gpio_get_config_rpcdata ();
extern bool_t xdr_qcsapi_led_get_rpcdata ();
extern bool_t xdr_qcsapi_led_set_rpcdata ();
extern bool_t xdr_qcsapi_led_pwm_enable_rpcdata ();
extern bool_t xdr_qcsapi_led_brightness_rpcdata ();
extern bool_t xdr_qcsapi_gpio_enable_wps_push_button_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_count_associations_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_associated_device_mac_addr_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_associated_device_ip_addr_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_link_quality_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_link_quality_max_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_rx_bytes_per_association_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_tx_bytes_per_association_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_rx_packets_per_association_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_tx_packets_per_association_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_tx_err_packets_per_association_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_rssi_per_association_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_rssi_in_dbm_per_association_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_bw_per_association_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_tx_phy_rate_per_association_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_rx_phy_rate_per_association_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_tx_mcs_per_association_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_rx_mcs_per_association_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_achievable_tx_phy_rate_per_association_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_achievable_rx_phy_rate_per_association_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_auth_enc_per_association_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_tput_caps_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_connection_mode_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_vendor_per_association_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_max_mimo_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_snr_per_association_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_time_associated_per_association_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_node_param_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_node_counter_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_node_stats_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_max_queued_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_hw_noise_per_association_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_mlme_stats_per_mac_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_mlme_stats_per_association_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_mlme_stats_macs_list_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_list_regulatory_regions_rpcdata ();
extern bool_t xdr_qcsapi_regulatory_get_list_regulatory_regions_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_list_regulatory_channels_rpcdata ();
extern bool_t xdr_qcsapi_regulatory_get_list_regulatory_channels_rpcdata ();
extern bool_t xdr_qcsapi_regulatory_get_list_regulatory_bands_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_regulatory_tx_power_rpcdata ();
extern bool_t xdr_qcsapi_regulatory_get_regulatory_tx_power_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_configured_tx_power_rpcdata ();
extern bool_t xdr_qcsapi_regulatory_get_configured_tx_power_rpcdata ();
extern bool_t xdr_qcsapi_regulatory_get_configured_tx_power_ext_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_regulatory_region_rpcdata ();
extern bool_t xdr_qcsapi_regulatory_set_regulatory_region_rpcdata ();
extern bool_t xdr_qcsapi_regulatory_restore_regulatory_tx_power_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_regulatory_region_rpcdata ();
extern bool_t xdr_qcsapi_regulatory_overwrite_country_code_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_regulatory_channel_rpcdata ();
extern bool_t xdr_qcsapi_regulatory_set_regulatory_channel_rpcdata ();
extern bool_t xdr_qcsapi_regulatory_get_db_version_rpcdata ();
extern bool_t xdr_qcsapi_regulatory_apply_tx_power_cap_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_list_DFS_channels_rpcdata ();
extern bool_t xdr_qcsapi_regulatory_get_list_DFS_channels_rpcdata ();
extern bool_t xdr_qcsapi_wifi_is_channel_DFS_rpcdata ();
extern bool_t xdr_qcsapi_regulatory_is_channel_DFS_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_dfs_cce_channels_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_DFS_alt_channel_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_DFS_alt_channel_rpcdata ();
extern bool_t xdr_qcsapi_wifi_start_dfs_reentry_rpcdata ();
extern bool_t xdr_qcsapi_wifi_start_scan_ext_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_csw_records_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_radar_status_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_cac_status_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_results_AP_scan_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_count_APs_scanned_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_properties_AP_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_scan_chk_inv_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_scan_chk_inv_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_scan_buf_max_size_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_scan_buf_max_size_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_scan_table_max_len_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_scan_table_max_len_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_dwell_times_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_dwell_times_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_bgscan_dwell_times_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_bgscan_dwell_times_rpcdata ();
extern bool_t xdr_qcsapi_wifi_start_scan_rpcdata ();
extern bool_t xdr_qcsapi_wifi_cancel_scan_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_scan_status_rpcdata ();
extern bool_t xdr_qcsapi_wifi_enable_bgscan_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_bgscan_status_rpcdata ();
extern bool_t xdr_qcsapi_wifi_wait_scan_completes_rpcdata ();
extern bool_t xdr_qcsapi_wifi_backoff_fail_max_rpcdata ();
extern bool_t xdr_qcsapi_wifi_backoff_timeout_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_mcs_rate_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_mcs_rate_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_pairing_id_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_pairing_id_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_pairing_enable_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_pairing_enable_rpcdata ();
extern bool_t xdr_qcsapi_non_wps_set_pp_enable_rpcdata ();
extern bool_t xdr_qcsapi_non_wps_get_pp_enable_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_vendor_fix_rpcdata ();
extern bool_t xdr_qcsapi_errno_get_message_rpcdata ();
extern bool_t xdr_qcsapi_get_interface_stats_rpcdata ();
extern bool_t xdr_qcsapi_get_phy_stats_rpcdata ();
extern bool_t xdr_qcsapi_reset_all_counters_rpcdata ();
extern bool_t xdr_qcsapi_get_uboot_info_rpcdata ();
extern bool_t xdr_qcsapi_firmware_get_version_rpcdata ();
extern bool_t xdr_qcsapi_flash_image_update_rpcdata ();
extern bool_t xdr_qcsapi_send_file_rpcdata ();
extern bool_t xdr_qcsapi_pm_set_mode_rpcdata ();
extern bool_t xdr_qcsapi_pm_get_mode_rpcdata ();
extern bool_t xdr_qcsapi_get_qpm_level_rpcdata ();
extern bool_t xdr_qcsapi_set_host_state_rpcdata ();
extern bool_t xdr_qcsapi_qtm_get_state_rpcdata ();
extern bool_t xdr_qcsapi_qtm_get_state_all_rpcdata ();
extern bool_t xdr_qcsapi_qtm_set_state_rpcdata ();
extern bool_t xdr_qcsapi_qtm_get_config_rpcdata ();
extern bool_t xdr_qcsapi_qtm_get_config_all_rpcdata ();
extern bool_t xdr_qcsapi_qtm_set_config_rpcdata ();
extern bool_t xdr_qcsapi_qtm_add_rule_rpcdata ();
extern bool_t xdr_qcsapi_qtm_del_rule_rpcdata ();
extern bool_t xdr_qcsapi_qtm_del_rule_index_rpcdata ();
extern bool_t xdr_qcsapi_qtm_get_rule_rpcdata ();
extern bool_t xdr_qcsapi_qtm_get_strm_rpcdata ();
extern bool_t xdr_qcsapi_qtm_get_stats_rpcdata ();
extern bool_t xdr_qcsapi_qtm_get_inactive_flags_rpcdata ();
extern bool_t xdr_qcsapi_wifi_run_script_rpcdata ();
extern bool_t xdr_qcsapi_wifi_test_traffic_rpcdata ();
extern bool_t xdr_qcsapi_wifi_add_ipff_rpcdata ();
extern bool_t xdr_qcsapi_wifi_del_ipff_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_ipff_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_rts_threshold_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_rts_threshold_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_nss_cap_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_nss_cap_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_tx_amsdu_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_tx_amsdu_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_disassoc_reason_rpcdata ();
extern bool_t xdr_qcsapi_wifi_block_bss_rpcdata ();
extern bool_t xdr_qcsapi_wifi_verify_repeater_mode_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_ap_interface_name_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_ap_interface_name_rpcdata ();
extern bool_t xdr_qcsapi_get_temperature_info_rpcdata ();
extern bool_t xdr_qcsapi_calcmd_set_test_mode_rpcdata ();
extern bool_t xdr_qcsapi_calcmd_show_test_packet_rpcdata ();
extern bool_t xdr_qcsapi_calcmd_send_test_packet_rpcdata ();
extern bool_t xdr_qcsapi_calcmd_stop_test_packet_rpcdata ();
extern bool_t xdr_qcsapi_calcmd_send_dc_cw_signal_rpcdata ();
extern bool_t xdr_qcsapi_calcmd_stop_dc_cw_signal_rpcdata ();
extern bool_t xdr_qcsapi_calcmd_get_test_mode_antenna_sel_rpcdata ();
extern bool_t xdr_qcsapi_calcmd_get_test_mode_mcs_rpcdata ();
extern bool_t xdr_qcsapi_calcmd_get_test_mode_bw_rpcdata ();
extern bool_t xdr_qcsapi_calcmd_get_tx_power_rpcdata ();
extern bool_t xdr_qcsapi_calcmd_set_tx_power_rpcdata ();
extern bool_t xdr_qcsapi_calcmd_get_test_mode_rssi_rpcdata ();
extern bool_t xdr_qcsapi_calcmd_set_mac_filter_rpcdata ();
extern bool_t xdr_qcsapi_calcmd_get_antenna_count_rpcdata ();
extern bool_t xdr_qcsapi_calcmd_clear_counter_rpcdata ();
extern bool_t xdr_qcsapi_calcmd_get_info_rpcdata ();
extern bool_t xdr_qcsapi_wowlan_set_match_type_rpcdata ();
extern bool_t xdr_qcsapi_wowlan_set_L2_type_rpcdata ();
extern bool_t xdr_qcsapi_wowlan_set_udp_port_rpcdata ();
extern bool_t xdr_qcsapi_wowlan_set_magic_pattern_rpcdata ();
extern bool_t xdr_qcsapi_wifi_wowlan_get_host_state_rpcdata ();
extern bool_t xdr_qcsapi_wifi_wowlan_get_match_type_rpcdata ();
extern bool_t xdr_qcsapi_wifi_wowlan_get_l2_type_rpcdata ();
extern bool_t xdr_qcsapi_wifi_wowlan_get_udp_port_rpcdata ();
extern bool_t xdr_qcsapi_wifi_wowlan_get_magic_pattern_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_enable_mu_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_enable_mu_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_mu_use_precode_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_mu_use_precode_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_mu_use_eq_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_mu_use_eq_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_mu_groups_rpcdata ();
extern bool_t xdr_qcsapi_wifi_enable_tdls_rpcdata ();
extern bool_t xdr_qcsapi_wifi_enable_tdls_over_qhop_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_tdls_status_rpcdata ();
extern bool_t xdr_qcsapi_wifi_set_tdls_params_rpcdata ();
extern bool_t xdr_qcsapi_wifi_get_tdls_params_rpcdata ();
extern bool_t xdr_qcsapi_wifi_tdls_operate_rpcdata ();

#endif /* K&R C */

#ifdef __cplusplus
}
#endif

#endif /* !_QCSAPI_RPC_H_RPCGEN */
