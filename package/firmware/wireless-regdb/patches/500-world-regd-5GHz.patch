Remove the NO-IR flag from channels 36-48 on the World domain,
to make it usable for AP mode.

Signed-off-by: <PERSON> <<EMAIL>>
---
--- a/db.txt
+++ b/db.txt
@@ -19,13 +19,15 @@ country 00:
 	# Channel 14. Only <PERSON> enables this and for 802.11b only
 	(2474 - 2494 @ 20), (20), NO-IR, NO-OFDM
 	# Channel 36 - 48
-	(5170 - 5250 @ 80), (20), NO-IR, AUTO-BW
+	(5170 - 5250 @ 80), (20), AUTO-BW
 	# Channel 52 - 64
 	(5250 - 5330 @ 80), (20), NO-IR, DFS, AUTO-BW
 	# Channel 100 - 144
 	(5490 - 5730 @ 160), (20), NO-IR, DFS
 	# Channel 149 - 165
 	(5735 - 5835 @ 80), (20), NO-IR
+	# Channel 1 - 223
+	(5925 - 7125 @ 320), (12), AUTO-BW
 	# IEEE 802.11ad (60GHz), channels 1..3
 	(57240 - 63720 @ 2160), (0)

@@ -1736,7 +1736,7 @@ country US: DFS-FCC
 	(5850 - 5895 @ 40), (27), NO-OUTDOOR, AUTO-BW, NO-IR
 	# 6g band
 	# https://www.federalregister.gov/documents/2020/05/26/2020-11236/unlicensed-use-of-the-6ghz-band
-	(5925 - 7125 @ 320), (12), NO-OUTDOOR, NO-IR
+	(5925 - 7125 @ 320), (12), AUTO-BW
 	# 60g band
 	# reference: section IV-D https://docs.fcc.gov/public/attachments/FCC-16-89A1.pdf
 	# channels 1-6 EIRP=40dBm(43dBm peak)
