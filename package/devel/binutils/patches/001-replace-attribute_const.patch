Fix this compile error:
----------------------
./../common/cpuid.c:27:1: error: expected '=', ',', ';', 'asm' or '__attribute__' before '__get_cpuid'
   27 | __get_cpuid (unsigned int op ATTRIBUTE_UNUSED, unsigned int *eax,
      | ^~~~~~~~~~~
----------------------

and this error:
----------------------
unwind.c: In function '__collector_ext_return_address':
unwind.c:236:34: error: '__u64' undeclared (first use in this function)
  236 |       context->uc_mcontext.sp = (__u64) __builtin_frame_address(0); \
      |                                  ^~~~~
unwind.c:490:3: note: in expansion of macro 'FILL_CONTEXT'
  490 |   FILL_CONTEXT ((&context));

----------------------
--- a/gprofng/common/cpuid.c
+++ b/gprofng/common/cpuid.c
@@ -23,7 +23,7 @@
 #elif defined(__aarch64__)
 #define ATTRIBUTE_UNUSED __attribute__((unused))
 
-static inline uint_t __attribute_const__
+static inline uint_t __attribute__((__const__))
 __get_cpuid (unsigned int op ATTRIBUTE_UNUSED, unsigned int *eax,
 	     unsigned int *ebx ATTRIBUTE_UNUSED,
 	     unsigned int *ecx ATTRIBUTE_UNUSED, unsigned int *edx ATTRIBUTE_UNUSED)
--- a/gprofng/libcollector/unwind.c
+++ b/gprofng/libcollector/unwind.c
@@ -237,7 +237,7 @@ typedef uint64_t __u64;
 
 #define FILL_CONTEXT(context) \
     { CALL_UTIL (getcontext) (context);  \
-      context->uc_mcontext.sp = (__u64) __builtin_frame_address(0); \
+      context->uc_mcontext.sp = (uint64_t) __builtin_frame_address(0); \
     }
 
 #endif /* ARCH() */
@@ -4583,11 +4583,11 @@ stack_unwind (char *buf, int size, void
   if (buf && bptr && eptr && context && size + mode > 0)
     getByteInstruction ((unsigned char *) eptr);
   int ind = 0;
-  __u64 *lbuf = (void *) buf;
-  int lsize = size / sizeof (__u64);
-  __u64 pc = context->uc_mcontext.pc;
-  __u64 sp = context->uc_mcontext.sp;
-  __u64 stack_base;
+  uint64_t *lbuf = (void *) buf;
+  int lsize = size / sizeof (uint64_t);
+  uint64_t pc = context->uc_mcontext.pc;
+  uint64_t sp = context->uc_mcontext.sp;
+  uint64_t stack_base;
   unsigned long tbgn = 0;
   unsigned long tend = 0;
 
@@ -4598,7 +4598,7 @@ stack_unwind (char *buf, int size, void
     {
       stack_base = sp + 0x100000;
       if (stack_base < sp)  // overflow
-	stack_base = (__u64) -1;
+	stack_base = (uint64_t) -1;
     }
   DprintfT (SP_DUMP_UNWIND,
     "unwind.c:%d stack_unwind %2d pc=0x%llx  sp=0x%llx  stack_base=0x%llx\n",
@@ -4629,17 +4629,17 @@ stack_unwind (char *buf, int size, void
 		      __LINE__, (unsigned long) sp);
 	    break;
 	  }
-      pc = ((__u64 *) sp)[1];
-      __u64 old_sp = sp;
-      sp = ((__u64 *) sp)[0];
+      pc = ((uint64_t *) sp)[1];
+      uint64_t old_sp = sp;
+      sp = ((uint64_t *) sp)[0];
       if (sp < old_sp)
 	break;
     }
   if (ind >= lsize)
     {
       ind = lsize - 1;
-      lbuf[ind++] = (__u64) SP_TRUNC_STACK_MARKER;
+      lbuf[ind++] = (uint64_t) SP_TRUNC_STACK_MARKER;
     }
-  return ind * sizeof (__u64);
+  return ind * sizeof (uint64_t);
 }
 #endif /* ARCH() */
