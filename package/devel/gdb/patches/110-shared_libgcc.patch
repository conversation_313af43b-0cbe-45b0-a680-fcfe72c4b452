--- a/configure.ac
+++ b/configure.ac
@@ -1410,13 +1410,13 @@ if test -z "$LD"; then
   fi
 fi
 
-# Check whether -static-libstdc++ -static-libgcc is supported.
+# Check whether -static-libstdc++ is supported.
 have_static_libs=no
 if test "$GCC" = yes; then
   saved_LDFLAGS="$LDFLAGS"
 
-  LDFLAGS="$LDFLAGS -static-libstdc++ -static-libgcc"
-  AC_MSG_CHECKING([whether g++ accepts -static-libstdc++ -static-libgcc])
+  LDFLAGS="$LDFLAGS -static-libstdc++"
+  AC_MSG_CHECKING([whether g++ accepts -static-libstdc++])
   AC_LANG_PUSH(C++)
   AC_LINK_IFELSE([AC_LANG_SOURCE([
 #if (__GNUC__ < 4) || (__GNUC__ == 4 && __GNUC_MINOR__ < 5)
@@ -1847,7 +1847,7 @@ AC_ARG_WITH(stage1-ldflags,
  # trust that they are doing what they want.
  if test "$with_static_standard_libraries" = yes -a "$stage1_libs" = "" \
      -a "$have_static_libs" = yes; then
-   stage1_ldflags="-static-libstdc++ -static-libgcc"
+   stage1_ldflags="-static-libstdc++"
  fi])
 AC_SUBST(stage1_ldflags)
 
@@ -1876,7 +1876,7 @@ AC_ARG_WITH(boot-ldflags,
  # statically.  But if the user explicitly specified the libraries to
  # use, trust that they are doing what they want.
  if test "$poststage1_libs" = ""; then
-   poststage1_ldflags="-static-libstdc++ -static-libgcc"
+   poststage1_ldflags="-static-libstdc++"
  fi])
 case $target in
   *-darwin2* | *-darwin1[[56789]]*)
--- a/configure
+++ b/configure
@@ -5428,14 +5428,14 @@ if test -z "$LD"; then
   fi
 fi
 
-# Check whether -static-libstdc++ -static-libgcc is supported.
+# Check whether -static-libstdc++ is supported.
 have_static_libs=no
 if test "$GCC" = yes; then
   saved_LDFLAGS="$LDFLAGS"
 
-  LDFLAGS="$LDFLAGS -static-libstdc++ -static-libgcc"
-  { $as_echo "$as_me:${as_lineno-$LINENO}: checking whether g++ accepts -static-libstdc++ -static-libgcc" >&5
-$as_echo_n "checking whether g++ accepts -static-libstdc++ -static-libgcc... " >&6; }
+  LDFLAGS="$LDFLAGS -static-libstdc++"
+  { $as_echo "$as_me:${as_lineno-$LINENO}: checking whether g++ accepts -static-libstdc++" >&5
+$as_echo_n "checking whether g++ accepts -static-libstdc++... " >&6; }
   ac_ext=cpp
 ac_cpp='$CXXCPP $CPPFLAGS'
 ac_compile='$CXX -c $CXXFLAGS $CPPFLAGS conftest.$ac_ext >&5'
@@ -9247,7 +9247,7 @@ else
  # trust that they are doing what they want.
  if test "$with_static_standard_libraries" = yes -a "$stage1_libs" = "" \
      -a "$have_static_libs" = yes; then
-   stage1_ldflags="-static-libstdc++ -static-libgcc"
+   stage1_ldflags="-static-libstdc++"
  fi
 fi
 
@@ -9283,7 +9283,7 @@ else
  # statically.  But if the user explicitly specified the libraries to
  # use, trust that they are doing what they want.
  if test "$poststage1_libs" = ""; then
-   poststage1_ldflags="-static-libstdc++ -static-libgcc"
+   poststage1_ldflags="-static-libstdc++"
  fi
 fi
 
