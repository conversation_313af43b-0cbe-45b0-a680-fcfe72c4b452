From e92f8932ef488de2a56db4299131ce6a4eb170bd Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON> <<EMAIL>>
Date: Wed, 23 Mar 2016 06:30:09 +0000
Subject: [PATCH] mips-linux-nat: Define _ABIO32 if not defined

This helps building gdb on mips64 on musl, since
musl does not provide sgidefs.h this define is
only defined when GCC is using o32 ABI, in that
case gcc emits it as built-in define and hence
it works ok for mips32

Signed-off-by: <PERSON><PERSON> <<EMAIL>>
---
Upstream-Status: Pending

 gdb/mips-linux-nat.c | 5 +++++
 1 file changed, 5 insertions(+)

--- a/gdb/mips-linux-nat.c
+++ b/gdb/mips-linux-nat.c
@@ -41,6 +41,11 @@
 #define PTRACE_GET_THREAD_AREA 25
 #endif
 
+/* musl does not define and relies on compiler built-in macros for it   */
+#ifndef _ABIO32
+#define _ABIO32 1
+#endif
+
 class mips_linux_nat_target final : public linux_nat_trad_target
 {
 public:
