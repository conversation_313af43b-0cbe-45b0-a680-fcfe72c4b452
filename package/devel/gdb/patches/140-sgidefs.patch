From 677b5b56135141c0d259e370aacd0e11c810aa15 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Fri, 5 Feb 2016 14:00:00 -0800
Subject: [PATCH] use <asm/sgidefs.h>

Build fix for MIPS with musl libc

The MIPS specific header <sgidefs.h> is provided by glibc and uclibc
but not by musl. Regardless of the libc, the kernel headers provide
<asm/sgidefs.h> which provides the same definitions, so use that
instead.

Upstream-Status: Pending

Signed-off-by: <PERSON> <<EMAIL>>
---
 gdb/mips-linux-nat.c | 2 +-
 1 file changed, 1 insertion(+), 1 deletion(-)

--- a/gdb/mips-linux-nat.c
+++ b/gdb/mips-linux-nat.c
@@ -30,7 +30,7 @@
 #include "gdb_proc_service.h"
 #include "gregset.h"
 
-#include <sgidefs.h>
+#include <asm/sgidefs.h>
 #include "nat/gdb_ptrace.h"
 #include <asm/ptrace.h>
 #include "inf-ptrace.h"
