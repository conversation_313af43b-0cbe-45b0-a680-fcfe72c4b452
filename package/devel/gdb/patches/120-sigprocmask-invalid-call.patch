From 56893a61aa4f0270fa8d1197b9848247f90fce0d Mon Sep 17 00:00:00 2001
From: <PERSON>son<PERSON> <<EMAIL>>
Date: Fri, 24 Mar 2017 10:36:03 +0800
Subject: [PATCH] Fix invalid sigprocmask call

The POSIX document says

    The pthread_sigmask() and sigprocmask() functions shall fail if:

    [EINVAL]
    The value of the how argument is not equal to one of the defined values.

and this is how musl-libc is currently doing.  Fix the call to be safe
and correct

 [1] http://pubs.opengroup.org/onlinepubs/9699919799/functions/pthread_sigmask.html

gdb/ChangeLog:
2017-03-24  Yousong <PERSON>  <<EMAIL>>

    * common/signals-state-save-restore.c (save_original_signals_state):
    Fix invalid sigprocmask call.
---
 gdb/ChangeLog                           | 5 +++++
 gdb/common/signals-state-save-restore.c | 2 +-
 2 files changed, 6 insertions(+), 1 deletion(-)

--- a/gdbsupport/signals-state-save-restore.cc
+++ b/gdbsupport/signals-state-save-restore.cc
@@ -37,7 +37,7 @@ save_original_signals_state (bool quiet)
   int i;
   int res;
 
-  res = gdb_sigmask (0,  NULL, &original_signal_mask);
+  res = gdb_sigmask (SIG_BLOCK,  NULL, &original_signal_mask);
   if (res == -1)
     perror_with_name (("sigprocmask"));
 
