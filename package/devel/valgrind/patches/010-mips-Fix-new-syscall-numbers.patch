From 86ab9452bd10f08dbfa22d94e1155838f6f9f2e0 Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON> <<EMAIL>>
Date: Sun, 31 Oct 2021 23:11:11 +0100
Subject: [PATCH] mips: Fix new syscall numbers

The MIPS32 and MIPS64 O32 ABI are adding 4000 to all syscall numbers.
The MIPS64 N64 ABI adds 5000 to each syscall and the MIPS64 N32 ABI adds
6000 to each syscall number. We can not sue the shared file for MIPS and
have to define this for each sycall separately.

Without this change valgrind is not able to detect new syscalls like
clock_gettime64 correctly.

Signed-off-by: Ha<PERSON> <<EMAIL>>
---
 include/pub_tool_vkiscnums_asm.h      |  3 ---
 include/vki/vki-scnums-mips32-linux.h | 36 +++++++++++++++++++++++++++
 include/vki/vki-scnums-mips64-linux.h | 32 ++++++++++++++++++++++++
 3 files changed, 68 insertions(+), 3 deletions(-)

--- a/include/pub_tool_vkiscnums_asm.h
+++ b/include/pub_tool_vkiscnums_asm.h
@@ -63,15 +63,12 @@
 #  include "vki/vki-scnums-arm64-linux.h"
 
 #elif defined(VGP_mips32_linux)
-#  include "vki/vki-scnums-shared-linux.h"
-#  include "vki/vki-scnums-32bit-linux.h"
 #  include "vki/vki-scnums-mips32-linux.h"
 
 #elif defined(VGP_nanomips_linux)
 #  include "vki/vki-scnums-nanomips-linux.h"
 
 #elif defined(VGP_mips64_linux)
-#  include "vki/vki-scnums-shared-linux.h"
 #  include "vki/vki-scnums-mips64-linux.h"
 
 #elif defined(VGP_x86_freebsd) || defined(VGP_amd64_freebsd)
--- a/include/vki/vki-scnums-mips32-linux.h
+++ b/include/vki/vki-scnums-mips32-linux.h
@@ -401,6 +401,42 @@
 #define __NR_pkey_free			(__NR_Linux + 365)
 #define __NR_statx			(__NR_Linux + 366)
 
+#define __NR_clock_gettime64		(__NR_Linux + 403)
+#define __NR_clock_settime64		(__NR_Linux + 404)
+#define __NR_clock_adjtime64		(__NR_Linux + 405)
+#define __NR_clock_getres_time64	(__NR_Linux + 406)
+#define __NR_clock_nanosleep_time64	(__NR_Linux + 407)
+#define __NR_timer_gettime64		(__NR_Linux + 408)
+#define __NR_timer_settime64		(__NR_Linux + 409)
+#define __NR_timerfd_gettime64		(__NR_Linux + 410)
+#define __NR_timerfd_settime64		(__NR_Linux + 411)
+#define __NR_utimensat_time64		(__NR_Linux + 412)
+#define __NR_pselect6_time64		(__NR_Linux + 413)
+#define __NR_ppoll_time64		(__NR_Linux + 414)
+#define __NR_io_pgetevents_time64	(__NR_Linux + 416)
+#define __NR_recvmmsg_time64		(__NR_Linux + 417)
+#define __NR_mq_timedsend_time64	(__NR_Linux + 418)
+#define __NR_mq_timedreceive_time64	(__NR_Linux + 419)
+#define __NR_semtimedop_time64		(__NR_Linux + 420)
+#define __NR_rt_sigtimedwait_time64	(__NR_Linux + 421)
+#define __NR_futex_time64		(__NR_Linux + 422)
+#define __NR_sched_rr_get_interval_time64	(__NR_Linux + 423)
+#define __NR_pidfd_send_signal		(__NR_Linux + 424)
+#define __NR_io_uring_setup		(__NR_Linux + 425)
+#define __NR_io_uring_enter		(__NR_Linux + 426)
+#define __NR_io_uring_register		(__NR_Linux + 427)
+#define __NR_open_tree			(__NR_Linux + 428)
+#define __NR_move_mount			(__NR_Linux + 429)
+#define __NR_fsopen			(__NR_Linux + 430)
+#define __NR_fsconfig			(__NR_Linux + 431)
+#define __NR_fsmount			(__NR_Linux + 432)
+#define __NR_fspick			(__NR_Linux + 433)
+
+#define __NR_clone3			(__NR_Linux + 435)
+#define __NR_close_range		(__NR_Linux + 436)
+
+#define __NR_faccessat2			(__NR_Linux + 439)
+
 /*
  * Offset of the last Linux o32 flavoured syscall
  */
--- a/include/vki/vki-scnums-mips64-linux.h
+++ b/include/vki/vki-scnums-mips64-linux.h
@@ -363,6 +363,22 @@
 #define __NR_pkey_free              (__NR_Linux + 325)
 #define __NR_statx                  (__NR_Linux + 326)
 
+#define __NR_pidfd_send_signal       (__NR_Linux + 424)
+#define __NR_io_uring_setup          (__NR_Linux + 425)
+#define __NR_io_uring_enter          (__NR_Linux + 426)
+#define __NR_io_uring_register       (__NR_Linux + 427)
+#define __NR_open_tree               (__NR_Linux + 428)
+#define __NR_move_mount              (__NR_Linux + 429)
+#define __NR_fsopen                  (__NR_Linux + 430)
+#define __NR_fsconfig                (__NR_Linux + 431)
+#define __NR_fsmount                 (__NR_Linux + 432)
+#define __NR_fspick                  (__NR_Linux + 433)
+
+#define __NR_clone3                  (__NR_Linux + 435)
+#define __NR_close_range             (__NR_Linux + 436)
+
+#define __NR_faccessat2              (__NR_Linux + 439)
+
 #elif defined(VGABI_N32)
 
 /*
@@ -702,6 +718,22 @@
 #define __NR_pkey_free               (__NR_Linux + 329)
 #define __NR_statx                   (__NR_Linux + 330)
 
+#define __NR_pidfd_send_signal       (__NR_Linux + 424)
+#define __NR_io_uring_setup          (__NR_Linux + 425)
+#define __NR_io_uring_enter          (__NR_Linux + 426)
+#define __NR_io_uring_register       (__NR_Linux + 427)
+#define __NR_open_tree               (__NR_Linux + 428)
+#define __NR_move_mount              (__NR_Linux + 429)
+#define __NR_fsopen                  (__NR_Linux + 430)
+#define __NR_fsconfig                (__NR_Linux + 431)
+#define __NR_fsmount                 (__NR_Linux + 432)
+#define __NR_fspick                  (__NR_Linux + 433)
+
+#define __NR_clone3                  (__NR_Linux + 435)
+#define __NR_close_range             (__NR_Linux + 436)
+
+#define __NR_faccessat2              (__NR_Linux + 439)
+
 #else
 #error unknown mips64 abi
 #endif
