From 2e01dc015bc8bb9ca45f369025c342ede990863e Mon Sep 17 00:00:00 2001
From: <PERSON> <daniel.schwier<PERSON><PERSON>@gmail.com>
Date: Mon, 12 Aug 2013 01:16:09 +0200
Subject: Makefile: add Lantiq NAND SPL images

Signed-off-by: <PERSON> <<EMAIL>>

--- a/.gitignore
+++ b/.gitignore
@@ -54,6 +54,9 @@
 /u-boot.ltq.lzma.norspl
 /u-boot.ltq.lzo.norspl
 /u-boot.ltq.norspl
+/u-boot.ltq.lzma.nandspl
+/u-boot.ltq.lzo.nandspl
+/u-boot.ltq.nandspl
 /u-boot.lzma.img
 /u-boot.lzo.img
 
--- a/Makefile
+++ b/Makefile
@@ -599,6 +599,24 @@ $(obj)u-boot.ltq.lzma.sfspl: $(obj)u-boo
 		$(obj)tools/ltq-boot-image -t sfspl -e $(CONFIG_SPL_TEXT_BASE) \
 			-s $(obj)spl/u-boot-spl.bin -u $< -o $@
 
+$(obj)u-boot.ltq.nandspl:	$(obj)u-boot.img $(obj)spl/u-boot-spl.bin
+		$(obj)tools/ltq-boot-image -t nandspl -e $(CONFIG_SPL_TEXT_BASE) \
+			-x $(CONFIG_SYS_NAND_U_BOOT_OFFS) \
+			-p $(CONFIG_SYS_NAND_PAGE_SIZE) \
+			-s $(obj)spl/u-boot-spl.bin -u $< -o $@
+
+$(obj)u-boot.ltq.lzo.nandspl: $(obj)u-boot.lzo.img $(obj)spl/u-boot-spl.bin
+		$(obj)tools/ltq-boot-image -t nandspl -e $(CONFIG_SPL_TEXT_BASE) \
+			-x $(CONFIG_SYS_NAND_U_BOOT_OFFS) \
+			-p $(CONFIG_SYS_NAND_PAGE_SIZE) \
+			-s $(obj)spl/u-boot-spl.bin -u $< -o $@
+
+$(obj)u-boot.ltq.lzma.nandspl: $(obj)u-boot.lzma.img $(obj)spl/u-boot-spl.bin
+		$(obj)tools/ltq-boot-image -t nandspl -e $(CONFIG_SPL_TEXT_BASE) \
+			-x $(CONFIG_SYS_NAND_U_BOOT_OFFS) \
+			-p $(CONFIG_SYS_NAND_PAGE_SIZE) \
+			-s $(obj)spl/u-boot-spl.bin -u $< -o $@
+
 $(obj)u-boot.ltq.norspl: $(obj)u-boot.img $(obj)spl/u-boot-spl.bin
 	cat $(obj)spl/u-boot-spl.bin $< > $@
 
