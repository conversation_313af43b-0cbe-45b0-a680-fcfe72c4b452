From fbdbf2ddf2b34d675d53de679c179788b0604c1a Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Sat, 12 Oct 2013 16:49:53 +0200
Subject: MIPS: add board support for Arcadyan ARV752DPW

Signed-off-by: <PERSON> <<EMAIL>>
Signed-off-by: <PERSON> <<EMAIL>>

--- /dev/null
+++ b/board/arcadyan/arv752dpw/Makefile
@@ -0,0 +1,27 @@
+#
+# Copyright (C) 2000-2011 <PERSON>, DENX Software Engineering, <EMAIL>
+#
+# SPDX-License-Identifier:	GPL-2.0+
+#
+
+include $(TOPDIR)/config.mk
+
+LIB	= $(obj)lib$(BOARD).o
+
+COBJS	= $(BOARD).o
+
+SRCS	:= $(SOBJS:.o=.S) $(COBJS:.o=.c)
+OBJS	:= $(addprefix $(obj),$(COBJS))
+SOBJS	:= $(addprefix $(obj),$(SOBJS))
+
+$(LIB):	$(obj).depend $(OBJS) $(SOBJS)
+	$(call cmd_link_o_target, $(OBJS) $(SOBJS))
+
+#########################################################################
+
+# defines $(obj).depend target
+include $(SRCTREE)/rules.mk
+
+sinclude $(obj).depend
+
+#########################################################################
--- /dev/null
+++ b/board/arcadyan/arv752dpw/arv752dpw.c
@@ -0,0 +1,51 @@
+/*
+ * Copyright (C) 2012 Luka Perkov <<EMAIL>>
+ * Copyright (C) 2013 Oliver Muth <<EMAIL>>
+ *
+ * SPDX-License-Identifier:	GPL-2.0+
+ */
+
+#include <common.h>
+#include <switch.h>
+#include <asm/gpio.h>
+#include <asm/lantiq/eth.h>
+#include <asm/lantiq/reset.h>
+#include <asm/lantiq/chipid.h>
+
+int board_early_init_f(void)
+{
+	return 0;
+}
+
+int checkboard(void)
+{
+	puts("Board: " CONFIG_BOARD_NAME "\n");
+	ltq_chip_print_info();
+
+	return 0;
+}
+
+static const struct ltq_eth_port_config eth_port_config[] = {
+	/* MAC0: Realtek rtl8306 switch */
+	{ 0, 0x0, LTQ_ETH_PORT_SWITCH, PHY_INTERFACE_MODE_RMII },
+};
+
+static const struct ltq_eth_board_config eth_board_config = {
+	.ports = eth_port_config,
+	.num_ports = ARRAY_SIZE(eth_port_config),
+};
+
+int board_eth_init(bd_t *bis)
+{
+	return ltq_eth_initialize(&eth_board_config);
+}
+static struct switch_device rtl8306_dev = {
+	.name = "rtl8306",
+	.cpu_port = 5,
+	.port_mask = 0xF,
+};
+
+int board_switch_init(void)
+{
+	return switch_device_register(&rtl8306_dev);
+}
--- /dev/null
+++ b/board/arcadyan/arv752dpw/config.mk
@@ -0,0 +1,7 @@
+#
+# Copyright (C) 2011-2013 Daniel Schwierzeck, <EMAIL>
+#
+# SPDX-License-Identifier:	GPL-2.0+
+#
+
+PLATFORM_CPPFLAGS += -I$(TOPDIR)/board/$(BOARDDIR)
--- /dev/null
+++ b/board/arcadyan/arv752dpw/ddr_settings.h
@@ -0,0 +1,55 @@
+/*
+ * Copyright (C) 2011-2013 Luka Perkov <<EMAIL>>
+ *
+ * This file has been generated with lantiq_ram_extract_magic.awk script.     
+ *
+ * SPDX-License-Identifier:	GPL-2.0+
+ */
+
+#define MC_DC00_VALUE	0x1B1B
+#define MC_DC01_VALUE	0x0
+#define MC_DC02_VALUE	0x0
+#define MC_DC03_VALUE	0x0
+#define MC_DC04_VALUE	0x0
+#define MC_DC05_VALUE	0x200
+#define MC_DC06_VALUE	0x605
+#define MC_DC07_VALUE	0x303
+#define MC_DC08_VALUE	0x102
+#define MC_DC09_VALUE	0x70A
+#define MC_DC10_VALUE	0x203
+#define MC_DC11_VALUE	0xC02
+#define MC_DC12_VALUE	0x1C8
+#define MC_DC13_VALUE	0x1
+#define MC_DC14_VALUE	0x0
+#define MC_DC15_VALUE	0x134
+#define MC_DC16_VALUE	0xC800
+#define MC_DC17_VALUE	0xD
+#define MC_DC18_VALUE	0x301
+#define MC_DC19_VALUE	0x200
+#define MC_DC20_VALUE	0xA03
+#define MC_DC21_VALUE	0x1400
+#define MC_DC22_VALUE	0x1414
+#define MC_DC23_VALUE	0x0
+#define MC_DC24_VALUE	0x5B
+#define MC_DC25_VALUE	0x0
+#define MC_DC26_VALUE	0x0
+#define MC_DC27_VALUE	0x0
+#define MC_DC28_VALUE	0x510
+#define MC_DC29_VALUE	0x4E20
+#define MC_DC30_VALUE	0x8235
+#define MC_DC31_VALUE	0x0
+#define MC_DC32_VALUE	0x0
+#define MC_DC33_VALUE	0x0
+#define MC_DC34_VALUE	0x0
+#define MC_DC35_VALUE	0x0
+#define MC_DC36_VALUE	0x0
+#define MC_DC37_VALUE	0x0
+#define MC_DC38_VALUE	0x0
+#define MC_DC39_VALUE	0x0
+#define MC_DC40_VALUE	0x0
+#define MC_DC41_VALUE	0x0
+#define MC_DC42_VALUE	0x0
+#define MC_DC43_VALUE	0x0
+#define MC_DC44_VALUE	0x0
+#define MC_DC45_VALUE	0x500
+#define MC_DC46_VALUE	0x0
--- a/boards.cfg
+++ b/boards.cfg
@@ -508,6 +508,9 @@ Active  mips        mips32         danub
 Active  mips        mips32         danube      arcadyan        arv7518pw           arv7518pw_brn                        arv7518pw:SYS_BOOT_BRN                                                                                                            Luka Perkov <<EMAIL>>
 Active  mips        mips32         danube      arcadyan        arv7518pw           arv7518pw_nor                        arv7518pw:SYS_BOOT_NOR                                                                                                            Luka Perkov <<EMAIL>>
 Active  mips        mips32         danube      arcadyan        arv7518pw           arv7518pw_ram                        arv7518pw:SYS_BOOT_RAM                                                                                                            Luka Perkov <<EMAIL>>
+Active  mips        mips32         danube      arcadyan        arv752dpw           arv752dpw_brn                        arv752dpw:SYS_BOOT_BRN                                                                                                            -
+Active  mips        mips32         danube      arcadyan        arv752dpw           arv752dpw_nor                        arv752dpw:SYS_BOOT_NOR                                                                                                            -
+Active  mips        mips32         danube      arcadyan        arv752dpw           arv752dpw_ram                        arv752dpw:SYS_BOOT_RAM                                                                                                            -
 Active  mips        mips32         danube      audiocodes      acmp252             acmp252_nor                          acmp252:SYS_BOOT_NOR                                                                                                              Daniel Golle <<EMAIL>>
 Active  mips        mips32         danube      audiocodes      acmp252             acmp252_ram                          acmp252:SYS_BOOT_RAM                                                                                                              Daniel Golle <<EMAIL>>
 Active  mips        mips32         danube      gigaset         sx76x               gigasx76x_nor                        sx76x:SYS_BOOT_NOR                                                                                                                Luka Perkov <<EMAIL>>
--- /dev/null
+++ b/include/configs/arv752dpw.h
@@ -0,0 +1,69 @@
+/*
+ * Copyright (C) 2012-2013 Luka Perkov <<EMAIL>>
+ *
+ * SPDX-License-Identifier:	GPL-2.0+
+ */
+
+#ifndef __CONFIG_H
+#define __CONFIG_H
+
+#define CONFIG_MACH_TYPE	"ARV752DPW"
+#define CONFIG_IDENT_STRING	" "CONFIG_MACH_TYPE
+#define CONFIG_BOARD_NAME	"Arcadyan ARV752DPW"
+
+/* Configure SoC */
+#define CONFIG_LTQ_SUPPORT_UART		/* Enable ASC and UART */
+
+#define CONFIG_LTQ_SUPPORT_ETHERNET	/* Enable ethernet */
+
+#define CONFIG_LTQ_SUPPORT_NOR_FLASH	/* Have a parallel NOR flash */
+
+#define CONFIG_SYS_BOOTM_LEN		0x1000000	/* 16 MB */
+
+/* Switch devices */
+#define CONFIG_SWITCH_MULTI
+#define CONFIG_SWITCH_RTL8306
+
+/* Environment */
+#if defined(CONFIG_SYS_BOOT_NOR)
+#define CONFIG_ENV_IS_IN_FLASH
+#define CONFIG_ENV_OVERWRITE
+#define CONFIG_ENV_OFFSET		(192 * 1024)
+#define CONFIG_ENV_SECT_SIZE		(64 * 1024)
+#else
+#define CONFIG_ENV_IS_NOWHERE
+#endif
+
+#define CONFIG_ENV_SIZE			(8 * 1024)
+#define CONFIG_LOADADDR			CONFIG_SYS_LOAD_ADDR
+
+/* Brnboot loadable image */
+#if defined(CONFIG_SYS_BOOT_BRN)
+#define CONFIG_SYS_TEXT_BASE		0x80002000
+#define CONFIG_SKIP_LOWLEVEL_INIT
+#define CONFIG_SYS_DISABLE_CACHE
+#define CONFIG_ENV_OVERWRITE 1
+#endif
+
+/* Console */
+#define CONFIG_LTQ_ADVANCED_CONSOLE
+#define CONFIG_BAUDRATE			115200
+#define CONFIG_CONSOLE_ASC		1
+#define CONFIG_CONSOLE_DEV		"ttyLTQ1"
+
+/* Pull in default board configs for Lantiq XWAY Danube */
+#include <asm/lantiq/config.h>
+#include <asm/arch/config.h>
+
+/* Pull in default OpenWrt configs for Lantiq SoC */
+#include "openwrt-lantiq-common.h"
+
+#define CONFIG_ENV_UPDATE_UBOOT_NOR		\
+	"update-uboot-nor=run load-uboot-nor write-uboot-nor\0"
+
+#define CONFIG_EXTRA_ENV_SETTINGS	\
+	CONFIG_ENV_LANTIQ_DEFAULTS	\
+	CONFIG_ENV_UPDATE_UBOOT_NOR	\
+	"kernel_addr=0xB0040000\0"
+
+#endif /* __CONFIG_H */
