From 5a6d8045190c887c7f65e65fb1bfc8854774c458 Mon Sep 17 00:00:00 2001
From: <PERSON> <daniel.schwier<PERSON><EMAIL>>
Date: Sun, 13 Oct 2013 15:40:07 +0200
Subject: sf: fix sector layout of S25FL256S_256K and S25FL512S_256K

Signed-off-by: <PERSON> <<EMAIL>>

--- a/drivers/mtd/spi/sf_probe.c
+++ b/drivers/mtd/spi/sf_probe.c
@@ -80,9 +80,9 @@ static const struct spi_flash_params spi
 	{"S25FL032P",	   0x010215, 0x4d00,    64 * 1024,    64,	             0},
 	{"S25FL064P",	   0x010216, 0x4d00,    64 * 1024,   128,	             0},
 	{"S25FL128S_64K",  0x012018, 0x4d01,    64 * 1024,   256,		     0},
-	{"S25FL256S_256K", 0x010219, 0x4d00,    64 * 1024,   512,	             0},
+	{"S25FL256S_256K", 0x010219, 0x4d00,   256 * 1024,   128,	             0},
 	{"S25FL256S_64K",  0x010219, 0x4d01,    64 * 1024,   512,	             0},
-	{"S25FL512S_256K", 0x010220, 0x4d00,    64 * 1024,  1024,	             0},
+	{"S25FL512S_256K", 0x010220, 0x4d00,   256 * 1024,   256,	             0},
 	{"S25FL512S_64K",  0x010220, 0x4d01,    64 * 1024,  1024,	             0},
 #endif
 #ifdef CONFIG_SPI_FLASH_STMICRO		/* STMICRO */
