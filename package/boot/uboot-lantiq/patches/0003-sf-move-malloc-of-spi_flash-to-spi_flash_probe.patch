From 36b7400465fe2339f1c78274b3fd258ade3a4c00 Mon Sep 17 00:00:00 2001
From: <PERSON> <daniel.schwier<PERSON><EMAIL>>
Date: Sat, 12 Oct 2013 21:30:07 +0200
Subject: sf: move malloc of spi_flash to spi_flash_probe()

Signed-off-by: <PERSON> <<EMAIL>>

--- a/drivers/mtd/spi/sf_probe.c
+++ b/drivers/mtd/spi/sf_probe.c
@@ -153,11 +153,10 @@ static const struct spi_flash_params spi
 	 */
 };
 
-static struct spi_flash *spi_flash_validate_params(struct spi_slave *spi,
+static int spi_flash_validate_params(struct spi_flash *flash,
 		u8 *idcode)
 {
 	const struct spi_flash_params *params;
-	struct spi_flash *flash;
 	int i;
 	u16 jedec = idcode[1] << 8 | idcode[2];
 	u16 ext_jedec = idcode[3] << 8 | idcode[4];
@@ -179,20 +178,12 @@ static struct spi_flash *spi_flash_valid
 		debug("SF: Unsupported flash IDs: ");
 		debug("manuf %02x, jedec %04x, ext_jedec %04x\n",
 		       idcode[0], jedec, ext_jedec);
-		return NULL;
-	}
-
-	flash = malloc(sizeof(*flash));
-	if (!flash) {
-		debug("SF: Failed to allocate spi_flash\n");
-		return NULL;
+		return -1;
 	}
-	memset(flash, '\0', sizeof(*flash));
 
 	/* Assign spi data */
-	flash->spi = spi;
 	flash->name = params->name;
-	flash->memory_map = spi->memory_map;
+	flash->memory_map = flash->spi->memory_map;
 
 	/* Assign spi_flash ops */
 	flash->write = spi_flash_cmd_write_ops;
@@ -239,7 +230,7 @@ static struct spi_flash *spi_flash_valid
 		if (spi_flash_read_common(flash, &flash->bank_read_cmd, 1,
 					  &curr_bank, 1)) {
 			debug("SF: fail to read bank addr register\n");
-			return NULL;
+			return -1;
 		}
 		flash->bank_curr = curr_bank;
 	} else {
@@ -254,7 +245,7 @@ static struct spi_flash *spi_flash_valid
 		spi_flash_cmd_write_status(flash, 0);
 #endif
 
-	return flash;
+	return 0;
 }
 
 #ifdef CONFIG_OF_CONTROL
@@ -289,15 +280,22 @@ struct spi_flash *spi_flash_probe(unsign
 		unsigned int max_hz, unsigned int spi_mode)
 {
 	struct spi_slave *spi;
-	struct spi_flash *flash = NULL;
+	struct spi_flash *flash;
 	u8 idcode[5];
 	int ret;
 
+	flash = malloc(sizeof(*flash));
+	if (!flash) {
+		debug("SF: Failed to allocate spi_flash\n");
+		return NULL;
+	}
+	memset(flash, 0, sizeof(*flash));
+
 	/* Setup spi_slave */
 	spi = spi_setup_slave(bus, cs, max_hz, spi_mode);
 	if (!spi) {
 		debug("SF: Failed to set up slave\n");
-		return NULL;
+		goto err_setup;
 	}
 
 	/* Claim spi bus */
@@ -320,8 +318,9 @@ struct spi_flash *spi_flash_probe(unsign
 #endif
 
 	/* Validate params from spi_flash_params table */
-	flash = spi_flash_validate_params(spi, idcode);
-	if (!flash)
+	flash->spi = spi;
+	ret = spi_flash_validate_params(flash, idcode);
+	if (ret)
 		goto err_read_id;
 
 #ifdef CONFIG_OF_CONTROL
@@ -355,6 +354,9 @@ err_read_id:
 	spi_release_bus(spi);
 err_claim_bus:
 	spi_free_slave(spi);
+err_setup:
+	free(flash);
+
 	return NULL;
 }
 
