From 6fb5f86b094756d94de8abe7425e3d290ff22dd2 Mon Sep 17 00:00:00 2001
From: <PERSON> <daniel.schwier<PERSON><EMAIL>>
Date: Sun, 13 Oct 2013 15:09:28 +0200
Subject: sf: make calculatiom of address bytes completely configurable

Signed-off-by: <PERSON> <<EMAIL>>

--- a/drivers/mtd/spi/sf_ops.c
+++ b/drivers/mtd/spi/sf_ops.c
@@ -15,12 +15,17 @@
 
 #include "sf_internal.h"
 
-static void spi_flash_addr(u32 addr, u8 *cmd)
+static void spi_flash_addr(const struct spi_flash *flash, u32 addr, u8 *cmd)
 {
 	/* cmd[0] is actual command */
-	cmd[1] = addr >> 16;
-	cmd[2] = addr >> 8;
-	cmd[3] = addr >> 0;
+	cmd[1] = addr >> (flash->addr_width * 8 - 8);
+	cmd[2] = addr >> (flash->addr_width * 8 - 16);
+	cmd[3] = addr >> (flash->addr_width * 8 - 24);
+}
+
+static int spi_flash_cmdsz(const struct spi_flash *flash)
+{
+	return 1 + flash->addr_width;
 }
 
 int spi_flash_cmd_write_status(struct spi_flash *flash, u8 sr)
@@ -158,7 +163,7 @@ int spi_flash_write_common(struct spi_fl
 int spi_flash_cmd_erase_ops(struct spi_flash *flash, u32 offset, size_t len)
 {
 	u32 erase_size;
-	u8 cmd[4];
+	u8 cmd[4], cmd_len;
 	int ret = -1;
 
 	erase_size = flash->erase_size;
@@ -180,12 +185,13 @@ int spi_flash_cmd_erase_ops(struct spi_f
 		if (ret < 0)
 			goto done;
 #endif
-		spi_flash_addr(offset, cmd);
+		spi_flash_addr(flash, offset, cmd);
+		cmd_len = spi_flash_cmdsz(flash);
 
 		debug("SF: erase %2x %2x %2x %2x (%x)\n", cmd[0], cmd[1],
 		      cmd[2], cmd[3], offset);
 
-		ret = spi_flash_write_common(flash, cmd, sizeof(cmd), NULL, 0);
+		ret = spi_flash_write_common(flash, cmd, cmd_len, NULL, 0);
 		if (ret < 0) {
 			debug("SF: erase failed\n");
 			goto done;
@@ -206,7 +212,7 @@ int spi_flash_cmd_write_ops(struct spi_f
 {
 	unsigned long byte_addr, page_size;
 	size_t chunk_len, actual;
-	u8 cmd[4];
+	u8 cmd[4], cmd_len;
 	int ret = -1;
 
 	ret = spi_claim_bus(flash->spi);
@@ -230,12 +236,13 @@ int spi_flash_cmd_write_ops(struct spi_f
 		if (flash->spi->max_write_size)
 			chunk_len = min(chunk_len, flash->spi->max_write_size);
 
-		spi_flash_addr(offset, cmd);
+		spi_flash_addr(flash, offset, cmd);
+		cmd_len = spi_flash_cmdsz(flash);
 
 		debug("PP: 0x%p => cmd = { 0x%02x 0x%02x%02x%02x } chunk_len = %zu\n",
 		      buf + actual, cmd[0], cmd[1], cmd[2], cmd[3], chunk_len);
 
-		ret = spi_flash_write_common(flash, cmd, sizeof(cmd),
+		ret = spi_flash_write_common(flash, cmd, cmd_len,
 					buf + actual, chunk_len);
 		if (ret < 0) {
 			debug("SF: write failed\n");
@@ -269,7 +276,7 @@ int spi_flash_read_common(struct spi_fla
 int spi_flash_cmd_read_ops(struct spi_flash *flash, u32 offset,
 		size_t len, void *data)
 {
-	u8 cmd[5], bank_sel = 0;
+	u8 cmd[5], cmd_len, bank_sel = 0;
 	u32 remain_len, read_len;
 	int ret = -1;
 
@@ -288,7 +295,6 @@ int spi_flash_cmd_read_ops(struct spi_fl
 	}
 
 	cmd[0] = CMD_READ_ARRAY_FAST;
-	cmd[4] = 0x00;
 
 	while (len) {
 #ifdef CONFIG_SPI_FLASH_BAR
@@ -306,9 +312,11 @@ int spi_flash_cmd_read_ops(struct spi_fl
 		else
 			read_len = remain_len;
 
-		spi_flash_addr(offset, cmd);
+		spi_flash_addr(flash, offset, cmd);
+		cmd_len = spi_flash_cmdsz(flash);
+		cmd[cmd_len] = 0x00;
 
-		ret = spi_flash_read_common(flash, cmd, sizeof(cmd),
+		ret = spi_flash_read_common(flash, cmd, cmd_len + 1,
 							data, read_len);
 		if (ret < 0) {
 			debug("SF: read failed\n");
--- a/drivers/mtd/spi/sf_probe.c
+++ b/drivers/mtd/spi/sf_probe.c
@@ -218,6 +218,9 @@ static int spi_flash_validate_params(str
 		flash->poll_cmd = CMD_FLAG_STATUS;
 #endif
 
+	/* Configure default 3-byte addressing */
+	flash->addr_width = 3;
+
 	/* Configure the BAR - discover bank cmds and read current bank */
 #ifdef CONFIG_SPI_FLASH_BAR
 	u8 curr_bank = 0;
--- a/include/spi_flash.h
+++ b/include/spi_flash.h
@@ -57,6 +57,7 @@ struct spi_flash {
 #endif
 	u8 poll_cmd;
 	u8 erase_cmd;
+	u8 addr_width;
 
 	void *memory_map;
 	int (*read)(struct spi_flash *flash, u32 offset, size_t len, void *buf);
