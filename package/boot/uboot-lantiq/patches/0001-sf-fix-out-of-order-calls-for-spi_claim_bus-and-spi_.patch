From 909840ef844013379e5ec399c1e76c65d1a6eb1d Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Sat, 12 Oct 2013 21:09:47 +0200
Subject: sf: fix out-of-order calls for spi_claim_bus and spi_release_bus

Signed-off-by: <PERSON> <<EMAIL>>

--- a/drivers/mtd/spi/sf_ops.c
+++ b/drivers/mtd/spi/sf_ops.c
@@ -132,12 +132,6 @@ int spi_flash_write_common(struct spi_fl
 	if (buf == NULL)
 		timeout = SPI_FLASH_PAGE_ERASE_TIMEOUT;
 
-	ret = spi_claim_bus(flash->spi);
-	if (ret) {
-		debug("SF: unable to claim SPI bus\n");
-		return ret;
-	}
-
 	ret = spi_flash_cmd_write_enable(flash);
 	if (ret < 0) {
 		debug("SF: enabling write failed\n");
@@ -158,8 +152,6 @@ int spi_flash_write_common(struct spi_fl
 		return ret;
 	}
 
-	spi_release_bus(spi);
-
 	return ret;
 }
 
@@ -175,12 +167,18 @@ int spi_flash_cmd_erase_ops(struct spi_f
 		return -1;
 	}
 
+	ret = spi_claim_bus(flash->spi);
+	if (ret) {
+		debug("SF: unable to claim SPI bus\n");
+		return ret;
+	}
+
 	cmd[0] = flash->erase_cmd;
 	while (len) {
 #ifdef CONFIG_SPI_FLASH_BAR
 		ret = spi_flash_bank(flash, offset);
 		if (ret < 0)
-			return ret;
+			goto done;
 #endif
 		spi_flash_addr(offset, cmd);
 
@@ -190,13 +188,16 @@ int spi_flash_cmd_erase_ops(struct spi_f
 		ret = spi_flash_write_common(flash, cmd, sizeof(cmd), NULL, 0);
 		if (ret < 0) {
 			debug("SF: erase failed\n");
-			break;
+			goto done;
 		}
 
 		offset += erase_size;
 		len -= erase_size;
 	}
 
+done:
+	spi_release_bus(flash->spi);
+
 	return ret;
 }
 
@@ -208,6 +209,12 @@ int spi_flash_cmd_write_ops(struct spi_f
 	u8 cmd[4];
 	int ret = -1;
 
+	ret = spi_claim_bus(flash->spi);
+	if (ret) {
+		debug("SF: unable to claim SPI bus\n");
+		return ret;
+	}
+
 	page_size = flash->page_size;
 
 	cmd[0] = CMD_PAGE_PROGRAM;
@@ -215,7 +222,7 @@ int spi_flash_cmd_write_ops(struct spi_f
 #ifdef CONFIG_SPI_FLASH_BAR
 		ret = spi_flash_bank(flash, offset);
 		if (ret < 0)
-			return ret;
+			goto done;
 #endif
 		byte_addr = offset % page_size;
 		chunk_len = min(len - actual, page_size - byte_addr);
@@ -232,12 +239,15 @@ int spi_flash_cmd_write_ops(struct spi_f
 					buf + actual, chunk_len);
 		if (ret < 0) {
 			debug("SF: write failed\n");
-			break;
+			goto done;
 		}
 
 		offset += chunk_len;
 	}
 
+done:
+	spi_release_bus(flash->spi);
+
 	return ret;
 }
 
@@ -247,20 +257,12 @@ int spi_flash_read_common(struct spi_fla
 	struct spi_slave *spi = flash->spi;
 	int ret;
 
-	ret = spi_claim_bus(flash->spi);
-	if (ret) {
-		debug("SF: unable to claim SPI bus\n");
-		return ret;
-	}
-
 	ret = spi_flash_cmd_read(spi, cmd, cmd_len, data, data_len);
 	if (ret < 0) {
 		debug("SF: read cmd failed\n");
 		return ret;
 	}
 
-	spi_release_bus(spi);
-
 	return ret;
 }
 
@@ -271,6 +273,12 @@ int spi_flash_cmd_read_ops(struct spi_fl
 	u32 remain_len, read_len;
 	int ret = -1;
 
+	ret = spi_claim_bus(flash->spi);
+	if (ret) {
+		debug("SF: unable to claim SPI bus\n");
+		return ret;
+	}
+
 	/* Handle memory-mapped SPI */
 	if (flash->memory_map) {
 		spi_xfer(flash->spi, 0, NULL, NULL, SPI_XFER_MMAP);
@@ -289,7 +297,7 @@ int spi_flash_cmd_read_ops(struct spi_fl
 		ret = spi_flash_cmd_bankaddr_write(flash, bank_sel);
 		if (ret) {
 			debug("SF: fail to set bank%d\n", bank_sel);
-			return ret;
+			goto done;
 		}
 #endif
 		remain_len = (SPI_FLASH_16MB_BOUN * (bank_sel + 1)) - offset;
@@ -304,7 +312,7 @@ int spi_flash_cmd_read_ops(struct spi_fl
 							data, read_len);
 		if (ret < 0) {
 			debug("SF: read failed\n");
-			break;
+			goto done;
 		}
 
 		offset += read_len;
@@ -312,6 +320,9 @@ int spi_flash_cmd_read_ops(struct spi_fl
 		data += read_len;
 	}
 
+done:
+	spi_release_bus(flash->spi);
+
 	return ret;
 }
 
