--- /dev/null
+++ b/board/lantiq/easy50712/Makefile
@@ -0,0 +1,27 @@
+#
+# Copyright (C) 2000-2011 <PERSON>, DENX Software Engineering, <EMAIL>
+#
+# SPDX-License-Identifier:	GPL-2.0+
+#
+
+include $(TOPDIR)/config.mk
+
+LIB	= $(obj)lib$(BOARD).o
+
+COBJS	= $(BOARD).o
+
+SRCS	:= $(SOBJS:.o=.S) $(COBJS:.o=.c)
+OBJS	:= $(addprefix $(obj),$(COBJS))
+SOBJS	:= $(addprefix $(obj),$(SOBJS))
+
+$(LIB):	$(obj).depend $(OBJS) $(SOBJS)
+	$(call cmd_link_o_target, $(OBJS) $(SOBJS))
+
+#########################################################################
+
+# defines $(obj).depend target
+include $(SRCTREE)/rules.mk
+
+sinclude $(obj).depend
+
+#########################################################################
--- /dev/null
+++ b/board/lantiq/easy50712/config.mk
@@ -0,0 +1,7 @@
+#
+# Copyright (C) 2011-2013 Daniel Schwierzeck, <EMAIL>
+#
+# SPDX-License-Identifier:	GPL-2.0+
+#
+
+PLATFORM_CPPFLAGS += -I$(TOPDIR)/board/$(BOARDDIR)
--- /dev/null
+++ b/board/lantiq/easy50712/ddr_settings.h
@@ -0,0 +1,54 @@
+/*
+ * Copyright (C) 2007-2010 Lantiq Deutschland GmbH
+ * Copyright (C) 2011-2013 Daniel Schwierzeck, <EMAIL>
+ *
+ * SPDX-License-Identifier:	GPL-2.0+
+ */
+
+#define MC_DC00_VALUE	0x1B1B
+#define MC_DC01_VALUE	0x0
+#define MC_DC02_VALUE	0x0
+#define MC_DC03_VALUE	0x0
+#define MC_DC04_VALUE	0x0
+#define MC_DC05_VALUE	0x200
+#define MC_DC06_VALUE	0x605
+#define MC_DC07_VALUE	0x303
+#define MC_DC08_VALUE	0x102
+#define MC_DC09_VALUE	0x70a
+#define MC_DC10_VALUE	0x203
+#define MC_DC11_VALUE	0xc02
+#define MC_DC12_VALUE	0x1C8
+#define MC_DC13_VALUE	0x1
+#define MC_DC14_VALUE	0x0
+#define MC_DC15_VALUE	0x13c
+#define MC_DC16_VALUE	0xC800
+#define MC_DC17_VALUE	0xd
+#define MC_DC18_VALUE	0x300
+#define MC_DC19_VALUE	0x200
+#define MC_DC20_VALUE	0xA04
+#define MC_DC21_VALUE	0xd00
+#define MC_DC22_VALUE	0xd0d
+#define MC_DC23_VALUE	0x0
+#define MC_DC24_VALUE	0x62
+#define MC_DC25_VALUE	0x0
+#define MC_DC26_VALUE	0x0
+#define MC_DC27_VALUE	0x0
+#define MC_DC28_VALUE	0x510
+#define MC_DC29_VALUE	0x2d89
+#define MC_DC30_VALUE	0x8300
+#define MC_DC31_VALUE	0x0
+#define MC_DC32_VALUE	0x0
+#define MC_DC33_VALUE	0x0
+#define MC_DC34_VALUE	0x0
+#define MC_DC35_VALUE	0x0
+#define MC_DC36_VALUE	0x0
+#define MC_DC37_VALUE	0x0
+#define MC_DC38_VALUE	0x0
+#define MC_DC39_VALUE	0x0
+#define MC_DC40_VALUE	0x0
+#define MC_DC41_VALUE	0x0
+#define MC_DC42_VALUE	0x0
+#define MC_DC43_VALUE	0x0
+#define MC_DC44_VALUE	0x0
+#define MC_DC45_VALUE	0x500
+#define MC_DC46_VALUE	0x0
--- /dev/null
+++ b/board/lantiq/easy50712/easy50712.c
@@ -0,0 +1,112 @@
+/*
+ * Copyright (C) 2010 Thomas Langer <<EMAIL>>
+ * Copyright (C) 2011-2013 Daniel Schwierzeck, <EMAIL>
+ *
+ * SPDX-License-Identifier:	GPL-2.0+
+ */
+
+#include <common.h>
+#include <switch.h>
+#include <spi.h>
+#include <asm/gpio.h>
+#include <asm/lantiq/eth.h>
+#include <asm/lantiq/reset.h>
+#include <asm/lantiq/chipid.h>
+
+static void gpio_init(void)
+{
+	/* SPI/CS output (low-active) for serial flash */
+	gpio_direction_output(22, 1);
+
+	/* EBU.FL_CS1 as output for NAND CE */
+	gpio_set_altfunc(23, GPIO_ALTSEL_SET, GPIO_ALTSEL_CLR, GPIO_DIR_OUT);
+	/* EBU.FL_A23 as output for NAND CLE */
+	gpio_set_altfunc(24, GPIO_ALTSEL_SET, GPIO_ALTSEL_CLR, GPIO_DIR_OUT);
+	/* EBU.FL_A24 as output for NAND ALE */
+	gpio_set_altfunc(13, GPIO_ALTSEL_SET, GPIO_ALTSEL_CLR, GPIO_DIR_OUT);
+
+	/* enable CLK_OUT2 for external switch */
+	gpio_set_altfunc(3, GPIO_ALTSEL_SET, GPIO_ALTSEL_CLR, GPIO_DIR_OUT);
+}
+
+int board_early_init_f(void)
+{
+	gpio_init();
+
+	return 0;
+}
+
+int checkboard(void)
+{
+	puts("Board: " CONFIG_BOARD_NAME "\n");
+	ltq_chip_print_info();
+
+	return 0;
+}
+
+static const struct ltq_eth_port_config eth_port_config[] = {
+	/* MAC0: Lantiq ADM6996I switch */
+	{ 0, 0x0, LTQ_ETH_PORT_SWITCH, PHY_INTERFACE_MODE_RMII },
+};
+
+static const struct ltq_eth_board_config eth_board_config = {
+	.ports = eth_port_config,
+	.num_ports = ARRAY_SIZE(eth_port_config),
+};
+
+int board_eth_init(bd_t *bis)
+{
+	return ltq_eth_initialize(&eth_board_config);
+}
+
+static struct switch_device adm6996i_dev = {
+	.name = "adm6996i",
+	.cpu_port = 5,
+	.port_mask = 0xF,
+};
+
+int board_switch_init(void)
+{
+	/* Deactivate HRST line to release reset of ADM6996I switch */
+	ltq_reset_once(LTQ_RESET_HARD, 200000);
+
+	/* ADM6996I needs some time to come out of reset */
+	__udelay(50000);
+
+	return switch_device_register(&adm6996i_dev);
+}
+
+int spi_cs_is_valid(unsigned int bus, unsigned int cs)
+{
+	if (bus)
+		return 0;
+
+	switch (cs) {
+	case 2:
+		return 1;
+	default:
+		return 0;
+	}
+}
+
+void spi_cs_activate(struct spi_slave *slave)
+{
+	switch (slave->cs) {
+	case 2:
+		gpio_set_value(22, 0);
+		break;
+	default:
+		break;
+	}
+}
+
+void spi_cs_deactivate(struct spi_slave *slave)
+{
+	switch (slave->cs) {
+	case 2:
+		gpio_set_value(22, 1);
+		break;
+	default:
+		break;
+	}
+}
--- a/boards.cfg
+++ b/boards.cfg
@@ -502,6 +502,9 @@ Active  mips        mips32         au1x0
 Active  mips        mips32         au1x00      -               dbau1x00            dbau1550                             dbau1x00:DBAU1550                                                                                                                 Thomas Lange <<EMAIL>>
 Active  mips        mips32         au1x00      -               dbau1x00            dbau1550_el                          dbau1x00:DBAU1550,SYS_LITTLE_ENDIAN                                                                                               Thomas Lange <<EMAIL>>
 Active  mips        mips32         au1x00      -               pb1x00              pb1000                               pb1x00:PB1000                                                                                                                     -
+Active  mips        mips32         danube      lantiq          easy50712           easy50712_nor                        easy50712:SYS_BOOT_NOR                                                                                                            Daniel Schwierzeck <<EMAIL>>
+Active  mips        mips32         danube      lantiq          easy50712           easy50712_norspl                     easy50712:SYS_BOOT_NORSPL                                                                                                         Daniel Schwierzeck <<EMAIL>>
+Active  mips        mips32         danube      lantiq          easy50712           easy50712_ram                        easy50712:SYS_BOOT_RAM                                                                                                            Daniel Schwierzeck <<EMAIL>>
 Active  mips        mips32         incaip      -               incaip              incaip                               -                                                                                                                                 Wolfgang Denk <<EMAIL>>
 Active  mips        mips32         incaip      -               incaip              incaip_100MHz                        incaip:CPU_CLOCK_RATE=100000000                                                                                                   Wolfgang Denk <<EMAIL>>
 Active  mips        mips32         incaip      -               incaip              incaip_133MHz                        incaip:CPU_CLOCK_RATE=133000000                                                                                                   Wolfgang Denk <<EMAIL>>
--- /dev/null
+++ b/include/configs/easy50712.h
@@ -0,0 +1,79 @@
+/*
+ * Copyright (C) 2011-2013 Daniel Schwierzeck, <EMAIL>
+ *
+ * SPDX-License-Identifier:	GPL-2.0+
+ */
+
+#ifndef __CONFIG_H
+#define __CONFIG_H
+
+#define CONFIG_MACH_TYPE	"EASY50712"
+#define CONFIG_IDENT_STRING	" "CONFIG_MACH_TYPE
+#define CONFIG_BOARD_NAME	"Lantiq EASY50712 Danube Reference Board"
+
+/* Configure SoC */
+#define CONFIG_LTQ_SUPPORT_UART		/* Enable ASC and UART */
+
+#define CONFIG_LTQ_SUPPORT_ETHERNET	/* Enable ethernet */
+
+#define CONFIG_LTQ_SUPPORT_NOR_FLASH	/* Have a parallel NOR flash */
+
+#define CONFIG_LTQ_SUPPORT_SPI_FLASH
+#define CONFIG_SPI_FLASH_ATMEL		/* Have an AT45DB321D serial flash */
+
+#define CONFIG_LTQ_SUPPORT_NAND_FLASH
+
+#define CONFIG_LTQ_SUPPORT_SPL_NOR_FLASH	/* Build NOR flash SPL */
+
+#define CONFIG_LTQ_SPL_COMP_LZO
+#define CONFIG_LTQ_SPL_CONSOLE
+
+/* Switch devices */
+#define CONFIG_SWITCH_MULTI
+#define CONFIG_SWITCH_ADM6996I
+
+/* Environment */
+#define CONFIG_ENV_SPI_BUS		0
+#define CONFIG_ENV_SPI_CS		2
+#define CONFIG_ENV_SPI_MAX_HZ		20000000
+#define CONFIG_ENV_SPI_MODE		0
+
+#if defined(CONFIG_SYS_BOOT_NOR)
+#define CONFIG_ENV_IS_IN_FLASH
+#define CONFIG_ENV_OVERWRITE
+#define CONFIG_ENV_OFFSET		(256 * 1024)
+#define CONFIG_ENV_SECT_SIZE		(64 * 1024)
+#elif defined(CONFIG_SYS_BOOT_NORSPL)
+#define CONFIG_ENV_IS_IN_FLASH
+#define CONFIG_ENV_OVERWRITE
+#define CONFIG_ENV_OFFSET		(128 * 1024)
+#define CONFIG_ENV_SECT_SIZE		(64 * 1024)
+#else
+#define CONFIG_ENV_IS_NOWHERE
+#endif
+
+#define CONFIG_ENV_SIZE			(8 * 1024)
+
+#define CONFIG_LOADADDR			CONFIG_SYS_LOAD_ADDR
+
+/* Console */
+#define CONFIG_LTQ_ADVANCED_CONSOLE
+#define CONFIG_BAUDRATE			115200
+#define CONFIG_CONSOLE_ASC		1
+#define CONFIG_CONSOLE_DEV		"ttyLTQ1"
+
+/* Pull in default board configs for Lantiq XWAY Danube */
+#include <asm/lantiq/config.h>
+#include <asm/arch/config.h>
+
+/* Pull in default OpenWrt configs for Lantiq SoC */
+#include "openwrt-lantiq-common.h"
+
+#define CONFIG_ENV_UPDATE_UBOOT_NOR					\
+	"update-uboot-nor=run load-uboot-norspl-lzo write-uboot-nor\0"
+
+#define CONFIG_EXTRA_ENV_SETTINGS	\
+	CONFIG_ENV_LANTIQ_DEFAULTS	\
+	CONFIG_ENV_UPDATE_UBOOT_NOR
+
+#endif /* __CONFIG_H */
