--- a/include/configs/goflexhome.h
+++ b/include/configs/goflexhome.h
@@ -60,17 +60,15 @@
  */
 #define CONFIG_BOOTCOMMAND \
 	"setenv bootargs ${console} ${mtdparts} ${bootargs_root}; " \
-	"ubi part root; " \
-	"ubifsmount ubi:root; " \
-	"ubifsload 0x800000 ${kernel}; " \
+	"ubi part ubi; " \
+	"ubi read 0x800000 kernel; " \
 	"bootm 0x800000"
 
 #define CONFIG_EXTRA_ENV_SETTINGS \
 	"console=console=ttyS0,115200\0" \
 	"mtdids=nand0=orion_nand\0" \
-	"mtdparts="CONFIG_MTDPARTS_DEFAULT \
-	"kernel=/boot/uImage\0" \
-	"bootargs_root=ubi.mtd=root root=ubi0:root rootfstype=ubifs ro\0"
+	"mtdparts="CONFIG_MTDPARTS_DEFAULT "\0" \
+	"bootargs_root=\0"
 
 /*
  * Ethernet Driver configuration
--- a/configs/goflexhome_defconfig
+++ b/configs/goflexhome_defconfig
@@ -28,7 +28,7 @@ CONFIG_CMD_FAT=y
 CONFIG_CMD_JFFS2=y
 CONFIG_CMD_MTDPARTS=y
 CONFIG_MTDIDS_DEFAULT="nand0=orion_nand"
-CONFIG_MTDPARTS_DEFAULT="mtdparts=orion_nand:1m(uboot),6M(uImage),-(root)"
+CONFIG_MTDPARTS_DEFAULT="mtdparts=orion_nand:1m(uboot),255m(ubi)"
 CONFIG_CMD_UBI=y
 CONFIG_ISO_PARTITION=y
 CONFIG_OF_CONTROL=y
