--- a/arch/arm/mach-kirkwood/Kconfig
+++ b/arch/arm/mach-kirkwood/Kconfig
@@ -107,4 +107,7 @@ source "board/alliedtelesis/SBx81LIFXCAT
 source "board/Marvell/db-88f6281-bp/Kconfig"
 source "board/checkpoint/l-50/Kconfig"
 
+config SECOND_STAGE
+	bool "OpenWrt second stage hack"
+
 endif
--- a/include/configs/dockstar.h
+++ b/include/configs/dockstar.h
@@ -60,4 +60,6 @@
  * File system
  */
 
+#include "openwrt-kirkwood-common.h"
+
 #endif /* _CONFIG_DOCKSTAR_H */
--- a/include/configs/ib62x0.h
+++ b/include/configs/ib62x0.h
@@ -77,4 +77,6 @@
 #define CONFIG_RTC_MV
 #endif /* CONFIG_CMD_DATE */
 
+#include "openwrt-kirkwood-common.h"
+
 #endif /* _CONFIG_IB62x0_H */
--- a/include/configs/iconnect.h
+++ b/include/configs/iconnect.h
@@ -67,4 +67,6 @@
  * File system
  */
 
+#include "openwrt-kirkwood-common.h"
+
 #endif /* _CONFIG_ICONNECT_H */
--- a/include/configs/l-50.h
+++ b/include/configs/l-50.h
@@ -12,6 +12,8 @@
 #ifndef _CONFIG_L50_H
 #define _CONFIG_L50_H
 
+#include "openwrt-kirkwood-common.h"
+
 /*
  * High Level Configuration Options (easy to change)
  */
--- /dev/null
+++ b/include/configs/openwrt-kirkwood-common.h
@@ -0,0 +1,31 @@
+/*
+ * Copyright (C) 2013 Luka Perkov <<EMAIL>>
+ *
+ * SPDX-License-Identifier:	GPL-2.0+
+ */
+
+#ifndef __OPENWRT_KIRKWOOD_COMMON_H
+#define __OPENWRT_KIRKWOOD_COMMON_H
+
+/* Ethernet */
+#if defined(CONFIG_CMD_NET)
+#define CONFIG_SERVERIP		***********
+#define CONFIG_IPADDR		***********
+#endif
+
+/* second stage loader */
+#if defined(CONFIG_SECOND_STAGE)
+#undef CONFIG_ENV_IS_IN_NAND
+#undef CONFIG_ENV_SECT_SIZE
+#define CONFIG_ENV_IS_NOWHERE
+#endif
+
+/* Various */
+#define CONFIG_BZIP2
+
+/* Unnecessary */
+#undef CONFIG_BOOTM_NETBSD
+#undef CONFIG_BOOTM_PLAN9
+#undef CONFIG_BOOTM_RTEMS
+
+#endif /* __OPENWRT_KIRKWOOD_COMMON_H */
--- a/include/configs/pogo_e02.h
+++ b/include/configs/pogo_e02.h
@@ -66,4 +66,6 @@
  * File system
  */
 
+#include "openwrt-kirkwood-common.h"
+
 #endif /* _CONFIG_POGO_E02_H */
--- a/include/configs/goflexhome.h
+++ b/include/configs/goflexhome.h
@@ -85,4 +85,6 @@
 #define CONFIG_SYS_ATA_IDE0_OFFSET      MV_SATA_PORT0_OFFSET
 #endif /*CONFIG_MVSATA_IDE*/
 
+#include "openwrt-kirkwood-common.h"
+
 #endif /* _CONFIG_GOFLEXHOME_H */
--- a/include/configs/nsa310.h
+++ b/include/configs/nsa310.h
@@ -100,4 +100,6 @@
 #define CONFIG_RTC_MV
 #endif /* CONFIG_CMD_DATE */
 
+#include "openwrt-kirkwood-common.h"
+
 #endif /* _CONFIG_NSA310_H */
--- a/configs/dockstar_defconfig
+++ b/configs/dockstar_defconfig
@@ -38,3 +38,8 @@ CONFIG_SYS_NS16550=y
 CONFIG_USB=y
 CONFIG_USB_EHCI_HCD=y
 CONFIG_USB_STORAGE=y
+CONFIG_CMD_BOOTZ=y
+CONFIG_FIT=y
+CONFIG_FIT_VERBOSE=y
+CONFIG_LZMA=y
+CONFIG_LZO=y
--- a/configs/goflexhome_defconfig
+++ b/configs/goflexhome_defconfig
@@ -49,3 +49,8 @@ CONFIG_USB=y
 CONFIG_DM_USB=y
 CONFIG_USB_EHCI_HCD=y
 CONFIG_USB_STORAGE=y
+CONFIG_CMD_BOOTZ=y
+CONFIG_FIT=y
+CONFIG_FIT_VERBOSE=y
+CONFIG_LZMA=y
+CONFIG_LZO=y
--- a/configs/ib62x0_defconfig
+++ b/configs/ib62x0_defconfig
@@ -43,4 +43,7 @@ CONFIG_SYS_NS16550=y
 CONFIG_USB=y
 CONFIG_USB_EHCI_HCD=y
 CONFIG_USB_STORAGE=y
+CONFIG_FIT=y
+CONFIG_FIT_VERBOSE=y
 CONFIG_LZMA=y
+CONFIG_LZO=y
--- a/configs/iconnect_defconfig
+++ b/configs/iconnect_defconfig
@@ -39,4 +39,8 @@ CONFIG_SYS_NS16550=y
 CONFIG_USB=y
 CONFIG_USB_EHCI_HCD=y
 CONFIG_USB_STORAGE=y
+CONFIG_CMD_BOOTZ=y
+CONFIG_FIT=y
+CONFIG_FIT_VERBOSE=y
 CONFIG_LZMA=y
+CONFIG_LZO=y
--- a/configs/l-50_defconfig
+++ b/configs/l-50_defconfig
@@ -57,3 +57,8 @@ CONFIG_SYS_I2C_MVTWSI=y
 CONFIG_USB=y
 CONFIG_USB_EHCI_HCD=y
 CONFIG_USB_STORAGE=y
+CONFIG_CMD_BOOTZ=y
+CONFIG_FIT=y
+CONFIG_FIT_VERBOSE=y
+CONFIG_LZMA=y
+CONFIG_LZO=y
--- a/configs/nsa310_defconfig
+++ b/configs/nsa310_defconfig
@@ -43,6 +43,9 @@ CONFIG_CMD_UBI=y
 CONFIG_USB=y
 CONFIG_USB_EHCI_HCD=y
 CONFIG_USB_STORAGE=y
+CONFIG_CMD_BOOTZ=y
+CONFIG_FIT=y
+CONFIG_FIT_VERBOSE=y
 CONFIG_LZMA=y
 CONFIG_LZO=y
 CONFIG_SYS_LONGHELP=y
--- a/configs/pogo_e02_defconfig
+++ b/configs/pogo_e02_defconfig
@@ -39,3 +39,8 @@ CONFIG_SYS_NS16550=y
 CONFIG_USB=y
 CONFIG_USB_EHCI_HCD=y
 CONFIG_USB_STORAGE=y
+CONFIG_CMD_BOOTZ=y
+CONFIG_FIT=y
+CONFIG_FIT_VERBOSE=y
+CONFIG_LZMA=y
+CONFIG_LZO=y
--- a/configs/nsa310s_defconfig
+++ b/configs/nsa310s_defconfig
@@ -40,5 +40,8 @@ CONFIG_SYS_NS16550=y
 CONFIG_USB=y
 CONFIG_USB_EHCI_HCD=y
 CONFIG_USB_STORAGE=y
+CONFIG_FIT=y
+CONFIG_FIT_VERBOSE=y
 CONFIG_LZMA=y
+CONFIG_LZO=y
 CONFIG_OF_LIBFDT=y
--- a/include/configs/nsa310s.h
+++ b/include/configs/nsa310s.h
@@ -63,4 +63,6 @@
 #define CONFIG_RTC_MV
 #endif /* CONFIG_CMD_DATE */
 
+#include "openwrt-kirkwood-common.h"
+
 #endif /* _CONFIG_NSA310S_H */
--- a/configs/sheevaplug_defconfig
+++ b/configs/sheevaplug_defconfig
@@ -49,4 +49,7 @@ CONFIG_USB=y
 CONFIG_DM_USB=y
 CONFIG_USB_EHCI_HCD=y
 CONFIG_USB_STORAGE=y
+CONFIG_FIT=y
+CONFIG_FIT_VERBOSE=y
 CONFIG_LZMA=y
+CONFIG_LZO=y
