--- a/include/configs/dockstar.h
+++ b/include/configs/dockstar.h
@@ -17,6 +17,7 @@
 #define CONFIG_FEROCEON_88FR131	1	/* CPU Core subversion */
 #define CONFIG_KW88F6281	1	/* SOC Name */
 #define CONFIG_SKIP_LOWLEVEL_INIT	/* disable board lowlevel_init */
+#define CONFIG_SYS_MVFS
 
 /*
  * mv-common.h should be defined after CMD configs since it used them
@@ -37,19 +38,15 @@
  */
 #define CONFIG_BOOTCOMMAND \
 	"setenv bootargs ${console} ${mtdparts} ${bootargs_root}; "	\
-	"ubi part root; " \
-	"ubifsmount ubi:root; " \
-	"ubifsload 0x800000 ${kernel}; " \
-	"ubifsload 0x1100000 ${initrd}; " \
-	"bootm 0x800000 0x1100000"
+	"ubi part ubi; " \
+	"ubi read 0x800000 kernel; " \
+	"bootm 0x800000"
 
 #define CONFIG_EXTRA_ENV_SETTINGS \
-	"console=console=ttyS0,115200\0" \
-	"mtdids=nand0=orion_nand\0" \
-	"mtdparts="CONFIG_MTDPARTS_DEFAULT \
-	"kernel=/boot/uImage\0" \
-	"initrd=/boot/uInitrd\0" \
-	"bootargs_root=ubi.mtd=1 root=ubi0:root rootfstype=ubifs ro\0"
+	"console=console=ttyS0,115200\0"	\
+	"mtdids=nand0=orion_nand\0"		\
+	"mtdparts="CONFIG_MTDPARTS_DEFAULT "\0"	\
+	"bootargs_root=\0"
 
 /*
  * Ethernet Driver configuration
--- a/configs/dockstar_defconfig
+++ b/configs/dockstar_defconfig
@@ -5,7 +5,7 @@ CONFIG_KIRKWOOD=y
 CONFIG_SYS_TEXT_BASE=0x600000
 CONFIG_TARGET_DOCKSTAR=y
 CONFIG_ENV_SIZE=0x20000
-CONFIG_ENV_OFFSET=0x80000
+CONFIG_ENV_OFFSET=0xE0000
 CONFIG_NR_DRAM_BANKS=2
 CONFIG_IDENT_STRING="\nSeagate FreeAgent DockStar"
 CONFIG_BOOTDELAY=3
@@ -23,7 +23,7 @@ CONFIG_CMD_EXT2=y
 CONFIG_CMD_FAT=y
 CONFIG_CMD_JFFS2=y
 CONFIG_CMD_MTDPARTS=y
-CONFIG_MTDPARTS_DEFAULT="mtdparts=orion_nand:1m(uboot),-(root)"
+CONFIG_MTDPARTS_DEFAULT="mtdparts=orion_nand:0xe0000@0x0(uboot),0x20000@0xe0000(uboot_env),0x100000@0x100000(second_stage_uboot),-@0x200000(ubi)"
 CONFIG_CMD_UBI=y
 CONFIG_ISO_PARTITION=y
 CONFIG_OF_CONTROL=y
