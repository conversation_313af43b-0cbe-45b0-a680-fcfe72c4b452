--- a/include/configs/sheevaplug.h
+++ b/include/configs/sheevaplug.h
@@ -45,15 +45,17 @@
 /*
  * Default environment variables
  */
-#define CONFIG_BOOTCOMMAND		"${x_bootcmd_kernel}; "	\
-	"setenv bootargs ${x_bootargs} ${x_bootargs_root}; "	\
-	"bootm 0x6400000;"
+#define CONFIG_BOOTCOMMAND					\
+	"setenv bootargs ${console} ${mtdparts} ${bootargs_root}; "	\
+	"ubi part ubi; " 						\
+	"ubi read 0x800000 kernel; " 			\
+	"bootm 0x800000"
 
-#define CONFIG_EXTRA_ENV_SETTINGS	"x_bootargs=console"	\
-	"=ttyS0,115200 mtdparts="CONFIG_MTDPARTS_DEFAULT	\
-	"x_bootcmd_kernel=nand read 0x6400000 0x100000 0x400000\0" \
-	"x_bootcmd_usb=usb start\0" \
-	"x_bootargs_root=root=/dev/mtdblock3 rw rootfstype=jffs2\0"
+#define CONFIG_EXTRA_ENV_SETTINGS			\
+	"console=console=ttyS0,115200\0"		\
+	"mtdids="CONFIG_MTDIDS_DEFAULT "\0"		\
+	"mtdparts="CONFIG_MTDPARTS_DEFAULT "\0"	\
+	"bootargs_root=\0"
 
 /*
  * Ethernet Driver configuration
--- a/configs/sheevaplug_defconfig
+++ b/configs/sheevaplug_defconfig
@@ -30,7 +30,7 @@ CONFIG_CMD_FAT=y
 CONFIG_CMD_JFFS2=y
 CONFIG_CMD_MTDPARTS=y
 CONFIG_MTDIDS_DEFAULT="nand0=orion_nand"
-CONFIG_MTDPARTS_DEFAULT="mtdparts=orion_nand:512K(uboot),512K(env),4M(kernel),-(rootfs)"
+CONFIG_MTDPARTS_DEFAULT="mtdparts=orion_nand:1M(uboot),-(ubi)"
 CONFIG_CMD_UBI=y
 CONFIG_ISO_PARTITION=y
 CONFIG_OF_CONTROL=y
