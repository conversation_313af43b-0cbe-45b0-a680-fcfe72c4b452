
arm: kirkwood: add ZyXEL NSA310 device

This patch add ZyXEL NSA310 1-Bay Media Server

The ZyXEL NSA310 device is a Kirkwood based NAS:

- SoC: Marvell 88F6702 1200Mhz
- SDRAM memory: 256MB DDR2 400Mhz
- Gigabit ethernet: PHY Realtek
- Flash memory: 128MB
- 1 Power button
- 1 Power LED (blue)
- 5 Status LED (green/red)
- 1 Copy/Sync button
- 1 Reset button
- 2 SATA II port (1 internal and 1 external eSata)
- 2 USB 2.0 ports (1 front and 1 back)
- Smart fan

Signed-off-by: <PERSON> <<EMAIL>>

NOTE: this patch is ready for upstream, LEDE-specific parts are in
      another patch

--- a/arch/arm/mach-kirkwood/Kconfig
+++ b/arch/arm/mach-kirkwood/Kconfig
@@ -53,6 +53,9 @@ config TARGET_GOFLEXHOME
 config TARGET_NAS220
 	bool "BlackArmor NAS220"
 
+config TARGET_NSA310
+	bool "Zyxel NSA310 Board"
+
 config TARGET_NSA310S
 	bool "Zyxel NSA310S"
 
@@ -86,6 +89,7 @@ source "board/raidsonic/ib62x0/Kconfig"
 source "board/Seagate/dockstar/Kconfig"
 source "board/Seagate/goflexhome/Kconfig"
 source "board/Seagate/nas220/Kconfig"
+source "board/zyxel/nsa310/Kconfig"
 source "board/zyxel/nsa310s/Kconfig"
 source "board/alliedtelesis/SBx81LIFKW/Kconfig"
 source "board/alliedtelesis/SBx81LIFXCAT/Kconfig"
--- /dev/null
+++ b/board/zyxel/nsa310/Kconfig
@@ -0,0 +1,12 @@
+if TARGET_NSA310
+
+config SYS_BOARD
+	default "nsa310"
+
+config SYS_VENDOR
+	default "zyxel"
+
+config SYS_CONFIG_NAME
+	default "nsa310"
+
+endif
--- /dev/null
+++ b/board/zyxel/nsa310/MAINTAINERS
@@ -0,0 +1,6 @@
+NSA310 BOARD
+M:	Alberto Bursi <<EMAIL>>
+S:	Maintained
+F:	board/zyxel/nsa310/
+F:	include/configs/nsa310.h
+F:	configs/nsa310_defconfig
--- /dev/null
+++ b/board/zyxel/nsa310/Makefile
@@ -0,0 +1,12 @@
+#
+# <AUTHOR> <EMAIL>
+#
+# Based on
+# (C) Copyright 2009
+# Marvell Semiconductor <www.marvell.com>
+# <AUTHOR> <EMAIL>
+#
+# SPDX-License-Identifier:	GPL-2.0+
+#
+
+obj-y	:= nsa310.o
--- /dev/null
+++ b/board/zyxel/nsa310/kwbimage.cfg
@@ -0,0 +1,166 @@
+#
+# Copyright (C) 2013  Rafal Kazmierowski
+#
+# Based on guruplug.c originally written by
+# <AUTHOR> <EMAIL>
+# (C) Copyright 2009
+# Marvell Semiconductor <www.marvell.com>
+#
+# See file CREDITS for list of people who contributed to this
+# project.
+#
+# This program is free software; you can redistribute it and/or
+# modify it under the terms of the GNU General Public License as
+# published by the Free Software Foundation; either version 2 of
+# the License, or (at your option) any later version.
+#
+# This program is distributed in the hope that it will be useful,
+# but WITHOUT ANY WARRANTY; without even the implied warranty of
+# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
+# GNU General Public License for more details.
+#
+# You should have received a copy of the GNU General Public License
+# along with this program; if not, write to the Free Software
+# Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston,
+# MA 02110-1301 USA
+#
+# Refer docs/README.kwimage for more details about how-to configure
+# and create kirkwood boot image
+#
+
+# Boot Media configurations
+BOOT_FROM	nand
+#BOOT_FROM	uart
+NAND_ECC_MODE	default
+NAND_PAGE_SIZE	0x0800
+
+# SOC registers configuration using bootrom header extension
+# Maximum KWBIMAGE_MAX_CONFIG configurations allowed
+
+# Configure RGMII-0 interface pad voltage to 1.8V
+DATA 0xFFD100e0 0x1b1b1b9b
+
+#Dram initalization for SINGLE x16 CL=5 @ 400MHz
+DATA 0xFFD01400 0x43010c30	# DDR Configuration register
+# bit13-0:  0xc30 (3120 DDR2 clks refresh rate)
+# bit23-14: zero
+# bit24: 1= enable exit self refresh mode on DDR access
+# bit25: 1 required
+# bit29-26: zero
+# bit31-30: 01
+
+DATA 0xFFD01404 0x37543000	# DDR Controller Control Low
+# bit 4:    0=addr/cmd in smame cycle
+# bit 5:    0=clk is driven during self refresh, we don't care for APX
+# bit 6:    0=use recommended falling edge of clk for addr/cmd
+# bit14:    0=input buffer always powered up
+# bit18:    1=cpu lock transaction enabled
+# bit23-20: 5=recommended value for CL=5 and STARTBURST_DEL disabled bit31=0
+# bit27-24: 7= CL+2, STARTBURST sample stages, for freqs 400MHz, unbuffered DIMM
+# bit30-28: 3 required
+# bit31:    0=no additional STARTBURST delay
+
+DATA 0xFFD01408 0x22125451	# DDR Timing (Low) (active cycles value +1)
+# bit3-0:   TRAS lsbs
+# bit7-4:   TRCD
+# bit11- 8: TRP
+# bit15-12: TWR
+# bit19-16: TWTR
+# bit20:    TRAS msb
+# bit23-21: 0x0
+# bit27-24: TRRD
+# bit31-28: TRTP
+
+DATA 0xFFD0140C 0x00000a33	#  DDR Timing (High)
+# bit6-0:   TRFC
+# bit8-7:   TR2R
+# bit10-9:  TR2W
+# bit12-11: TW2W
+# bit31-13: zero required
+
+DATA 0xFFD01410 0x0000000c	#  DDR Address Control
+# bit1-0:   01, Cs0width=x8
+# bit3-2:   10, Cs0size=1Gb
+# bit5-4:   01, Cs1width=x8
+# bit7-6:   10, Cs1size=1Gb
+# bit9-8:   00, Cs2width=nonexistent
+# bit11-10: 00, Cs2size =nonexistent
+# bit13-12: 00, Cs3width=nonexistent
+# bit15-14: 00, Cs3size =nonexistent
+# bit16:    0,  Cs0AddrSel
+# bit17:    0,  Cs1AddrSel
+# bit18:    0,  Cs2AddrSel
+# bit19:    0,  Cs3AddrSel
+# bit31-20: 0 required
+
+DATA 0xFFD01414 0x00000000	#  DDR Open Pages Control
+# bit0:    0,  OpenPage enabled
+# bit31-1: 0 required
+
+DATA 0xFFD01418 0x00000000	#  DDR Operation
+# bit3-0:   0x0, DDR cmd
+# bit31-4:  0 required
+
+DATA 0xFFD0141C 0x00000652	#  DDR Mode
+# bit2-0:   2, BurstLen=2 required
+# bit3:     0, BurstType=0 required
+# bit6-4:   4, CL=5
+# bit7:     0, TestMode=0 normal
+# bit8:     0, DLL reset=0 normal
+# bit11-9:  6, auto-precharge write recovery ????????????
+# bit12:    0, PD must be zero
+# bit31-13: 0 required
+
+DATA 0xFFD01420 0x00000004	#  DDR Extended Mode
+# bit0:    0,  DDR DLL enabled
+# bit1:    0,  DDR drive strenght normal
+# bit2:    0,  DDR ODT control lsd (disabled)
+# bit5-3:  000, required
+# bit6:    1,  DDR ODT control msb, (disabled)
+# bit9-7:  000, required
+# bit10:   0,  differential DQS enabled
+# bit11:   0, required
+# bit12:   0, DDR output buffer enabled
+# bit31-13: 0 required
+
+DATA 0xFFD01424 0x0000F17F	#  DDR Controller Control High
+# bit2-0:  111, required
+# bit3  :  1  , MBUS Burst Chop disabled
+# bit6-4:  111, required
+# bit7  :  0
+# bit8  :  1  , add writepath sample stage, must be 1 for DDR freq >= 300MHz
+# bit9  :  0  , no half clock cycle addition to dataout
+# bit10 :  0  , 1/4 clock cycle skew enabled for addr/ctl signals
+# bit11 :  0  , 1/4 clock cycle skew disabled for write mesh
+# bit15-12: 1111 required
+# bit31-16: 0    required
+
+DATA 0xFFD01428 0x00085520	# DDR2 ODT Read Timing (default values)
+DATA 0xFFD0147C 0x00008552	# DDR2 ODT Write Timing (default values)
+
+
+DATA 0xFFD01504 0x0FFFFFF1	# CS[0]n Size
+#DATA 0xFFD01500 0x00000000	# CS[0]n Base address to 0x0
+# bit0:    1,  Window enabled
+# bit1:    0,  Write Protect disabled
+# bit3-2:  00, CS0 hit selected
+# bit23-4: ones, required
+# bit31-24: 0x0F, Size (i.e. 256MB)
+
+DATA 0xFFD01508 0x10000000	# CS[1]n Base address to 256Mb
+DATA 0xFFD0150C 0x00000000	# CS[2]n Size, window disabled        KAZ z 400db
+DATA 0xFFD01514 0x00000000	# CS[3]n Size, window disabled
+
+DATA 0xFFD0151C 0x00000000	#  DDR ODT Control (Low)
+DATA 0xFFD01494 0x00120012	#  DDR ODT Control (High)              KAZ  z nowy  STATIC_SDRAM_ODT_CTRL_LOW
+# bit1-0:  00, ODT0 controlled by ODT Control (low) register above
+# bit3-2:  01, ODT1 active NEVER!
+# bit31-4: zero, required
+
+DATA 0xFFD01498 0x00000000	# CPU ODT Control                     KAZ STATIC_SDRAM_ODT_CTRL_HI
+DATA 0xFFD0149C 0x0000E403	# DDR Initialization Control          KAZ STATIC_SDRAM_DUNIT_ODT_CTRL
+DATA 0xFFD01480 0x00000001	# DDR Initialization Control
+#bit0=1, enable DDR init upon this register write
+
+# End of Header extension
+DATA 0x0 0x0
--- /dev/null
+++ b/board/zyxel/nsa310/nsa310.c
@@ -0,0 +1,190 @@
+/*
+ * Copyright (C) 2013 Rafal Kazmierowski
+ *
+ * Based on NSA320.c Peter Schildmann <<EMAIL>>
+ * originally written by
+ * Marvell Semiconductor <www.marvell.com>
+ * Written-by: Prafulla Wadaskar <<EMAIL>>
+ *
+ * See file CREDITS for list of people who contributed to this
+ * project.
+ *
+ * This program is free software; you can redistribute it and/or
+ * modify it under the terms of the GNU General Public License as
+ * published by the Free Software Foundation; either version 2 of
+ * the License, or (at your option) any later version.
+ *
+ * This program is distributed in the hope that it will be useful,
+ * but WITHOUT ANY WARRANTY; without even the implied warranty of
+ * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
+ * GNU General Public License for more details.
+ *
+ * You should have received a copy of the GNU General Public License
+ * along with this program; if not, write to the Free Software
+ * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston,
+ * MA 02110-1301 USA
+ */
+
+#include <common.h>
+#include <miiphy.h>
+#include <asm/arch/cpu.h>
+#include <asm/arch/soc.h>
+#include <asm/arch/mpp.h>
+#include <asm/io.h>
+#include "nsa310.h"
+
+DECLARE_GLOBAL_DATA_PTR;
+
+int board_early_init_f(void)
+{
+	/*
+	 * default gpio configuration
+	 * There are maximum 64 gpios controlled through 2 sets of registers
+	 * the below configuration configures mainly initial LED status
+	 */
+	mvebu_config_gpio(NSA310_VAL_LOW, NSA310_VAL_HIGH,
+		       NSA310_OE_LOW, NSA310_OE_HIGH);
+
+	/* Multi-Purpose Pins Functionality configuration */
+	/* (all LEDs & power off active high) */
+	static const u32 kwmpp_config[] = {
+		MPP0_NF_IO2,
+		MPP1_NF_IO3,
+		MPP2_NF_IO4,
+		MPP3_NF_IO5,
+		MPP4_NF_IO6,
+		MPP5_NF_IO7,
+		MPP6_SYSRST_OUTn,
+		MPP7_GPO,
+		MPP8_TW_SDA,	/* PCF8563 RTC chip   */
+		MPP9_TW_SCK,	/* connected to TWSI  */
+		MPP10_UART0_TXD,
+		MPP11_UART0_RXD,
+		MPP12_GPO,		/* SATA2 LED (green)  */
+		MPP13_GPIO,		/* SATA2 LED (red)    */
+		MPP14_GPIO,		/* MCU DATA pin (in)  */
+		MPP15_GPIO,		/* USB LED (green)    */
+		MPP16_GPIO,		/* MCU CLK pin (out)  */
+		MPP17_GPIO,		/* MCU ACT pin (out)  */
+		MPP18_NF_IO0,
+		MPP19_NF_IO1,
+		MPP20_GPIO,
+		MPP21_GPIO,		/* USB LED (red)-Power*/
+		MPP22_GPIO,
+		MPP23_GPIO,
+		MPP24_GPIO,
+		MPP25_GPIO,
+		MPP26_GPIO,
+		MPP27_GPIO,
+		MPP28_GPIO,		/* SYS LED (green)    */
+		MPP29_GPIO,		/* SYS LED (red)      */
+		MPP30_GPIO,
+		MPP31_GPIO,
+		MPP32_GPIO,
+		MPP33_GPIO,
+		MPP34_GPIO,
+		MPP35_GPIO,
+		MPP36_GPIO,		/* Reset button       */
+		MPP37_GPIO,		/* Copy button        */
+		MPP38_GPIO,		/* VID B0             */
+		MPP39_GPIO,		/* COPY LED (green)   */
+		MPP40_GPIO,		/* COPY LED (red)     */
+		MPP41_GPIO,		/* SATA1 LED (green)  */
+		MPP42_GPIO,		/* SATA1 LED (red)    */
+		MPP43_GPIO,		/* HTP pin            */
+		MPP44_GPIO,		/* Buzzer             */
+		MPP45_GPIO,		/* VID B1             */
+		MPP46_GPIO,		/* Power button       */
+		MPP47_GPIO,		/* Power resume data  */
+		MPP48_GPIO,		/* Power off          */
+		MPP49_GPIO,		/* Power resume clock */
+		0
+	};
+	kirkwood_mpp_conf(kwmpp_config,NULL);
+	return 0;
+}
+
+int board_init(void)
+{
+	/* address of boot parameters */
+	gd->bd->bi_boot_params = mvebu_sdram_bar(0) + 0x100;
+
+	return 0;
+}
+
+#ifdef CONFIG_RESET_PHY_R
+/* Configure and enable MV88E1318 PHY */
+void reset_phy(void)
+{
+	u16 reg;
+	u16 devadr;
+	char *name = "egiga0";
+
+	if (miiphy_set_current_dev(name))
+		return;
+
+	/* command to read PHY dev address */
+	if (miiphy_read(name, 0xEE, 0xEE, (u16 *) &devadr)) {
+		printf("Err..%s could not read PHY dev address\n",
+			__FUNCTION__);
+		return;
+	}
+
+	/* Set RGMII delay */
+	miiphy_write(name, devadr, MV88E1318_PGADR_REG, 2);
+	miiphy_read(name, devadr, MV88E1318_MAC_CTRL_REG, &reg);
+	reg |= (MV88E1318_RGMII_RXTM_CTRL | MV88E1318_RGMII_TXTM_CTRL);
+	miiphy_write(name, devadr, MV88E1318_MAC_CTRL_REG, reg);
+	miiphy_write(name, devadr, MV88E1318_PGADR_REG, 0);
+
+	/* reset the phy */
+	miiphy_reset(name, devadr);
+
+	printf("MV88E1318 PHY initialized on %s\n", name);
+}
+#endif /* CONFIG_RESET_PHY_R */
+
+#ifdef CONFIG_SHOW_BOOT_PROGRESS
+void show_boot_progress(int val)
+{
+	struct kwgpio_registers *gpio0 = (struct kwgpio_registers *)MVEBU_GPIO0_BASE;
+	u32 dout0 = readl(&gpio0->dout);
+	u32 blen0 = readl(&gpio0->blink_en);
+
+	struct kwgpio_registers *gpio1 = (struct kwgpio_registers *)MVEBU_GPIO1_BASE;
+	u32 dout1 = readl(&gpio1->dout);
+	u32 blen1 = readl(&gpio1->blink_en);
+
+	switch (val) {
+	case BOOTSTAGE_ID_DECOMP_IMAGE:
+		writel(blen0 & ~(SYS_GREEN_LED | SYS_RED_LED), &gpio0->blink_en);
+		writel((dout0 & ~SYS_GREEN_LED) | SYS_RED_LED, &gpio0->dout);
+		break;
+	case BOOTSTAGE_ID_RUN_OS:
+		writel(dout0 & ~SYS_RED_LED, &gpio0->dout);
+		writel(blen0 | SYS_GREEN_LED, &gpio0->blink_en);
+		break;
+	case BOOTSTAGE_ID_NET_START:
+		writel(dout1 & ~COPY_RED_LED, &gpio1->dout);
+		writel((blen1 & ~COPY_RED_LED) | COPY_GREEN_LED, &gpio1->blink_en);
+		break;
+	case BOOTSTAGE_ID_NET_LOADED:
+		writel(blen1 & ~(COPY_RED_LED | COPY_GREEN_LED), &gpio1->blink_en);
+		writel((dout1 & ~COPY_RED_LED) | COPY_GREEN_LED, &gpio1->dout);
+		break;
+	case -BOOTSTAGE_ID_NET_NETLOOP_OK:
+	case -BOOTSTAGE_ID_NET_LOADED:
+		writel(dout1 & ~COPY_GREEN_LED, &gpio1->dout);
+		writel((blen1 & ~COPY_GREEN_LED) | COPY_RED_LED, &gpio1->blink_en);
+		break;
+	default:
+		if (val < 0) {
+			/* error */
+			printf("Error occured, error code = %d\n", -val);
+			writel(dout0 & ~SYS_GREEN_LED, &gpio0->dout);
+			writel(blen0 | SYS_RED_LED, &gpio0->blink_en);
+		}
+		break;
+	}
+}
+#endif
--- /dev/null
+++ b/board/zyxel/nsa310/nsa310.h
@@ -0,0 +1,56 @@
+/*
+ * Copyright (C) 2013 Rafal Kazmierowski
+ *
+ * Based on Peter Schildmann <<EMAIL>>
+ * and  guruplug.h originally written by
+ * Siddarth Gore <<EMAIL>>
+ * (C) Copyright 2009
+ * Marvell Semiconductor <www.marvell.com>
+ *
+ * See file CREDITS for list of people who contributed to this
+ * project.
+ *
+ * This program is free software; you can redistribute it and/or
+ * modify it under the terms of the GNU General Public License as
+ * published by the Free Software Foundation; either version 2 of
+ * the License, or (at your option) any later version.
+ *
+ * This program is distributed in the hope that it will be useful,
+ * but WITHOUT ANY WARRANTY; without even the implied warranty of
+ * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
+ * GNU General Public License for more details.
+ *
+ * You should have received a copy of the GNU General Public License
+ * along with this program; if not, write to the Free Software
+ * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston,
+ * MA 02110-1301 USA
+ */
+
+#ifndef __NSA310_H
+#define __NSA310_H
+
+/* GPIO's */
+#define SYS_GREEN_LED		(1 << 28)
+#define SYS_RED_LED		(1 << 29)
+#define SATA1_GREEN_LED		(1ULL << 41)
+#define SATA1_RED_LED		(1ULL << 42)
+#define SATA2_GREEN_LED		(1 << 12)
+#define SATA2_RED_LED		(1 << 13)
+#define USB_GREEN_LED		(1 << 15)
+#define USB_RED_LED		(1 << 21)
+#define COPY_GREEN_LED		(1ULL << 39)
+#define COPY_RED_LED		(1ULL << 40)
+
+#define NSA310_OE_LOW   (0)
+#define NSA310_VAL_LOW    (SYS_GREEN_LED)
+#define NSA310_OE_HIGH		(((COPY_GREEN_LED | COPY_RED_LED | \
+                                   SATA1_GREEN_LED | SATA1_RED_LED)) >> 32UL)
+#define NSA310_VAL_HIGH		(0)
+
+/* PHY related */
+#define MV88E1318_MAC_CTRL_REG		21
+#define MV88E1318_PGADR_REG		22
+#define MV88E1318_RGMII_TXTM_CTRL	(1 << 4)
+#define MV88E1318_RGMII_RXTM_CTRL	(1 << 5)
+
+#endif /* __NSA310_H */
--- /dev/null
+++ b/configs/nsa310_defconfig
@@ -0,0 +1,48 @@
+CONFIG_ARM=y
+CONFIG_SYS_DCACHE_OFF=y
+CONFIG_ARCH_CPU_INIT=y
+CONFIG_KIRKWOOD=y
+CONFIG_SYS_TEXT_BASE=0x600000
+CONFIG_TARGET_NSA310=y
+CONFIG_IDENT_STRING="\nZyXEL NSA310 1-Bay Power Media Server"
+CONFIG_NR_DRAM_BANKS=2
+CONFIG_BOOTDELAY=3
+CONFIG_SYS_PROMPT="NSA310> "
+# CONFIG_CMD_IMLS is not set
+# CONFIG_CMD_FLASH is not set
+CONFIG_MVGBE=y
+CONFIG_MII=y
+CONFIG_SYS_NS16550=y
+CONFIG_CMD_FDT=y
+CONFIG_OF_LIBFDT=y
+CONFIG_CMD_SETEXPR=y
+CONFIG_CMD_DHCP=y
+CONFIG_CMD_MII=y
+CONFIG_CMD_PING=y
+CONFIG_CMD_DNS=y
+CONFIG_CMD_SNTP=y
+CONFIG_CMD_USB=y
+CONFIG_CMD_DATE=y
+CONFIG_CMD_EXT2=y
+CONFIG_CMD_EXT4=y
+CONFIG_CMD_FAT=y
+CONFIG_CMD_JFFS2=y
+CONFIG_MTD=y
+CONFIG_MTD_RAW_NAND=y
+CONFIG_MTDPARTS_DEFAULT="mtdparts=orion_nand:0x0c0000(uboot),0x80000(uboot_env),0x7ec0000(ubi)"
+CONFIG_CMD_MTDPARTS=y
+CONFIG_CMD_ENV=y
+CONFIG_CMD_NAND=y
+CONFIG_EFI_PARTITION=y
+CONFIG_ENV_IS_IN_NAND=y
+CONFIG_ENV_SIZE=0x20000
+CONFIG_ENV_OFFSET=0xC0000
+CONFIG_ENV_SECT_SIZE=0x20000
+CONFIG_ENV_ADDR=0xC0000
+CONFIG_CMD_UBI=y
+CONFIG_USB=y
+CONFIG_USB_EHCI_HCD=y
+CONFIG_USB_STORAGE=y
+CONFIG_LZMA=y
+CONFIG_LZO=y
+CONFIG_SYS_LONGHELP=y
--- /dev/null
+++ b/include/configs/nsa310.h
@@ -0,0 +1,103 @@
+/* Copyright (C) 2015-2016 bodhi <<EMAIL>>
+ *
+ * Based on
+ * Copyright (C) 2012  Peter Schildmann <<EMAIL>>
+ *
+ * Based on guruplug.h originally written by
+ * Siddarth Gore <<EMAIL>>
+ * (C) Copyright 2009
+ * Marvell Semiconductor <www.marvell.com>
+ *
+ * See file CREDITS for list of people who contributed to this
+ * project.
+ *
+ * This program is free software; you can redistribute it and/or
+ * modify it under the terms of the GNU General Public License as
+ * published by the Free Software Foundation; either version 2 of
+ * the License, or (at your option) any later version.
+ *
+ * This program is distributed in the hope that it will be useful,
+ * but WITHOUT ANY WARRANTY; without even the implied warranty of
+ * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
+ * GNU General Public License for more details.
+ *
+ * You should have received a copy of the GNU General Public License
+ * along with this program; if not, write to the Free Software
+ * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston,
+ * MA 02110-1301 USA
+ */
+
+#ifndef _CONFIG_NSA310_H
+#define _CONFIG_NSA310_H
+
+/*
+ * High Level Configuration Options (easy to change)
+ */
+#define CONFIG_FEROCEON_88FR131		/* CPU Core subversion */
+#define CONFIG_KW88F6281		/* SOC Name */
+
+#define CONFIG_SKIP_LOWLEVEL_INIT	/* disable board lowlevel_init */
+
+/*
+ * Misc Configuration Options
+ */
+#define CONFIG_SHOW_BOOT_PROGRESS 1     /* boot progess display (LED's) */
+
+/*
+ * Commands configuration
+ */
+#define CONFIG_PREBOOT
+
+/*
+ * mv-common.h should be defined after CMD configs since it used them
+ * to enable certain macros
+ */
+#include "mv-common.h"
+
+/*
+ * Default environment variables
+ */
+#define CONFIG_BOOTCOMMAND \
+	"ubi part ubi; " \
+	"ubi read 0x800000 kernel; " \
+	"bootm 0x800000"
+
+#define CONFIG_EXTRA_ENV_SETTINGS \
+	"console=console=ttyS0,115200\0"	\
+	"mtdids=nand0=orion_nand\0"		\
+	"mtdparts="CONFIG_MTDPARTS_DEFAULT "\0"	\
+	"bootargs_root=\0"
+
+/*
+ * Ethernet Driver configuration
+ */
+#ifdef CONFIG_CMD_NET
+#define CONFIG_NETCONSOLE
+#define CONFIG_NET_MULTI
+#define CONFIG_MVGBE_PORTS		{1, 0}	/* enable port 0 only */
+#define CONFIG_PHY_BASE_ADR		0x1
+#define CONFIG_RESET_PHY_R
+#endif /* CONFIG_CMD_NET */
+
+/*
+ * SATA Driver configuration
+ */
+#ifdef CONFIG_MVSATA_IDE
+#define CONFIG_SYS_ATA_IDE0_OFFSET      MV_SATA_PORT0_OFFSET
+#define CONFIG_SYS_ATA_IDE1_OFFSET      MV_SATA_PORT1_OFFSET
+#endif /* CONFIG_MVSATA_IDE */
+
+/*
+ * File system
+ */
+#define CONFIG_JFFS2_NAND
+#define CONFIG_JFFS2_LZO
+
+/*
+ *  Date Time
+ */
+#ifdef CONFIG_CMD_DATE
+#define CONFIG_RTC_MV
+#endif /* CONFIG_CMD_DATE */
+
+#endif /* _CONFIG_NSA310_H */
