--- a/include/configs/ib62x0.h
+++ b/include/configs/ib62x0.h
@@ -39,21 +39,15 @@
  */
 #define CONFIG_BOOTCOMMAND \
 	"setenv bootargs ${console} ${mtdparts} ${bootargs_root}; "	\
-	"ubi part root; "						\
-	"ubifsmount ubi:rootfs; "					\
-	"ubifsload 0x800000 ${kernel}; "				\
-	"ubifsload 0x700000 ${fdt}; "					\
-	"ubifsumount; "							\
-	"fdt addr 0x700000; fdt resize; fdt chosen; "			\
-	"bootz 0x800000 - 0x700000"
+	"ubi part ubi; " \
+	"ubi read 0x800000 kernel; " \
+	"bootm 0x800000"
 
 #define CONFIG_EXTRA_ENV_SETTINGS \
 	"console=console=ttyS0,115200\0"				\
 	"mtdids=nand0=orion_nand\0"					\
-	"mtdparts="CONFIG_MTDPARTS_DEFAULT			\
-	"kernel=/boot/zImage\0"						\
-	"fdt=/boot/ib62x0.dtb\0"					\
-	"bootargs_root=ubi.mtd=2 root=ubi0:rootfs rootfstype=ubifs rw\0"
+	"mtdparts="CONFIG_MTDPARTS_DEFAULT "\0"			\
+	"bootargs_root=\0"
 
 /*
  * Ethernet driver configuration
--- a/configs/ib62x0_defconfig
+++ b/configs/ib62x0_defconfig
@@ -26,7 +26,7 @@ CONFIG_CMD_EXT2=y
 CONFIG_CMD_FAT=y
 CONFIG_CMD_JFFS2=y
 CONFIG_CMD_MTDPARTS=y
-CONFIG_MTDPARTS_DEFAULT="mtdparts=orion_nand:0xe0000@0x0(uboot),0x20000@0xe0000(uboot_env),-@0x100000(root)"
+CONFIG_MTDPARTS_DEFAULT="mtdparts=orion_nand:0xe0000@0x0(uboot),0x20000@0xe0000(uboot_env),0x100000@0x100000(second_stage_uboot),-@0x200000(ubi)"
 CONFIG_CMD_UBI=y
 CONFIG_ISO_PARTITION=y
 CONFIG_OF_CONTROL=y
