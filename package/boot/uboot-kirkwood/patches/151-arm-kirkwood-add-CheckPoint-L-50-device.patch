From 742f780f62ace452b83e2463f1f1afdda4b724ea Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON><PERSON> <paweld<PERSON><PERSON><PERSON>@gmail.com>
Date: Sun, 26 Jan 2020 07:27:24 +0100
Subject: [PATCH] arm: kirkwood: add CheckPoint L-50 device

This patch adds support for the Check Point L-50 from 600/1100 series
routers.

Specification:
-CPU: <PERSON><PERSON> 88F6821 1200MHz
-RAM: 512MB
-Flash: NAND 512MB
-WiFi: mPCIe card based on Atheros AR9287 b/g/n
-WAN: 1 Gigabit Port (Marvell 88E1116R PHY)
-LAN: 9 Gigabit Ports (2x Marvell 88E6171(5+4))
-USB: 2x USB2.0
-Express card slot
-SD card slot
-Serial console: RJ-45 115200 8n1
-Unsupported DSL

Known limitations:
- In board is used two switches in chain. Second Marvell is not used
  in u-Boot.

Signed-off-by: <PERSON><PERSON><PERSON> <paweld<PERSON><PERSON><PERSON>@gmail.com>
---
 arch/arm/dts/Makefile              |   1 +
 arch/arm/dts/kirkwood-l-50.dts     | 439 +++++++++++++++++++++++++++++
 arch/arm/mach-kirkwood/Kconfig     |   4 +
 board/checkpoint/l-50/Kconfig      |  12 +
 board/checkpoint/l-50/MAINTAINERS  |   6 +
 board/checkpoint/l-50/Makefile     |  11 +
 board/checkpoint/l-50/kwbimage.cfg |  36 +++
 board/checkpoint/l-50/l-50.c       | 172 +++++++++++
 board/checkpoint/l-50/l-50.h       |  29 ++
 configs/l-50_defconfig             |  59 ++++
 include/configs/l-50.h             |  59 ++++
 11 files changed, 828 insertions(+)
 create mode 100644 arch/arm/dts/kirkwood-l-50.dts
 create mode 100644 board/checkpoint/l-50/Kconfig
 create mode 100644 board/checkpoint/l-50/MAINTAINERS
 create mode 100644 board/checkpoint/l-50/Makefile
 create mode 100644 board/checkpoint/l-50/kwbimage.cfg
 create mode 100644 board/checkpoint/l-50/l-50.c
 create mode 100644 board/checkpoint/l-50/l-50.h
 create mode 100644 configs/l-50_defconfig
 create mode 100644 include/configs/l-50.h

--- a/arch/arm/dts/Makefile
+++ b/arch/arm/dts/Makefile
@@ -51,6 +51,7 @@ dtb-$(CONFIG_KIRKWOOD) += \
 	kirkwood-iconnect.dtb \
 	kirkwood-is2.dtb \
 	kirkwood-km_kirkwood.dtb \
+	kirkwood-l-50.dtb \
 	kirkwood-lsxhl.dtb \
 	kirkwood-lschlv2.dtb \
 	kirkwood-net2big.dtb \
--- /dev/null
+++ b/arch/arm/dts/kirkwood-l-50.dts
@@ -0,0 +1,439 @@
+// SPDX-License-Identifier: GPL-2.0
+/*
+ * Check Point L-50 Board Description
+ * Copyright 2020 Pawel Dembicki <<EMAIL>>
+ */
+
+/dts-v1/;
+
+#include "kirkwood.dtsi"
+#include "kirkwood-6281.dtsi"
+
+/ {
+	model = "Check Point L-50";
+	compatible = "checkpoint,l-50", "marvell,kirkwood-88f6281", "marvell,kirkwood";
+
+	memory {
+		device_type = "memory";
+		reg = <0x00000000 0x20000000>;
+	};
+
+	chosen {
+		bootargs = "console=ttyS0,115200n8";
+		stdout-path = &uart0;
+	};
+
+	ocp@f1000000 {
+		pinctrl: pin-controller@10000 {
+			pinctrl-0 = <&pmx_led38 &pmx_sysrst &pmx_button29>;
+			pinctrl-names = "default";
+
+			pmx_sysrst: pmx-sysrst {
+				marvell,pins = "mpp6";
+				marvell,function = "sysrst";
+			};
+
+			pmx_button29: pmx_button29 {
+				marvell,pins = "mpp29";
+				marvell,function = "gpio";
+			};
+
+			pmx_led38: pmx_led38 {
+				marvell,pins = "mpp38";
+				marvell,function = "gpio";
+			};
+
+			pmx_sdio_cd: pmx-sdio-cd {
+				marvell,pins = "mpp46";
+				marvell,function = "gpio";
+			};
+		};
+
+		serial@12000 {
+			status = "okay";
+		};
+
+		mvsdio@90000 {
+			status = "okay";
+			cd-gpios = <&gpio1 14 9>;
+		};
+
+		i2c@11000 {
+			status = "okay";
+			clock-frequency = <400000>;
+
+			gpio2: gpio-expander@20{
+				#gpio-cells = <2>;
+				#interrupt-cells = <2>;
+				compatible = "semtech,sx1505q";
+				reg = <0x20>;
+
+				gpio-controller;
+			};
+
+			/* Three GPIOs from 0x21 exp. are undescribed in dts:
+			 * 1: DSL module reset (active low)
+			 * 5: mPCIE reset (active low)
+			 * 6: Express card reset (active low)
+			 */
+			gpio3: gpio-expander@21{
+				#gpio-cells = <2>;
+				#interrupt-cells = <2>;
+				compatible = "semtech,sx1505q";
+				reg = <0x21>;
+
+				gpio-controller;
+			};
+
+			rtc@30 {
+				compatible = "s35390a";
+				reg = <0x30>;
+			};
+		};
+	};
+
+	leds {
+		compatible = "gpio-leds";
+
+		status_green {
+			label = "l-50:green:status";
+			gpios = <&gpio1 6 GPIO_ACTIVE_LOW>;
+		};
+
+		status_red {
+			label = "l-50:red:status";
+			gpios = <&gpio3 2 GPIO_ACTIVE_LOW>;
+		};
+
+		wifi {
+			label = "l-50:green:wifi";
+			gpios = <&gpio2 7 GPIO_ACTIVE_LOW>;
+			linux,default-trigger = "phy0tpt";
+		};
+
+		internet_green {
+			label = "l-50:green:internet";
+			gpios = <&gpio2 3 GPIO_ACTIVE_LOW>;
+		};
+
+		internet_red {
+			label = "l-50:red:internet";
+			gpios = <&gpio2 1 GPIO_ACTIVE_LOW>;
+		};
+
+		usb1_green {
+			label = "l-50:green:usb1";
+			gpios = <&gpio2 0 GPIO_ACTIVE_LOW>;
+			linux,default-trigger = "usbport";
+			trigger-sources = <&hub_port3>;
+		};
+
+		usb1_red {
+			label = "l-50:red:usb1";
+			gpios = <&gpio2 4 GPIO_ACTIVE_LOW>;
+		};
+
+		usb2_green {
+			label = "l-50:green:usb2";
+			gpios = <&gpio2 2 GPIO_ACTIVE_LOW>;
+			linux,default-trigger = "usbport";
+			trigger-sources = <&hub_port1>;
+		};
+
+		usb2_red {
+			label = "l-50:red:usb2";
+			gpios = <&gpio2 5 GPIO_ACTIVE_LOW>;
+		};
+	};
+
+	usb2_pwr {
+		compatible = "regulator-fixed";
+		regulator-name = "usb2_pwr";
+
+		regulator-min-microvolt = <5000000>;
+		regulator-max-microvolt = <5000000>;
+		gpio = <&gpio3 3 GPIO_ACTIVE_LOW>;
+		regulator-always-on;
+	};
+
+	usb1_pwr {
+		compatible = "regulator-fixed";
+		regulator-name = "usb1_pwr";
+
+		regulator-min-microvolt = <5000000>;
+		regulator-max-microvolt = <5000000>;
+		gpio = <&gpio3 4 GPIO_ACTIVE_LOW>;
+		regulator-always-on;
+	};
+
+	mpcie_pwr {
+		compatible = "regulator-fixed";
+		regulator-name = "mpcie_pwr";
+
+		regulator-min-microvolt = <3300000>;
+		regulator-max-microvolt = <3300000>;
+		gpio = <&gpio3 5 GPIO_ACTIVE_HIGH>;
+		enable-active-high;
+		regulator-always-on;
+	};
+
+	express_card_pwr {
+		compatible = "regulator-fixed";
+		regulator-name = "express_card_pwr";
+
+		regulator-min-microvolt = <3300000>;
+		regulator-max-microvolt = <3300000>;
+		gpio = <&gpio3 7 GPIO_ACTIVE_HIGH>;
+		enable-active-high;
+		regulator-always-on;
+	};
+
+	keys {
+		compatible = "gpio-keys";
+
+		factory_defaults {
+			label = "factory_defaults";
+			gpios = <&gpio0 29 GPIO_ACTIVE_LOW>;
+			linux,code = <KEY_RESTART>;
+		};
+	};
+};
+
+&mdio {
+	status = "okay";
+
+	ethphy8: ethernet-phy@8 {
+		reg = <0x08>;
+	};
+
+	switch0: switch@10 {
+		compatible = "marvell,mv88e6085";
+		#address-cells = <1>;
+		#size-cells = <0>;
+		reg = <0x10>;
+		dsa,member = <0 0>;
+
+		ports {
+			#address-cells = <1>;
+			#size-cells = <0>;
+
+			port@0 {
+				reg = <0>;
+				label = "lan5";
+			};
+
+			port@1 {
+			       reg = <1>;
+			       label = "lan1";
+			};
+
+			port@2 {
+			       reg = <2>;
+			       label = "lan6";
+			};
+
+			port@3 {
+			       reg = <3>;
+			       label = "lan2";
+			};
+
+			port@4 {
+				reg = <4>;
+				label = "lan7";
+			};
+
+			switch0port5: port@5 {
+				reg = <5>;
+				phy-mode = "rgmii-txid";
+				link = <&switch1port5>;
+				fixed-link {
+					speed = <1000>;
+					full-duplex;
+				};
+			};
+
+			port@6 {
+				reg = <6>;
+				label = "cpu";
+				phy-mode = "rgmii-id";
+				ethernet = <&eth1port>;
+				fixed-link {
+					speed = <1000>;
+					full-duplex;
+				};
+			};
+		};
+	};
+
+	switch@11 {
+		compatible = "marvell,mv88e6085";
+		#address-cells = <1>;
+		#size-cells = <0>;
+		reg = <0x11>;
+		dsa,member = <0 1>;
+
+		ports {
+			#address-cells = <1>;
+			#size-cells = <0>;
+
+			port@0 {
+				reg = <0>;
+				label = "lan3";
+			};
+
+			port@1 {
+			       reg = <1>;
+			       label = "lan8";
+			};
+
+			port@2 {
+			       reg = <2>;
+			       label = "lan4";
+			};
+
+			port@3 {
+			       reg = <3>;
+			       label = "dmz";
+			};
+
+			switch1port5: port@5 {
+				reg = <5>;
+				phy-mode = "rgmii-txid";
+				link = <&switch0port5>;
+				fixed-link {
+					speed = <1000>;
+					full-duplex;
+				};
+			};
+
+			port@6 {
+				reg = <6>;
+				label = "dsl";
+				fixed-link {
+					speed = <100>;
+					full-duplex;
+				};
+			};
+		};
+	};
+};
+
+&eth0 {
+	status = "okay";
+	ethernet0-port@0 {
+		phy-handle = <&ethphy8>;
+	};
+};
+
+&eth1 {
+	status = "okay";
+	ethernet1-port@0 {
+		speed = <1000>;
+		duplex = <1>;
+		phy-handle = <&switch0>;
+	};
+};
+
+&nand {
+	status = "okay";
+	pinctrl-0 = <&pmx_nand>;
+	pinctrl-names = "default";
+
+	partition@0 {
+		label = "u-boot";
+		reg = <0x00000000 0x000c0000>;
+	};
+
+	partition@a0000 {
+		label = "bootldr-env";
+		reg = <0x000c0000 0x00040000>;
+	};
+
+	partition@100000 {
+		label = "kernel-1";
+		reg = <0x00100000 0x00800000>;
+	};
+
+	partition@900000 {
+		label = "rootfs-1";
+		reg = <0x00900000 0x07100000>;
+	};
+
+	partition@7a00000 {
+		label = "kernel-2";
+		reg = <0x07a00000 0x00800000>;
+	};
+
+	partition@8200000 {
+		label = "rootfs-2";
+		reg = <0x08200000 0x07100000>;
+	};
+
+	partition@f300000 {
+		label = "default_sw";
+		reg = <0x0f300000 0x07900000>;
+	};
+
+	partition@16c00000 {
+		label = "logs";
+		reg = <0x16c00000 0x01800000>;
+	};
+
+	partition@18400000 {
+		label = "preset_cfg";
+		reg = <0x18400000 0x00100000>;
+	};
+
+	partition@18500000 {
+		label = "adsl";
+		reg = <0x18500000 0x00100000>;
+	};
+
+	partition@18600000 {
+		label = "storage";
+		reg = <0x18600000 0x07a00000>;
+	};
+};
+
+&rtc {
+	status = "disabled";
+};
+
+&pciec {
+	status = "okay";
+};
+
+&pcie0 {
+	status = "okay";
+};
+
+&sata_phy0 {
+	status = "disabled";
+};
+
+&sata_phy1 {
+	status = "disabled";
+};
+
+&usb0 {
+	#address-cells = <1>;
+	#size-cells = <0>;
+	status = "okay";
+
+	port@1 {
+		#address-cells = <1>;
+		#size-cells = <0>;
+		reg = <1>;
+		#trigger-source-cells = <0>;
+
+		hub_port1: port@1 {
+			reg = <1>;
+			#trigger-source-cells = <0>;
+		};
+
+		hub_port3: port@3 {
+			reg = <3>;
+			#trigger-source-cells = <0>;
+		};
+	};
+};
--- a/arch/arm/mach-kirkwood/Kconfig
+++ b/arch/arm/mach-kirkwood/Kconfig
@@ -74,6 +74,9 @@ config TARGET_DB_88F6281_BP
 config TARGET_NSA325
 	bool "Zyxel NSA325 board"
 
+config TARGET_L50
+	bool "Check Point L-50"
+
 endchoice
 
 config SYS_SOC
@@ -102,5 +105,6 @@ source "board/zyxel/nsa325/Kconfig"
 source "board/alliedtelesis/SBx81LIFKW/Kconfig"
 source "board/alliedtelesis/SBx81LIFXCAT/Kconfig"
 source "board/Marvell/db-88f6281-bp/Kconfig"
+source "board/checkpoint/l-50/Kconfig"
 
 endif
--- /dev/null
+++ b/board/checkpoint/l-50/Kconfig
@@ -0,0 +1,12 @@
+if TARGET_L50
+
+config SYS_BOARD
+	default "l-50"
+
+config SYS_VENDOR
+	default "checkpoint"
+
+config SYS_CONFIG_NAME
+	default "l-50"
+
+endif
--- /dev/null
+++ b/board/checkpoint/l-50/MAINTAINERS
@@ -0,0 +1,6 @@
+L50 BOARD
+M:	Pawel Dembicki <<EMAIL>>
+S:	Maintained
+F:	board/checkpoint/l-50/
+F:	include/configs/l-50.h
+F:	configs/l-50_defconfig
--- /dev/null
+++ b/board/checkpoint/l-50/Makefile
@@ -0,0 +1,11 @@
+# SPDX-License-Identifier: GPL-2.0+
+#
+# (C) Copyright 2020
+# <AUTHOR> <EMAIL>
+#
+# Based on Kirkwood support:
+# (C) Copyright 2009
+# Marvell Semiconductor <www.marvell.com>
+# <AUTHOR> <EMAIL>
+
+obj-y	:= l-50.o
--- /dev/null
+++ b/board/checkpoint/l-50/kwbimage.cfg
@@ -0,0 +1,36 @@
+# SPDX-License-Identifier: GPL-2.0+
+#
+# Values taken from original bootloader source.
+# Based on:
+# dramregs_seattle_400rd_A.txt from uboot_src_CP600_1100.
+
+# Boot Media configurations
+BOOT_FROM	nand
+NAND_ECC_MODE	default
+NAND_PAGE_SIZE	0x0800
+
+DATA 0xFFD100e0 0x1b1b1b9b
+DATA 0xFFD01400 0x43000c30
+DATA 0xFFD01404 0x39543000
+DATA 0xFFD01408 0x22125451
+DATA 0xFFD0140C 0x00000833
+DATA 0xFFD01410 0x000000cc
+DATA 0xFFD01414 0x00000000
+DATA 0xFFD01418 0x00000000
+DATA 0xFFD0141C 0x00000C52
+DATA 0xFFD01420 0x00000004
+DATA 0xFFD01424 0x0000F17F
+DATA 0xFFD01428 0x00085520
+DATA 0xFFD0147C 0x00008552
+DATA 0xFFD01504 0x0FFFFFF1
+DATA 0xFFD01508 0x10000000
+DATA 0xFFD0150C 0x0FFFFFF5
+DATA 0xFFD01514 0x00000000
+DATA 0xFFD0151C 0x00000000
+DATA 0xFFD01494 0x00120012
+DATA 0xFFD01498 0x00000000
+DATA 0xFFD0149C 0x0000E40F
+DATA 0xFFD01480 0x00000001
+DATA 0xFFD20134 0x66666666
+DATA 0xFFD20138 0x66666666
+DATA 0x0 0x0
--- /dev/null
+++ b/board/checkpoint/l-50/l-50.c
@@ -0,0 +1,172 @@
+// SPDX-License-Identifier: GPL-2.0+
+/*
+ * Copyright (C) 2020
+ * Pawel Dembicki <<EMAIL>>
+ *
+ * Based on Kirkwood support:
+ * (C) Copyright 2009
+ * Marvell Semiconductor <www.marvell.com>
+ * Written-by: Prafulla Wadaskar <<EMAIL>>
+ */
+
+#include <common.h>
+#include <dm.h>
+#include <i2c.h>
+#include <miiphy.h>
+#include <netdev.h>
+#include <asm/arch/cpu.h>
+#include <asm/arch/soc.h>
+#include <asm/arch/mpp.h>
+#include <asm/arch/gpio.h>
+#include "l-50.h"
+
+DECLARE_GLOBAL_DATA_PTR;
+
+int board_early_init_f(void)
+{
+	/* Gpio configuration */
+	mvebu_config_gpio(L50_OE_VAL_LOW, L50_OE_VAL_HIGH,
+			  L50_OE_LOW, L50_OE_HIGH);
+
+	/* Multi-Purpose Pins Functionality configuration */
+	static const u32 kwmpp_config[] = {
+		MPP0_NF_IO2,
+		MPP1_NF_IO3,
+		MPP2_NF_IO4,
+		MPP3_NF_IO5,
+		MPP4_NF_IO6,
+		MPP5_NF_IO7,
+		MPP6_SYSRST_OUTn,
+		MPP7_SPI_SCn,
+		MPP8_TW_SDA,
+		MPP9_TW_SCK,
+		MPP10_UART0_TXD,
+		MPP11_UART0_RXD,
+		MPP12_SD_CLK,
+		MPP13_SD_CMD,
+		MPP14_SD_D0,
+		MPP15_SD_D1,
+		MPP16_SD_D2,
+		MPP17_SD_D3,
+		MPP18_NF_IO0,
+		MPP19_NF_IO1,
+		MPP20_GE1_0,
+		MPP21_GE1_1,
+		MPP22_GE1_2,
+		MPP23_GE1_3,
+		MPP24_GE1_4,
+		MPP25_GE1_5,
+		MPP26_GE1_6,
+		MPP27_GE1_7,
+		MPP28_GPIO,
+		MPP29_GPIO,
+		MPP30_GE1_10,
+		MPP31_GE1_11,
+		MPP32_GE1_12,
+		MPP33_GE1_13,
+		MPP34_GPIO,
+		MPP35_GPIO,
+		MPP36_AUDIO_SPDIFI,	/* value from stock u-boot */
+		MPP37_GPIO,
+		MPP38_GPIO,
+		MPP39_TDM_SPI_CS0,
+		MPP40_GPIO,
+		MPP41_GPIO,
+		MPP42_TDM_SPI_MOSI,
+		MPP43_TDM_CODEC_INTn,
+		MPP44_GPIO,
+		MPP45_TDM_PCLK,
+		MPP46_GPIO,
+		MPP47_TDM_DRX,
+		MPP48_GPIO,
+		MPP49_GPIO,
+		0
+	};
+	kirkwood_mpp_conf(kwmpp_config, NULL);
+
+	return 0;
+}
+
+void board_gpio_expander_init(void)
+{
+	struct udevice *dev0, *dev1;
+	uchar data_buffer;
+	int ret;
+
+	ret = i2c_get_chip_for_busnum(0, L50_GPIO0_I2C_ADDRESS, 1, &dev0);
+	if (ret) {
+		debug("%s: Cannot find I2C GPIO expander chip 0x02%X\n",
+		      __func__, L50_GPIO0_I2C_ADDRESS);
+		return;
+	}
+
+	ret = i2c_get_chip_for_busnum(0, L50_GPIO1_I2C_ADDRESS, 1, &dev1);
+	if (ret) {
+		debug("%s: Cannot find I2C GPIO expander chip 0x02%X\n",
+		      __func__, L50_GPIO1_I2C_ADDRESS);
+		return;
+	}
+
+	/* Set IO as output */
+	data_buffer = 0x0;
+	dm_i2c_write(dev0, 1, &data_buffer, 1);
+	dm_i2c_write(dev1, 1, &data_buffer, 1);
+
+	/* Set all leds off, reset asserted, pwr off */
+	data_buffer = 0xbf;
+	dm_i2c_write(dev0, 0, &data_buffer, 1);
+	data_buffer = 0x1c;
+	dm_i2c_write(dev1, 0, &data_buffer, 1);
+
+	mdelay(100);
+
+	/* Set pwr on */
+	data_buffer = 0xa5;
+	dm_i2c_write(dev1, 0, &data_buffer, 1);
+
+	mdelay(100);
+
+	/* Set reset deasserted, status red led enabled*/
+	data_buffer = 0xff;
+	dm_i2c_write(dev0, 0, &data_buffer, 1);
+	data_buffer = 0xe3;
+	dm_i2c_write(dev1, 0, &data_buffer, 1);
+}
+
+int board_init(void)
+{
+	/* Boot parameters address */
+	gd->bd->bi_boot_params = mvebu_sdram_bar(0) + 0x100;
+
+	board_gpio_expander_init();
+
+	return 0;
+}
+
+#ifdef CONFIG_RESET_PHY_R
+/* Configure and initialize PHY */
+void reset_phy(void)
+{
+	u16 devadr;
+	char *name = "ethernet-controller@72000";
+
+	if (miiphy_set_current_dev(name))
+		return;
+
+	/* command to read PHY dev address */
+	if (miiphy_read(name, 0xEE, 0xEE, (u16 *)&devadr)) {
+		printf("Err..(%s) could not read PHY dev address\n", __func__);
+		return;
+	}
+
+	/*
+	 * Fix PHY led configuration
+	 */
+	miiphy_write(name, devadr, MV88E1116_PGADR_REG, 3);
+	miiphy_write(name, devadr, 0x10, 0x1177);
+	miiphy_write(name, devadr, 0x11, 0x4417);
+	miiphy_write(name, devadr, MV88E1116_PGADR_REG, 0);
+
+	debug("88E1116 Initialized on %s\n", name);
+}
+#endif /* CONFIG_RESET_PHY_R */
--- /dev/null
+++ b/board/checkpoint/l-50/l-50.h
@@ -0,0 +1,29 @@
+/* SPDX-License-Identifier: GPL-2.0+ */
+/*
+ * Copyright (C) 2020
+ * Pawel Dembicki <<EMAIL>>
+ *
+ * Based on Kirkwood support:
+ * (C) Copyright 2009
+ * Marvell Semiconductor <www.marvell.com>
+ * Written-by: Prafulla Wadaskar <<EMAIL>>
+ */
+
+#ifndef __L50_H
+#define __L50_H
+
+/* GPIO configuration */
+#define L50_OE_LOW			0x30000000
+#define L50_OE_HIGH			0x0000004c
+#define L50_OE_VAL_LOW		0x00000000
+#define L50_OE_VAL_HIGH		0x00000000
+
+/* Expander GPIO addresses */
+
+#define L50_GPIO0_I2C_ADDRESS		0x20
+#define L50_GPIO1_I2C_ADDRESS		0x21
+
+/* PHY register */
+#define MV88E1116_PGADR_REG		22
+
+#endif /* __L50_H */
--- /dev/null
+++ b/configs/l-50_defconfig
@@ -0,0 +1,59 @@
+CONFIG_ARM=y
+CONFIG_SYS_DCACHE_OFF=y
+CONFIG_ARCH_CPU_INIT=y
+CONFIG_KIRKWOOD=y
+CONFIG_SYS_TEXT_BASE=0x600000
+CONFIG_TARGET_L50=y
+CONFIG_ENV_SIZE=0x20000
+CONFIG_ENV_OFFSET=0xC0000
+CONFIG_ENV_SECT_SIZE=0x20000
+CONFIG_ENV_ADDR=0xC0000
+CONFIG_IDENT_STRING="\nCheck Point L-50"
+CONFIG_NR_DRAM_BANKS=2
+# CONFIG_SYS_MALLOC_F is not set
+CONFIG_BOOTDELAY=1
+CONFIG_CONSOLE_MUX=y
+CONFIG_DISPLAY_BOARDINFO=y
+CONFIG_HUSH_PARSER=y
+# CONFIG_CMD_FLASH is not set
+#CONFIG_CMD_IDE=y
+CONFIG_CMD_I2C=y
+CONFIG_CMD_NAND=y
+CONFIG_CMD_USB=y
+# CONFIG_CMD_SETEXPR is not set
+CONFIG_CMD_DHCP=y
+CONFIG_CMD_MII=y
+CONFIG_CMD_PING=y
+CONFIG_CMD_EXT2=y
+CONFIG_CMD_FAT=y
+CONFIG_CMD_JFFS2=y
+CONFIG_CMD_MTDPARTS=y
+CONFIG_MTD=y
+CONFIG_MTD_RAW_NAND=y
+CONFIG_MTDIDS_DEFAULT="nand0=orion_nand"
+CONFIG_MTDPARTS_DEFAULT="mtdparts=orion_nand:0xc0000@0x0(u-boot)ro,0x40000@0xc0000(bootldr-env),0x7900000@0x100000(ubi),0x800000@0x7a00000(kernel-2),0x7100000@0x8200000(rootfs-2),0x7900000@0xf300000(default_sw),0x1800000@0x16c00000(logs),0x100000@0x18400000(preset_cfg),0x100000@0x18500000(adsl),-@0x18600000(storage)"
+CONFIG_CMD_UBI=y
+CONFIG_ISO_PARTITION=y
+CONFIG_OF_CONTROL=y
+CONFIG_DEFAULT_DEVICE_TREE="kirkwood-l-50"
+CONFIG_ENV_IS_IN_NAND=y
+CONFIG_DM=y
+CONFIG_DM_ETH=y
+#CONFIG_MVSATA_IDE=y
+CONFIG_MMC=y
+CONFIG_MVGBE=y
+CONFIG_MII=y
+CONFIG_PHYLIB=y
+CONFIG_PHY_MARVELL=y
+CONFIG_MV88E61XX_SWITCH=y
+CONFIG_MV88E61XX_CPU_PORT=6
+CONFIG_MV88E61XX_PHY_PORTS=0x01f
+CONFIG_MV88E61XX_FIXED_PORTS=0
+#CONFIG_DM_RTC=y
+#CONFIG_RTC_MV=y
+CONFIG_SYS_NS16550=y
+CONFIG_DM_I2C=y
+CONFIG_SYS_I2C_MVTWSI=y
+CONFIG_USB=y
+CONFIG_USB_EHCI_HCD=y
+CONFIG_USB_STORAGE=y
--- /dev/null
+++ b/include/configs/l-50.h
@@ -0,0 +1,59 @@
+/* SPDX-License-Identifier: GPL-2.0+ */
+/*
+ * Copyright (C) 2020
+ * Pawel Dembicki <<EMAIL>>
+ *
+ * Based on Kirkwood support:
+ * (C) Copyright 2009
+ * Marvell Semiconductor <www.marvell.com>
+ * Written-by: Prafulla Wadaskar <<EMAIL>>
+ */
+
+#ifndef _CONFIG_L50_H
+#define _CONFIG_L50_H
+
+/*
+ * High Level Configuration Options (easy to change)
+ */
+#define CONFIG_FEROCEON_88FR131		/* CPU Core subversion */
+#define CONFIG_KW88F6281		/* SOC Name */
+#define CONFIG_SKIP_LOWLEVEL_INIT	/* disable board lowlevel_init */
+
+/*
+ * mv-common.h should be defined after CMD configs since it used them
+ * to enable certain macros
+ */
+#include "mv-common.h"
+
+/* Remove or override few declarations from mv-common.h */
+
+/*
+ * Ethernet Driver configuration
+ */
+#ifdef CONFIG_CMD_NET
+#define CONFIG_MVGBE_PORTS		{1, 1} /* enable port 0 only */
+#define CONFIG_NETCONSOLE
+#endif
+
+#define CONFIG_MV88E61XX_CPU_PORT_RX_DELAY
+#define CONFIG_MV88E61XX_CPU_PORT_TX_DELAY
+
+/*
+ * Enable GPI0 support
+ */
+#define CONFIG_KIRKWOOD_GPIO
+
+/*
+ * Default environment variables
+ */
+#define CONFIG_BOOTCOMMAND \
+	"ubi part ubi; " \
+	"ubi read 0x800000 kernel; " \
+	"bootm 0x800000"
+
+#define CONFIG_EXTRA_ENV_SETTINGS \
+	"bootargs=console=ttyS0,115200\0"	\
+	"mtdids=" CONFIG_MTDIDS_DEFAULT "\0"	\
+	"mtdparts=" CONFIG_MTDPARTS_DEFAULT "\0"	\
+	"bootargs_root=\0"
+#endif /* _CONFIG_L50_H */
