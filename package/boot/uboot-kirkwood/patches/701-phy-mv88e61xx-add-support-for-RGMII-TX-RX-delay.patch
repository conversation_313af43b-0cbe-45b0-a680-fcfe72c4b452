From 940e9a5828480e4185c9a276ad7f35a4069a2393 Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON><PERSON> <paweld<PERSON><PERSON><PERSON>@gmail.com>
Date: Thu, 23 Jan 2020 22:04:15 +0100
Subject: [PATCH 1/2] phy: mv88e61xx: add support for RGMII TX/RX delay

Clock delay in RGMII is required for some boards.
This patch introduce CONFIG_MV88E61XX_CPU_PORT_TX_DELAY and
CONFIG_MV88E61XX_CPU_PORT_RX_DELAY defines, which are setting
proper bits in PORT_REG_PHYS_CTRL register.

Cc: <PERSON> <<EMAIL>>
Cc: <PERSON> <<EMAIL>>
Cc: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
Cc: <PERSON> <<EMAIL>>
Signed-off-by: <PERSON><PERSON><PERSON> <paweldem<PERSON><EMAIL>>
---
 drivers/net/phy/mv88e61xx.c | 11 ++++++++++-
 1 file changed, 10 insertions(+), 1 deletion(-)

--- a/drivers/net/phy/mv88e61xx.c
+++ b/drivers/net/phy/mv88e61xx.c
@@ -94,6 +94,8 @@
 #define PORT_REG_STATUS_CMODE_1000BASE_X	0x9
 #define PORT_REG_STATUS_CMODE_SGMII		0xa
 
+#define PORT_REG_PHYS_CTRL_RGMII_RX_DELAY	BIT(15)
+#define PORT_REG_PHYS_CTRL_RGMII_TX_DELAY	BIT(14)
 #define PORT_REG_PHYS_CTRL_PCS_AN_EN	BIT(10)
 #define PORT_REG_PHYS_CTRL_PCS_AN_RST	BIT(9)
 #define PORT_REG_PHYS_CTRL_FC_VALUE	BIT(7)
@@ -747,9 +749,16 @@ static int mv88e61xx_fixed_port_setup(st
 		       PORT_REG_PHYS_CTRL_SPD1000;
 	}
 
-	if (port == CONFIG_MV88E61XX_CPU_PORT)
+	if (port == CONFIG_MV88E61XX_CPU_PORT) {
 		val |= PORT_REG_PHYS_CTRL_LINK_VALUE |
 		       PORT_REG_PHYS_CTRL_LINK_FORCE;
+#if defined(CONFIG_MV88E61XX_CPU_PORT_RX_DELAY)
+		val |= PORT_REG_PHYS_CTRL_RGMII_RX_DELAY;
+#endif
+#if defined(CONFIG_MV88E61XX_CPU_PORT_TX_DELAY)
+		val |= PORT_REG_PHYS_CTRL_RGMII_TX_DELAY;
+#endif
+	}
 
 	return mv88e61xx_port_write(phydev, port, PORT_REG_PHYS_CTRL,
 				   val);
