From 33573ea6842198cfdb5b3fdd320db9e2045855e9 Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON> <<EMAIL>>
Date: Wed, 11 Dec 2024 11:20:04 +0100
Subject: [PATCH] fix(stm32mp1-fdts): re-enable RTC clock

On STM32MP15 ST boards, RTC clock configuration by OPTEE is not ready
yet. Re-enable it temporary to get LSE as clock source of RTC.

Signed-off-by: <PERSON><PERSON> <<EMAIL>>
Change-Id: Ib6071229552e456faffb4fdfc8db9808140d54a7
---
 fdts/stm32mp157c-ed1.dts  | 2 ++
 fdts/stm32mp15xx-dkx.dtsi | 2 ++
 2 files changed, 4 insertions(+)

--- a/fdts/stm32mp157c-ed1.dts
+++ b/fdts/stm32mp157c-ed1.dts
@@ -194,6 +194,7 @@
 		CLK_MPU_PLL1P
 		CLK_AXI_PLL2P
 		CLK_MCU_PLL3P
+		CLK_RTC_LSE
 		CLK_MCO1_DISABLED
 		CLK_MCO2_DISABLED
 		CLK_CKPER_HSE
@@ -242,6 +243,7 @@
 		DIV(DIV_APB3, 1)
 		DIV(DIV_APB4, 1)
 		DIV(DIV_APB5, 2)
+		DIV(DIV_RTC, 23)
 		DIV(DIV_MCO1, 0)
 		DIV(DIV_MCO2, 0)
 	>;
--- a/fdts/stm32mp15xx-dkx.dtsi
+++ b/fdts/stm32mp15xx-dkx.dtsi
@@ -198,6 +198,7 @@
 		CLK_MPU_PLL1P
 		CLK_AXI_PLL2P
 		CLK_MCU_PLL3P
+		CLK_RTC_LSE
 		CLK_MCO1_DISABLED
 		CLK_MCO2_DISABLED
 		CLK_CKPER_HSE
@@ -246,6 +247,7 @@
 		DIV(DIV_APB3, 1)
 		DIV(DIV_APB4, 1)
 		DIV(DIV_APB5, 2)
+		DIV(DIV_RTC, 23)
 		DIV(DIV_MCO1, 0)
 		DIV(DIV_MCO2, 0)
 	>;
