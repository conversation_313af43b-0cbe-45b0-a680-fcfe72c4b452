diff --git a/cmd/version.c b/cmd/version.c
index b2fffe99..bcbbeb18 100644
--- a/cmd/version.c
+++ b/cmd/version.c
@@ -18,7 +18,7 @@ static int do_version(cmd_tbl_t *cmdtp, int flag, int argc, char * const argv[])
 {
 	char buf[DISPLAY_OPTIONS_BANNER_LENGTH];
 
-	printf(display_options_get_banner(false, buf, sizeof(buf)));
+	printf("%s",display_options_get_banner(false, buf, sizeof(buf)));
 #ifdef CC_VERSION_STRING
 	puts(CC_VERSION_STRING "\n");
 #endif
diff --git a/drivers/pinctrl/pinctrl-uclass.c b/drivers/pinctrl/pinctrl-uclass.c
index 3425ed11..8c2e1d5c 100644
--- a/drivers/pinctrl/pinctrl-uclass.c
+++ b/drivers/pinctrl/pinctrl-uclass.c
@@ -368,7 +368,7 @@ int pinctrl_get_pin_name(struct udevice *dev, int selector, char *buf,
 	if (!ops->get_pin_name)
 		return -ENOSYS;
 
-	snprintf(buf, size, ops->get_pin_name(dev, selector));
+	snprintf(buf, size, "%s", ops->get_pin_name(dev, selector));
 
 	return 0;
 }
diff --git a/lib/efi_loader/efi_variable.c b/lib/efi_loader/efi_variable.c
index c316bdfe..5fe8129c 100644
--- a/lib/efi_loader/efi_variable.c
+++ b/lib/efi_loader/efi_variable.c
@@ -522,7 +522,7 @@ efi_status_t EFIAPI efi_set_variable(u16 *variable_name,
 
 	if (old_size)
 		/* APPEND_WRITE */
-		s += sprintf(s, old_val);
+		s += sprintf(s, "%s", old_val);
 	else
 		s += sprintf(s, "(blob)");
 
