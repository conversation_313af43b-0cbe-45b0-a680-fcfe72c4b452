#ifndef _NAS782X_TIMER_H
#define _NAS782X_TIMER_H

#define TIMER1_BASE		(RPSA_BASE + 0x200)
#define TIMER2_BASE		(RPSA_BASE + 0x220)

#define TIMER_LOAD		0
#define TIMER_CURR		4
#define TIMER_CTRL		8
#define	TIMER_INTR		0x0C

#define TIMER_PRESCALE_SHIFT		2
#define TIMER_PRESCALE_1		0
#define TIMER_PRESCALE_16		1
#define TIMER_PRESCALE_256		2
#define TIMER_MODE_SHIFT		6
#define TIMER_MODE_FREE_RUNNING		0
#define TIMER_MODE_PERIODIC		1
#define TIMER_ENABLE_SHIFT		7
#define TIMER_DISABLE			0
#define TIMER_ENABLE			1

#endif /* _NAS782X_TIMER_H */
