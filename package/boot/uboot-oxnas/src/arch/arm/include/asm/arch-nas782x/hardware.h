#ifndef _NAS782X_HARDWARE_H
#define _NAS782X_HARDWARE_H

/* Core addresses */
#define USB_HOST_BASE		0x40200000
#define MACA_BASE		0x40400000
#define MACB_BASE		0x40800000
#define MAC_BASE		MACA_BASE
#define STATIC_CS0_BASE		0x41000000
#define STATIC_CS1_BASE		0x41400000
#define STATIC_CONTROL_BASE	0x41C00000
#define SATA_DATA_BASE		0x42000000 /* non-functional, DMA just needs an address */
#define GPIO_1_BASE		0x44000000
#define GPIO_2_BASE		0x44100000
#define UART_1_BASE		0x44200000
#define UART_2_BASE		0x44300000
#define SYS_CONTROL_BASE	0x44e00000
#define SEC_CONTROL_BASE	0x44f00000
#define RPSA_BASE		0x44400000
#define RPSC_BASE		0x44500000
#define DDR_BASE		0x44700000

#define SATA_BASE		0x45900000
#define SATA_0_REGS_BASE	0x45900000
#define SATA_1_REGS_BASE	0x45910000
#define SATA_DMA_REGS_BASE	0x459a0000
#define SATA_SGDMA_REGS_BASE	0x459b0000
#define SATA_HOST_REGS_BASE	0x459e0000

#endif /* _NAS782X_HARDWARE_H */
