--- a/common/spl/Makefile
+++ b/common/spl/Makefile
@@ -19,4 +19,5 @@ obj-$(CONFIG_SPL_MMC_SUPPORT) += spl_mmc
 obj-$(CONFIG_SPL_USB_SUPPORT) += spl_usb.o
 obj-$(CONFIG_SPL_FAT_SUPPORT) += spl_fat.o
 obj-$(CONFIG_SPL_SATA_SUPPORT) += spl_sata.o
+obj-$(CONFIG_SPL_BLOCK_SUPPORT) += spl_block.o
 endif
--- a/common/spl/spl.c
+++ b/common/spl/spl.c
@@ -191,6 +191,14 @@ void board_init_r(gd_t *dummy1, ulong du
 		spl_spi_load_image();
 		break;
 #endif
+#ifdef CONFIG_SPL_BLOCK_SUPPORT
+	case BOOT_DEVICE_BLOCK:
+	{
+		extern void spl_block_load_image(void);
+		spl_block_load_image();
+		break;
+	}
+#endif
 #ifdef CONFIG_SPL_ETH_SUPPORT
 	case BOOT_DEVICE_CPGMAC:
 #ifdef CONFIG_SPL_ETH_DEVICE
--- a/common/cmd_nvedit.c
+++ b/common/cmd_nvedit.c
@@ -49,6 +49,7 @@ DECLARE_GLOBAL_DATA_PTR;
 	!defined(CONFIG_ENV_IS_IN_SPI_FLASH)	&& \
 	!defined(CONFIG_ENV_IS_IN_REMOTE)	&& \
 	!defined(CONFIG_ENV_IS_IN_UBI)		&& \
+	!defined(CONFIG_ENV_IS_IN_EXT4)		&& \
 	!defined(CONFIG_ENV_IS_NOWHERE)
 # error Define one of CONFIG_ENV_IS_IN_{EEPROM|FLASH|DATAFLASH|ONENAND|\
 SPI_FLASH|NVRAM|MMC|FAT|REMOTE|UBI} or CONFIG_ENV_IS_NOWHERE
--- a/common/Makefile
+++ b/common/Makefile
@@ -63,6 +63,7 @@ obj-$(CONFIG_ENV_IS_IN_ONENAND) += env_o
 obj-$(CONFIG_ENV_IS_IN_SPI_FLASH) += env_sf.o
 obj-$(CONFIG_ENV_IS_IN_REMOTE) += env_remote.o
 obj-$(CONFIG_ENV_IS_IN_UBI) += env_ubi.o
+obj-$(CONFIG_ENV_IS_IN_EXT4) += env_ext4.o
 obj-$(CONFIG_ENV_IS_NOWHERE) += env_nowhere.o
 
 # command
@@ -213,6 +214,8 @@ obj-$(CONFIG_UPDATE_TFTP) += update.o
 obj-$(CONFIG_USB_KEYBOARD) += usb_kbd.o
 obj-$(CONFIG_CMD_DFU) += cmd_dfu.o
 obj-$(CONFIG_CMD_GPT) += cmd_gpt.o
+else
+obj-$(CONFIG_SPL_BLOCK_SUPPORT) += cmd_ide.o
 endif
 
 ifdef CONFIG_SPL_BUILD
