--- a/tools/socfpgaimage.c
+++ b/tools/socfpgaimage.c
@@ -74,12 +74,12 @@ static uint16_t hdr_checksum(struct socf
 static void build_header(uint8_t *buf, uint8_t version, uint8_t flags,
 			 uint16_t length_bytes)
 {
-	header.validation = htole32(VALIDATION_WORD);
+	header.validation = cpu_to_le32(VALIDATION_WORD);
 	header.version = version;
 	header.flags = flags;
-	header.length_u32 = htole16(length_bytes/4);
+	header.length_u32 = cpu_to_le16(length_bytes/4);
 	header.zero = 0;
-	header.checksum = htole16(hdr_checksum(&header));
+	header.checksum = cpu_to_le16(hdr_checksum(&header));
 
 	memcpy(buf, &header, sizeof(header));
 }
@@ -92,12 +92,12 @@ static int verify_header(const uint8_t *
 {
 	memcpy(&header, buf, sizeof(header));
 
-	if (le32toh(header.validation) != VALIDATION_WORD)
+	if (le32_to_cpu(header.validation) != VALIDATION_WORD)
 		return -1;
-	if (le16toh(header.checksum) != hdr_checksum(&header))
+	if (le16_to_cpu(header.checksum) != hdr_checksum(&header))
 		return -1;
 
-	return le16toh(header.length_u32) * 4;
+	return le16_to_cpu(header.length_u32) * 4;
 }
 
 /* Sign the buffer and return the signed buffer size */
@@ -116,7 +116,7 @@ static int sign_buffer(uint8_t *buf,
 	/* Calculate and apply the CRC */
 	calc_crc = ~pbl_crc32(0, (char *)buf, len);
 
-	*((uint32_t *)(buf + len)) = htole32(calc_crc);
+	*((uint32_t *)(buf + len)) = cpu_to_le32(calc_crc);
 
 	if (!pad_64k)
 		return len + 4;
@@ -150,7 +150,7 @@ static int verify_buffer(const uint8_t *
 
 	calc_crc = ~pbl_crc32(0, (const char *)buf, len);
 
-	buf_crc = le32toh(*((uint32_t *)(buf + len)));
+	buf_crc = le32_to_cpu(*((uint32_t *)(buf + len)));
 
 	if (buf_crc != calc_crc) {
 		fprintf(stderr, "CRC32 does not match (%08x != %08x)\n",
