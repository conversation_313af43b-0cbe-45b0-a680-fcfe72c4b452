From e719404ee1241af679a51879eaad291bc27e4817 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Tue, 2 Dec 2014 14:46:05 +0100
Subject: [PATCH] net/phy: add back icplus driver

IC+ phy driver was removed due to the lack of users some time ago.
Add it back, so we can use it.
---
 drivers/net/phy/Makefile |  1 +
 drivers/net/phy/icplus.c | 80 ++++++++++++++++++++++++++++++++++++++++++++++++
 drivers/net/phy/phy.c    |  3 ++
 3 files changed, 84 insertions(+)
 create mode 100644 drivers/net/phy/icplus.c

--- a/drivers/net/phy/Makefile
+++ b/drivers/net/phy/Makefile
@@ -15,6 +15,7 @@ obj-$(CONFIG_PHY_ATHEROS) += atheros.o
 obj-$(CONFIG_PHY_BROADCOM) += broadcom.o
 obj-$(CONFIG_PHY_DAVICOM) += davicom.o
 obj-$(CONFIG_PHY_ET1011C) += et1011c.o
+obj-$(CONFIG_PHY_ICPLUS) += icplus.o
 obj-$(CONFIG_PHY_LXT) += lxt.o
 obj-$(CONFIG_PHY_MARVELL) += marvell.o
 obj-$(CONFIG_PHY_MICREL) += micrel.o
--- /dev/null
+++ b/drivers/net/phy/icplus.c
@@ -0,0 +1,93 @@
+/*
+ * ICPlus PHY drivers
+ *
+ * SPDX-License-Identifier:	GPL-2.0+
+ *
+ * Copyright (c) 2007 Freescale Semiconductor, Inc.
+ */
+#include <phy.h>
+
+/* IP101A/G - IP1001 */
+#define IP10XX_SPEC_CTRL_STATUS         16      /* Spec. Control Register */
+#define IP1001_SPEC_CTRL_STATUS_2       20      /* IP1001 Spec. Control Reg 2 */
+#define IP1001_PHASE_SEL_MASK           3       /* IP1001 RX/TXPHASE_SEL */
+#define IP1001_APS_ON                   11      /* IP1001 APS Mode  bit */
+#define IP101A_G_APS_ON                 2       /* IP101A/G APS Mode bit */
+#define IP101A_G_IRQ_CONF_STATUS        0x11    /* Conf Info IRQ & Status Reg */
+#define IP101A_G_IRQ_PIN_USED           (1<<15) /* INTR pin used */
+#define IP101A_G_IRQ_DEFAULT            IP101A_G_IRQ_PIN_USED
+#define IP1001LF_DRIVE_MASK     (15 << 5)
+#define IP1001LF_RXCLKDRIVE_HI  (2  << 5)
+#define IP1001LF_RXDDRIVE_HI    (2  << 7)
+#define IP1001LF_RXCLKDRIVE_M   (1  << 5)
+#define IP1001LF_RXDDRIVE_M     (1  << 7)
+#define IP1001LF_RXCLKDRIVE_L   (0  << 5)
+#define IP1001LF_RXDDRIVE_L     (0  << 7)
+#define IP1001LF_RXCLKDRIVE_VL  (3  << 5)
+#define IP1001LF_RXDDRIVE_VL    (3  << 7)
+
+static int ip1001_config(struct phy_device *phydev)
+{
+	int c;
+
+	/* Enable Auto Power Saving mode */
+	c = phy_read(phydev, MDIO_DEVAD_NONE, IP1001_SPEC_CTRL_STATUS_2);
+	if (c < 0)
+		return c;
+	c |= IP1001_APS_ON;
+	c = phy_write(phydev, MDIO_DEVAD_NONE, IP1001_SPEC_CTRL_STATUS_2, c);
+	if (c < 0)
+		return c;
+
+	/* INTR pin used: speed/link/duplex will cause an interrupt */
+	c = phy_write(phydev, MDIO_DEVAD_NONE, IP101A_G_IRQ_CONF_STATUS,
+		      IP101A_G_IRQ_DEFAULT);
+	if (c < 0)
+		return c;
+
+	if (phydev->interface == PHY_INTERFACE_MODE_RGMII) {
+		/*
+		 * Additional delay (2ns) used to adjust RX clock phase
+		 * at RGMII interface
+		 */
+		c = phy_read(phydev, MDIO_DEVAD_NONE, IP10XX_SPEC_CTRL_STATUS);
+		if (c < 0)
+			return c;
+
+		c |= IP1001_PHASE_SEL_MASK;
+		/* adjust digtial drive strength */
+		c &= ~IP1001LF_DRIVE_MASK;
+		c |=  IP1001LF_RXCLKDRIVE_M;
+		c |=  IP1001LF_RXDDRIVE_M;
+		c = phy_write(phydev, MDIO_DEVAD_NONE, IP10XX_SPEC_CTRL_STATUS,
+			      c);
+		if (c < 0)
+			return c;
+	}
+
+	return 0;
+}
+
+static int ip1001_startup(struct phy_device *phydev)
+{
+	genphy_update_link(phydev);
+	genphy_parse_link(phydev);
+
+	return 0;
+}
+static struct phy_driver IP1001_driver = {
+	.name = "ICPlus IP1001",
+	.uid = 0x02430d90,
+	.mask = 0x0ffffff0,
+	.features = PHY_GBIT_FEATURES,
+	.config = &ip1001_config,
+	.startup = &ip1001_startup,
+	.shutdown = &genphy_shutdown,
+};
+
+int phy_icplus_init(void)
+{
+	phy_register(&IP1001_driver);
+
+	return 0;
+}
--- a/drivers/net/phy/phy.c
+++ b/drivers/net/phy/phy.c
@@ -454,6 +454,9 @@ int phy_init(void)
 #ifdef CONFIG_PHY_ET1011C
 	phy_et1011c_init();
 #endif
+#ifdef CONFIG_PHY_ICPLUS
+	phy_icplus_init();
+#endif
 #ifdef CONFIG_PHY_LXT
 	phy_lxt_init();
 #endif
--- a/include/phy.h
+++ b/include/phy.h
@@ -225,6 +225,7 @@ int phy_atheros_init(void);
 int phy_broadcom_init(void);
 int phy_davicom_init(void);
 int phy_et1011c_init(void);
+int phy_icplus_init(void);
 int phy_lxt_init(void);
 int phy_marvell_init(void);
 int phy_micrel_init(void);
