--- a/arch/arm/include/asm/mach-types.h
+++ b/arch/arm/include/asm/mach-types.h
@@ -212,6 +212,7 @@ extern unsigned int __machine_arch_type;
 #define MACH_TYPE_EDB9307A             1128
 #define MACH_TYPE_OMAP_3430SDP         1138
 #define MACH_TYPE_VSTMS                1140
+#define MACH_TYPE_OXNAS                1152
 #define MACH_TYPE_MICRO9M              1169
 #define MACH_TYPE_BUG                  1179
 #define MACH_TYPE_AT91SAM9263EK        1202
--- a/drivers/block/Makefile
+++ b/drivers/block/Makefile
@@ -21,3 +21,4 @@ obj-$(CONFIG_IDE_SIL680) += sil680.o
 obj-$(CONFIG_SANDBOX) += sandbox.o
 obj-$(CONFIG_SCSI_SYM53C8XX) += sym53c8xx.o
 obj-$(CONFIG_SYSTEMACE) += systemace.o
+obj-$(CONFIG_IDE_PLX) += plxsata_ide.o
--- a/drivers/usb/host/Makefile
+++ b/drivers/usb/host/Makefile
@@ -33,6 +33,7 @@ obj-$(CONFIG_USB_EHCI_MX6) += ehci-mx6.o
 obj-$(CONFIG_USB_EHCI_OMAP) += ehci-omap.o
 obj-$(CONFIG_USB_EHCI_PPC4XX) += ehci-ppc4xx.o
 obj-$(CONFIG_USB_EHCI_MARVELL) += ehci-marvell.o
+obj-$(CONFIG_USB_EHCI_OXNAS) += ehci-oxnas.o
 obj-$(CONFIG_USB_EHCI_PCI) += ehci-pci.o
 obj-$(CONFIG_USB_EHCI_SPEAR) += ehci-spear.o
 obj-$(CONFIG_USB_EHCI_SUNXI) += ehci-sunxi.o
--- a/tools/.gitignore
+++ b/tools/.gitignore
@@ -9,6 +9,7 @@
 /mkenvimage
 /mkimage
 /mkexynosspl
+/mkox820crc
 /mpc86x_clk
 /mxsboot
 /mksunxiboot
--- a/tools/Makefile
+++ b/tools/Makefile
@@ -143,6 +143,12 @@ hostprogs-$(CONFIG_KIRKWOOD) += kwboot
 hostprogs-y += proftool
 hostprogs-$(CONFIG_STATIC_RELA) += relocate-rela
 
+
+hostprogs-$(CONFIG_OX820) += mkox820crc$(SFX)
+
+mkox820crc$(SFX)-objs := mkox820crc.o lib/crc32.o
+
+
 # We build some files with extra pedantic flags to try to minimize things
 # that won't build on some weird host compiler -- though there are lots of
 # exceptions for files that aren't complaint.
--- a/drivers/serial/ns16550.c
+++ b/drivers/serial/ns16550.c
@@ -118,6 +118,14 @@ int ns16550_calc_divisor(NS16550_t port,
 	}
 	port->osc_12m_sel = 0;			/* clear if previsouly set */
 #endif
+#ifdef CONFIG_OX820
+	{
+		/* with additional 3 bit fractional */
+		u32 div = (CONFIG_SYS_NS16550_CLK + baudrate) / (baudrate * 2);
+		port->reg9 = (div & 7) << 5;
+		return (div >> 3);
+	}
+#endif
 
 	return DIV_ROUND_CLOSEST(clock, mode_x_div * baudrate);
 }
--- a/scripts/Makefile.spl
+++ b/scripts/Makefile.spl
@@ -202,6 +202,9 @@ OBJCOPYFLAGS_$(SPL_BIN).bin = $(SPL_OBJC
 
 $(obj)/$(SPL_BIN).bin: $(obj)/$(SPL_BIN) FORCE
 	$(call if_changed,objcopy)
+ifdef CONFIG_OX820
+	$(OBJTREE)/tools/mkox820crc $@
+endif
 
 LDFLAGS_$(SPL_BIN) += -T u-boot-spl.lds $(LDFLAGS_FINAL)
 ifneq ($(CONFIG_SPL_TEXT_BASE),)
--- a/arch/arm/Kconfig
+++ b/arch/arm/Kconfig
@@ -488,6 +488,9 @@ config TARGET_BALLOON3
 config TARGET_H2200
 	bool "Support h2200"
 
+config TARGET_OX820
+	bool "Support ox820"
+
 config TARGET_PALMLD
 	bool "Support palmld"
 
@@ -650,6 +653,7 @@ source "board/logicpd/imx27lite/Kconfig"
 source "board/logicpd/imx31_litekit/Kconfig"
 source "board/mpl/vcma9/Kconfig"
 source "board/olimex/mx23_olinuxino/Kconfig"
+source "board/ox820/Kconfig"
 source "board/palmld/Kconfig"
 source "board/palmtc/Kconfig"
 source "board/palmtreo680/Kconfig"
