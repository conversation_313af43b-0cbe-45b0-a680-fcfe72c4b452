From df9fb90120423c4c55b66a5dc09af23f605a406b Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Mon, 1 Dec 2014 21:37:25 +0100
Subject: [PATCH] disk/part.c: use unsigned format when printing capacity
To: <EMAIL>

Large disks otherwise produce highly unplausible output such as
        Capacity: 1907729.0 MB = 1863.0 GB (-387938128 x 512)

As supposedly all size-related decimals are unsigned, use unsigned
format in printf statement, resulting in a correct capacity being
displayed:
        Capacity: 1907729.0 MB = 1863.0 GB (3907029168 x 512)

Signed-off-by: <PERSON> <<EMAIL>>
---
 disk/part.c | 4 ++--
 1 file changed, 2 insertions(+), 2 deletions(-)

--- a/disk/part.c
+++ b/disk/part.c
@@ -229,13 +229,13 @@ void dev_print (block_dev_desc_t *dev_de
 			printf ("            Supports 48-bit addressing\n");
 #endif
 #if defined(CONFIG_SYS_64BIT_LBA)
-		printf ("            Capacity: %ld.%ld MB = %ld.%ld GB (%Ld x %ld)\n",
+		printf ("            Capacity: %lu.%lu MB = %lu.%lu GB (%Lu x %lu)\n",
 			mb_quot, mb_rem,
 			gb_quot, gb_rem,
 			lba,
 			dev_desc->blksz);
 #else
-		printf ("            Capacity: %ld.%ld MB = %ld.%ld GB (%ld x %ld)\n",
+		printf ("            Capacity: %lu.%lu MB = %lu.%lu GB (%lu x %lu)\n",
 			mb_quot, mb_rem,
 			gb_quot, gb_rem,
 			(ulong)lba,
