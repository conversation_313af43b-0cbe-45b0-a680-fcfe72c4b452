From 793bed29e78cc54d989333d756fef51efaca4e56 Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON><PERSON> <<EMAIL>>
Date: Tue, 26 Jul 2022 09:29:18 +0800
Subject: [PATCH 58/71] mmc: mtk-sd: add support to display verbose error log

Add an option to enable debug log, and also display verbose error log for
both command and data.

Signed-off-by: <PERSON><PERSON><PERSON> <<EMAIL>>
---
 drivers/mmc/Kconfig  |  8 ++++++++
 drivers/mmc/Makefile |  4 ++++
 drivers/mmc/mtk-sd.c | 24 +++++++++++++++---------
 3 files changed, 27 insertions(+), 9 deletions(-)

--- a/drivers/mmc/Kconfig
+++ b/drivers/mmc/Kconfig
@@ -815,6 +815,14 @@ config MMC_MTK
 	  This is needed if support for any SD/SDIO/MMC devices is required.
 	  If unsure, say N.
 
+config MMC_MTK_DEBUG
+	bool "Display verbose error log"
+	default n
+	depends on MMC_MTK
+	help
+	  Enable this option to allow verbose error log being displayed for
+	  debugging.
+
 endif
 
 config FSL_SDHC_V2_3
--- a/drivers/mmc/Makefile
+++ b/drivers/mmc/Makefile
@@ -82,3 +82,7 @@ obj-$(CONFIG_RENESAS_SDHI)		+= tmio-comm
 obj-$(CONFIG_MMC_BCM2835)		+= bcm2835_sdhost.o
 obj-$(CONFIG_MMC_MTK)			+= mtk-sd.o
 obj-$(CONFIG_MMC_SDHCI_F_SDH30)		+= f_sdh30.o
+
+ifdef CONFIG_MMC_MTK_DEBUG
+CFLAGS_mtk-sd.o += -DDEBUG
+endif
--- a/drivers/mmc/mtk-sd.c
+++ b/drivers/mmc/mtk-sd.c
@@ -779,18 +779,24 @@ static int msdc_ops_send_cmd(struct udev
 	if (cmd_ret &&
 	    !(cmd_ret == -EIO &&
 	    (cmd->cmdidx == MMC_CMD_SEND_TUNING_BLOCK ||
-	    cmd->cmdidx == MMC_CMD_SEND_TUNING_BLOCK_HS200)))
+	    cmd->cmdidx == MMC_CMD_SEND_TUNING_BLOCK_HS200))) {
+		dev_dbg(dev, "MSDC start command failure with %d, cmd=%d, arg=0x%x\n",
+			cmd_ret, cmd->cmdidx, cmd->cmdarg);
 		return cmd_ret;
-
-	if (data) {
-		data_ret = msdc_start_data(host, data);
-		if (cmd_ret)
-			return cmd_ret;
-		else
-			return data_ret;
 	}
 
-	return 0;
+	if (!data)
+		return cmd_ret;
+
+	data_ret = msdc_start_data(host, data);
+	if (cmd_ret)
+		return cmd_ret;
+
+	if (data_ret)
+		dev_dbg(dev, "MSDC start data failure with %d, cmd=%d, arg=0x%x\n",
+			data_ret, cmd->cmdidx, cmd->cmdarg);
+
+	return data_ret;
 }
 
 static void msdc_set_timeout(struct msdc_host *host, u32 ns, u32 clks)
