--- a/configs/mt7988_sd_rfb_defconfig
+++ b/configs/mt7988_sd_rfb_defconfig
@@ -11,6 +11,24 @@ CONFIG_DEBUG_UART_BASE=0x11000000
 CONFIG_DEBUG_UART_CLOCK=40000000
 CONFIG_SYS_LOAD_ADDR=0x46000000
 CONFIG_DEBUG_UART=y
+CONFIG_OF_LIBFDT_OVERLAY=y
+CONFIG_SMBIOS_PRODUCT_NAME=""
+CONFIG_CFB_CONSOLE_ANSI=y
+CONFIG_BOARD_LATE_INIT=y
+CONFIG_BUTTON=y
+CONFIG_BUTTON_GPIO=y
+CONFIG_GPIO_HOG=y
+CONFIG_CMD_ENV_FLAGS=y
+CONFIG_FIT=y
+CONFIG_FIT_ENABLE_SHA256_SUPPORT=y
+CONFIG_LED=y
+CONFIG_LED_BLINK=y
+CONFIG_LED_GPIO=y
+CONFIG_SPI_BOOT=y
+CONFIG_SD_BOOT=y
+CONFIG_NAND_BOOT=y
+CONFIG_BOOTSTD_DEFAULTS=y
+CONFIG_BOOTSTD_FULL=y
 # CONFIG_AUTOBOOT is not set
 CONFIG_DEFAULT_FDT_FILE="mt7988-sd-rfb"
 CONFIG_LOGLEVEL=7
@@ -22,15 +40,118 @@ CONFIG_SYS_PBSIZE=1049
 # CONFIG_BOOTM_PLAN9 is not set
 # CONFIG_BOOTM_RTEMS is not set
 # CONFIG_BOOTM_VXWORKS is not set
-# CONFIG_CMD_ELF is not set
+CONFIG_CMD_BOOTMENU=y
+CONFIG_CMD_BOOTP=y
+CONFIG_CMD_BUTTON=y
+CONFIG_CMD_CACHE=y
+CONFIG_CMD_CDP=y
+CONFIG_CMD_CPU=y
+CONFIG_CMD_DHCP=y
+CONFIG_CMD_DM=y
+CONFIG_CMD_ELF=y
+CONFIG_CMD_DNS=y
+CONFIG_CMD_ECHO=y
+CONFIG_CMD_ENV_READMEM=y
+CONFIG_CMD_ERASEENV=y
+CONFIG_CMD_EXT4=y
+CONFIG_CMD_FAT=y
+CONFIG_CMD_FDT=y
+CONFIG_CMD_FS_GENERIC=y
+CONFIG_CMD_FS_UUID=y
 CONFIG_CMD_CLK=y
 CONFIG_CMD_DM=y
 CONFIG_CMD_GPIO=y
+CONFIG_CMD_GPT=y
+CONFIG_CMD_HASH=y
+CONFIG_CMD_ITEST=y
+CONFIG_CMD_LED=y
+CONFIG_CMD_LICENSE=y
+CONFIG_CMD_LINK_LOCAL=y
+CONFIG_CMD_MMC=y
+CONFIG_CMD_MTD=y
+CONFIG_CMD_NAND=y
+CONFIG_CMD_NAND_TRIMFFS=y
+CONFIG_CMD_PCI=y
+CONFIG_CMD_PSTORE=y
+CONFIG_CMD_PSTORE_MEM_ADDR=0x42ff0000
+CONFIG_CMD_SF_TEST=y
+CONFIG_CMD_PING=y
+CONFIG_CMD_PXE=y
 CONFIG_CMD_PWM=y
 CONFIG_CMD_MMC=y
 CONFIG_CMD_MTD=y
 CONFIG_CMD_PING=y
+CONFIG_CMD_SF=y
 CONFIG_CMD_SMC=y
+CONFIG_CMD_TFTPBOOT=y
+CONFIG_CMD_TFTPSRV=y
+CONFIG_CMD_UBI=y
+CONFIG_CMD_UBI_RENAME=y
+CONFIG_CMD_UBIFS=y
+CONFIG_CMD_ASKENV=y
+CONFIG_CMD_PART=y
+CONFIG_CMD_RARP=y
+CONFIG_CMD_SETEXPR=y
+CONFIG_CMD_SLEEP=y
+CONFIG_CMD_SNTP=y
+CONFIG_CMD_SOURCE=y
+CONFIG_CMD_STRINGS=y
+CONFIG_CMD_USB=y
+CONFIG_CMD_UUID=y
+CONFIG_DISPLAY_CPUINFO=y
+CONFIG_DM_MMC=y
+CONFIG_DM_MTD=y
+CONFIG_DM_REGULATOR=y
+CONFIG_DM_REGULATOR_FIXED=y
+CONFIG_DM_REGULATOR_GPIO=y
+CONFIG_DM_USB=y
+CONFIG_DM_PWM=y
+CONFIG_PWM_MTK=y
+CONFIG_HUSH_PARSER=y
+CONFIG_SYS_REDUNDAND_ENVIRONMENT=y
+CONFIG_SYS_RELOC_GD_ENV_ADDR=y
+CONFIG_VERSION_VARIABLE=y
+CONFIG_PARTITION_UUIDS=y
+CONFIG_NETCONSOLE=y
+CONFIG_DM_GPIO=y
+CONFIG_DM_SCSI=y
+CONFIG_PHY=y
+CONFIG_PHY_MTK_TPHY=y
+CONFIG_PCI=y
+CONFIG_MTD=y
+CONFIG_MTD_UBI_FASTMAP=y
+# CONFIG_MTD_RAW_NAND is not set
+CONFIG_DM_PCI=y
+CONFIG_PCIE_MEDIATEK=y
+CONFIG_PINCTRL_MT7988=y
+CONFIG_PRE_CONSOLE_BUFFER=y
+CONFIG_PRE_CON_BUF_ADDR=0x4007EF00
+CONFIG_RAM=y
+CONFIG_DM_SERIAL=y
+CONFIG_MTK_SERIAL=y
+CONFIG_SPI=y
+CONFIG_DM_SPI=y
+CONFIG_MTK_SPI_NAND=y
+CONFIG_MTK_SPI_NAND_MTD=y
+CONFIG_SYSRESET_WATCHDOG=y
+CONFIG_WDT_MTK=y
+CONFIG_LZO=y
+CONFIG_ZSTD=y
+CONFIG_HEXDUMP=y
+CONFIG_RANDOM_UUID=y
+CONFIG_REGEX=y
+CONFIG_USB=y
+CONFIG_USB_HOST=y
+CONFIG_USB_XHCI_HCD=y
+CONFIG_USB_XHCI_MTK=y
+CONFIG_USB_STORAGE=y
+CONFIG_OF_EMBED=y
+CONFIG_ENV_OVERWRITE=y
+CONFIG_ENV_IS_IN_MMC=y
+CONFIG_ENV_OFFSET=0x400000
+CONFIG_ENV_OFFSET_REDUND=0x440000
+CONFIG_ENV_SIZE=0x40000
+CONFIG_ENV_SIZE_REDUND=0x40000
 CONFIG_DOS_PARTITION=y
 CONFIG_EFI_PARTITION=y
 CONFIG_PARTITION_TYPE_GUID=y
@@ -46,6 +167,9 @@ CONFIG_PROT_TCP=y
 CONFIG_REGMAP=y
 CONFIG_SYSCON=y
 CONFIG_CLK=y
+CONFIG_MMC=y
+CONFIG_MMC_DEFAULT_DEV=1
+CONFIG_MMC_SUPPORTS_TUNING=y
 CONFIG_MMC_HS200_SUPPORT=y
 CONFIG_MMC_MTK=y
 CONFIG_MTD=y
--- a/configs/mt7988_rfb_defconfig
+++ b/configs/mt7988_rfb_defconfig
@@ -11,7 +11,24 @@ CONFIG_DEBUG_UART_BASE=0x11000000
 CONFIG_DEBUG_UART_CLOCK=40000000
 CONFIG_SYS_LOAD_ADDR=0x46000000
 CONFIG_DEBUG_UART=y
-# CONFIG_AUTOBOOT is not set
+CONFIG_OF_LIBFDT_OVERLAY=y
+CONFIG_SMBIOS_PRODUCT_NAME=""
+CONFIG_CFB_CONSOLE_ANSI=y
+CONFIG_BOARD_LATE_INIT=y
+CONFIG_BUTTON=y
+CONFIG_BUTTON_GPIO=y
+CONFIG_GPIO_HOG=y
+CONFIG_CMD_ENV_FLAGS=y
+CONFIG_FIT=y
+CONFIG_FIT_ENABLE_SHA256_SUPPORT=y
+CONFIG_LED=y
+CONFIG_LED_BLINK=y
+CONFIG_LED_GPIO=y
+CONFIG_SPI_BOOT=y
+CONFIG_SD_BOOT=y
+CONFIG_NAND_BOOT=y
+CONFIG_BOOTSTD_DEFAULTS=y
+CONFIG_BOOTSTD_FULL=y
 CONFIG_DEFAULT_FDT_FILE="mt7988-rfb"
 CONFIG_LOGLEVEL=7
 CONFIG_LOG=y
@@ -22,15 +39,120 @@ CONFIG_SYS_PBSIZE=1049
 # CONFIG_BOOTM_PLAN9 is not set
 # CONFIG_BOOTM_RTEMS is not set
 # CONFIG_BOOTM_VXWORKS is not set
-# CONFIG_CMD_ELF is not set
+CONFIG_CMD_BOOTMENU=y
+CONFIG_CMD_BOOTP=y
+CONFIG_CMD_BUTTON=y
+CONFIG_CMD_CACHE=y
+CONFIG_CMD_CDP=y
+CONFIG_CMD_CPU=y
+CONFIG_CMD_DHCP=y
+CONFIG_CMD_DM=y
+CONFIG_CMD_ELF=y
+CONFIG_CMD_DNS=y
+CONFIG_CMD_ECHO=y
+CONFIG_CMD_ENV_READMEM=y
+CONFIG_CMD_ERASEENV=y
+CONFIG_CMD_EXT4=y
+CONFIG_CMD_FAT=y
+CONFIG_CMD_FDT=y
+CONFIG_CMD_FS_GENERIC=y
+CONFIG_CMD_FS_UUID=y
 CONFIG_CMD_CLK=y
 CONFIG_CMD_DM=y
 CONFIG_CMD_GPIO=y
+CONFIG_CMD_GPT=y
+CONFIG_CMD_HASH=y
+CONFIG_CMD_ITEST=y
+CONFIG_CMD_LED=y
+CONFIG_CMD_LICENSE=y
+CONFIG_CMD_LINK_LOCAL=y
+CONFIG_CMD_MMC=y
+CONFIG_CMD_MTD=y
+CONFIG_CMD_NAND=y
+CONFIG_CMD_NAND_TRIMFFS=y
+CONFIG_CMD_PCI=y
+CONFIG_CMD_PSTORE=y
+CONFIG_CMD_PSTORE_MEM_ADDR=0x42ff0000
+CONFIG_CMD_SF_TEST=y
+CONFIG_CMD_PING=y
+CONFIG_CMD_PXE=y
 CONFIG_CMD_PWM=y
 CONFIG_CMD_MMC=y
 CONFIG_CMD_MTD=y
 CONFIG_CMD_PING=y
+CONFIG_CMD_SF=y
 CONFIG_CMD_SMC=y
+CONFIG_CMD_TFTPBOOT=y
+CONFIG_CMD_TFTPSRV=y
+CONFIG_CMD_UBI=y
+CONFIG_CMD_UBI_RENAME=y
+CONFIG_CMD_UBIFS=y
+CONFIG_CMD_ASKENV=y
+CONFIG_CMD_PART=y
+CONFIG_CMD_RARP=y
+CONFIG_CMD_SETEXPR=y
+CONFIG_CMD_SLEEP=y
+CONFIG_CMD_SNTP=y
+CONFIG_CMD_SOURCE=y
+CONFIG_CMD_STRINGS=y
+CONFIG_CMD_USB=y
+CONFIG_CMD_UUID=y
+CONFIG_DISPLAY_CPUINFO=y
+CONFIG_DM_MMC=y
+CONFIG_DM_MTD=y
+CONFIG_DM_REGULATOR=y
+CONFIG_DM_REGULATOR_FIXED=y
+CONFIG_DM_REGULATOR_GPIO=y
+CONFIG_DM_USB=y
+CONFIG_DM_PWM=y
+CONFIG_PWM_MTK=y
+CONFIG_HUSH_PARSER=y
+CONFIG_SYS_REDUNDAND_ENVIRONMENT=y
+CONFIG_SYS_RELOC_GD_ENV_ADDR=y
+CONFIG_VERSION_VARIABLE=y
+CONFIG_PARTITION_UUIDS=y
+CONFIG_NETCONSOLE=y
+CONFIG_DM_GPIO=y
+CONFIG_DM_SCSI=y
+CONFIG_PHY=y
+CONFIG_PHY_MTK_TPHY=y
+CONFIG_PCI=y
+CONFIG_MTD=y
+CONFIG_MTD_UBI_FASTMAP=y
+# CONFIG_MTD_RAW_NAND is not set
+CONFIG_DM_PCI=y
+CONFIG_PCIE_MEDIATEK=y
+CONFIG_PINCTRL_MT7988=y
+CONFIG_PRE_CONSOLE_BUFFER=y
+CONFIG_PRE_CON_BUF_ADDR=0x4007EF00
+CONFIG_RAM=y
+CONFIG_DM_SERIAL=y
+CONFIG_MTK_SERIAL=y
+CONFIG_SPI=y
+CONFIG_DM_SPI=y
+CONFIG_MTK_SPI_NAND=y
+CONFIG_MTK_SPI_NAND_MTD=y
+CONFIG_SYSRESET_WATCHDOG=y
+CONFIG_WDT_MTK=y
+CONFIG_LZO=y
+CONFIG_ZSTD=y
+CONFIG_HEXDUMP=y
+CONFIG_RANDOM_UUID=y
+CONFIG_REGEX=y
+CONFIG_USB=y
+CONFIG_USB_HOST=y
+CONFIG_USB_XHCI_HCD=y
+CONFIG_USB_XHCI_MTK=y
+CONFIG_USB_STORAGE=y
+CONFIG_OF_EMBED=y
+CONFIG_OF_SYSTEM_SETUP=y
+CONFIG_ENV_OVERWRITE=y
+CONFIG_ENV_IS_IN_UBI=y
+CONFIG_ENV_UBI_PART="ubi"
+CONFIG_ENV_SIZE=0x1f000
+CONFIG_ENV_SIZE_REDUND=0x1f000
+CONFIG_ENV_UBI_VOLUME="ubootenv"
+CONFIG_ENV_UBI_VOLUME_REDUND="ubootenv2"
 CONFIG_DOS_PARTITION=y
 CONFIG_EFI_PARTITION=y
 CONFIG_PARTITION_TYPE_GUID=y
@@ -46,6 +168,9 @@ CONFIG_PROT_TCP=y
 CONFIG_REGMAP=y
 CONFIG_SYSCON=y
 CONFIG_CLK=y
+CONFIG_MMC=y
+CONFIG_MMC_DEFAULT_DEV=1
+CONFIG_MMC_SUPPORTS_TUNING=y
 CONFIG_MMC_HS200_SUPPORT=y
 CONFIG_MMC_MTK=y
 CONFIG_MTD=y
--- a/arch/arm/dts/mt7988-rfb.dts
+++ b/arch/arm/dts/mt7988-rfb.dts
@@ -144,6 +144,23 @@
 		compatible = "spi-nand";
 		reg = <0>;
 		spi-max-frequency = <52000000>;
+
+		partitions {
+			compatible = "fixed-partitions";
+			#address-cells = <1>;
+			#size-cells = <1>;
+
+			partition@0 {
+				label = "bl2";
+				reg = <0x0 0x200000>;
+			};
+
+			partition@200000 {
+				label = "ubi";
+				reg = <0x200000 0x7e00000>;
+				compatible = "linux,ubi";
+			};
+		};
 	};
 };
 
