--- a/board/mediatek/mt7986/mt7986_rfb.c
+++ b/board/mediatek/mt7986/mt7986_rfb.c
@@ -6,9 +6,16 @@
 
 #include <common.h>
 #include <config.h>
+#include <dm.h>
+#include <button.h>
 #include <env.h>
 #include <init.h>
 #include <asm/global_data.h>
+#include <linux/delay.h>
+
+#ifndef CONFIG_RESET_BUTTON_LABEL
+#define CONFIG_RESET_BUTTON_LABEL "reset"
+#endif
 
 #include <mtd.h>
 #include <linux/mtd/mtd.h>
@@ -24,7 +31,22 @@ int board_init(void)
 
 int board_late_init(void)
 {
-	gd->env_valid = 1; //to load environment variable from persistent store
+	struct udevice *dev;
+
+	gd->env_valid = ENV_VALID;
+	if (!button_get_by_label(CONFIG_RESET_BUTTON_LABEL, &dev)) {
+		puts("reset button found\n");
+#ifdef CONFIG_RESET_BUTTON_SETTLE_DELAY
+		if (CONFIG_RESET_BUTTON_SETTLE_DELAY > 0) {
+			button_get_state(dev);
+			mdelay(CONFIG_RESET_BUTTON_SETTLE_DELAY);
+		}
+#endif
+		if (button_get_state(dev) == BUTTON_ON) {
+			puts("button pushed, resetting environment\n");
+			gd->env_valid = ENV_INVALID;
+		}
+	}
 	env_relocate();
 	return 0;
 }
