--- a/configs/mt7981_rfb_defconfig
+++ b/configs/mt7981_rfb_defconfig
@@ -30,6 +30,9 @@ CONFIG_CMD_MTD=y
 CONFIG_CMD_SF_TEST=y
 CONFIG_CMD_PING=y
 CONFIG_CMD_SMC=y
+CONFIG_CMD_UBI=y
+CONFIG_CMD_UBI_RENAME=y
+CONFIG_ENV_OVERWRITE=y
 CONFIG_ENV_VARS_UBOOT_RUNTIME_CONFIG=y
 CONFIG_NET_RANDOM_ETHADDR=y
 CONFIG_REGMAP=y
--- a/configs/mt7981_snfi_nand_rfb_defconfig
+++ b/configs/mt7981_snfi_nand_rfb_defconfig
@@ -1,11 +1,12 @@
 CONFIG_ARM=y
+CONFIG_SYS_HAS_NONCACHED_MEMORY=y
 CONFIG_POSITION_INDEPENDENT=y
 CONFIG_ARCH_MEDIATEK=y
 CONFIG_TEXT_BASE=0x41e00000
 CONFIG_SYS_MALLOC_F_LEN=0x4000
 CONFIG_NR_DRAM_BANKS=1
-CONFIG_ENV_SIZE=0x20000
 CONFIG_DEFAULT_DEVICE_TREE="mt7981-snfi-nand-rfb"
+CONFIG_SYS_PROMPT="MT7981> "
 CONFIG_TARGET_MT7981=y
 CONFIG_DEBUG_UART_BASE=0x11002000
 CONFIG_DEBUG_UART_CLOCK=********
@@ -15,7 +16,6 @@ CONFIG_DEBUG_UART=y
 CONFIG_DEFAULT_FDT_FILE="mt7981-snfi-nand-rfb"
 CONFIG_LOGLEVEL=7
 CONFIG_LOG=y
-CONFIG_SYS_PROMPT="MT7981> "
 CONFIG_SYS_CBSIZE=512
 CONFIG_SYS_PBSIZE=1049
 # CONFIG_BOOTM_NETBSD is not set
@@ -29,8 +29,6 @@ CONFIG_CMD_GPIO=y
 CONFIG_CMD_MTD=y
 CONFIG_CMD_PING=y
 CONFIG_CMD_SMC=y
-CONFIG_MTDIDS_DEFAULT="spi-nand0=spi-nand0"
-CONFIG_MTDPARTS_DEFAULT="spi-nand0:1024k(bl2),512k(u-boot-env),2048k(factory),2048k(fip),65536k(ubi)"
 CONFIG_CMD_UBI=y
 CONFIG_CMD_UBI_RENAME=y
 CONFIG_ENV_OVERWRITE=y
@@ -45,7 +43,6 @@ CONFIG_DM_MTD=y
 CONFIG_MTK_SPI_NAND=y
 CONFIG_MTK_SPI_NAND_MTD=y
 CONFIG_PHY_FIXED=y
-CONFIG_DM_ETH=y
 CONFIG_MEDIATEK_ETH=y
 CONFIG_PINCTRL=y
 CONFIG_PINCONF=y
@@ -55,3 +52,4 @@ CONFIG_MTK_POWER_DOMAIN=y
 CONFIG_DM_SERIAL=y
 CONFIG_MTK_SERIAL=y
 CONFIG_HEXDUMP=y
+CONFIG_LMB_MAX_REGIONS=64
--- /dev/null
+++ b/configs/mt7981_nor_rfb_defconfig
@@ -0,0 +1,68 @@
+CONFIG_ARM=y
+CONFIG_SYS_HAS_NONCACHED_MEMORY=y
+CONFIG_POSITION_INDEPENDENT=y
+CONFIG_ARCH_MEDIATEK=y
+CONFIG_TEXT_BASE=0x41e00000
+CONFIG_SYS_MALLOC_F_LEN=0x4000
+CONFIG_NR_DRAM_BANKS=1
+CONFIG_DEFAULT_DEVICE_TREE="mt7981-rfb"
+CONFIG_SYS_PROMPT="MT7981> "
+CONFIG_TARGET_MT7981=y
+CONFIG_DEBUG_UART_BASE=0x11002000
+CONFIG_DEBUG_UART_CLOCK=********
+CONFIG_SYS_LOAD_ADDR=0x46000000
+CONFIG_DEBUG_UART=y
+# CONFIG_AUTOBOOT is not set
+CONFIG_DEFAULT_FDT_FILE="mt7981-rfb"
+CONFIG_LOGLEVEL=7
+CONFIG_LOG=y
+CONFIG_SYS_CBSIZE=512
+CONFIG_SYS_PBSIZE=1049
+# CONFIG_BOOTM_NETBSD is not set
+# CONFIG_BOOTM_PLAN9 is not set
+# CONFIG_BOOTM_RTEMS is not set
+# CONFIG_BOOTM_VXWORKS is not set
+# CONFIG_CMD_ELF is not set
+# CONFIG_CMD_UNLZ4 is not set
+# CONFIG_CMD_UNZIP is not set
+CONFIG_CMD_GPIO=y
+CONFIG_CMD_MTD=y
+CONFIG_CMD_SF_TEST=y
+CONFIG_CMD_PING=y
+CONFIG_CMD_SMC=y
+CONFIG_ENV_OVERWRITE=y
+CONFIG_ENV_VARS_UBOOT_RUNTIME_CONFIG=y
+CONFIG_NET_RANDOM_ETHADDR=y
+CONFIG_REGMAP=y
+CONFIG_SYSCON=y
+CONFIG_CLK=y
+# CONFIG_MMC is not set
+CONFIG_MTD=y
+CONFIG_DM_MTD=y
+CONFIG_MTD_SPI_NAND=y
+CONFIG_DM_SPI_FLASH=y
+CONFIG_SPI_FLASH_SFDP_SUPPORT=y
+CONFIG_SPI_FLASH_EON=y
+CONFIG_SPI_FLASH_GIGADEVICE=y
+CONFIG_SPI_FLASH_ISSI=y
+CONFIG_SPI_FLASH_MACRONIX=y
+CONFIG_SPI_FLASH_SPANSION=y
+CONFIG_SPI_FLASH_STMICRO=y
+CONFIG_SPI_FLASH_WINBOND=y
+CONFIG_SPI_FLASH_XMC=y
+CONFIG_SPI_FLASH_XTX=y
+CONFIG_SPI_FLASH_MTD=y
+CONFIG_PHY_FIXED=y
+CONFIG_MEDIATEK_ETH=y
+CONFIG_PINCTRL=y
+CONFIG_PINCONF=y
+CONFIG_PINCTRL_MT7981=y
+CONFIG_POWER_DOMAIN=y
+CONFIG_MTK_POWER_DOMAIN=y
+CONFIG_DM_SERIAL=y
+CONFIG_MTK_SERIAL=y
+CONFIG_SPI=y
+CONFIG_DM_SPI=y
+CONFIG_MTK_SPIM=y
+CONFIG_HEXDUMP=y
+CONFIG_LMB_MAX_REGIONS=64
