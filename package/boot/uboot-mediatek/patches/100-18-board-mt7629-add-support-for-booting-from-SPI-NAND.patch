From 452dc98572f8353f77551bcce5a2ca8cd050f498 Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON><PERSON> <<EMAIL>>
Date: Wed, 3 Mar 2021 10:48:53 +0800
Subject: [PATCH 53/71] board: mt7629: add support for booting from SPI-NAND

Add support for mt7629 to boot from SPI-NAND.
Add a new defconfig for mt7629+spi-nand configuration.

Signed-off-by: <PERSON><PERSON><PERSON> <<EMAIL>>
---
 arch/arm/dts/mt7629-rfb-u-boot.dtsi |   8 ++
 arch/arm/dts/mt7629-rfb.dts         |  10 +++
 arch/arm/dts/mt7629.dtsi            |  16 ++++
 arch/arm/mach-mediatek/Kconfig      |   4 +-
 board/mediatek/mt7629/Kconfig       |  40 ++++++++++
 board/mediatek/mt7629/mt7629_rfb.c  |   5 ++
 configs/mt7629_nand_rfb_defconfig   | 113 ++++++++++++++++++++++++++++
 7 files changed, 195 insertions(+), 1 deletion(-)
 create mode 100644 board/mediatek/mt7629/Kconfig
 create mode 100644 configs/mt7629_nand_rfb_defconfig

--- a/arch/arm/dts/mt7629-rfb-u-boot.dtsi
+++ b/arch/arm/dts/mt7629-rfb-u-boot.dtsi
@@ -40,3 +40,11 @@
 &snfi {
 	bootph-all;
 };
+
+&pinctrl {
+	u-boot,dm-pre-reloc;
+};
+
+&snand {
+	u-boot,dm-pre-reloc;
+};
--- a/arch/arm/dts/mt7629-rfb.dts
+++ b/arch/arm/dts/mt7629-rfb.dts
@@ -47,9 +47,12 @@
 	};
 
 	snfi_pins: snfi-pins {
+		u-boot,dm-pre-reloc;
+
 		mux {
 			function = "flash";
 			groups = "snfi";
+			u-boot,dm-pre-reloc;
 		};
 	};
 
@@ -102,6 +105,13 @@
 	};
 };
 
+&snand {
+	pinctrl-names = "default";
+	pinctrl-0 = <&snfi_pins>;
+	status = "okay";
+	quad-spi;
+};
+
 &uart0 {
 	pinctrl-names = "default";
 	pinctrl-0 = <&uart0_pins>;
--- a/arch/arm/dts/mt7629.dtsi
+++ b/arch/arm/dts/mt7629.dtsi
@@ -229,6 +229,22 @@
 		#size-cells = <0>;
 	};
 
+	snand: snand@1100d000 {
+		compatible = "mediatek,mt7629-snand";
+		reg = <0x1100d000 0x1000>,
+		      <0x1100e000 0x1000>;
+		reg-names = "nfi", "ecc";
+		clocks = <&pericfg CLK_PERI_NFI_PD>,
+			 <&pericfg CLK_PERI_SNFI_PD>,
+			 <&pericfg CLK_PERI_NFIECC_PD>;
+		clock-names = "nfi_clk", "pad_clk", "ecc_clk";
+		assigned-clocks = <&topckgen CLK_TOP_AXI_SEL>,
+				  <&topckgen CLK_TOP_NFI_INFRA_SEL>;
+		assigned-clock-parents = <&topckgen CLK_TOP_SYSPLL1_D2>,
+					 <&topckgen CLK_TOP_UNIVPLL2_D8>;
+		status = "disabled";
+	};
+
 	snor: snor@11014000 {
 		compatible = "mediatek,mtk-snor";
 		reg = <0x11014000 0x1000>;
--- a/arch/arm/mach-mediatek/Kconfig
+++ b/arch/arm/mach-mediatek/Kconfig
@@ -144,9 +144,11 @@ config SYS_CONFIG_NAME
 
 config MTK_BROM_HEADER_INFO
 	string
-	default "media=nor" if TARGET_MT8518 || TARGET_MT8512 || TARGET_MT7629 || TARGET_MT7622
+	default "media=nor" if TARGET_MT8518 || TARGET_MT8512 || TARGET_MT7622
 	default "media=emmc" if TARGET_MT8516 || TARGET_MT8365 || TARGET_MT8183
 	default "media=snand;nandinfo=2k+64" if TARGET_MT7981 || TARGET_MT7986 || TARGET_MT7988
 	default "lk=1" if TARGET_MT7623
 
+source "board/mediatek/mt7629/Kconfig"
+
 endif
--- /dev/null
+++ b/board/mediatek/mt7629/Kconfig
@@ -0,0 +1,40 @@
+if TARGET_MT7629
+
+config MTK_BROM_HEADER_INFO
+	string
+	default "media=nor" if BOOT_FROM_SNOR
+	default "media=snand;nandinfo=2k+64" if BOOT_FROM_SNAND_2K_64
+	default "media=snand;nandinfo=2k+128" if BOOT_FROM_SNAND_2K_128
+	default "media=snand;nandinfo=4k+128" if BOOT_FROM_SNAND_4K_128
+	default "media=snand;nandinfo=4k+256" if BOOT_FROM_SNAND_4K_256
+
+choice
+	prompt "Boot device"
+	default BOOT_FROM_SNOR
+
+config BOOT_FROM_SNOR
+	bool "SPI-NOR"
+
+config BOOT_FROM_SNAND_2K_64
+	bool "SPI-NAND (2K+64)"
+	select MT7629_BOOT_FROM_SNAND
+
+config BOOT_FROM_SNAND_2K_128
+	bool "SPI-NAND (2K+128)"
+	select MT7629_BOOT_FROM_SNAND
+
+config BOOT_FROM_SNAND_4K_128
+	bool "SPI-NAND (4K+128)"
+	select MT7629_BOOT_FROM_SNAND
+
+config BOOT_FROM_SNAND_4K_256
+	bool "SPI-NAND (4K+256)"
+	select MT7629_BOOT_FROM_SNAND
+
+endchoice
+
+config MT7629_BOOT_FROM_SNAND
+	bool
+	default n
+
+endif
--- a/board/mediatek/mt7629/mt7629_rfb.c
+++ b/board/mediatek/mt7629/mt7629_rfb.c
@@ -15,3 +15,8 @@ int board_init(void)
 
 	return 0;
 }
+
+uint32_t spl_nand_get_uboot_raw_page(void)
+{
+	return CONFIG_SPL_PAD_TO;
+}
--- /dev/null
+++ b/configs/mt7629_nand_rfb_defconfig
@@ -0,0 +1,113 @@
+CONFIG_ARM=y
+CONFIG_SYS_ARCH_TIMER=y
+CONFIG_SYS_THUMB_BUILD=y
+CONFIG_ARCH_MEDIATEK=y
+CONFIG_TEXT_BASE=0x41e00000
+CONFIG_SYS_MALLOC_F_LEN=0x4000
+CONFIG_NR_DRAM_BANKS=1
+CONFIG_ENV_SIZE=0x20000
+CONFIG_ENV_OFFSET=0x0
+CONFIG_DEFAULT_DEVICE_TREE="mt7629-rfb"
+CONFIG_SPL_TEXT_BASE=0x201000
+CONFIG_TARGET_MT7629=y
+CONFIG_BOOT_FROM_SNAND_2K_64=y
+CONFIG_SPL_SERIAL=y
+CONFIG_SPL_STACK_R_ADDR=0x40800000
+CONFIG_SYS_LOAD_ADDR=0x42007f1c
+CONFIG_SPL_PAYLOAD="u-boot.img"
+CONFIG_BUILD_TARGET="u-boot-mtk.bin"
+CONFIG_HAS_CUSTOM_SYS_INIT_SP_ADDR=y
+CONFIG_CUSTOM_SYS_INIT_SP_ADDR=0x41fffef0
+CONFIG_SPL_IMAGE="spl/u-boot-spl-mtk.bin"
+CONFIG_FIT=y
+# CONFIG_AUTOBOOT is not set
+CONFIG_DEFAULT_FDT_FILE="mt7629-rfb"
+CONFIG_SYS_CONSOLE_IS_IN_ENV=y
+# CONFIG_DISPLAY_BOARDINFO is not set
+CONFIG_SPL_MAX_SIZE=0x20000
+CONFIG_SPL_FOOTPRINT_LIMIT=y
+CONFIG_SPL_MAX_FOOTPRINT=0x20000
+CONFIG_SPL_SYS_MALLOC_SIMPLE=y
+# CONFIG_SPL_SHARES_INIT_SP_ADDR is not set
+CONFIG_SPL_STACK=0x106000
+CONFIG_SPL_STACK_R=y
+CONFIG_SPL_MTD_SUPPORT=y
+CONFIG_SPL_NAND_SUPPORT=y
+CONFIG_SPL_WATCHDOG=y
+CONFIG_HUSH_PARSER=y
+CONFIG_SYS_PROMPT="U-Boot> "
+# CONFIG_BOOTM_NETBSD is not set
+# CONFIG_BOOTM_PLAN9 is not set
+# CONFIG_BOOTM_RTEMS is not set
+# CONFIG_BOOTM_VXWORKS is not set
+CONFIG_SYS_BOOTM_LEN=0x4000000
+CONFIG_CMD_BOOTMENU=y
+# CONFIG_CMD_ELF is not set
+# CONFIG_CMD_XIMG is not set
+CONFIG_CMD_BIND=y
+CONFIG_CMD_DM=y
+# CONFIG_CMD_FLASH is not set
+CONFIG_CMD_GPIO=y
+CONFIG_CMD_MTD=y
+CONFIG_CMD_USB=y
+# CONFIG_CMD_SETEXPR is not set
+# CONFIG_CMD_NFS is not set
+CONFIG_CMD_PING=y
+CONFIG_CMD_FAT=y
+CONFIG_CMD_FS_GENERIC=y
+CONFIG_CMD_LOG=y
+CONFIG_OF_SPL_REMOVE_PROPS="interrupt-parent assigned-clocks assigned-clock-parents"
+CONFIG_ENV_OVERWRITE=y
+CONFIG_ENV_IS_IN_MTD=y
+CONFIG_ENV_MTD_NAME="u-boot-env"
+CONFIG_ENV_SIZE_REDUND=0x40000
+CONFIG_SYS_RELOC_GD_ENV_ADDR=y
+CONFIG_NET_RANDOM_ETHADDR=y
+CONFIG_SPL_DM_SEQ_ALIAS=y
+CONFIG_REGMAP=y
+CONFIG_SPL_REGMAP=y
+CONFIG_SYSCON=y
+CONFIG_SPL_SYSCON=y
+CONFIG_CLK=y
+CONFIG_SPL_CLK=y
+# CONFIG_MMC is not set
+CONFIG_MTD=y
+CONFIG_DM_MTD=y
+CONFIG_MTK_SPI_NAND=y
+CONFIG_MTK_SPI_NAND_MTD=y
+CONFIG_SPL_MTK_SPI_NAND=y
+CONFIG_DM_ETH=y
+CONFIG_MEDIATEK_ETH=y
+CONFIG_PHY=y
+CONFIG_PHY_MTK_TPHY=y
+CONFIG_PINCTRL=y
+CONFIG_PINCONF=y
+CONFIG_SPL_PINCTRL=y
+CONFIG_SPL_PINCONF=y
+CONFIG_PINCTRL_MT7629=y
+CONFIG_POWER_DOMAIN=y
+CONFIG_MTK_POWER_DOMAIN=y
+CONFIG_DM_REGULATOR=y
+CONFIG_DM_REGULATOR_FIXED=y
+CONFIG_RAM=y
+CONFIG_SPL_RAM=y
+CONFIG_DM_SERIAL=y
+CONFIG_MTK_SERIAL=y
+CONFIG_SPI=y
+CONFIG_DM_SPI=y
+CONFIG_SPI_MEM=y
+CONFIG_MTK_SNFI_SPI=y
+CONFIG_SYSRESET=y
+CONFIG_SPL_SYSRESET=y
+CONFIG_SYSRESET_WATCHDOG=y
+CONFIG_USB=y
+# CONFIG_SPL_DM_USB is not set
+CONFIG_USB_XHCI_HCD=y
+CONFIG_USB_XHCI_MTK=y
+CONFIG_USB_STORAGE=y
+CONFIG_WDT_MTK=y
+# CONFIG_SHA256 is not set
+# CONFIG_SPL_SHA1 is not set
+CONFIG_LZMA=y
+CONFIG_SPL_LZMA=y
+# CONFIG_EFI_LOADER is not set
