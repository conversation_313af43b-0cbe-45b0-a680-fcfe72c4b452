From d26a789c451068caf4bbb4d1ac7bc1f592b5493e Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON><PERSON> <<EMAIL>>
Date: Mon, 25 Jul 2022 10:58:06 +0800
Subject: [PATCH 39/71] mtd: add a new mtd device type for NMBM

This patch adds a new mtd device type for NMBM so that mtdparts can be
correctly probed. And this also gives us an opportunity to add NMBM support
for filesystems in the future.

Signed-off-by: <PERSON><PERSON><PERSON> <<EMAIL>>
---
 cmd/mtdparts.c              | 3 +++
 include/jffs2/load_kernel.h | 4 +++-
 2 files changed, 6 insertions(+), 1 deletion(-)

--- a/cmd/mtdparts.c
+++ b/cmd/mtdparts.c
@@ -1057,6 +1057,9 @@ int mtd_id_parse(const char *id, const c
 	} else if (strncmp(p, "spi-nand", 8) == 0) {
 		*dev_type = MTD_DEV_TYPE_SPINAND;
 		p += 8;
+	} else if (strncmp(p, "nmbm", 4) == 0) {
+		*dev_type = MTD_DEV_TYPE_NMBM;
+		p += 4;
 	} else {
 		printf("incorrect device type in %s\n", id);
 		return 1;
--- a/include/jffs2/load_kernel.h
+++ b/include/jffs2/load_kernel.h
@@ -16,11 +16,13 @@
 #define MTD_DEV_TYPE_NAND	0x0002
 #define MTD_DEV_TYPE_ONENAND	0x0004
 #define MTD_DEV_TYPE_SPINAND	0x0008
+#define MTD_DEV_TYPE_NMBM	0x0010
 
 #define MTD_DEV_TYPE(type) (type == MTD_DEV_TYPE_NAND ? "nand" :	\
 			    (type == MTD_DEV_TYPE_NOR ? "nor" :		\
 			     (type == MTD_DEV_TYPE_ONENAND ? "onenand" : \
-			      "spi-nand")))				\
+			      (type == MTD_DEV_TYPE_SPINAND ? "spi-nand" : \
+			       "nmbm"))))				\
 
 struct mtd_device {
 	struct list_head link;
