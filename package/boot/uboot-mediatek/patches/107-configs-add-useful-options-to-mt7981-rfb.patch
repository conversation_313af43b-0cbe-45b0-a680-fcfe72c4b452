--- a/configs/mt7981_emmc_rfb_defconfig
+++ b/configs/mt7981_emmc_rfb_defconfig
@@ -13,7 +13,22 @@ CONFIG_DEBUG_UART_BASE=0x11002000
 CONFIG_DEBUG_UART_CLOCK=40000000
 CONFIG_SYS_LOAD_ADDR=0x46000000
 CONFIG_DEBUG_UART=y
-# CONFIG_AUTOBOOT is not set
+CONFIG_OF_LIBFDT_OVERLAY=y
+CONFIG_SMBIOS_PRODUCT_NAME=""
+CONFIG_CFB_CONSOLE_ANSI=y
+CONFIG_BOARD_LATE_INIT=y
+CONFIG_BUTTON=y
+CONFIG_BUTTON_GPIO=y
+CONFIG_GPIO_HOG=y
+CONFIG_CMD_ENV_FLAGS=y
+CONFIG_FIT=y
+CONFIG_FIT_ENABLE_SHA256_SUPPORT=y
+CONFIG_LED=y
+CONFIG_LED_BLINK=y
+CONFIG_LED_GPIO=y
+CONFIG_MMC_BOOT=y
+CONFIG_BOOTSTD_DEFAULTS=y
+CONFIG_BOOTSTD_FULL=y
 CONFIG_DEFAULT_FDT_FILE="mt7981-emmc-rfb"
 CONFIG_LOGLEVEL=7
 CONFIG_LOG=y
@@ -24,9 +39,23 @@ CONFIG_SYS_PBSIZE=1049
 # CONFIG_BOOTM_PLAN9 is not set
 # CONFIG_BOOTM_RTEMS is not set
 # CONFIG_BOOTM_VXWORKS is not set
-# CONFIG_CMD_ELF is not set
 # CONFIG_CMD_UNLZ4 is not set
 # CONFIG_CMD_UNZIP is not set
+CONFIG_CMD_BOOTMENU=y
+CONFIG_CMD_BOOTP=y
+CONFIG_CMD_BUTTON=y
+CONFIG_CMD_CACHE=y
+CONFIG_CMD_CDP=y
+CONFIG_CMD_CPU=y
+CONFIG_CMD_DHCP=y
+CONFIG_CMD_DM=y
+CONFIG_CMD_ELF=y
+CONFIG_CMD_DNS=y
+CONFIG_CMD_ECHO=y
+CONFIG_CMD_ENV_READMEM=y
+CONFIG_CMD_ERASEENV=y
+CONFIG_CMD_FDT=y
+CONFIG_CMD_FS_UUID=y
 CONFIG_CMD_GPIO=y
 CONFIG_CMD_GPT=y
 CONFIG_CMD_GPT_RENAME=y
@@ -36,13 +65,35 @@ CONFIG_CMD_PART=y
 CONFIG_CMD_READ=y
 CONFIG_CMD_PING=y
 CONFIG_CMD_SMC=y
+CONFIG_CMD_TFTPBOOT=y
+CONFIG_CMD_TFTPSRV=y
+CONFIG_CMD_ASKENV=y
+CONFIG_CMD_RARP=y
+CONFIG_CMD_SETEXPR=y
+CONFIG_CMD_SLEEP=y
+CONFIG_CMD_SNTP=y
+CONFIG_CMD_SOURCE=y
+CONFIG_CMD_STRINGS=y
+CONFIG_CMD_UUID=y
 CONFIG_CMD_FAT=y
 CONFIG_CMD_FS_GENERIC=y
+CONFIG_CMD_HASH=y
+CONFIG_CMD_ITEST=y
+CONFIG_CMD_LED=y
+CONFIG_CMD_LICENSE=y
+CONFIG_CMD_LINK_LOCAL=y
+CONFIG_CMD_PSTORE=y
+CONFIG_CMD_PSTORE_MEM_ADDR=0x42ff0000
+CONFIG_CMD_SF=y
+CONFIG_CMD_SF_TEST=y
+CONFIG_CMD_PING=y
+CONFIG_CMD_PXE=y
 CONFIG_PARTITION_TYPE_GUID=y
 CONFIG_ENV_OVERWRITE=y
 CONFIG_ENV_IS_IN_MMC=y
 CONFIG_ENV_VARS_UBOOT_RUNTIME_CONFIG=y
 CONFIG_NET_RANDOM_ETHADDR=y
+CONFIG_REGEX=y
 CONFIG_REGMAP=y
 CONFIG_SYSCON=y
 CONFIG_CLK=y
--- a/configs/mt7981_rfb_defconfig
+++ b/configs/mt7981_rfb_defconfig
@@ -11,7 +11,23 @@ CONFIG_DEBUG_UART_BASE=0x11002000
 CONFIG_DEBUG_UART_CLOCK=40000000
 CONFIG_SYS_LOAD_ADDR=0x46000000
 CONFIG_DEBUG_UART=y
-# CONFIG_AUTOBOOT is not set
+CONFIG_OF_LIBFDT_OVERLAY=y
+CONFIG_SMBIOS_PRODUCT_NAME=""
+CONFIG_CFB_CONSOLE_ANSI=y
+CONFIG_BOARD_LATE_INIT=y
+CONFIG_BUTTON=y
+CONFIG_BUTTON_GPIO=y
+CONFIG_GPIO_HOG=y
+CONFIG_CMD_ENV_FLAGS=y
+CONFIG_FIT=y
+CONFIG_FIT_ENABLE_SHA256_SUPPORT=y
+CONFIG_LED=y
+CONFIG_LED_BLINK=y
+CONFIG_LED_GPIO=y
+CONFIG_SPI_BOOT=y
+CONFIG_NAND_BOOT=y
+CONFIG_BOOTSTD_DEFAULTS=y
+CONFIG_BOOTSTD_FULL=y
 CONFIG_DEFAULT_FDT_FILE="mt7981-rfb"
 CONFIG_LOGLEVEL=7
 CONFIG_LOG=y
@@ -22,23 +38,74 @@ CONFIG_SYS_PBSIZE=1049
 # CONFIG_BOOTM_PLAN9 is not set
 # CONFIG_BOOTM_RTEMS is not set
 # CONFIG_BOOTM_VXWORKS is not set
-# CONFIG_CMD_ELF is not set
 # CONFIG_CMD_UNLZ4 is not set
 # CONFIG_CMD_UNZIP is not set
+CONFIG_CMD_BOOTMENU=y
+CONFIG_CMD_BOOTP=y
+CONFIG_CMD_BUTTON=y
+CONFIG_CMD_CACHE=y
+CONFIG_CMD_CDP=y
+CONFIG_CMD_CPU=y
+CONFIG_CMD_DHCP=y
+CONFIG_CMD_DM=y
+CONFIG_CMD_ELF=y
+CONFIG_CMD_DNS=y
+CONFIG_CMD_ECHO=y
+CONFIG_CMD_ENV_READMEM=y
+CONFIG_CMD_ERASEENV=y
+CONFIG_CMD_FDT=y
+CONFIG_CMD_FS_GENERIC=y
+CONFIG_CMD_FS_UUID=y
 CONFIG_CMD_GPIO=y
 CONFIG_CMD_MTD=y
-CONFIG_CMD_SF_TEST=y
+CONFIG_CMD_HASH=y
+CONFIG_CMD_ITEST=y
+CONFIG_CMD_LED=y
+CONFIG_CMD_LICENSE=y
+CONFIG_CMD_LINK_LOCAL=y
+CONFIG_CMD_MTD=y
+CONFIG_CMD_NAND=y
+# CONFIG_MTD_RAW_NAND is not set
+CONFIG_CMD_NAND_TRIMFFS=y
+CONFIG_CMD_PCI=y
+CONFIG_CMD_PSTORE=y
+CONFIG_CMD_PSTORE_MEM_ADDR=0x42ff0000
 CONFIG_CMD_PING=y
+CONFIG_CMD_PXE=y
+CONFIG_CMD_SF=y
+CONFIG_CMD_SF_TEST=y
 CONFIG_CMD_SMC=y
 CONFIG_CMD_UBI=y
 CONFIG_CMD_UBI_RENAME=y
+CONFIG_CMD_UBIFS=y
+CONFIG_CMD_TFTPBOOT=y
+CONFIG_CMD_TFTPSRV=y
+CONFIG_CMD_ASKENV=y
+CONFIG_CMD_RARP=y
+CONFIG_CMD_SETEXPR=y
+CONFIG_CMD_SLEEP=y
+CONFIG_CMD_SNTP=y
+CONFIG_CMD_SOURCE=y
+CONFIG_CMD_STRINGS=y
+CONFIG_CMD_UUID=y
+CONFIG_ENV_IS_IN_UBI=y
+CONFIG_ENV_UBI_PART="ubi"
+CONFIG_ENV_SIZE=0x1f000
+CONFIG_ENV_SIZE_REDUND=0x1f000
+CONFIG_ENV_UBI_VOLUME="ubootenv"
+CONFIG_ENV_UBI_VOLUME_REDUND="ubootenv2"
+CONFIG_ENV_VARS_UBOOT_RUNTIME_CONFIG=y
 CONFIG_ENV_OVERWRITE=y
 CONFIG_ENV_VARS_UBOOT_RUNTIME_CONFIG=y
 CONFIG_NET_RANDOM_ETHADDR=y
+CONFIG_REGEX=y
 CONFIG_REGMAP=y
 CONFIG_SYSCON=y
 CONFIG_CLK=y
 # CONFIG_MMC is not set
+CONFIG_HUSH_PARSER=y
+CONFIG_SYS_REDUNDAND_ENVIRONMENT=y
+CONFIG_SYS_RELOC_GD_ENV_ADDR=y
 CONFIG_MTD=y
 CONFIG_DM_MTD=y
 CONFIG_MTD_SPI_NAND=y
--- a/configs/mt7981_sd_rfb_defconfig
+++ b/configs/mt7981_sd_rfb_defconfig
@@ -13,7 +13,22 @@ CONFIG_DEBUG_UART_BASE=0x11002000
 CONFIG_DEBUG_UART_CLOCK=40000000
 CONFIG_SYS_LOAD_ADDR=0x46000000
 CONFIG_DEBUG_UART=y
-# CONFIG_AUTOBOOT is not set
+CONFIG_OF_LIBFDT_OVERLAY=y
+CONFIG_SMBIOS_PRODUCT_NAME=""
+CONFIG_CFB_CONSOLE_ANSI=y
+CONFIG_BOARD_LATE_INIT=y
+CONFIG_BUTTON=y
+CONFIG_BUTTON_GPIO=y
+CONFIG_GPIO_HOG=y
+CONFIG_CMD_ENV_FLAGS=y
+CONFIG_FIT=y
+CONFIG_FIT_ENABLE_SHA256_SUPPORT=y
+CONFIG_LED=y
+CONFIG_LED_BLINK=y
+CONFIG_LED_GPIO=y
+CONFIG_MMC_BOOT=y
+CONFIG_BOOTSTD_DEFAULTS=y
+CONFIG_BOOTSTD_FULL=y
 CONFIG_DEFAULT_FDT_FILE="mt7981-sd-rfb"
 CONFIG_LOGLEVEL=7
 CONFIG_LOG=y
@@ -24,9 +39,23 @@ CONFIG_SYS_PBSIZE=1049
 # CONFIG_BOOTM_PLAN9 is not set
 # CONFIG_BOOTM_RTEMS is not set
 # CONFIG_BOOTM_VXWORKS is not set
-# CONFIG_CMD_ELF is not set
 # CONFIG_CMD_UNLZ4 is not set
 # CONFIG_CMD_UNZIP is not set
+CONFIG_CMD_BOOTMENU=y
+CONFIG_CMD_BOOTP=y
+CONFIG_CMD_BUTTON=y
+CONFIG_CMD_CACHE=y
+CONFIG_CMD_CDP=y
+CONFIG_CMD_CPU=y
+CONFIG_CMD_DHCP=y
+CONFIG_CMD_DM=y
+CONFIG_CMD_ELF=y
+CONFIG_CMD_DNS=y
+CONFIG_CMD_ECHO=y
+CONFIG_CMD_ENV_READMEM=y
+CONFIG_CMD_ERASEENV=y
+CONFIG_CMD_FDT=y
+CONFIG_CMD_FS_UUID=y
 CONFIG_CMD_GPIO=y
 CONFIG_CMD_GPT=y
 CONFIG_CMD_GPT_RENAME=y
@@ -36,13 +65,35 @@ CONFIG_CMD_PART=y
 CONFIG_CMD_READ=y
 CONFIG_CMD_PING=y
 CONFIG_CMD_SMC=y
+CONFIG_CMD_TFTPBOOT=y
+CONFIG_CMD_TFTPSRV=y
+CONFIG_CMD_ASKENV=y
+CONFIG_CMD_RARP=y
+CONFIG_CMD_SETEXPR=y
+CONFIG_CMD_SLEEP=y
+CONFIG_CMD_SNTP=y
+CONFIG_CMD_SOURCE=y
+CONFIG_CMD_STRINGS=y
+CONFIG_CMD_UUID=y
 CONFIG_CMD_FAT=y
 CONFIG_CMD_FS_GENERIC=y
+CONFIG_CMD_HASH=y
+CONFIG_CMD_ITEST=y
+CONFIG_CMD_LED=y
+CONFIG_CMD_LICENSE=y
+CONFIG_CMD_LINK_LOCAL=y
+CONFIG_CMD_PSTORE=y
+CONFIG_CMD_PSTORE_MEM_ADDR=0x42ff0000
+CONFIG_CMD_SF=y
+CONFIG_CMD_SF_TEST=y
+CONFIG_CMD_PING=y
+CONFIG_CMD_PXE=y
 CONFIG_PARTITION_TYPE_GUID=y
 CONFIG_ENV_OVERWRITE=y
 CONFIG_ENV_IS_IN_MMC=y
 CONFIG_ENV_VARS_UBOOT_RUNTIME_CONFIG=y
 CONFIG_NET_RANDOM_ETHADDR=y
+CONFIG_REGEX=y
 CONFIG_REGMAP=y
 CONFIG_SYSCON=y
 CONFIG_CLK=y
--- a/configs/mt7981_snfi_nand_rfb_defconfig
+++ b/configs/mt7981_snfi_nand_rfb_defconfig
@@ -12,7 +12,23 @@ CONFIG_DEBUG_UART_BASE=0x11002000
 CONFIG_DEBUG_UART_CLOCK=40000000
 CONFIG_SYS_LOAD_ADDR=0x46000000
 CONFIG_DEBUG_UART=y
-# CONFIG_AUTOBOOT is not set
+CONFIG_OF_LIBFDT_OVERLAY=y
+CONFIG_SMBIOS_PRODUCT_NAME=""
+CONFIG_CFB_CONSOLE_ANSI=y
+CONFIG_BOARD_LATE_INIT=y
+CONFIG_BUTTON=y
+CONFIG_BUTTON_GPIO=y
+CONFIG_GPIO_HOG=y
+CONFIG_CMD_ENV_FLAGS=y
+CONFIG_FIT=y
+CONFIG_FIT_ENABLE_SHA256_SUPPORT=y
+CONFIG_LED=y
+CONFIG_LED_BLINK=y
+CONFIG_LED_GPIO=y
+CONFIG_SPI_BOOT=y
+CONFIG_NAND_BOOT=y
+CONFIG_BOOTSTD_DEFAULTS=y
+CONFIG_BOOTSTD_FULL=y
 CONFIG_DEFAULT_FDT_FILE="mt7981-snfi-nand-rfb"
 CONFIG_LOGLEVEL=7
 CONFIG_LOG=y
@@ -22,22 +38,73 @@ CONFIG_SYS_PBSIZE=1049
 # CONFIG_BOOTM_PLAN9 is not set
 # CONFIG_BOOTM_RTEMS is not set
 # CONFIG_BOOTM_VXWORKS is not set
-# CONFIG_CMD_ELF is not set
 # CONFIG_CMD_UNLZ4 is not set
 # CONFIG_CMD_UNZIP is not set
+CONFIG_CMD_BOOTMENU=y
+CONFIG_CMD_BOOTP=y
+CONFIG_CMD_BUTTON=y
+CONFIG_CMD_CACHE=y
+CONFIG_CMD_CDP=y
+CONFIG_CMD_CPU=y
+CONFIG_CMD_DHCP=y
+CONFIG_CMD_DM=y
+CONFIG_CMD_ELF=y
+CONFIG_CMD_DNS=y
+CONFIG_CMD_ECHO=y
+CONFIG_CMD_ENV_READMEM=y
+CONFIG_CMD_ERASEENV=y
+CONFIG_CMD_FDT=y
+CONFIG_CMD_FS_GENERIC=y
+CONFIG_CMD_FS_UUID=y
 CONFIG_CMD_GPIO=y
 CONFIG_CMD_MTD=y
+CONFIG_CMD_HASH=y
+CONFIG_CMD_ITEST=y
+CONFIG_CMD_LED=y
+CONFIG_CMD_LICENSE=y
+CONFIG_CMD_LINK_LOCAL=y
+CONFIG_CMD_MTD=y
+CONFIG_CMD_NAND=y
+# CONFIG_MTD_RAW_NAND is not set
+CONFIG_CMD_NAND_TRIMFFS=y
+CONFIG_CMD_PCI=y
+CONFIG_CMD_PSTORE=y
+CONFIG_CMD_PSTORE_MEM_ADDR=0x42ff0000
+CONFIG_CMD_SF=y
+CONFIG_CMD_SF_TEST=y
 CONFIG_CMD_PING=y
+CONFIG_CMD_PXE=y
 CONFIG_CMD_SMC=y
 CONFIG_CMD_UBI=y
 CONFIG_CMD_UBI_RENAME=y
+CONFIG_CMD_UBIFS=y
+CONFIG_CMD_TFTPBOOT=y
+CONFIG_CMD_TFTPSRV=y
+CONFIG_CMD_ASKENV=y
+CONFIG_CMD_RARP=y
+CONFIG_CMD_SETEXPR=y
+CONFIG_CMD_SLEEP=y
+CONFIG_CMD_SNTP=y
+CONFIG_CMD_SOURCE=y
+CONFIG_CMD_STRINGS=y
+CONFIG_CMD_UUID=y
 CONFIG_ENV_OVERWRITE=y
+CONFIG_ENV_IS_IN_UBI=y
+CONFIG_ENV_UBI_PART="ubi"
+CONFIG_ENV_SIZE=0x1f000
+CONFIG_ENV_SIZE_REDUND=0x1f000
+CONFIG_ENV_UBI_VOLUME="ubootenv"
+CONFIG_ENV_UBI_VOLUME_REDUND="ubootenv2"
 CONFIG_ENV_VARS_UBOOT_RUNTIME_CONFIG=y
 CONFIG_NET_RANDOM_ETHADDR=y
+CONFIG_REGEX=y
 CONFIG_REGMAP=y
 CONFIG_SYSCON=y
 CONFIG_CLK=y
 # CONFIG_MMC is not set
+CONFIG_HUSH_PARSER=y
+CONFIG_SYS_REDUNDAND_ENVIRONMENT=y
+CONFIG_SYS_RELOC_GD_ENV_ADDR=y
 CONFIG_MTD=y
 CONFIG_DM_MTD=y
 CONFIG_MTK_SPI_NAND=y
--- a/configs/mt7981_nor_rfb_defconfig
+++ b/configs/mt7981_nor_rfb_defconfig
@@ -12,7 +12,22 @@ CONFIG_DEBUG_UART_BASE=0x11002000
 CONFIG_DEBUG_UART_CLOCK=40000000
 CONFIG_SYS_LOAD_ADDR=0x46000000
 CONFIG_DEBUG_UART=y
-# CONFIG_AUTOBOOT is not set
+CONFIG_OF_LIBFDT_OVERLAY=y
+CONFIG_SMBIOS_PRODUCT_NAME=""
+CONFIG_CFB_CONSOLE_ANSI=y
+CONFIG_BOARD_LATE_INIT=y
+CONFIG_BUTTON=y
+CONFIG_BUTTON_GPIO=y
+CONFIG_GPIO_HOG=y
+CONFIG_CMD_ENV_FLAGS=y
+CONFIG_FIT=y
+CONFIG_FIT_ENABLE_SHA256_SUPPORT=y
+CONFIG_LED=y
+CONFIG_LED_BLINK=y
+CONFIG_LED_GPIO=y
+CONFIG_SPI_BOOT=y
+CONFIG_BOOTSTD_DEFAULTS=y
+CONFIG_BOOTSTD_FULL=y
 CONFIG_DEFAULT_FDT_FILE="mt7981-rfb"
 CONFIG_LOGLEVEL=7
 CONFIG_LOG=y
@@ -22,21 +37,66 @@ CONFIG_SYS_PBSIZE=1049
 # CONFIG_BOOTM_PLAN9 is not set
 # CONFIG_BOOTM_RTEMS is not set
 # CONFIG_BOOTM_VXWORKS is not set
-# CONFIG_CMD_ELF is not set
 # CONFIG_CMD_UNLZ4 is not set
 # CONFIG_CMD_UNZIP is not set
+CONFIG_CMD_BOOTMENU=y
+CONFIG_CMD_BOOTP=y
+CONFIG_CMD_BUTTON=y
+CONFIG_CMD_CACHE=y
+CONFIG_CMD_CDP=y
+CONFIG_CMD_CPU=y
+CONFIG_CMD_DHCP=y
+CONFIG_CMD_DM=y
+CONFIG_CMD_ELF=y
+CONFIG_CMD_DNS=y
+CONFIG_CMD_ECHO=y
+CONFIG_CMD_ENV_READMEM=y
+CONFIG_CMD_ERASEENV=y
+CONFIG_CMD_FDT=y
+CONFIG_CMD_FS_GENERIC=y
+CONFIG_CMD_FS_UUID=y
 CONFIG_CMD_GPIO=y
 CONFIG_CMD_MTD=y
+CONFIG_CMD_HASH=y
+CONFIG_CMD_ITEST=y
+CONFIG_CMD_LED=y
+CONFIG_CMD_LICENSE=y
+CONFIG_CMD_LINK_LOCAL=y
+CONFIG_CMD_MTD=y
+CONFIG_CMD_PCI=y
+CONFIG_CMD_PSTORE=y
+CONFIG_CMD_PSTORE_MEM_ADDR=0x42ff0000
+CONFIG_CMD_SF=y
 CONFIG_CMD_SF_TEST=y
 CONFIG_CMD_PING=y
+CONFIG_CMD_PXE=y
 CONFIG_CMD_SMC=y
+CONFIG_CMD_TFTPBOOT=y
+CONFIG_CMD_TFTPSRV=y
+CONFIG_CMD_ASKENV=y
+CONFIG_CMD_RARP=y
+CONFIG_CMD_SETEXPR=y
+CONFIG_CMD_SLEEP=y
+CONFIG_CMD_SNTP=y
+CONFIG_CMD_SOURCE=y
+CONFIG_CMD_STRINGS=y
+CONFIG_CMD_UUID=y
+CONFIG_ENV_IS_IN_MTD=y
+CONFIG_ENV_MTD_NAME="u-boot-env"
+CONFIG_ENV_SIZE_REDUND=0x4000
+CONFIG_ENV_SIZE=0x4000
+CONFIG_ENV_OFFSET=0x0
 CONFIG_ENV_OVERWRITE=y
 CONFIG_ENV_VARS_UBOOT_RUNTIME_CONFIG=y
 CONFIG_NET_RANDOM_ETHADDR=y
+CONFIG_REGEX=y
 CONFIG_REGMAP=y
 CONFIG_SYSCON=y
 CONFIG_CLK=y
 # CONFIG_MMC is not set
+CONFIG_HUSH_PARSER=y
+CONFIG_SYS_REDUNDAND_ENVIRONMENT=y
+CONFIG_SYS_RELOC_GD_ENV_ADDR=y
 CONFIG_MTD=y
 CONFIG_DM_MTD=y
 CONFIG_MTD_SPI_NAND=y
