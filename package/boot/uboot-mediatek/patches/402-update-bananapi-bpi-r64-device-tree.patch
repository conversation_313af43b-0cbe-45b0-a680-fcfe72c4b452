--- a/arch/arm/dts/mt7622-bananapi-bpi-r64.dts
+++ b/arch/arm/dts/mt7622-bananapi-bpi-r64.dts
@@ -5,6 +5,7 @@
  */
 
 /dts-v1/;
+#include <dt-bindings/input/linux-event-codes.h>
 #include "mt7622.dtsi"
 #include "mt7622-u-boot.dtsi"
 
@@ -20,6 +21,7 @@
 
 	aliases {
 		spi0 = &snfi;
+		ethernet0 = &eth;
 	};
 
 	memory@40000000 {
@@ -27,6 +29,44 @@
 		reg = <0x40000000 0x40000000>;
 	};
 
+	gpio-keys {
+		compatible = "gpio-keys";
+
+		reset {
+			label = "reset";
+			gpios = <&gpio 0 GPIO_ACTIVE_LOW>;
+			linux,code = <KEY_RESTART>;
+		};
+
+		wps {
+			label = "wps";
+			gpios = <&gpio 102 GPIO_ACTIVE_LOW>;
+			linux,code = <KEY_WPS_BUTTON>;
+		};
+	};
+
+	leds {
+		compatible = "gpio-leds";
+/*
+ *		red {
+ *			label = "bpi-r64:pio:red";
+ *			gpios = <&gpio 88 GPIO_ACTIVE_HIGH>;
+ *			default-state = "off";
+ *		};
+ */
+		green {
+			label = "bpi-r64:pio:green";
+			gpios = <&gpio 89 GPIO_ACTIVE_HIGH>;
+			default-state = "off";
+		};
+
+		blue {
+			label = "bpi-r64:pio:blue";
+			gpios = <&gpio 85 GPIO_ACTIVE_LOW>;
+			default-state = "off";
+		};
+	};
+
 	reg_1p8v: regulator-1p8v {
 		compatible = "regulator-fixed";
 		regulator-name = "fixed-1.8V";
@@ -182,6 +222,7 @@
 };
 
 &uart0 {
+	mediatek,force-highspeed;
 	status = "okay";
 };
 
@@ -197,7 +238,7 @@
 	status = "okay";
 	bus-width = <8>;
 	max-frequency = <50000000>;
-	cap-sd-highspeed;
+	cap-mmc-highspeed;
 	vmmc-supply = <&reg_3p3v>;
 	vqmmc-supply = <&reg_3p3v>;
 	non-removable;
@@ -208,7 +249,7 @@
 	pinctrl-0 = <&mmc1_pins_default>;
 	status = "okay";
 	bus-width = <4>;
-	max-frequency = <50000000>;
+	max-frequency = <12000000>;
 	cap-sd-highspeed;
 	r_smpl = <1>;
 	vmmc-supply = <&reg_3p3v>;
