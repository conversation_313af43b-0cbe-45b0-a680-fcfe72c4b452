--- a/board/mediatek/mt7623/mt7623_rfb.c
+++ b/board/mediatek/mt7623/mt7623_rfb.c
@@ -4,9 +4,18 @@
  */
 
 #include <common.h>
+#include <dm.h>
+#include <button.h>
+#include <env.h>
+#include <init.h>
 #include <mmc.h>
 #include <part.h>
 #include <asm/global_data.h>
+#include <linux/delay.h>
+
+#ifndef CONFIG_RESET_BUTTON_LABEL
+#define CONFIG_RESET_BUTTON_LABEL "reset"
+#endif
 
 DECLARE_GLOBAL_DATA_PTR;
 
@@ -60,3 +69,25 @@ int mmc_get_env_dev(void)
 	return dev_seq(dev);
 }
 #endif
+
+int board_late_init(void)
+{
+	struct udevice *dev;
+
+	if (!button_get_by_label(CONFIG_RESET_BUTTON_LABEL, &dev)) {
+		puts("reset button found\n");
+#ifdef CONFIG_RESET_BUTTON_SETTLE_DELAY
+		if (CONFIG_RESET_BUTTON_SETTLE_DELAY > 0) {
+			button_get_state(dev);
+			mdelay(CONFIG_RESET_BUTTON_SETTLE_DELAY);
+		}
+#endif
+		if (button_get_state(dev) == BUTTON_ON) {
+			puts("button pushed, resetting environment\n");
+			gd->env_valid = ENV_INVALID;
+		}
+	}
+
+	env_relocate();
+	return 0;
+}
