From dcf24c8deeb43a4406ae18136c8700dc2f867415 Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON><PERSON> <<EMAIL>>
Date: Mon, 25 Jul 2022 11:18:03 +0800
Subject: [PATCH 42/71] common: board_r: add support to initialize NMBM after
 nand initialization

This patch add support to initialize NMBM after nand initialized.

Signed-off-by: <PERSON><PERSON><PERSON> <<EMAIL>>
---
 common/board_r.c | 17 +++++++++++++++++
 1 file changed, 17 insertions(+)

--- a/common/board_r.c
+++ b/common/board_r.c
@@ -373,6 +373,20 @@ static int initr_nand(void)
 }
 #endif
 
+#ifdef CONFIG_NMBM_MTD
+
+__weak int board_nmbm_init(void)
+{
+	return 0;
+}
+
+/* go init the NMBM */
+static int initr_nmbm(void)
+{
+	return board_nmbm_init();
+}
+#endif
+
 #if defined(CONFIG_CMD_ONENAND)
 /* go init the NAND */
 static int initr_onenand(void)
@@ -675,6 +689,9 @@ static init_fnc_t init_sequence_r[] = {
 #ifdef CONFIG_CMD_ONENAND
 	initr_onenand,
 #endif
+#ifdef CONFIG_NMBM_MTD
+	initr_nmbm,
+#endif
 #ifdef CONFIG_MMC
 	initr_mmc,
 #endif
