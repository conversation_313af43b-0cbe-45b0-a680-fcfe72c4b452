--- a/board/mediatek/mt7623/mt7623_rfb.c
+++ b/board/mediatek/mt7623/mt7623_rfb.c
@@ -91,3 +91,43 @@ int board_late_init(void)
 	env_relocate();
 	return 0;
 }
+
+int ft_system_setup(void *blob, struct bd_info *bd)
+{
+	const u32 *media_handle_p;
+	int chosen, len, ret;
+	const char *media;
+	u32 media_handle;
+
+#ifdef CONFIG_MMC
+	switch (mmc_get_boot_dev()) {
+	case 0:
+		media = "rootdisk-emmc";
+		break
+		;;
+	case 1:
+		media = "rootdisk-sd";
+		break
+		;;
+	}
+
+	chosen = fdt_path_offset(blob, "/chosen");
+	if (chosen <= 0)
+		return 0;
+
+	media_handle_p = fdt_getprop(blob, chosen, media, &len);
+	if (media_handle_p <= 0 || len != 4)
+		return 0;
+
+	media_handle = *media_handle_p;
+	ret = fdt_setprop(blob, chosen, "rootdisk", &media_handle, sizeof(media_handle));
+	if (ret) {
+		printf("cannot set media phandle %s as rootdisk /chosen node\n", media);
+		return ret;
+	}
+
+	printf("set /chosen/rootdisk to bootrom media: %s (phandle 0x%08x)\n", media, fdt32_to_cpu(media_handle));
+#endif
+
+	return 0;
+}
