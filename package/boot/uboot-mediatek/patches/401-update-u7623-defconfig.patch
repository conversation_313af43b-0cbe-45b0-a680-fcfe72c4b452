--- a/configs/mt7623a_unielec_u7623_02_defconfig
+++ b/configs/mt7623a_unielec_u7623_02_defconfig
@@ -7,33 +7,109 @@ CONFIG_SYS_MALLOC_F_LEN=0x4000
 CONFIG_NR_DRAM_BANKS=1
 CONFIG_HAS_CUSTOM_SYS_INIT_SP_ADDR=y
 CONFIG_CUSTOM_SYS_INIT_SP_ADDR=0x81ffff10
-CONFIG_ENV_SIZE=0x1000
+CONFIG_ENV_SIZE=0x10000
 CONFIG_ENV_OFFSET=0x100000
 CONFIG_DEFAULT_DEVICE_TREE="mt7623a-unielec-u7623-02-emmc"
 CONFIG_TARGET_MT7623=y
 CONFIG_SYS_LOAD_ADDR=0x84000000
 CONFIG_FIT=y
 CONFIG_FIT_VERBOSE=y
+CONFIG_FIT_ENABLE_SHA256_SUPPORT=y
+CONFIG_LED=y
+CONFIG_LED_BLINK=y
+CONFIG_LED_GPIO=y
+CONFIG_LOGLEVEL=7
+CONFIG_LOG=y
+CONFIG_AUTOBOOT_KEYED=y
+CONFIG_AUTOBOOT_MENU_SHOW=y
+CONFIG_BOARD_LATE_INIT=y
+# CONFIG_BOOTSTD is not set
+# CONFIG_BOOT_DEFAULTS is not set
 CONFIG_DISTRO_DEFAULTS=y
 CONFIG_BOOTDELAY=3
+CONFIG_BOOTP_SEND_HOSTNAME=y
 CONFIG_DEFAULT_FDT_FILE="mt7623a-unielec-u7623-02-emmc.dtb"
 CONFIG_SYS_CONSOLE_IS_IN_ENV=y
+CONFIG_DEFAULT_ENV_FILE="unielec_u7623-02_env"
+CONFIG_BUTTON=y
+CONFIG_BUTTON_GPIO=y
+CONFIG_RESET_BUTTON_LABEL="factory"
+CONFIG_CFB_CONSOLE_ANSI=y
+CONFIG_CMD_ENV_FLAGS=y
 # CONFIG_DISPLAY_BOARDINFO is not set
-CONFIG_SYS_PROMPT="U-Boot> "
+CONFIG_USE_DEFAULT_ENV_FILE=y
+CONFIG_SYS_PROMPT="MT7623> "
 CONFIG_SYS_MAXARGS=8
 CONFIG_SYS_PBSIZE=1049
 CONFIG_SYS_BOOTM_LEN=0x4000000
 CONFIG_CMD_BOOTMENU=y
+CONFIG_CMD_BOOTP=y
+CONFIG_CMD_BOOTZ=y
+CONFIG_CMD_BUTTON=y
+CONFIG_CMD_CACHE=y
+CONFIG_CMD_CDP=y
+CONFIG_CMD_DHCP=y
+CONFIG_CMD_DM=y
+CONFIG_CMD_DNS=y
+CONFIG_CMD_ECHO=y
+CONFIG_CMD_ENV_READMEM=y
+CONFIG_CMD_ERASEENV=y
+CONFIG_CMD_EXT4=y
+CONFIG_CMD_FAT=y
+CONFIG_CMD_FS_GENERIC=y
+CONFIG_CMD_FS_UUID=y
 # CONFIG_CMD_ELF is not set
 # CONFIG_CMD_XIMG is not set
 CONFIG_CMD_GPIO=y
-CONFIG_CMD_GPT=y
+# CONFIG_CMD_GPT is not set
+CONFIG_CMD_HASH=y
+CONFIG_CMD_ITEST=y
+CONFIG_CMD_LED=y
+CONFIG_CMD_LICENSE=y
+CONFIG_CMD_LINK_LOCAL=y
+CONFIG_CMD_MBR=y
 CONFIG_CMD_MMC=y
 CONFIG_CMD_READ=y
-# CONFIG_CMD_SETEXPR is not set
+CONFIG_CMD_SF_TEST=y
+CONFIG_CMD_PING=y
+CONFIG_CMD_PXE=y
+CONFIG_CMD_TFTPBOOT=y
+CONFIG_CMD_TFTPSRV=y
+CONFIG_CMD_ASKENV=y
+CONFIG_CMD_PART=y
+CONFIG_CMD_RARP=y
+CONFIG_CMD_SATA=y
+CONFIG_CMD_SETEXPR=y
+CONFIG_CMD_SLEEP=y
+CONFIG_CMD_SNTP=y
+CONFIG_CMD_SOURCE=y
+CONFIG_CMD_STRINGS=y
+CONFIG_CMD_USB=y
+CONFIG_CMD_UUID=y
+CONFIG_CMD_READ=y
+CONFIG_CMD_SCSI=y
+CONFIG_DISPLAY_CPUINFO=y
+CONFIG_DM_ETH=y
+CONFIG_DM_GPIO=y
+CONFIG_DM_SCSI=y
+CONFIG_DM_MMC=y
+CONFIG_DM_REGULATOR=y
+CONFIG_DM_REGULATOR_FIXED=y
+CONFIG_DM_SERIAL=y
+CONFIG_DM_REGULATOR_GPIO=y
+CONFIG_DM_USB=y
+CONFIG_DM_PCI=y
+CONFIG_AHCI=y
+CONFIG_AHCI_PCI=y
+CONFIG_SCSI_AHCI=y
+CONFIG_SCSI=y
+CONFIG_HUSH_PARSER=y
 CONFIG_ENV_IS_IN_MMC=y
 CONFIG_SYS_RELOC_GD_ENV_ADDR=y
+CONFIG_SYS_MMC_ENV_DEV=0
+CONFIG_ENV_OVERWRITE=y
 CONFIG_NET_RANDOM_ETHADDR=y
+CONFIG_NETCONSOLE=y
 CONFIG_USE_IPADDR=y
 CONFIG_IPADDR="***********"
 CONFIG_USE_SERVERIP=y
@@ -45,6 +121,11 @@ CONFIG_CLK=y
 CONFIG_SUPPORT_EMMC_BOOT=y
 CONFIG_MMC_HS400_SUPPORT=y
 CONFIG_MMC_MTK=y
+CONFIG_OF_LIBFDT_OVERLAY=y
+CONFIG_PARTITION_UUIDS=y
+CONFIG_PCI=y
+CONFIG_PCIE_MEDIATEK=y
+CONFIG_PHY=y
 CONFIG_PHY_FIXED=y
 CONFIG_MEDIATEK_ETH=y
 CONFIG_PINCTRL=y
@@ -54,9 +135,12 @@ CONFIG_POWER_DOMAIN=y
 CONFIG_MTK_POWER_DOMAIN=y
 CONFIG_DM_SERIAL=y
 CONFIG_MTK_SERIAL=y
+CONFIG_RANDOM_UUID=y
+CONFIG_REGEX=y
 CONFIG_SYSRESET=y
 CONFIG_SYSRESET_WATCHDOG=y
 CONFIG_TIMER=y
 CONFIG_MTK_TIMER=y
+CONFIG_VERSION_VARIABLE=y
 CONFIG_WDT_MTK=y
 CONFIG_LZMA=y
--- /dev/null
+++ b/unielec_u7623-02_env
@@ -0,0 +1,47 @@
+ipaddr=***********
+serverip=*************
+loadaddr=0x88000000
+dtaddr=0x83f00000
+console=earlycon=uart8250,mmio32,0x11004000 console=ttyS0,115200
+initrd_high=0xafffffff
+part_default=3
+part_recovery=2
+bootcmd=run boot_mmc
+bootdelay=0
+bootfile=openwrt-mediatek-mt7623-unielec_u7623-02-initramfs-recovery.itb
+bootfile_upg=openwrt-mediatek-mt7623-unielec_u7623-02-squashfs-sysupgrade.itb
+bootled_rec=u7623-01:green:led3
+bootmenu_confirm_return=askenv - Press ENTER to return to menu ; bootmenu 60
+bootmenu_default=0
+bootmenu_delay=0
+bootmenu_title=      [0;34m( ( ( [1;39mOpenWrt[0;34m ) ) )
+bootmenu_0=Initialize environment.=run _firstboot
+bootmenu_0d=Run default boot command.=run boot_default
+bootmenu_1=Boot system via TFTP.=run boot_tftp ; run bootmenu_confirm_return
+bootmenu_2=Boot production system from eMMC.=run boot_production ; run bootmenu_confirm_return
+bootmenu_3=Boot recovery system from eMMC.=run boot_recovery ; run bootmenu_confirm_return
+bootmenu_4=Load production system via TFTP then write to eMMC.=setenv noboot 1 ; setenv replacevol 1 ; run boot_tftp_production ; setenv noboot ; setenv replacevol ; run bootmenu_confirm_return
+bootmenu_5=Load recovery system via TFTP then write to eMMC.=setenv noboot 1 ; setenv replacevol 1 ; run boot_tftp_recovery ; setenv noboot ; setenv replacevol ; run bootmenu_confirm_return
+bootmenu_6=Reboot.=reset
+bootmenu_7=Reset all settings to factory defaults.=run reset_factory ; reset
+boot_default=if env exists flag_recover ; then else run bootcmd ; fi ; run boot_recovery ; setenv replacevol 1 ; run boot_tftp_forever
+boot_first=if button factory ; then led $bootled_rec on ; run boot_tftp_recovery ; setenv flag_recover 1 ; run boot_default ; fi ; bootmenu
+boot_production=run emmc_read_production && bootm $loadaddr
+boot_recovery=run emmc_read_recovery && bootm $loadaddr
+boot_tftp_forever=led bpi-r64:pio:blue on ; while true ; do run boot_tftp_recovery ; sleep 1 ; done
+boot_tftp_production=tftpboot $loadaddr $bootfile_upg && env exists replacevol && iminfo $loadaddr && run emmc_write_production ; if env exists noboot ; then else bootm $loadaddr ; fi
+boot_tftp_recovery=tftpboot $loadaddr $bootfile && env exists replacevol && iminfo $loadaddr && run emmc_write_recovery ; if env exists noboot ; then else bootm $loadaddr ; fi
+boot_tftp=tftpboot $loadaddr $bootfile && bootm $loadaddr
+boot_mmc=run boot_production ; run boot_recovery
+emmc_write_production=mmc dev 0 0 ; iminfo $loadaddr && part start mmc 0 $part_default part_addr && part size mmc 0 $part_default part_size && run mmc_write_vol
+emmc_write_recovery=mmc dev 0 0 ; iminfo $loadaddr && part start mmc 0 $part_recovery part_addr && part size mmc 0 $part_recovery part_size && run mmc_write_vol
+emmc_read_production=mmc dev 0 0 ; part start mmc 0 $part_default part_addr && part size mmc 0 $part_default part_size && run mmc_read_vol
+emmc_read_recovery=mmc dev 0 0 ; part start mmc 0 $part_recovery part_addr && part size mmc 0 $part_recovery part_size && run mmc_read_vol
+mmc_write_vol=imszb $loadaddr image_size && test 0x$image_size -le 0x$part_size && mmc erase 0x$part_addr 0x$image_size && mmc write $loadaddr 0x$part_addr 0x$image_size
+mmc_read_vol=mmc read $loadaddr $part_addr 0x8 && imszb $loadaddr image_size && test 0x$image_size -le 0x$part_size && mmc read $loadaddr 0x$part_addr 0x$image_size && setexpr filesize $image_size * 0x200
+reset_factory=eraseenv && reset
+_init_env=setenv _init_env ; saveenv ; saveenv
+_firstboot=setenv _firstboot ; run _switch_to_menu _update_bootdev _init_env boot_first
+_update_bootdev=setenv _update_bootdev ; setenv bootargs "$console root=/dev/fit0 rootwait"
+_switch_to_menu=setenv _switch_to_menu ; setenv bootdelay 3 ; setenv bootmenu_delay 3 ; setenv bootmenu_0 $bootmenu_0d ; setenv bootmenu_0d ; run _bootmenu_update_title
+_bootmenu_update_title=setenv _bootmenu_update_title ; setenv bootmenu_title "$bootmenu_title                [33m$ver[0m"
--- a/arch/arm/dts/mt7623a-unielec-u7623-02-emmc.dts
+++ b/arch/arm/dts/mt7623a-unielec-u7623-02-emmc.dts
@@ -6,6 +6,7 @@
  */
 
 /dts-v1/;
+#include <dt-bindings/input/linux-event-codes.h>
 #include "mt7623.dtsi"
 #include "mt7623-u-boot.dtsi"
 
@@ -50,6 +51,18 @@
 		regulator-always-on;
 	};
 
+	gpio-keys {
+		compatible = "gpio-keys";
+		pinctrl-names = "default";
+		pinctrl-0 = <&key_pins_a>;
+
+		factory {
+			label = "factory";
+			gpios = <&gpio 256 GPIO_ACTIVE_LOW>;
+			linux,code = <KEY_RESTART>;
+		};
+	};
+
 	leds {
 		compatible = "gpio-leds";
 
@@ -109,6 +122,19 @@
 		};
 	};
 
+	key_pins_a: keys-alt {
+		mux {
+			function = "gpio";
+			groups = "msdc3";
+		};
+
+		conf {
+			pins = "MSDC0E_DAT0", "MSDC0E_DAT1";
+			input-enable;
+			bias-pull-up;
+		};
+	};
+
 	mmc0_pins_default: mmc0default {
 		mux {
 			function = "msdc";
