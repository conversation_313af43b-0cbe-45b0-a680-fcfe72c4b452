From 40a3661bebb3d738ab95b7de66e9d8382d5b9ab1 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Sun, 10 Mar 2024 17:48:09 +0000
Subject: [PATCH 3/3] mediatek: snfi: adjust drive strength to 12mA like old
 loader does

In addition to FM35X1GA, also change the driver strength to 12mA for
all chips where this is done by the old/legacy U-Boot:
 * Winbond 512Mb
 * Winbond 1Gb
 * Winbond 2Gb
 * GD5F4GQ4UBYIG
 * GD5F4GQ4UAYIG
 * GD5F1GQ4UX
 * GD5F1GQ4UE
 * GD5F2GQ4UX
 * GD5F2GQ4UE

Signed-off-by: <PERSON> <danie<PERSON>@makrotopia.org>
---
 .../apsoc_common/drivers/snfi/mtk-snand-ids.c | 59 ++++++++++++++-----
 1 file changed, 44 insertions(+), 15 deletions(-)

--- a/plat/mediatek/apsoc_common/drivers/snfi/mtk-snand-ids.c
+++ b/plat/mediatek/apsoc_common/drivers/snfi/mtk-snand-ids.c
@@ -80,65 +80,94 @@ static const struct snand_flash_info sna
 	SNAND_INFO("W25N512GV", SNAND_ID(SNAND_ID_DYMMY, 0xef, 0xaa, 0x20),
 		   SNAND_MEMORG_512M_2K_64,
 		   &snand_cap_read_from_cache_quad,
-		   &snand_cap_program_load_x4),
+		   &snand_cap_program_load_x4,
+		   NULL,
+		   SNAND_DRV_12mA),
 	SNAND_INFO("W25N01GV", SNAND_ID(SNAND_ID_DYMMY, 0xef, 0xaa, 0x21),
 		   SNAND_MEMORG_1G_2K_64,
 		   &snand_cap_read_from_cache_quad,
-		   &snand_cap_program_load_x4),
+		   &snand_cap_program_load_x4,
+		   NULL,
+		   SNAND_DRV_12mA),
 	SNAND_INFO("W25M02GV", SNAND_ID(SNAND_ID_DYMMY, 0xef, 0xab, 0x21),
 		   SNAND_MEMORG_2G_2K_64_2D,
 		   &snand_cap_read_from_cache_quad,
 		   &snand_cap_program_load_x4,
-		   mtk_snand_winbond_select_die),
+		   mtk_snand_winbond_select_die,
+		   SNAND_DRV_12mA),
 	SNAND_INFO("W25N02KV", SNAND_ID(SNAND_ID_DYMMY, 0xef, 0xaa, 0x22),
 		   SNAND_MEMORG_2G_2K_128,
 		   &snand_cap_read_from_cache_quad,
-		   &snand_cap_program_load_x4),
+		   &snand_cap_program_load_x4,
+		   NULL,
+		   SNAND_DRV_12mA),
 
 	SNAND_INFO("GD5F1GQ4UAWxx", SNAND_ID(SNAND_ID_ADDR, 0xc8, 0x10),
 		   SNAND_MEMORG_1G_2K_64,
 		   &snand_cap_read_from_cache_quad_q2d,
-		   &snand_cap_program_load_x4),
+		   &snand_cap_program_load_x4,
+		   NULL,
+		   SNAND_DRV_12mA),
 	SNAND_INFO("GD5F1GQ4UExIG", SNAND_ID(SNAND_ID_ADDR, 0xc8, 0xd1),
 		   SNAND_MEMORG_1G_2K_128,
 		   &snand_cap_read_from_cache_quad_q2d,
-		   &snand_cap_program_load_x4),
+		   &snand_cap_program_load_x4,
+		   NULL,
+		   SNAND_DRV_12mA),
 	SNAND_INFO("GD5F1GQ4UExxH", SNAND_ID(SNAND_ID_ADDR, 0xc8, 0xd9),
 		   SNAND_MEMORG_1G_2K_64,
 		   &snand_cap_read_from_cache_quad_q2d,
-		   &snand_cap_program_load_x4),
+		   &snand_cap_program_load_x4,
+		   NULL,
+		   SNAND_DRV_12mA),
 	SNAND_INFO("GD5F1GQ4xAYIG", SNAND_ID(SNAND_ID_ADDR, 0xc8, 0xf1),
 		   SNAND_MEMORG_1G_2K_64,
 		   &snand_cap_read_from_cache_quad_q2d,
-		   &snand_cap_program_load_x4),
+		   &snand_cap_program_load_x4,
+		   NULL,
+		   SNAND_DRV_12mA),
 	SNAND_INFO("GD5F2GQ4UExIG", SNAND_ID(SNAND_ID_ADDR, 0xc8, 0xd2),
 		   SNAND_MEMORG_2G_2K_128,
 		   &snand_cap_read_from_cache_quad_q2d,
-		   &snand_cap_program_load_x4),
+		   &snand_cap_program_load_x4,
+		   NULL,
+		   SNAND_DRV_12mA),
 	SNAND_INFO("GD5F2GQ5UExxH", SNAND_ID(SNAND_ID_ADDR, 0xc8, 0x32),
 		   SNAND_MEMORG_2G_2K_64,
 		   &snand_cap_read_from_cache_quad_a8d,
-		   &snand_cap_program_load_x4),
+		   &snand_cap_program_load_x4,
+		   NULL,
+		   SNAND_DRV_12mA),
 	SNAND_INFO("GD5F2GQ4xAYIG", SNAND_ID(SNAND_ID_ADDR, 0xc8, 0xf2),
 		   SNAND_MEMORG_2G_2K_64,
 		   &snand_cap_read_from_cache_quad_q2d,
-		   &snand_cap_program_load_x4),
+		   &snand_cap_program_load_x4,
+		   NULL,
+		   SNAND_DRV_12mA),
 	SNAND_INFO("GD5F4GQ4UBxIG", SNAND_ID(SNAND_ID_ADDR, 0xc8, 0xd4),
 		   SNAND_MEMORG_4G_4K_256,
 		   &snand_cap_read_from_cache_quad_q2d,
-		   &snand_cap_program_load_x4),
+		   &snand_cap_program_load_x4,
+		   NULL,
+		   SNAND_DRV_12mA),
 	SNAND_INFO("GD5F4GQ4xAYIG", SNAND_ID(SNAND_ID_ADDR, 0xc8, 0xf4),
 		   SNAND_MEMORG_4G_2K_64,
 		   &snand_cap_read_from_cache_quad_q2d,
-		   &snand_cap_program_load_x4),
+		   &snand_cap_program_load_x4,
+		   NULL,
+		   SNAND_DRV_12mA),
 	SNAND_INFO("GD5F2GQ5UExxG", SNAND_ID(SNAND_ID_DYMMY, 0xc8, 0x52),
 		   SNAND_MEMORG_2G_2K_128,
 		   &snand_cap_read_from_cache_quad_a8d,
-		   &snand_cap_program_load_x4),
+		   &snand_cap_program_load_x4,
+		   NULL,
+		   SNAND_DRV_12mA),
 	SNAND_INFO("GD5F4GQ4UCxIG", SNAND_ID(SNAND_ID_DYMMY, 0xc8, 0xb4),
 		   SNAND_MEMORG_4G_4K_256,
 		   &snand_cap_read_from_cache_quad_q2d,
-		   &snand_cap_program_load_x4),
+		   &snand_cap_program_load_x4,
+		   NULL,
+		   SNAND_DRV_12mA),
 
 	SNAND_INFO("MX35LF1GE4AB", SNAND_ID(SNAND_ID_DYMMY, 0xc2, 0x12),
 		   SNAND_MEMORG_1G_2K_64,
