--- a/src/mtd.c
+++ b/src/mtd.c
@@ -28,6 +28,7 @@
 #include <unistd.h>
 #include <stdlib.h>
 #include <string.h>
+#include <stddef.h>
 #include <fcntl.h>
 #include <ctype.h>
 #include <errno.h>
--- a/src/mtd.h
+++ b/src/mtd.h
@@ -25,8 +25,11 @@
 #ifndef MTD_H
 #define MTD_H
 
+#define _GNU_SOURCE
 #include <mtd/mtd-user.h>
 #include <endian.h>
+#include <stdint.h>
+#include <fcntl.h>
 
 #include "BootControlBlocks.h"
 #include "rom_nand_hamming_code_ecc.h"
