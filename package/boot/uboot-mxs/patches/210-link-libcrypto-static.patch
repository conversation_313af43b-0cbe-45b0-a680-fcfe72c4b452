OpenWrt links the libressl statically against mkimage, make sure all the 
needed dependencies are added too.

--- a/tools/Makefile
+++ b/tools/Makefile
@@ -147,7 +147,7 @@ endif
 # MXSImage needs LibSSL
 ifneq ($(CONFIG_MX23)$(CONFIG_MX28)$(CONFIG_ARMADA_38X)$(CONFIG_ARMADA_39X)$(CONFIG_FIT_SIGNATURE),)
 HOSTLOADLIBES_mkimage += \
-	$(shell pkg-config --libs libssl libcrypto 2> /dev/null || echo "-lssl -lcrypto")
+	$(shell pkg-config --libs --static libssl libcrypto 2> /dev/null || echo "-lssl -lpthread -lcrypto")
 
 # OS X deprecate openssl in favour of CommonCrypto, supress deprecation
 # warnings on those systems
