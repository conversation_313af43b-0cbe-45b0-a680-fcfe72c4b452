diff --git a/wtmi/sys_init/Makefile b/wtmi/sys_init/Makefile
--- a/wtmi/sys_init/Makefile
+++ b/wtmi/sys_init/Makefile
@@ -51,7 +51,8 @@ ECHO     = @echo
 SED      = @sed
 
 LOCAL_VERSION_STRING	?= -armada
-BUILD_STRING		:= $(shell git log -n 1 --pretty=format:"%h")
+A3700_UTILS_COMMIT_ID	?= $(shell git log -n 1 --pretty=format:"%h")
+BUILD_STRING		:= $(A3700_UTILS_COMMIT_ID)
 VERSION_STRING		:= $(LOCAL_VERSION_STRING)-$(BUILD_STRING)
 
 CPUOPTS  = -mthumb -mcpu=cortex-m3 -mlittle-endian
