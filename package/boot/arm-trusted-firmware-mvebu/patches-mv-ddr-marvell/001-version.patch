diff --git a/scripts/localversion.sh b/scripts/localversion.sh
--- a/scripts/localversion.sh
+++ b/scripts/localversion.sh
@@ -103,7 +103,7 @@ MV_DDR_ROOT=$1
 MV_DDR_VER_CSRC=$2
 
 # get mv_ddr git commit id
-MV_DDR_COMMIT_ID=`git -C $MV_DDR_ROOT rev-parse --verify --quiet --short HEAD 2> /dev/null`
+test -z "$MV_DDR_COMMIT_ID" && MV_DDR_COMMIT_ID=`git -C $MV_DDR_ROOT rev-parse --verify --quiet --short HEAD 2> /dev/null`
 
 # check for uncommitted changes in mv_ddr git
 MV_DDR_DIRTY_CHK=`git -C $MV_DDR_ROOT diff-index --name-only HEAD 2> /dev/null`
