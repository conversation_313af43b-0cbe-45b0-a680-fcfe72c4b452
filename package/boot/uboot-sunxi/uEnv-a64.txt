setenv mmc_rootpart 2
part uuid mmc ${mmc_bootdev}:${mmc_rootpart} uuid
setenv loadkernel fatload mmc \$mmc_bootdev \$kernel_addr_r uImage
setenv loaddtb fatload mmc \$mmc_bootdev \$fdt_addr_r dtb
setenv bootargs console=ttyS0,115200 earlyprintk root=PARTUUID=${uuid} rootwait earlycon=uart,mmio32,0x01c28000
setenv uenvcmd run loadkernel \&\& run loaddtb \&\& booti \$kernel_addr_r - \$fdt_addr_r
run uenvcmd
