From a58eb20fb80f478038243e9e0f30f6984725e265 Mon Sep 17 00:00:00 2001
From: <PERSON> <philipp.toms<PERSON>@theobroma-systems.com>
Date: Tue, 6 Jan 2015 15:47:18 +0100
Subject: sun6i: Sync PLL1 multipliers/dividers with Boot1

This change syncs up the multipliers and dividers used to initialize
PLL1 (i.e. the fast clock driving the ARM cores) with the values used
in Allwinner's Boot1 on sun6i.

More specifically, the following settings are now used:
 * up to 768MHz:  mul=2, div=2 (was: mul=1, div=1)
 * up to 1152MHz: mul=3, div=2 (unchanged)
 * above 1152MHz: mul=4, div=2 (was: mul=2, div=1)

--- a/arch/arm/mach-sunxi/clock_sun6i.c
+++ b/arch/arm/mach-sunxi/clock_sun6i.c
@@ -131,11 +131,12 @@ void clock_set_pll1(unsigned int clk)
 	struct sunxi_ccm_reg * const ccm =
 		(struct sunxi_ccm_reg *)SUNXI_CCM_BASE;
 	const int p = 0;
-	int k = 1;
-	int m = 1;
+	int k = 2;
+	int m = 2;
 
 	if (clk > 1152000000) {
-		k = 2;
+		k = 4;
+		m = 2;
 	} else if (clk > 768000000) {
 		k = 4;
 		m = 2;
