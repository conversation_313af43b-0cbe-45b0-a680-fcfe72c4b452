From b2b385df5095fff80b4655142f58a2a6801e6c80 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Tue, 6 Jan 2015 21:26:44 +0100
Subject: sun6i: Fix and document PLL LDO voltage selection

The PRCM_PLL_CTRL_LDO_OUT_L and PRCM_PLL_CTRL_LDO_OUT_H macros had
their meaning reversed. This is fixed by this change-set. With this
changed, the PRCM_PLL_CTRL_LDO_OUT_L(1370) now becomes self-evident
as setting the voltage to 1.37v (which it had done all along, even
though stating a different target voltage).

After changing the PLL LDO setting, it will take a little while for
the voltage output to settle. A sdelay()-based loop waits the same
order of magnitude as Boot1.

Furthermore, a bit of documentation is added to clarify that the
required setting for the PLL LDO is 1.37v as per the A31 manual.

--- a/arch/arm/mach-sunxi/clock_sun6i.c
+++ b/arch/arm/mach-sunxi/clock_sun6i.c
@@ -28,13 +28,26 @@ void clock_init_safe(void)
 	struct sunxi_prcm_reg * const prcm =
 		(struct sunxi_prcm_reg *)SUNXI_PRCM_BASE;
 
-	/* Set PLL ldo voltage without this PLL6 does not work properly */
+	/* Set PLL ldo voltage without this PLL6 does not work properly.
+	 *
+	 * As the A31 manual states, that "before enable PLL, PLLVDD
+	 * LDO should be set to 1.37v", we need to configure this to 2.5v
+	 * in the "PLL Input Power Select" (0 << 15) and (7 << 16).
+	 */
 	clrsetbits_le32(&prcm->pll_ctrl1, PRCM_PLL_CTRL_LDO_KEY_MASK,
 			PRCM_PLL_CTRL_LDO_KEY);
 	clrsetbits_le32(&prcm->pll_ctrl1, ~PRCM_PLL_CTRL_LDO_KEY_MASK,
 		PRCM_PLL_CTRL_LDO_DIGITAL_EN | PRCM_PLL_CTRL_LDO_ANALOG_EN |
-		PRCM_PLL_CTRL_EXT_OSC_EN | PRCM_PLL_CTRL_LDO_OUT_L(1140));
+		PRCM_PLL_CTRL_EXT_OSC_EN | PRCM_PLL_CTRL_LDO_OUT_L(1370));
 	clrbits_le32(&prcm->pll_ctrl1, PRCM_PLL_CTRL_LDO_KEY_MASK);
+
+	/* Give the PLL LDO voltage setting some time to take hold.
+	 * Notes:
+	 *   1) We need to use sdelay() as the timers aren't set up yet.
+	 *   2) The 100k iterations come from Boot1, which spin's for 100k
+	 *      iterations through a loop.
+	 */
+	sdelay(100000);
 #endif
 
 #if defined(CONFIG_MACH_SUN8I_R40) || defined(CONFIG_MACH_SUN50I)
--- a/arch/arm/include/asm/arch-sunxi/prcm_sun6i.h
+++ b/arch/arm/include/asm/arch-sunxi/prcm_sun6i.h
@@ -110,13 +110,13 @@
 #define PRCM_PLL_CTRL_LDO_OUT_MASK \
 	__PRCM_PLL_CTRL_LDO_OUT(0x7)
 /* When using the low voltage 20 mV steps, and high voltage 30 mV steps */
-#define PRCM_PLL_CTRL_LDO_OUT_L(n) \
-	__PRCM_PLL_CTRL_VDD_LDO_OUT((((n) - 1000) / 20) & 0x7)
 #define PRCM_PLL_CTRL_LDO_OUT_H(n) \
+	__PRCM_PLL_CTRL_VDD_LDO_OUT((((n) - 1000) / 20) & 0x7)
+#define PRCM_PLL_CTRL_LDO_OUT_L(n) \
 	__PRCM_PLL_CTRL_VDD_LDO_OUT((((n) - 1160) / 30) & 0x7)
-#define PRCM_PLL_CTRL_LDO_OUT_LV(n) \
-	__PRCM_PLL_CTRL_VDD_LDO_OUT((((n) & 0x7) * 20) + 1000)
 #define PRCM_PLL_CTRL_LDO_OUT_HV(n) \
+	__PRCM_PLL_CTRL_VDD_LDO_OUT((((n) & 0x7) * 20) + 1000)
+#define PRCM_PLL_CTRL_LDO_OUT_LV(n) \
 	__PRCM_PLL_CTRL_VDD_LDO_OUT((((n) & 0x7) * 30) + 1160)
 #define PRCM_PLL_CTRL_LDO_KEY (0xa7 << 24)
 #define PRCM_PLL_CTRL_LDO_KEY_MASK (0xff << 24)
