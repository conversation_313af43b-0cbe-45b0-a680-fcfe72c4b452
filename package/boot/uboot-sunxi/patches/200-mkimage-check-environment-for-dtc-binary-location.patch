From 637800493945ffed2f454756300437a4ec86e3b1 Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON> <<EMAIL>>
Date: Wed, 19 Jul 2017 22:23:15 +0200
Subject: mkimage: check environment for dtc binary location

Currently mkimage assumes the dtc binary is in the path and fails
otherwise. This patch makes it check the DTC environment variable first
for the dtc binary and then fall back to the default path. This makes
it possible to call the u-boot build with make DTC=... and build a fit
image with the dtc binary not being the the default path.

Signed-off-by: <PERSON><PERSON> <<EMAIL>>
Cc: <PERSON> <<EMAIL>>
---
 tools/fit_image.c | 7 ++++++-
 1 file changed, 6 insertions(+), 1 deletion(-)

--- a/tools/fit_image.c
+++ b/tools/fit_image.c
@@ -774,9 +774,14 @@ static int fit_handle_file(struct image_
 		}
 		*cmd = '\0';
 	} else if (params->datafile) {
+		const char* dtc = getenv("DTC");
+
+		if (!dtc)
+			dtc = MKIMAGE_DTC;
+
 		/* dtc -I dts -O dtb -p 500 -o tmpfile datafile */
 		snprintf(cmd, sizeof(cmd), "%s %s -o \"%s\" \"%s\"",
-			 MKIMAGE_DTC, params->dtc, tmpfile, params->datafile);
+			 dtc, params->dtc, tmpfile, params->datafile);
 		debug("Trying to execute \"%s\"\n", cmd);
 	} else {
 		snprintf(cmd, sizeof(cmd), "cp \"%s\" \"%s\"",
