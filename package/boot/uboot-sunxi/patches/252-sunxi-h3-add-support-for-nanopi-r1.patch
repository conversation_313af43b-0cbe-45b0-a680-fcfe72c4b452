From 0e8043aff1aae95d1f7b7422b91b57d9569860d3 Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON><PERSON> <<EMAIL>>
Date: Mon, 12 Oct 2020 18:39:53 +0000
Subject: [PATCH] sunxi: add support for FriendlyARM NanoPi R1

Signed-off-by: <PERSON><PERSON><PERSON> <<EMAIL>>
---
 arch/arm/dts/Makefile               |   1 +
 arch/arm/dts/sun8i-h3-nanopi-r1.dts | 146 ++++++++++++++++++++++++++++
 configs/nanopi_r1_defconfig         |  22 +++++
 3 files changed, 169 insertions(+)
 create mode 100644 arch/arm/dts/sun8i-h3-nanopi-r1.dts
 create mode 100644 configs/nanopi_r1_defconfig

--- /dev/null
+++ b/configs/nanopi_r1_defconfig
@@ -0,0 +1,21 @@
+CONFIG_ARM=y
+CONFIG_ARCH_SUNXI=y
+CONFIG_SPL=y
+CONFIG_MACH_SUN8I_H3=y
+CONFIG_DRAM_CLK=408
+CONFIG_DRAM_ZQ=3881979
+CONFIG_DRAM_ODT_EN=y
+CONFIG_MACPWR="PD6"
+# CONFIG_VIDEO_DE2 is not set
+CONFIG_NR_DRAM_BANKS=1
+# CONFIG_SYS_MALLOC_CLEAR_ON_INIT is not set
+CONFIG_CONSOLE_MUX=y
+# CONFIG_CMD_FLASH is not set
+# CONFIG_SPL_DOS_PARTITION is not set
+# CONFIG_SPL_EFI_PARTITION is not set
+CONFIG_DEFAULT_DEVICE_TREE="sun8i-h3-nanopi-r1"
+CONFIG_SUN8I_EMAC=y
+CONFIG_USB_EHCI_HCD=y
+CONFIG_USB_OHCI_HCD=y
+CONFIG_SYS_USB_EVENT_POLL_VIA_INT_QUEUE=y
+CONFIG_MMC_SUNXI_SLOT_EXTRA=2
