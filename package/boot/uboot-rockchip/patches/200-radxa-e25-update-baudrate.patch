--- a/configs/radxa-e25-rk3568_defconfig
+++ b/configs/radxa-e25-rk3568_defconfig
@@ -63,6 +63,7 @@ CONFIG_REGULATOR_RK8XX=y
 CONFIG_PWM_ROCKCHIP=y
 CONFIG_SPL_RAM=y
 CONFIG_SCSI=y
+CONFIG_BAUDRATE=1500000
 CONFIG_DEBUG_UART_SHIFT=2
 CONFIG_SYS_NS16550_MEM32=y
 CONFIG_SYSRESET=y
--- a/dts/upstream/src/arm64/rockchip/rk3568-radxa-cm3i.dtsi
+++ b/dts/upstream/src/arm64/rockchip/rk3568-radxa-cm3i.dtsi
@@ -13,7 +13,7 @@
 	};
 
 	chosen {
-		stdout-path = "serial2:115200n8";
+		stdout-path = "serial2:1500000n8";
 	};
 
 	gpio-leds {
