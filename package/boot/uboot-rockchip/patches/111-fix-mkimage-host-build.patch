--- a/tools/image-host.c
+++ b/tools/image-host.c
@@ -1189,6 +1189,7 @@ static int fit_config_add_verification_d
  * 2) get public key (X509_get_pubkey)
  * 3) provide der format (d2i_RSAPublicKey)
  */
+#ifdef CONFIG_TOOLS_LIBCRYPTO
 static int read_pub_key(const char *keydir, const void *name,
 			unsigned char **pubkey, int *pubkey_len)
 {
@@ -1242,6 +1243,13 @@ err_cert:
 	fclose(f);
 	return ret;
 }
+#else
+static int read_pub_key(const char *keydir, const void *name,
+			unsigned char **pubkey, int *pubkey_len)
+{
+	return -ENOSYS;
+}
+#endif
 
 int fit_pre_load_data(const char *keydir, void *keydest, void *fit)
 {
