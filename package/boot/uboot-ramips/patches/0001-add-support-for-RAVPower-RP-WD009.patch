From 593db38363297247df731566c2aa307a5d795005 Mon Sep 17 00:00:00 2001
From: <PERSON> <mail@david-b<PERSON>.net>
Date: Thu, 18 Jun 2020 00:13:11 +0200
Subject: [PATCH] add support for RAVPower RP-WD009

---
 arch/mips/dts/Makefile                  |  3 +-
 arch/mips/dts/ravpower-rp-wd009.dts     | 50 +++++++++++++++++++++
 arch/mips/mach-mtmips/Kconfig           |  9 ++++
 board/ravpower/rp-wd009/Kconfig         | 12 +++++
 board/ravpower/rp-wd009/Makefile        |  3 ++
 board/ravpower/rp-wd009/board.c         | 16 +++++++
 configs/ravpower-rp-wd009-ram_defconfig | 59 +++++++++++++++++++++++++
 include/configs/ravpower-rp-wd009.h     | 48 ++++++++++++++++++++
 8 files changed, 199 insertions(+), 1 deletion(-)
 create mode 100644 arch/mips/dts/ravpower-rp-wd009.dts
 create mode 100644 board/ravpower/rp-wd009/Kconfig
 create mode 100644 board/ravpower/rp-wd009/Makefile
 create mode 100644 board/ravpower/rp-wd009/board.c
 create mode 100644 configs/ravpower-rp-wd009-ram_defconfig
 create mode 100644 include/configs/ravpower-rp-wd009.h

diff --git a/arch/mips/dts/Makefile b/arch/mips/dts/Makefile
index c9d75596f2..23868ae1d2 100644
--- a/arch/mips/dts/Makefile
+++ b/arch/mips/dts/Makefile
@@ -2,7 +2,8 @@
 
 dtb-$(CONFIG_ARCH_MTMIPS) += \
 	gardena-smart-gateway-mt7688.dtb \
-	linkit-smart-7688.dtb
+	linkit-smart-7688.dtb \
+	ravpower-rp-wd009.dtb
 dtb-$(CONFIG_TARGET_AP121) += ap121.dtb
 dtb-$(CONFIG_TARGET_AP143) += ap143.dtb
 dtb-$(CONFIG_TARGET_AP152) += ap152.dtb
diff --git a/arch/mips/dts/ravpower-rp-wd009.dts b/arch/mips/dts/ravpower-rp-wd009.dts
new file mode 100644
index **********..b271d5bfbc
--- /dev/null
+++ b/arch/mips/dts/ravpower-rp-wd009.dts
@@ -0,0 +1,50 @@
+// SPDX-License-Identifier: GPL-2.0
+/*
+ * Copyright (C) 2018 Stefan Roese <<EMAIL>>
+ */
+
+/dts-v1/;
+
+#include "mt7628a.dtsi"
+#include <dt-bindings/gpio/gpio.h>
+
+/ {
+	compatible = "ravpower,rp-wd009", "ralink,mt7628a-soc";
+	model = "RAVPower RP-WD009";
+
+	aliases {
+		serial0 = &uart0;
+		spi0 = &spi0;
+	};
+
+	memory@0 {
+		device_type = "memory";
+		reg = <0x0 0x4000000>;
+	};
+
+	chosen {
+		stdout-path = "serial0:115200n8";
+	};
+};
+
+&uart0 {
+	status = "okay";
+};
+
+&spi0 {
+	status = "okay";
+	num-cs = <2>;
+
+	spi-flash@0 {
+		#address-cells = <1>;
+		#size-cells = <1>;
+		compatible = "jedec,spi-nor";
+		spi-max-frequency = <40000000>;
+		reg = <0>;
+	};
+};
+
+&eth {
+	pinctrl-names = "default";
+	pinctrl-0 = <&ephy_router_mode>;
+};
diff --git a/arch/mips/mach-mtmips/Kconfig b/arch/mips/mach-mtmips/Kconfig
index c8dcf19c0d..85ac8878ab 100644
--- a/arch/mips/mach-mtmips/Kconfig
+++ b/arch/mips/mach-mtmips/Kconfig
@@ -32,6 +32,14 @@ config BOARD_GARDENA_SMART_GATEWAY_MT7688
 	  GARDENA smart Gateway boards have a MT7688 SoC with 128 MiB of RAM
 	  and 8 MiB of flash (SPI NOR) and additional SPI NAND storage.
 
+config BOARD_RAVPOWER_RP_WD009
+	bool "RAVPower RP-WD009"
+	depends on SOC_MT7628
+	select BOARD_LATE_INIT
+	select SUPPORTS_BOOT_RAM
+	help
+	  RAVPower RP-WD009
+
 config BOARD_LINKIT_SMART_7688
 	bool "LinkIt Smart 7688"
 	depends on SOC_MT7628
@@ -133,6 +141,7 @@ config SUPPORTS_BOOT_RAM
 	bool
 
 source "board/gardena/smart-gateway-mt7688/Kconfig"
+source "board/ravpower/rp-wd009/Kconfig"
 source "board/seeed/linkit-smart-7688/Kconfig"
 
 endmenu
diff --git a/board/ravpower/rp-wd009/Kconfig b/board/ravpower/rp-wd009/Kconfig
new file mode 100644
index **********..111f8e4478
--- /dev/null
+++ b/board/ravpower/rp-wd009/Kconfig
@@ -0,0 +1,12 @@
+if BOARD_RAVPOWER_RP_WD009
+
+config SYS_BOARD
+	default "rp-wd009"
+
+config SYS_VENDOR
+	default "ravpower"
+
+config SYS_CONFIG_NAME
+	default "ravpower-rp-wd009"
+
+endif
diff --git a/board/ravpower/rp-wd009/Makefile b/board/ravpower/rp-wd009/Makefile
new file mode 100644
index **********..70cd7a8e56
--- /dev/null
+++ b/board/ravpower/rp-wd009/Makefile
@@ -0,0 +1,3 @@
+# SPDX-License-Identifier: GPL-2.0+
+
+obj-y += board.o
diff --git a/board/ravpower/rp-wd009/board.c b/board/ravpower/rp-wd009/board.c
new file mode 100644
index **********..eabcf85735
--- /dev/null
+++ b/board/ravpower/rp-wd009/board.c
@@ -0,0 +1,16 @@
+// SPDX-License-Identifier: GPL-2.0+
+/*
+ * Copyright (C) 2018 Stefan Roese <<EMAIL>>
+ */
+
+
+int board_early_init_f(void)
+{
+	return 0;
+}
+
+
+int board_late_init(void)
+{
+	return 0;
+}
diff --git a/configs/ravpower-rp-wd009-ram_defconfig b/configs/ravpower-rp-wd009-ram_defconfig
new file mode 100644
index **********..08cbf40638
--- /dev/null
+++ b/configs/ravpower-rp-wd009-ram_defconfig
@@ -0,0 +1,59 @@
+CONFIG_MIPS=y
+CONFIG_SYS_TEXT_BASE=0x80010000
+CONFIG_NR_DRAM_BANKS=1
+CONFIG_ARCH_MTMIPS=y
+CONFIG_MIPS_BOOT_FDT=y
+CONFIG_LEGACY_IMAGE_FORMAT=y
+CONFIG_OF_STDOUT_VIA_ALIAS=y
+CONFIG_USE_BOOTCOMMAND=y
+CONFIG_BOOTCOMMAND="sf probe && mtd read firmware ******** && bootm ********"
+CONFIG_USE_PREBOOT=y
+CONFIG_SYS_CONSOLE_INFO_QUIET=y
+CONFIG_VERSION_VARIABLE=y
+CONFIG_BOARD_RAVPOWER_RP_WD009=y
+CONFIG_BOARD_EARLY_INIT_F=y
+CONFIG_HUSH_PARSER=y
+CONFIG_CMD_LICENSE=y
+# CONFIG_CMD_ELF is not set
+# CONFIG_CMD_XIMG is not set
+CONFIG_CMD_MEMINFO=y
+# CONFIG_CMD_FLASH is not set
+CONFIG_CMD_GPIO=y
+# CONFIG_CMD_LOADS is not set
+CONFIG_CMD_MTD=y
+CONFIG_CMD_SPI=y
+CONFIG_CMD_WDT=y
+CONFIG_CMD_DHCP=y
+CONFIG_CMD_MII=y
+CONFIG_CMD_PING=y
+CONFIG_CMD_TIME=y
+CONFIG_CMD_UUID=y
+CONFIG_CMD_MTDPARTS=y
+CONFIG_MTDIDS_DEFAULT="nor0=spi0.0"
+CONFIG_MTDPARTS_DEFAULT="spi0.0:192k(factory-uboot),64k(config),64k(factory),1536k(loader),64k(params),64k(user_backup),64k(user),14272k(firmware),64k(mode)"
+CONFIG_DEFAULT_DEVICE_TREE="ravpower-rp-wd009"
+CONFIG_NET_RANDOM_ETHADDR=y
+# CONFIG_DM_DEVICE_REMOVE is not set
+CONFIG_HAVE_BLOCK_DEVICE=y
+CONFIG_LED=y
+CONFIG_LED_BLINK=y
+CONFIG_LED_GPIO=y
+CONFIG_MTD=y
+CONFIG_DM_MTD=y
+CONFIG_SPI_FLASH_GIGADEVICE=y
+CONFIG_SPI_FLASH_MACRONIX=y
+CONFIG_SPI_FLASH_SPANSION=y
+CONFIG_SPI_FLASH_STMICRO=y
+CONFIG_SPI_FLASH_WINBOND=y
+CONFIG_SPI_FLASH_XMC=y
+CONFIG_SPI_FLASH_MTD=y
+CONFIG_MTD_UBI_BEB_LIMIT=22
+CONFIG_MT7628_ETH=y
+CONFIG_PHY=y
+CONFIG_SPI=y
+CONFIG_MT7621_SPI=y
+CONFIG_SYSRESET_SYSCON=y
+CONFIG_WDT=y
+CONFIG_WDT_MT7621=y
+CONFIG_LZMA=y
+CONFIG_BAUDRATE=57600
diff --git a/include/configs/ravpower-rp-wd009.h b/include/configs/ravpower-rp-wd009.h
new file mode 100644
index **********..bb4145197c
--- /dev/null
+++ b/include/configs/ravpower-rp-wd009.h
@@ -0,0 +1,48 @@
+/* SPDX-License-Identifier: GPL-2.0+ */
+/*
+ * Copyright (C) 2018 Stefan Roese <<EMAIL>>
+ */
+
+#ifndef __CONFIG_RAVPOWER_RP_WD009_H
+#define __CONFIG_RAVPOWER_RP_WD009_H
+
+/* CPU */
+#define CONFIG_SYS_MIPS_TIMER_FREQ	290000000
+
+/* RAM */
+#define CONFIG_SYS_SDRAM_BASE		0x80000000
+
+#define CONFIG_SYS_LOAD_ADDR		CONFIG_SYS_SDRAM_BASE + 0x100000
+
+#define CONFIG_SYS_INIT_SP_OFFSET	0x400000
+
+#ifdef CONFIG_BOOT_RAM
+#define CONFIG_SKIP_LOWLEVEL_INIT
+#endif
+
+/* UART */
+#define CONFIG_SYS_BAUDRATE_TABLE	{ 9600, 19200, 38400, 57600, 115200, \
+					  230400, 460800, 921600 }
+
+/* RAM */
+#define CONFIG_SYS_MEMTEST_START	0x80100000
+#define CONFIG_SYS_MEMTEST_END		0x80400000
+
+/* Memory usage */
+#define CONFIG_SYS_MAXARGS		64
+#define CONFIG_SYS_MALLOC_LEN		(16 * 1024 * 1024)
+#define CONFIG_SYS_BOOTPARAMS_LEN	(128 * 1024)
+#define CONFIG_SYS_CBSIZE		512
+
+/* U-Boot */
+#define CONFIG_SYS_MONITOR_BASE		CONFIG_SYS_TEXT_BASE
+
+/* Environment settings */
+
+/*
+ * Environment is right behind U-Boot in flash. Make sure U-Boot
+ * doesn't grow into the environment area.
+ */
+#define CONFIG_BOARD_SIZE_LIMIT		CONFIG_ENV_OFFSET
+
+#endif /* __CONFIG_RAVPOWER_RP_WD009_H */
-- 
2.27.0

