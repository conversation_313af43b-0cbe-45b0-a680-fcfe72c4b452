From 8a458876013991fe2f288bbe4694264b16c3b9e9 Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON><PERSON> <<EMAIL>>
Date: Fri, 26 Jul 2019 15:44:10 +0800
Subject: [PATCH 3/3] plat/nxp/tools: fix create_pbl and byte_swap host build

Not compile create_pbl and byte_swap in the process of cross compilation

Signed-off-by: <PERSON><PERSON><PERSON> <<EMAIL>>
---
 plat/nxp/tools/pbl_ch2.mk | 3 ---
 plat/nxp/tools/pbl_ch3.mk | 5 -----
 2 files changed, 8 deletions(-)

diff --git a/plat/nxp/tools/pbl_ch2.mk b/plat/nxp/tools/pbl_ch2.mk
index afa43520..ff624dd9 100644
--- a/plat/nxp/tools/pbl_ch2.mk
+++ b/plat/nxp/tools/pbl_ch2.mk
@@ -20,8 +20,6 @@ ifeq ($(RCW),"")
 else
 	# Generate header for bl2.bin
 	$(Q)$(CST_DIR)/create_hdr_isbc --in ${BUILD_PLAT}/bl2.bin --out ${BUILD_PLAT}/hdr_bl2 ${BL2_INPUT_FILE}
-	# Compile create_pbl tool
-	${Q}${MAKE} CPPFLAGS="-DVERSION='\"${VERSION_STRING}\"'" --no-print-directory -C ${PLAT_TOOL_PATH};\
 	# Add bl2.bin to RCW
 	${CREATE_PBL} -r ${RCW} -i ${BUILD_PLAT}/bl2.bin -b ${BOOT_MODE} -c ${SOC_NUM} -d ${BL2_BASE} -e ${BL2_BASE}\
 			-o ${BUILD_PLAT}/bl2_${BOOT_MODE}.pbl ;\
@@ -43,7 +41,6 @@ ifeq ($(RCW),"")
 	${Q}echo "Platform ${PLAT} requires rcw file. Please set RCW to point to the right RCW file for boot mode ${BOOT_MODE}"
 else
 	# -a option appends the image for Chassis 3 devices in case of non secure boot
-	${Q}${MAKE} CPPFLAGS="-DVERSION='\"${VERSION_STRING}\"'" --no-print-directory -C ${PLAT_TOOL_PATH};
 	${CREATE_PBL} -r ${RCW} -i ${BUILD_PLAT}/bl2.bin -b ${BOOT_MODE} -c ${SOC_NUM} -d ${BL2_BASE} -e ${BL2_BASE} \
 	-o ${BUILD_PLAT}/bl2_${BOOT_MODE}.pbl ;
 # Swapping of RCW is required for QSPi Chassis 2 devices
diff --git a/plat/nxp/tools/pbl_ch3.mk b/plat/nxp/tools/pbl_ch3.mk
index 944ae3bb..9aa8f635 100644
--- a/plat/nxp/tools/pbl_ch3.mk
+++ b/plat/nxp/tools/pbl_ch3.mk
@@ -27,9 +27,6 @@ else
 	# Generate header for bl2.bin
 	$(Q)$(CST_DIR)/create_hdr_isbc --in ${BUILD_PLAT}/bl2.bin --out ${BUILD_PLAT}/hdr_bl2 ${BL2_INPUT_FILE}
 
-	# Compile create_pbl tool
-	${Q}${MAKE} CPPFLAGS="-DVERSION='\"${VERSION_STRING}\"'" --no-print-directory -C ${PLAT_TOOL_PATH};\
-
 	# Add Block Copy command for bl2.bin to RCW
 	${CREATE_PBL} -r ${RCW} -i ${BUILD_PLAT}/bl2.bin -b ${BOOT_MODE} -c ${SOC_NUM} -d ${BL2_BASE} -e ${BL2_BASE}\
 			-o ${BUILD_PLAT}/bl2_${BOOT_MODE}.pbl -f ${BL2_SRC_OFFSET};\
@@ -57,8 +54,6 @@ else  #SECURE_BOOT
 ifeq ($(RCW),"")
 	${Q}echo "Platform ${PLAT} requires rcw file. Please set RCW to point to the right RCW file for boot mode ${BOOT_MODE}"
 else
-	${Q}${MAKE} CPPFLAGS="-DVERSION='\"${VERSION_STRING}\"'" --no-print-directory -C ${PLAT_TOOL_PATH};
-
 	# Add Block Copy command and populate boot loc ptrfor bl2.bin to RCW
 	${CREATE_PBL} -r ${RCW} -i ${BUILD_PLAT}/bl2.bin -b ${BOOT_MODE} -c ${SOC_NUM} -d ${BL2_BASE} -e ${BL2_BASE} \
 	-o ${BUILD_PLAT}/bl2_${BOOT_MODE}.pbl -f ${BL2_SRC_OFFSET};
-- 
2.17.1

