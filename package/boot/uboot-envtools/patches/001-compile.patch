--- a/tools/env/Makefile
+++ b/tools/env/Makefile
@@ -8,6 +8,13 @@
 # with "CC" here for the maximum code reuse of scripts/Makefile.host.
 override HOSTCC = $(CC)
 
+ifneq ($(TARGET_CFLAGS),)
+KBUILD_HOSTCFLAGS = $(TARGET_CFLAGS)
+endif
+ifneq ($(TARGET_LDFLAGS),)
+KBUILD_HOSTLDFLAGS = $(TARGET_LDFLAGS)
+endif
+
 # Compile for a hosted environment on the target
 HOST_EXTRACFLAGS  = -I$(srctree)/tools \
 		$(patsubst -I%,-idirafter%, $(filter -I%, $(UBOOTINCLUDE))) \
