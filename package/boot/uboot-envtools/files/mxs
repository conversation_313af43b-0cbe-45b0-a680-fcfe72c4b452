#
# Copyright (C) 2013 OpenWrt.org
#

[ -e /etc/config/ubootenv ] && exit 0

touch /etc/config/ubootenv

. /lib/uboot-envtools.sh
. /lib/functions.sh

board=$(board_name)

case "$board" in
i2se,duckbill)
	ubootenv_add_uci_config "/dev/mmcblk0" "0x20000" "0x20000"
	ubootenv_add_uci_config "/dev/mmcblk0" "0x40000" "0x20000"
	;;
olimex,imx23-olinuxino)
	ubootenv_add_uci_config "/dev/mmcblk0" "0x40000" "0x4000"
	;;
esac

config_load ubootenv
config_foreach ubootenv_add_app_config ubootenv

exit 0
