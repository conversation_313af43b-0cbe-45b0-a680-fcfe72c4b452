#
# Copyright (C) 2012 OpenWrt.org
#

[ -e /etc/config/ubootenv ] && exit 0

touch /etc/config/ubootenv

. /lib/uboot-envtools.sh
. /lib/functions.sh

board=$(board_name)

case "$board" in
bt,homehub-v2b)
	ubootenv_add_uci_config "/dev/mtd1" "0x0" "0x10000" "0x10000" "1"
	;;
bt,homehub-v3a)
	ubootenv_add_uci_config "/dev/mtd2" "0x0" "0x4000" "0x4000" "1"
	;;
siemens,gigaset-sx76x)
	ubootenv_add_uci_config "/dev/mtd1" "0x0" "0x10000" "0x10000" "1"
	;;
zyxel,p-2812hnu-f1)
	ubootenv_add_uci_config "/dev/mtd1" "0x0" "0x2000" "0x20000" "1"
	;;
buffalo,wbmr-300hpd)
	idx="$(find_mtd_index u-boot-env)"
	[ -n "$idx" ] && \
		ubootenv_add_uci_config "/dev/mtd$idx" "0x0" "0x2000" "0x1000" "2"
	;;
esac

config_load ubootenv
config_foreach ubootenv_add_app_config ubootenv

exit 0
