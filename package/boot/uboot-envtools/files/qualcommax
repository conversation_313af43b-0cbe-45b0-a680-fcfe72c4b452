[ -e /etc/config/ubootenv ] && exit 0

touch /etc/config/ubootenv

. /lib/uboot-envtools.sh
. /lib/functions.sh

board=$(board_name)

ubootenv_add_mtd() {
	local idx="$(find_mtd_index "${1}")"
	[ -n "$idx" ] && \
		ubootenv_add_uci_config "/dev/mtd$idx" "${2}" "${3}" "${4}"
}

ubootenv_add_sys_mtd() {
	local idx="$(find_mtd_index "${1}")"
	[ -n "$idx" ] && \
		ubootenv_add_uci_sys_config "/dev/mtd$idx" "${2}" "${3}" "${4}"
}

case "$board" in
compex,wpq873|\
edgecore,eap102|\
zyxel,nbg7815)
	idx="$(find_mtd_index 0:appsblenv)"
	[ -n "$idx" ] && \
		ubootenv_add_uci_config "/dev/mtd$idx" "0x0" "0x10000" "0x10000" "1"
	;;
dynalink,dl-wrx36|\
glinet,gl-ax1800|\
glinet,gl-axt1800|\
netgear,rax120v2|\
netgear,wax218|\
netgear,wax620|\
netgear,wax630)
	idx="$(find_mtd_index 0:appsblenv)"
	[ -n "$idx" ] && \
		ubootenv_add_uci_config "/dev/mtd$idx" "0x0" "0x40000" "0x20000" "2"
	;;
edimax,cax1800)
	idx="$(find_mtd_index 0:appsblenv)"
	[ -n "$idx" ] && \
		ubootenv_add_uci_config "/dev/mtd$idx" "0x0" "0x10000" "0x20000"
	;;
linksys,mr7350|\
linksys,mx4200v1|\
linksys,mx4200v2)
	idx="$(find_mtd_index u_env)"
	[ -n "$idx" ] && \
		ubootenv_add_uci_config "/dev/mtd$idx" "0x0" "0x40000" "0x20000" "2"
	;;
prpl,haze)
	mmcpart="$(find_mmc_part 0:APPSBLENV)"
	[ -n "$mmcpart" ] && \
		ubootenv_add_uci_config "$mmcpart" "0x0" "0x40000" "0x400" "0x100"
	;;
qnap,301w)
	idx="$(find_mtd_index 0:appsblenv)"
	[ -n "$idx" ] && \
		ubootenv_add_uci_config "/dev/mtd$idx" "0x0" "0x20000" "0x20000" "1"
	;;
redmi,ax5-jdcloud)
	mmcpart="$(find_mmc_part 0:APPSBLENV)"
	[ -n "$mmcpart" ] && \
		ubootenv_add_uci_config "$mmcpart" "0x0" "0x40000" "0x20000" "2"
	;;
xiaomi,ax6000)
	ubootenv_add_mtd "0:appsblenv" "0x0" "0x10000" "0x20000"
	ubootenv_add_sys_mtd "bdata" "0x0" "0x10000" "0x20000"
	;;
esac

config_load ubootenv
config_foreach ubootenv_add_app_config

exit 0
