#
# Copyright (C) 2011-2014 OpenWrt.org
#

[ -e /etc/config/ubootenv ] && exit 0

touch /etc/config/ubootenv

. /lib/uboot-envtools.sh
. /lib/functions.sh

board=$(board_name)

case "$board" in
alfa-network,ap121f|\
alfa-network,ap121fe|\
alfa-network,n2q|\
alfa-network,n5q|\
alfa-network,pi-wifi4|\
alfa-network,r36a|\
alfa-network,tube-2hq|\
alibaba,ap121-db|\
allnet,all-wap02860ac|\
araknis,an-300-ap-i-n|\
araknis,an-500-ap-i-ac|\
araknis,an-700-ap-i-ac|\
arduino,yun|\
buffalo,bhr-4grv2|\
devolo,magic-2-wifi|\
engenius,eap1200h|\
engenius,eap1750h|\
engenius,eap300-v2|\
engenius,eap350-v1|\
engenius,eap600|\
engenius,ecb1200|\
engenius,ecb1750|\
engenius,ecb350-v1|\
engenius,ecb600|\
engenius,enh202-v1|\
engenius,ens202ext-v1|\
engenius,enstationac-v1|\
etactica,eg200|\
glinet,gl-ar750s-nor|\
glinet,gl-ar750s-nor-nand|\
librerouter,librerouter-v1|\
netgear,ex6400|\
netgear,ex7300|\
netgear,ex7300-v2|\
netgear,wndr4300-v2|\
netgear,wndr4500-v3|\
netgear,wnr1000-v2|\
netgear,wnr2000-v3|\
netgear,wnr2200-8m|\
netgear,wnr2200-16m|\
netgear,wnr612-v2|\
ocedo,koala|\
ocedo,raccoon|\
openmesh,a40|\
openmesh,a60|\
openmesh,mr600-v1|\
openmesh,mr600-v2|\
openmesh,mr900-v1|\
openmesh,mr900-v2|\
openmesh,mr1750-v1|\
openmesh,mr1750-v2|\
openmesh,om5p|\
openmesh,om5p-an|\
openmesh,om5p-ac-v1|\
openmesh,om5p-ac-v2|\
samsung,wam250|\
ubnt,airrouter|\
ubnt,bullet-m-ar7240|\
ubnt,bullet-m-ar7241|\
ubnt,nanobridge-m|\
ubnt,nanostation-loco-m|\
ubnt,nanostation-m|\
ubnt,picostation-m|\
ubnt,powerbridge-m|\
ubnt,rocket-m|\
watchguard,ap100|\
watchguard,ap200|\
watchguard,ap300|\
yuncore,a770|\
yuncore,a782|\
yuncore,a930|\
yuncore,xd3200|\
yuncore,xd4200|\
ziking,cpe46b|\
zte,e8820|\
zyxel,nbg6616)
	ubootenv_add_uci_config "/dev/mtd1" "0x0" "0x10000" "0x10000"
	;;
buffalo,wzr-hp-ag300h)
	ubootenv_add_uci_config "/dev/mtd3" "0x0" "0x10000" "0x10000"
	;;
buffalo,wzr-hp-g300nh-rb|\
buffalo,wzr-hp-g300nh-s|\
linksys,ea4500-v3)
	ubootenv_add_uci_config "/dev/mtd1" "0x0" "0x20000" "0x20000"
	;;
domywifi,dw33d)
	ubootenv_add_uci_config "/dev/mtd4" "0x0" "0x10000" "0x10000"
	;;
dongwon,dw02-412h-64m|\
dongwon,dw02-412h-128m|\
glinet,gl-ar300m-lite|\
glinet,gl-ar300m-nand|\
glinet,gl-ar300m-nor|\
glinet,gl-ar300m16)
	idx="$(find_mtd_index u-boot-env)"
	[ -n "$idx" ] && \
		ubootenv_add_uci_config "/dev/mtd$idx" "0x0" "0x10000" "0x10000"
	;;
glinet,gl-ar150)
	ubootenv_add_uci_config "/dev/mtd1" "0x0" "0x8000" "0x10000"
	;;
netgear,wndr3700|\
netgear,wndr3700-v2|\
netgear,wndrmac-v1)
	ubootenv_add_uci_config "/dev/mtd1" "0x0" "0x20000" "0x10000"
	;;
netgear,pgzng1|\
netgear,wndr3700-v4|\
netgear,wndr4300|\
netgear,wndr4300tn|\
netgear,wndr4300sw)
	ubootenv_add_uci_config "/dev/mtd1" "0x0" "0x40000" "0x20000"
	;;
openmesh,om2p-v1|\
openmesh,om2p-v2|\
openmesh,om2p-v4|\
openmesh,om2p-hs-v1|\
openmesh,om2p-hs-v2|\
openmesh,om2p-hs-v3|\
openmesh,om2p-hs-v4|\
openmesh,om2p-lc|\
plasmacloud,pa300|\
plasmacloud,pa300e)
	ubootenv_add_uci_config "/dev/mtd1" "0x0" "0x40000" "0x40000"
	;;
qihoo,c301)
	ubootenv_add_uci_config "/dev/mtd9" "0x0" "0x10000" "0x10000"
	;;
ruckus,zf7025)
	ubootenv_add_uci_config "/dev/mtd5" "0x0" "0x40000" "0x40000"
	;;
ruckus,zf7321|\
ruckus,zf7372)
	ubootenv_add_uci_config "/dev/mtd2" "0x0" "0x40000" "0x10000"
	;;
sophos,ap15|\
sophos,ap55|\
sophos,ap55c|\
sophos,ap100|\
sophos,ap100c)
	ubootenv_add_uci_config "/dev/mtd1" "0x0" "0x1000" "0x10000"
	;;
wallys,dr531)
	ubootenv_add_uci_config "/dev/mtd1" "0x0" "0xf800" "0x10000"
	;;
zte,mf286|\
zte,mf286a|\
zte,mf286r)
	ubootenv_add_uci_config "/dev/mtd7" "0x0" "0x20000" "0x10000"
	;;
esac

config_load ubootenv
config_foreach ubootenv_add_app_config ubootenv

exit 0
