#
# Copyright (C) 2021 OpenWrt.org
#

[ -e /etc/config/ubootenv ] && exit 0

touch /etc/config/ubootenv

. /lib/uboot-envtools.sh
. /lib/functions.sh

board=$(board_name)

case "$board" in
linksys,e8450-ubi)
	ubootenv_add_uci_config "/dev/ubi0_0" "0x0" "0x1f000" "0x1f000" "1"
	ubootenv_add_uci_config "/dev/ubi0_1" "0x0" "0x1f000" "0x1f000" "1"
	;;
bananapi,bpi-r64)
	case "$(cmdline_get_var root)" in
	/dev/mmc*)
		local envdev=$(find_mmc_part "ubootenv" $rootdev)
		ubootenv_add_uci_config "$envdev" "0x0" "0x80000" "0x80000" "1"
		ubootenv_add_uci_config "$envdev" "0x80000" "0x80000" "0x80000" "1"
		;;
	/dev/ubi*)
		ubootenv_add_uci_config "/dev/ubi0_0" "0x0" "0x1f000" "0x1f000" "1"
		ubootenv_add_uci_config "/dev/ubi0_1" "0x0" "0x1f000" "0x1f000" "1"
		;;
	esac
	;;
buffalo,wsr-2533dhp2)
	ubootenv_add_uci_config "/dev/mtd3" "0x0" "0x1000" "0x20000"
	;;
ruijie,rg-ew3200gx-pro)
	ubootenv_add_uci_config "/dev/mtd3" "0x0" "0x20000" "0x20000"
	;;
ubnt,unifi-6-lr-ubootmod)
	ubootenv_add_uci_config "/dev/mtd2" "0x0" "0x4000" "0x1000"
	;;
xiaomi,redmi-router-ax6s)
	ubootenv_add_uci_config "/dev/mtd3" "0x0" "0x10000" "0x40000"
	;;
esac

config_load ubootenv
config_foreach ubootenv_add_app_config ubootenv

exit 0
