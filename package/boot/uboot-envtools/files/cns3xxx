#
# Copyright (C) 2013 OpenWrt.org
#

[ -e /etc/config/ubootenv ] && exit 0

touch /etc/config/ubootenv

. /lib/uboot-envtools.sh
. /lib/functions.sh

board=$(board_name)

case "$board" in
laguna)
	# Laguna uboot env size/erasesize vary depending on NOR vs SPI FLASH
	size=$(grep mtd1 /proc/mtd | awk '{print $2}')
	erasesize=$(grep mtd1 /proc/mtd | awk '{print $3}')
	ubootenv_add_uci_config "/dev/mtd1" "0x0" "0x$size" "0x$erasesize"
	;;
esac

config_load ubootenv
config_foreach ubootenv_add_app_config ubootenv

exit 0
