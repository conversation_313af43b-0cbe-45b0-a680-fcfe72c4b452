[ -e /etc/config/ubootenv ] && exit 0

touch /etc/config/ubootenv

. /lib/uboot-envtools.sh
. /lib/functions.sh

board=$(board_name)

case "$board" in
meraki,mr24)
	ubootenv_add_uci_config "/dev/mtd1" "0x0" "0x4000" "0x4000" "4"
	ubootenv_add_uci_config "/dev/mtd2" "0x0" "0x4000" "0x4000" "4"
	;;
meraki,mx60)
	ubootenv_add_uci_config "/dev/mtd1" "0x0" "0x20000" "0x20000" "4"
	;;
netgear,wndap620|\
netgear,wndap660)
	ubootenv_add_uci_config "/dev/mtd1" "0x0" "0x4000" "0x4000" "4"
	;;
wd,mybooklive)
	ubootenv_add_uci_config "/dev/mtd1" "0x0" "0x1000" "0x1000" "1"
	ubootenv_add_uci_config "/dev/mtd1" "0x1000" "0x1000" "0x1000" "1"
	;;
esac

config_load ubootenv
config_foreach ubootenv_add_app_config ubootenv

exit 0
