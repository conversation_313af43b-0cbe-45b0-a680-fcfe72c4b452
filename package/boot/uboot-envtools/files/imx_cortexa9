#
# Copyright (C) 2013-2014 OpenWrt.org
#

[ -e /etc/config/ubootenv ] && exit 0

touch /etc/config/ubootenv

. /lib/imx.sh
. /lib/uboot-envtools.sh
. /lib/functions.sh

board=$(board_name)

case "$board" in
gw,imx6dl-gw51xx|\
gw,imx6dl-gw52xx|\
gw,imx6dl-gw53xx|\
gw,imx6dl-gw54xx|\
gw,imx6dl-gw551x|\
gw,imx6dl-gw552x|\
gw,imx6dl-gw553x|\
gw,imx6dl-gw5904|\
gw,imx6dl-gw5907|\
gw,imx6dl-gw5910|\
gw,imx6dl-gw5912|\
gw,imx6dl-gw5913|\
gw,imx6q-gw51xx|\
gw,imx6q-gw52xx|\
gw,imx6q-gw53xx|\
gw,imx6q-gw5400-a|\
gw,imx6q-gw54xx|\
gw,imx6q-gw551x|\
gw,imx6q-gw552x|\
gw,imx6q-gw553x|\
gw,imx6q-gw5904|\
gw,imx6q-gw5907|\
gw,imx6q-gw5910|\
gw,imx6q-gw5912|\
gw,imx6q-gw5913)
	if [ -c /dev/mtd1 ]; then
		# board boots from NAND
		ubootenv_add_uci_config /dev/mtd1 0x0 0x20000 0x40000
		ubootenv_add_uci_config /dev/mtd1 0x80000 0x20000 0x40000
	else
		# board boots from microSD
		ubootenv_add_uci_config /dev/mmcblk0 0xb1400 0x20000
		ubootenv_add_uci_config /dev/mmcblk0 0xd1400 0x20000
	fi
	;;
toradex,apalis_imx6q-eval|\
toradex,apalis_imx6q-ixora|\
toradex,apalis_imx6q-ixora-v1.1)
	ubootenv_add_uci_config $(bootdev_from_uuid)boot0 -0x2200 0x2000 0x200 10
	;;
wand,imx6dl-wandboard)
	ubootenv_add_uci_config "/dev/mmcblk0" "0x60000" "0x2000" "0x2000"
	;;
esac

config_load ubootenv
config_foreach ubootenv_add_app_config ubootenv

exit 0
