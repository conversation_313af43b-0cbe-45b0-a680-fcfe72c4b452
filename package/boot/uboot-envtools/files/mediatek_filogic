#
# Copyright (C) 2021 OpenWrt.org
#

[ -e /etc/config/ubootenv ] && exit 0

touch /etc/config/ubootenv

. /lib/uboot-envtools.sh
. /lib/functions.sh

board=$(board_name)

case "$board" in
bananapi,bpi-r3|\
bananapi,bpi-r4|\
bananapi,bpi-r4-2g5|\
bananapi,bpi-r4-poe)
	case "$(cmdline_get_var root)" in
	/dev/mmc*)
		local envdev=$(find_mmc_part "ubootenv" $rootdev)
		ubootenv_add_uci_config "$envdev" "0x0" "0x40000" "0x40000" "1"
		ubootenv_add_uci_config "$envdev" "0x40000" "0x40000" "0x40000" "1"
		;;
	/dev/mtd*)
		local envdev=/dev/mtd$(find_mtd_index "u-boot-env")
		ubootenv_add_uci_config "$envdev" "0x0" "0x20000" "0x20000" "1"
		ubootenv_add_uci_config "$envdev" "0x20000" "0x20000" "0x20000" "1"
		;;
	/dev/ubi*)
		. /lib/upgrade/nand.sh
		local envubi=$(nand_find_ubi ubi)
		local envdev=/dev/$(nand_find_volume $envubi ubootenv)
		local envdev2=/dev/$(nand_find_volume $envubi ubootenv2)
		ubootenv_add_uci_config "$envdev" "0x0" "0x1f000" "0x1f000" "1"
		ubootenv_add_uci_config "$envdev2" "0x0" "0x1f000" "0x1f000" "1"
		;;
	esac
	;;
cetron,ct3003|\
imou,lc-hx3001)
	ubootenv_add_uci_config "/dev/mtd1" "0x0" "0x20000" "0x20000"
	;;
glinet,gl-mt2500|\
glinet,gl-mt6000)
	local envdev=$(find_mmc_part "u-boot-env")
	ubootenv_add_uci_config "$envdev" "0x0" "0x80000"
	;;
glinet,gl-mt3000)
	ubootenv_add_uci_config "/dev/mtd1" "0x0" "0x80000" "0x20000"
	;;
openembed,som7981)
	ubootenv_add_uci_config "/dev/mtd1" "0x0" "0x80000" "0x80000"
	ubootenv_add_uci_sys_config "/dev/mtd3" "0x0" "0x100000" "0x100000"
	;;
xiaomi,mi-router-wr30u|\
xiaomi,mi-router-ax3000t|\
xiaomi,redmi-router-ax6000)
	ubootenv_add_uci_config "/dev/mtd1" "0x0" "0x10000" "0x20000"
	ubootenv_add_uci_sys_config "/dev/mtd2" "0x0" "0x10000" "0x20000"
	;;
esac

config_load ubootenv
config_foreach ubootenv_add_app_config

exit 0
