[ -e /etc/config/ubootenv ] && exit 0

touch /etc/config/ubootenv

. /lib/uboot-envtools.sh
. /lib/functions.sh

board=$(board_name)

case "$board" in
apresia,aplgs120gtss)
	idx="$(find_mtd_index u-boot-env)"
	[ -n "$idx" ] && \
		ubootenv_add_uci_config "/dev/mtd$idx" "0x0" "0x40000" "0x10000"
	idx2="$(find_mtd_index u-boot-env2)"
	[ -n "$idx2" ] && \
		ubootenv_add_uci_sys_config "/dev/mtd$idx2" "0x0" "0x40000" "0x10000"
	;;
d-link,dgs-1210-10mp|\
d-link,dgs-1210-10p|\
d-link,dgs-1210-16|\
d-link,dgs-1210-20|\
d-link,dgs-1210-28|\
zyxel,gs1900-8|\
zyxel,gs1900-8hp-v1|\
zyxel,gs1900-8hp-v2|\
zyxel,gs1900-10hp|\
zyxel,gs1900-16|\
zyxel,gs1900-24-v1|\
zyxel,gs1900-24e|\
zyxel,gs1900-24ep|\
zyxel,gs1900-24hp-v1|\
zyxel,gs1900-24hp-v2)
	idx="$(find_mtd_index u-boot-env)"
	[ -n "$idx" ] && \
		ubootenv_add_uci_config "/dev/mtd$idx" "0x0" "0x400" "0x10000"
	idx2="$(find_mtd_index u-boot-env2)"
	[ -n "$idx2" ] && \
		ubootenv_add_uci_sys_config "/dev/mtd$idx2" "0x0" "0x1000" "0x10000"
	;;
tplink,sg2008p-v1|\
tplink,sg2210p-v3|\
tplink,sg2452p-v4)
	idx="$(find_mtd_index u-boot-env)"
	[ -n "$idx" ] && \
		ubootenv_add_uci_config "/dev/mtd$idx" "0x0" "0x20000" "0x10000"
	;;
iodata,bsh-g24mb)
	idx="$(find_mtd_index u-boot-env)"
	[ -n "$idx" ] && \
		ubootenv_add_uci_config "/dev/mtd$idx" "0x0" "0x10000" "0x10000"
	idx2="$(find_mtd_index u-boot-env2)"
	[ -n "$idx2" ] && \
		ubootenv_add_uci_sys_config "/dev/mtd$idx2" "0x0" "0x3800" "0x10000"
	;;
*)
	idx="$(find_mtd_index u-boot-env)"
	[ -n "$idx" ] && \
		ubootenv_add_uci_config "/dev/mtd$idx" "0x0" "0x10000" "0x10000"
	idx2="$(find_mtd_index u-boot-env2)"
	[ -n "$idx2" ] && \
		ubootenv_add_uci_sys_config "/dev/mtd$idx2" "0x0" "0x1000" "0x10000"
	;;
esac

config_load ubootenv
config_foreach ubootenv_add_app_config

exit 0
