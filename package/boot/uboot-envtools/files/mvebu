#
# Copyright (C) 2014-2016 OpenWrt.org
# Copyright (C) 2016 LEDE-Project.org
#

[ -e /etc/config/ubootenv ] && exit 0

touch /etc/config/ubootenv

. /lib/uboot-envtools.sh
. /lib/functions.sh

board=$(board_name)

case "$board" in
buffalo,ls421de)
	ubootenv_add_uci_config "/dev/mtd3" "0x0" "0x10000"
	;;
cznic,turris-omnia)
	idx="$(find_mtd_index u-boot-env)"
	if [ -n "$idx" ]; then
		ubootenv_add_uci_config "/dev/mtd${idx}" "0x0" "0x10000" "0x10000"
	elif grep -q 'U-Boot 2015.10-rc2' /dev/mtd0; then
		ubootenv_add_uci_config "/dev/mtd0" "0xc0000" "0x10000" "0x40000"
	else
		ubootenv_add_uci_config "/dev/mtd0" "0xf0000" "0x10000" "0x10000"
	fi
	;;
glinet,gl-mv1000)
	ubootenv_add_uci_config "/dev/mtd1" "0x0" "0x8000" "0x8000" "1"
	;;
globalscale,espressobin|\
globalscale,espressobin-emmc|\
globalscale,espressobin-ultra|\
globalscale,espressobin-v7|\
globalscale,espressobin-v7-emmc|\
globalscale,mochabin)
	idx="$(find_mtd_index u-boot-env)"
	if [ -n "$idx" ]; then
		ubootenv_add_uci_config "/dev/mtd$idx" "0x0" "0x10000" "0x10000" "1"
	else
		ubootenv_add_uci_config "/dev/mtd0" "0x3f0000" "0x10000" "0x10000" "1"
	fi
	;;
marvell,armada8040-mcbin-doubleshot|\
marvell,armada8040-mcbin-singleshot)
	ubootenv_add_uci_config "/dev/mtd0" "0x3f0000" "0x10000" "0x10000" "1"
	;;
linksys,wrt1200ac|\
linksys,wrt1900ac-v2|\
linksys,wrt1900acs)
	ubootenv_add_uci_config "/dev/mtd1" "0x0" "0x20000" "0x40000"
	;;
linksys,wrt1900ac-v1)
	ubootenv_add_uci_config "/dev/mtd1" "0x0" "0x40000" "0x20000"
	;;
linksys,wrt3200acm|\
linksys,wrt32x)
	ubootenv_add_uci_config "/dev/mtd1" "0x0" "0x20000" "0x20000"
	;;
methode,udpu|\
methode,edpu)
	idx="$(find_mtd_index u-boot-env)"
	if [ -n "$idx" ]; then
	ubootenv_add_uci_config "/dev/mtd$idx" "0x0" "0x10000" "0x10000" "1"
	else
	ubootenv_add_uci_config "/dev/mtd0" "0x180000" "0x10000" "0x10000"
	fi
	;;
esac

config_load ubootenv
config_foreach ubootenv_add_app_config ubootenv

exit 0
