#
# Copyright (C) 2013 OpenWrt.org
#

[ -e /etc/config/ubootenv ] && exit 0

touch /etc/config/ubootenv

. /lib/uboot-envtools.sh
. /lib/functions.sh

board=$(board_name)

case "$board" in
"cloudengines,pogoplug"*|\
"shuttle,kd20")
	ubootenv_add_uci_config "/dev/mtd2" "0x0" "0x2000" "0x20000" "1"
	;;
"mitrastar,stg-212")
	ubootenv_add_uci_config "/dev/mtd2" "0x0" "0x20000" "0x20000" "1"
	;;
esac

config_load ubootenv
config_foreach ubootenv_add_app_config ubootenv

exit 0
