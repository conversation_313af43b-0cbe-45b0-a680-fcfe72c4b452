#
# Copyright (C) 2012-2014 OpenWrt.org
#

[ -e /etc/config/ubootenv ] && exit 0

touch /etc/config/ubootenv

. /lib/uboot-envtools.sh
. /lib/functions.sh

board=$(board_name)

case "$board" in
checkpoint,l-50|\
cloudengines,pogoe02|\
cloudengines,pogoplugv4|\
globalscale,sheevaplug|\
iom,ix2-200|\
linksys,e4200-v2|\
linksys,ea4500|\
netgear,readynas-duo-v2|\
raidsonic,ib-nas62x0|\
seagate,dockstar|\
zyxel,nsa310b|\
zyxel,nsa310s|\
zyxel,nsa325)
	ubootenv_add_uci_config "/dev/mtd1" "0x0" "0x20000" "0x20000"
	;;
linksys,ea3500)
	ubootenv_add_uci_config "/dev/mtd1" "0x0" "0x4000" "0x4000"
	;;
esac

config_load ubootenv
config_foreach ubootenv_add_app_config ubootenv

exit 0
