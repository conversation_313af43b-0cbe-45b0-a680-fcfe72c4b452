#
# Copyright (C) 2011-2012 OpenWrt.org
#

[ -e /etc/config/ubootenv ] && exit 0

touch /etc/config/ubootenv

. /lib/uboot-envtools.sh
. /lib/functions.sh

board=$(board_name)

case "$board" in
alfa-network,ac1200rm|\
alfa-network,awusfree1|\
alfa-network,quad-e4g|\
alfa-network,r36m-e4g|\
alfa-network,tube-e4g)
	ubootenv_add_uci_config "/dev/mtd1" "0x0" "0x1000" "0x1000"
	;;
allnet,all0256n-4m|\
allnet,all0256n-8m|\
allnet,all5002)
	ubootenv_add_uci_config "/dev/mtd1" "0x0" "0x10000" "0x10000"
	;;
ampedwireless,ally-00x19k|\
ampedwireless,ally-r1900k)
	ubootenv_add_uci_config "/dev/mtd1" "0x0" "0x1000" "0x20000" "4"
	;;
beeline,smartbox-giga|\
beeline,smartbox-turbo-plus)
	ubootenv_add_uci_config "/dev/mtd0" "0x80000" "0x1000" "0x20000"
	;;
buffalo,wsr-1166dhp|\
buffalo,wsr-600dhp|\
huasifei,mt7621dtu|\
mediatek,linkit-smart-7688|\
samknows,whitebox-v8|\
xiaomi,mi-router-4c|\
xiaomi,miwifi-nano|\
zbtlink,zbt-wg2626|\
zte,mf283plus)
	ubootenv_add_uci_config "/dev/mtd1" "0x0" "0x1000" "0x10000"
	;;
h3c,tx1800-plus|\
h3c,tx1801-plus|\
h3c,tx1806|\
jcg,q20)
	ubootenv_add_uci_config "/dev/mtd1" "0x0" "0x20000" "0x20000"
	;;
hootoo,ht-tm05|\
ravpower,rp-wd03)
	idx="$(find_mtd_index u-boot-env)"
	[ -n "$idx" ] && \
		ubootenv_add_uci_config "/dev/mtd$idx" "0x4000" "0x1000" "0x1000"
	;;
linksys,ea7300-v1|\
linksys,ea7500-v2|\
linksys,ea8100-v1|\
xiaomi,mir3p|\
xiaomi,mir3g|\
xiaomi,mir4|\
xiaomi,miwifi-r3|\
xiaomi,mi-router-ac2100|\
xiaomi,redmi-router-ac2100)
	ubootenv_add_uci_config "/dev/mtd1" "0x0" "0x1000" "0x20000"
	;;
xiaomi,mi-router-cr660x)
	ubootenv_add_uci_config "/dev/mtd1" "0x0" "0x10000" "0x20000"
	;;
zyxel,nr7101)
	idx="$(find_mtd_index Config)"
	[ -n "$idx" ] && \
		ubootenv_add_uci_config "/dev/mtd$idx" "0x0" "0x1000" "0x80000"
	;;
esac

config_load ubootenv
config_foreach ubootenv_add_app_config ubootenv

exit 0
