OpenWrt links the libressl statically against mkimage, make sure all the 
needed dependencies are added too.

--- a/tools/Makefile
+++ b/tools/Makefile
@@ -151,7 +151,7 @@ ifneq ($(CONFIG_MX23)$(CONFIG_MX28)$(CON
 HOSTCFLAGS_kwbimage.o += \
 	$(shell pkg-config --cflags libssl libcrypto 2> /dev/null || echo "")
 HOSTLOADLIBES_mkimage += \
-	$(shell pkg-config --libs libssl libcrypto 2> /dev/null || echo "-lssl -lcrypto")
+	$(shell pkg-config --libs --static libssl libcrypto 2> /dev/null || echo "-lssl -lpthread -lcrypto")
 
 # OS X deprecate openssl in favour of CommonCrypto, supress deprecation
 # warnings on those systems
