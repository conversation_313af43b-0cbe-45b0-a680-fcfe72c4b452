From 67db0da72eb7ed87ebaaeb8a26891cb2cf916500 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Sun, 21 Jul 2019 23:24:12 -0400
Subject: [U-Boot] [PATCH] arm: zynq: read mac address from SPI flash memory

Implement a method for reading the MAC address from an
SPI flash memory.
In particular, this method is used by the Zybo Z7 board
to read the MAC address from the OTP region in the SPI NOR
memory

Signed-off-by: <PERSON> <<EMAIL>>
---
As of 2019-08-18, this patch has not been accepted by upstream U-Boot.
Keep this patch until an alternative is accepted by upstream.
---
 board/xilinx/common/board.c    | 28 ++++++++++++++++++++++++++++
 configs/zynq_zybo_z7_defconfig |  3 +++
 drivers/misc/Kconfig           | 17 +++++++++++++++++
 3 files changed, 48 insertions(+)

--- a/board/xilinx/common/board.c
+++ b/board/xilinx/common/board.c
@@ -6,7 +6,10 @@
 
 #include <common.h>
 #include <dm/uclass.h>
+#include <dm/device.h>
+#include <dm/device-internal.h>
 #include <i2c.h>
+#include <spi_flash.h>
 
 int zynq_board_read_rom_ethaddr(unsigned char *ethaddr)
 {
@@ -34,5 +37,30 @@ int zynq_board_read_rom_ethaddr(unsigned
 		debug("%s: I2C EEPROM MAC %pM\n", __func__, ethaddr);
 #endif
 
+#if defined(CONFIG_MAC_ADDR_IN_SPI_FLASH)
+	struct spi_flash *flash;
+	struct udevice *dev;
+
+	ret = spi_flash_probe_bus_cs(CONFIG_SF_DEFAULT_BUS,
+				     CONFIG_SF_DEFAULT_CS,
+				     0, 0, &dev);
+	if (ret) {
+		printf("SPI(bus:%u cs:%u) probe failed\n",
+		       CONFIG_SF_DEFAULT_BUS,
+		       CONFIG_SF_DEFAULT_CS);
+		return 0;
+	}
+
+	flash = dev_get_uclass_priv(dev);
+	flash->read_opcode = CONFIG_MAC_ADDR_SPI_FLASH_READ_CMD;
+
+	if (spi_flash_read_dm(dev,
+			      CONFIG_MAC_ADDR_SPI_FLASH_DATA_OFFSET,
+			      6, ethaddr))
+		printf("SPI MAC address read failed\n");
+
+	device_remove(dev, DM_REMOVE_NORMAL);
+#endif
+
 	return ret;
 }
--- a/configs/zynq_zybo_z7_defconfig
+++ b/configs/zynq_zybo_z7_defconfig
@@ -42,6 +42,9 @@ CONFIG_DFU_RAM=y
 CONFIG_FPGA_XILINX=y
 CONFIG_FPGA_ZYNQPL=y
 CONFIG_DM_GPIO=y
+CONFIG_MAC_ADDR_IN_SPI_FLASH=y
+CONFIG_MAC_ADDR_SPI_FLASH_READ_CMD=0x4b
+CONFIG_MAC_ADDR_SPI_FLASH_DATA_OFFSET=0x20
 CONFIG_MMC_SDHCI=y
 CONFIG_MMC_SDHCI_ZYNQ=y
 CONFIG_SPI_FLASH=y
--- a/drivers/misc/Kconfig
+++ b/drivers/misc/Kconfig
@@ -366,6 +366,23 @@ config SYS_I2C_EEPROM_ADDR_OVERFLOW
 
 endif
 
+config MAC_ADDR_IN_SPI_FLASH
+	bool "MAC address in SPI flash"
+	help
+	  Read MAC address from an SPI flash memory
+
+if MAC_ADDR_IN_SPI_FLASH
+
+config MAC_ADDR_SPI_FLASH_READ_CMD
+	hex "Read command for the SPI flash memory"
+	default 0
+
+config MAC_ADDR_SPI_FLASH_DATA_OFFSET
+	hex "Offset of MAC data in SPI flash memory"
+	default 0
+
+endif
+
 config GDSYS_RXAUI_CTRL
 	bool "Enable gdsys RXAUI control driver"
 	depends on MISC
