From e33a814e772cdc36436c8c188d8c42d019fda639 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Tue, 14 Jan 2020 18:53:41 +0100
Subject: [PATCH] scripts/dtc: Remove redundant YYLOC global declaration

gcc 10 will default to -fno-common, which causes this error at link
time:

  (.text+0x0): multiple definition of `yylloc'; dtc-lexer.lex.o (symbol from plugin):(.text+0x0): first defined here

This is because both dtc-lexer as well as dtc-parser define the same
global symbol yyloc. Before with -fcommon those were merged into one
defintion. The proper solution would be to to mark this as "extern",
however that leads to:

  dtc-lexer.l:26:16: error: redundant redeclaration of 'yylloc' [-Werror=redundant-decls]
   26 | extern YYLTYPE yylloc;
      |                ^~~~~~
In file included from dtc-lexer.l:24:
dtc-parser.tab.h:127:16: note: previous declaration of 'yylloc' was here
  127 | extern YYLTYPE yylloc;
      |                ^~~~~~
cc1: all warnings being treated as errors

which means the declaration is completely redundant and can just be
dropped.

Signed-off-by: <PERSON>eller <<EMAIL>>
Signed-off-by: David <PERSON> <<EMAIL>>
[robh: cherry-pick from upstream]
Cc: <EMAIL>
Signed-off-by: Rob Herring <<EMAIL>>
---
 scripts/dtc/dtc-lexer.l | 1 -
 1 file changed, 1 deletion(-)

diff --git a/scripts/dtc/dtc-lexer.l b/scripts/dtc/dtc-lexer.l
index 5c6c3fd557d7f..b3b7270300de5 100644
--- a/scripts/dtc/dtc-lexer.l
+++ b/scripts/dtc/dtc-lexer.l
@@ -38,7 +38,6 @@ LINECOMMENT	"//".*\n
 #include "srcpos.h"
 #include "dtc-parser.tab.h"
 
-YYLTYPE yylloc;
 extern bool treesource_error;
 
 /* CAUTION: this will stop working if we ever use yyless() or yyunput() */
