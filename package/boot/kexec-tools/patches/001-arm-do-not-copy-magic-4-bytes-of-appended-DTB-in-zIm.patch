From 9817ec81968a5eec7863902833fb77680544eae4 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Mon, 12 Apr 2021 13:18:05 +0200
Subject: [PATCH 1/1] arm: do not copy magic 4 bytes of appended DTB in zImage

If the passed zImage happens to have a DTB appended, then the magic 4 bytes
of the DTB are copied together with the kernel image. This leads to
failed kexec boots because the decompressor finds the aforementioned
DTB magic and falsely tries to replace the DTB passed in the register r2
with the non-existent appended one.

Signed-off-by: <PERSON> <<EMAIL>>
Signed-off-by: <PERSON> <<EMAIL>>
---
 kexec/arch/arm/kexec-zImage-arm.c | 12 +++++++++++-
 1 file changed, 11 insertions(+), 1 deletion(-)

--- a/kexec/arch/arm/kexec-zImage-arm.c
+++ b/kexec/arch/arm/kexec-zImage-arm.c
@@ -382,6 +382,7 @@ int zImage_arm_load(int argc, char **arg
 	unsigned int atag_offset = 0x1000; /* 4k offset from memory start */
 	unsigned int extra_size = 0x8000; /* TEXT_OFFSET */
 	const struct zimage_tag *tag;
+	size_t kernel_buf_size;
 	size_t kernel_mem_size;
 	const char *command_line;
 	char *modified_cmdline = NULL;
@@ -538,6 +539,15 @@ int zImage_arm_load(int argc, char **arg
 	}
 
 	/*
+	 * Save the length of the compressed kernel image w/o the appended DTB.
+	 * This will be required later on when the kernel image contained
+	 * in the zImage will be loaded into a kernel memory segment.
+	 * And we want to load ONLY the compressed kernel image from the zImage
+	 * and discard the appended DTB.
+	 */
+	kernel_buf_size = len;
+
+	/*
 	 * Always extend the zImage by four bytes to ensure that an appended
 	 * DTB image always sees an initialised value after _edata.
 	 */
@@ -759,7 +769,7 @@ int zImage_arm_load(int argc, char **arg
 		add_segment(info, dtb_buf, dtb_length, dtb_offset, dtb_length);
 	}
 
-	add_segment(info, buf, len, kernel_base, kernel_mem_size);
+	add_segment(info, buf, kernel_buf_size, kernel_base, kernel_mem_size);
 
 	info->entry = (void*)kernel_base;
 
