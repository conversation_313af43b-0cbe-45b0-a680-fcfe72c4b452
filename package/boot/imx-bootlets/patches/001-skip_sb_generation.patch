--- a/<PERSON><PERSON>le
+++ b/Makefile
@@ -32,10 +32,11 @@ ifeq "$(DFT_IMAGE)" "$(wildcard $(DFT_IM
 	sed -i 's,[^ *]image.*;,\timage="$(DFT_UBOOT)";,' uboot.db
 	elftosb2 -z -c ./uboot.db -o i$(ARCH)_uboot.sb
 else
-	@echo "by using the pre-built kernel"
-	elftosb2 -z -c ./linux_prebuilt.db -o i$(ARCH)_linux.sb
-	@echo "generating U-Boot boot stream image"
-	elftosb2 -z -c ./uboot_prebuilt.db -o i$(ARCH)_uboot.sb
+	@echo "... not generating any image for now."
+	#@echo "by using the pre-built kernel"
+	#elftosb2 -z -c ./linux_prebuilt.db -o i$(ARCH)_linux.sb
+	#@echo "generating U-Boot boot stream image"
+	#elftosb2 -z -c ./uboot_prebuilt.db -o i$(ARCH)_uboot.sb
 endif
 	#@echo "generating kernel bootstream file sd_mmc_bootstream.raw"
 	#Please use cfimager to burn xxx_linux.sb. The below way will no
