--- /dev/null
+++ b/linux_prep/board/imx23_olinuxino_dev.c
@@ -0,0 +1,54 @@
+/*
+ * Platform specific data for the IMX23_OLINUXINO development board
+ *
+ * <PERSON><PERSON><PERSON> <<EMAIL>>
+ *
+ * Copyright 2008 SigmaTel, Inc
+ * Copyright 2008 Embedded Alley Solutions, Inc
+ * Copyright 2009-2010 Freescale Semiconductor, Inc. All Rights Reserved.
+ *
+ * This file is licensed under the terms of the GNU General Public License
+ * version 2. This program is licensed "as is" without any warranty of any
+ * kind, whether express or implied.
+ *
+ * http://www.opensource.org/licenses/gpl-license.html
+ * http://www.gnu.org/copyleft/gpl.html
+ */
+#include <setup.h>
+#include <keys.h>
+#include <lradc_buttons.h>
+
+/************************************************
+ * LRADC keyboard data *
+ ************************************************/
+int lradc_keypad_ch = LRADC_CH0;
+int lradc_vddio_ch = LRADC_CH6;
+
+struct lradc_keycode lradc_keycodes[] = {
+ { 100, KEY4 },
+ { 306, KEY5 },
+ { 601, KEY6 },
+ { 932, KEY7 },
+ { 1260, KEY8 },
+ { 1424, KEY9 },
+ { 1707, KEY10 },
+ { 2207, KEY11 },
+ { 2525, KEY12 },
+ { 2831, KEY13 },
+ { 3134, KEY14 },
+ { -1, 0 },
+};
+
+/************************************************
+ * Magic key combinations for Armadillo *
+ ************************************************/
+u32 magic_keys[MAGIC_KEY_NR] = {
+ [MAGIC_KEY1] = KEY4,
+ [MAGIC_KEY2] = KEY6,
+ [MAGIC_KEY3] = KEY10,
+};
+
+/************************************************
+ * Default command line *
+ ************************************************/
+char cmdline_def[] = "console=ttyAMA0,115200";
--- /dev/null
+++ b/linux_prep/cmdlines/imx23_olinuxino_dev.txt
@@ -0,0 +1 @@
+noinitrd console=ttyAMA0,115200 root=/dev/mmcblk0p2 rw rootwait ssp1=mmc
--- a/linux_prep/core/setup.c
+++ b/linux_prep/core/setup.c
@@ -84,6 +84,8 @@ static void *memcpy(void *s1, const void
 #include "../../mach-mx28/includes/registers/regsrtc.h"
 #elif defined(STMP378X)
 #include "../../mach-mx23/includes/registers/regsrtc.h"
+#elif defined(IMX23_OLINUXINO)
+#include "../../mach-mx23/includes/registers/regsrtc.h"
 #endif
 
 #define NAND_SECONDARY_BOOT          0x00000002
--- a/linux_prep/include/mx23/platform.h
+++ b/linux_prep/include/mx23/platform.h
@@ -19,6 +19,10 @@
 
 #if defined (BOARD_STMP378X_DEV)
 #define	MACHINE_ID	0xa45
+
+#elif defined (BOARD_IMX23_OLINUXINO_DEV)
+#define MACHINE_ID 0x1009
+
 #else
 #error "Allocate a machine ID for your board"
 #endif
--- a/linux_prep/Makefile
+++ b/linux_prep/Makefile
@@ -69,6 +69,11 @@ ARCH = mx28
 HW_OBJS = $(LRADC_OBJS)
 CFLAGS += -DMX28 -DBOARD_MX28_EVK
 endif
+ifeq ($(BOARD), imx23_olinuxino_dev)
+ARCH = mx23
+HW_OBJS = $(LRADC_OBJS)
+CFLAGS += -DIMX23_OLINUXINO -DBOARD_IMX23_OLINUXINO_DEV
+endif
 
 # Generic code
 CORE_OBJS = entry.o resume.o cmdlines.o setup.o keys.o
--- a/Makefile
+++ b/Makefile
@@ -3,9 +3,9 @@ MEM_TYPE ?= MEM_DDR1
 export MEM_TYPE
 
 DFT_IMAGE=$(DEV_IMAGE)/boot/zImage
-DFT_UBOOT=$(DEV_IMAGE)/boot/u-boot
+DFT_UBOOT=../boot/u-boot
 
-BOARD ?= stmp378x_dev
+BOARD ?= imx23_olinuxino_dev
 
 ifeq ($(BOARD), stmp37xx_dev)
 ARCH = 37xx
@@ -16,6 +16,9 @@ endif
 ifeq ($(BOARD), iMX28_EVK)
 ARCH = mx28
 endif
+ifeq ($(BOARD), imx23_olinuxino_dev)
+ARCH = mx23
+endif
 
 all: build_prep gen_bootstream
 
@@ -94,6 +97,8 @@ distclean: clean
 clean:
 	-rm -rf *.sb
 	rm -f sd_mmc_bootstream.raw
+	rm -f linux_prep/board/*.o
+	rm -f power_prep/*.o
 	$(MAKE) -C linux_prep clean ARCH=$(ARCH)
 	$(MAKE) -C boot_prep clean ARCH=$(ARCH)
 	$(MAKE) -C power_prep clean ARCH=$(ARCH)
--- a/uboot.db
+++ b/uboot.db
@@ -3,7 +3,7 @@
 sources {
 	power_prep="./power_prep/power_prep";
 	sdram_prep="./boot_prep/boot_prep";
-	image="/home/<USER>/repos/ltib_latest/rootfs/boot/u-boot";
+	image="../boot/u-boot";
 }
 
 section (0) {
