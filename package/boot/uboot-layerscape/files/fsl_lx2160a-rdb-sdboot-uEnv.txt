fdtaddr=0x8f000000
loadaddr=0x81000000
bootm_size=0x10000000
hwconfig=fsl_ddr:bank_intlv=auto
mc_init=mmc read ******** 5000 1800;mmc read ******** 7000 800;fsl_mc start mc ******** ********;mmc read ******** 6800 800;fsl_mc apply dpl ********
sd_boot=ext4load mmc 0:1 ${loadaddr} fitImage;bootm ${loadaddr}
bootargs=root=/dev/mmcblk0p2 rw rootwait rootfstype=squashfs,f2fs noinitrd earlycon=pl011,mmio32,0x21c0000 console=ttyAMA0,115200
bootcmd=echo starting openwrt ...;run mc_init;run sd_boot
bootdelay=3
