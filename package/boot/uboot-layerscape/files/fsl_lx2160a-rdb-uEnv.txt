fdtaddr=0x8f000000
loadaddr=0x81000000
bootm_size=0x10000000
hwconfig=fsl_ddr:bank_intlv=auto
mc_init=sf probe 0:0;sf read ******** a00000 300000;sf read ******** e00000 100000;fsl_mc start mc ******** ********;sf read ******** d00000 100000;fsl_mc apply dpl ********
xspi_boot=sf probe 0:0;sf read $fdtaddr f00000 100000;sf read $loadaddr 1000000 1000000;bootm $loadaddr - $fdtaddr
bootargs=root=/dev/mtdblock9 rootfstype=squashfs,jffs2 noinitrd earlycon=pl011,mmio32,0x21c0000 console=ttyAMA0,115200 mtdparts=20c0000.spi-0:1m(bl2),4m(fip),1m(u-boot-env),4m(reserved-1),3m(mc),1m(dpl),1m(dpc),1m(dtb),16m(kernel),32m(rootfs),49m@0xf00000(firmware)
bootcmd=echo starting openwrt ...;run mc_init;run xspi_boot
bootdelay=3
