fdtaddr=0x8f000000
loadaddr=0x81000000
bootm_size=0x10000000
qspi_boot=sf probe 0:0;sf read $fdtaddr f00000 100000;sf read $loadaddr 1000000 1000000;bootm $loadaddr - $fdtaddr
bootargs=root=/dev/mtdblock8 rootfstype=squashfs,jffs2 noinitrd earlycon=uart8250,mmio,0x21c0500 console=ttyS0,115200 mtdparts=1550000.spi:1m(bl2),4m(fip),1m(u-boot-env),4m(reserved-1),3m(pfe),2m(reserved-2),1m(dtb),16m(kernel),32m(rootfs),49m@0xf00000(firmware)
bootcmd=echo starting openwrt ...;pfe stop;run qspi_boot
bootdelay=3
