From 45f9941ddc6346b38aa9eb7f033e1e169b63bdc7 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Fri, 8 Dec 2023 11:24:37 +0100
Subject: [PATCH] riscv: sifive: fu740: reduce DDR speed from 1866MT/s to
 1600MT/s

Signed-off-by: <PERSON> <<EMAIL>>
---
 arch/riscv/dts/fu740-c000-u-boot.dtsi | 2 +-
 1 file changed, 1 insertion(+), 1 deletion(-)

--- a/arch/riscv/dts/fu740-c000-u-boot.dtsi
+++ b/arch/riscv/dts/fu740-c000-u-boot.dtsi
@@ -77,7 +77,7 @@
 			       0x0 0x100b2000 0x0 0x2000
 			       0x0 0x100b8000 0x0 0x1000>;
 			clocks = <&prci FU740_PRCI_CLK_DDRPLL>;
-			clock-frequency = <933333324>;
+			clock-frequency = <800000004>;
 			bootph-pre-ram;
 		};
 	};
