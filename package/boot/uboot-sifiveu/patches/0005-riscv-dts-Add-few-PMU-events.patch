From 9b2868e9fda750c985313a40e60b67f96dc77ed1 Mon Sep 17 00:00:00 2001
From: Atish <PERSON>ra <<EMAIL>>
Date: Mon, 25 Oct 2021 11:35:41 -0700
Subject: [PATCH 5/5] riscv: dts: Add few PMU events

fu740 has 2 HPM counters and many HPM events defined in the fu740 manual[1].
This patch adds some of these events and their mapping as per the
OpenSBI PMU DT binding for now.

[1]https://sifive.cdn.prismic.io/sifive/de1491e5-077c-461d-9605-e8a0ce57337d_fu740-c000-manual-v1p3.pdf

Upstream-Status: Pending
Signed-off-by: Atish Patra <<EMAIL>>
---
 arch/riscv/dts/fu740-c000.dtsi | 11 +++++++++++
 1 file changed, 11 insertions(+)

--- a/arch/riscv/dts/fu740-c000.dtsi
+++ b/arch/riscv/dts/fu740-c000.dtsi
@@ -140,6 +140,17 @@
 		#size-cells = <2>;
 		compatible = "simple-bus";
 		ranges;
+		pmu {
+			compatible = "riscv,pmu";
+			riscv,raw-event-to-mhpmcounters = <0x00000000 0x200 0x18
+							 0x00000000 0x400 0x18
+							 0x00000000 0x800 0x18>;
+			riscv,event-to-mhpmcounters = <0x05 0x06 0x18
+						     0x10009 0x10009 0x18>;
+			riscv,event-to-mhpmevent = <0x05 0x00000000 0x4000
+						  0x06 0x00000000 0x4001
+						  0x10008 0x00000000 0x102>;
+		};
 		plic0: interrupt-controller@c000000 {
 			#interrupt-cells = <1>;
 			#address-cells = <0>;
