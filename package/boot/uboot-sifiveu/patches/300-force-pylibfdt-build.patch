--- a/<PERSON>file
+++ b/Makefile
@@ -2028,26 +2028,7 @@ endif
 # Check dtc and pylibfdt, if DTC is provided, else build them
 PHONY += scripts_dtc
 scripts_dtc: scripts_basic
-	$(Q)if test "$(DTC)" = "$(DTC_INTREE)"; then \
-		$(MAKE) $(build)=scripts/dtc; \
-	else \
-		if ! $(DTC) -v >/dev/null; then \
-			echo '*** Failed to check dtc version: $(DTC)'; \
-			false; \
-		else \
-			if test "$(call dtc-version)" -lt $(DTC_MIN_VERSION); then \
-				echo '*** Your dtc is too old, please upgrade to dtc $(DTC_MIN_VERSION) or newer'; \
-				false; \
-			else \
-				if [ -n "$(CONFIG_PYLIBFDT)" ]; then \
-					if ! echo "import libfdt" | $(PYTHON3) 2>/dev/null; then \
-						echo '*** pylibfdt does not seem to be available with $(PYTHON3)'; \
-						false; \
-					fi; \
-				fi; \
-			fi; \
-		fi; \
-	fi
+	$(MAKE) $(build)=scripts/dtc
 
 # ---------------------------------------------------------------------------
 quiet_cmd_cpp_lds = LDS     $@
