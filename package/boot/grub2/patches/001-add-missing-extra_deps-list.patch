From 4d4dae6a52b1749642261a15f5dcc1e3d4150b36 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Fri, 22 Dec 2023 19:02:53 +0100
Subject: [PATCH] Add missing grub-core/extra_deps.lst file in release tarball

A file is missing in the grub-2.12 release tarballs (both .gz and .xz).
See [1]. The issue was reported in [2] and fixed upstream in [3].

This patch adds the missing file, on top of the release tarball. This
patch won't apply on upstream git, since the file is present in the
source repository. Since the issue is fixed upstream in [3], it is
expected upcoming releases tarballs will include the file.

The file content was fetched from the upstream git repo:
https://git.savannah.gnu.org/gitweb/?p=grub.git;a=blob_plain;f=grub-core/extra_deps.lst;hb=refs/tags/grub-2.12

[1] https://ftp.gnu.org/gnu/grub/grub-2.12.tar.xz
[2] https://lists.gnu.org/archive/html/grub-devel/2023-12/msg00054.html
[3] https://git.savannah.gnu.org/gitweb/?p=grub.git;a=commit;h=b835601c7639ed1890f2d3db91900a8506011a8e

Signed-off-by: Julien Olivain <<EMAIL>>
Upstream: Fixed by: https://git.savannah.gnu.org/gitweb/?p=grub.git;a=commit;h=b835601c7639ed1890f2d3db91900a8506011a8e
---
 grub-core/extra_deps.lst | 1 +
 1 file changed, 1 insertion(+)
 create mode 100644 grub-core/extra_deps.lst

diff --git a/grub-core/extra_deps.lst b/grub-core/extra_deps.lst
new file mode 100644
index 0000000..f44ad6a
--- /dev/null
+++ b/grub-core/extra_deps.lst
@@ -0,0 +1 @@
+depends bli part_gpt
-- 
2.43.0
