--- a/arch/arm/mach-rockchip/decode_bl31.py
+++ b/arch/arm/mach-rockchip/decode_bl31.py
@@ -1,4 +1,4 @@
-#!/usr/bin/env python2
+#!/usr/bin/env python3
 #
 # Copyright (C) 2020 Rockchip Electronics Co., Ltd
 #
--- a/arch/arm/mach-rockchip/make_fit_atf.py
+++ b/arch/arm/mach-rockchip/make_fit_atf.py
@@ -1,4 +1,4 @@
-#!/usr/bin/env python2
+#!/usr/bin/env python3
 """
 A script to generate FIT image source for rockchip boards
 with ARM Trusted Firmware
