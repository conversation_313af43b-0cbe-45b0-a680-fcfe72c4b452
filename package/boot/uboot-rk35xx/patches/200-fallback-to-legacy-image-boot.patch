diff --git a/disk/part_efi.c b/disk/part_efi.c
index d4d03de..b803d17 100644
--- a/disk/part_efi.c
+++ b/disk/part_efi.c
@@ -351,7 +351,7 @@ int part_get_info_efi(struct blk_desc *dev_desc, int part,
 	return 0;
 }
 
-#ifdef CONFIG_RKIMG_BOOTLOADER
+#if 0
 #if defined(CONFIG_SPL_KERNEL_BOOT) || !defined(CONFIG_SPL_BUILD)
 static void gpt_entry_modify(struct blk_desc *dev_desc,
 			     gpt_entry *gpt_pte,
@@ -452,7 +452,7 @@ static int part_test_efi(struct blk_desc *dev_desc)
 		|| (is_pmbr_valid(legacymbr) != 1)) {
 		return -1;
 	}
-#ifdef CONFIG_RKIMG_BOOTLOADER
+#if 0
 #if defined(CONFIG_SPL_KERNEL_BOOT) || !defined(CONFIG_SPL_BUILD)
 	gpt_entry *h_gpt_pte = NULL;
 	gpt_header *h_gpt_head = NULL;
@@ -1084,7 +1084,7 @@ static int is_pmbr_valid(legacy_mbr * mbr)
 {
 	int i = 0;
 
-#ifdef CONFIG_ARCH_ROCKCHIP
+#if 0
 	/*
 	 * In sd-update card, we use RKPARM partition in bootloader to load
 	 * firmware, and use MS-DOS partition in recovery to update system.
diff --git a/include/configs/rockchip-common.h b/include/configs/rockchip-common.h
index c35b7db..b695a7d 100644
--- a/include/configs/rockchip-common.h
+++ b/include/configs/rockchip-common.h
@@ -179,6 +179,8 @@
 	"run distro_bootcmd;"
 #endif
 
+#define CONFIG_IMAGE_FORMAT_LEGACY /* enable also legacy image format */
+
 #endif /* CONFIG_SPL_BUILD */
 
 #define CONFIG_DISPLAY_BOARDINFO_LATE