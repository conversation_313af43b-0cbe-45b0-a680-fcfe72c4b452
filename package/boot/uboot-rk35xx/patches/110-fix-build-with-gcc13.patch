--- a/common/edid.c
+++ b/common/edid.c
@@ -3579,7 +3579,7 @@ int add_cea_modes(struct hdmi_edid_data
 {
 	const u8 *cea = drm_find_cea_extension(edid);
 	const u8 *db, *hdmi = NULL, *video = NULL;
-	u8 dbl, hdmi_len, video_len = 0;
+	u8 dbl, hdmi_len = 0, video_len = 0;
 	int modes = 0;
 
 	if (cea && cea_revision(cea) >= 3) {
--- a/include/command.h
+++ b/include/command.h
@@ -139,7 +139,7 @@ enum command_ret_t {
  *			number of ticks the command took to complete.
  * @return 0 if the command succeeded, 1 if it failed
  */
-int cmd_process(int flag, int argc, char * const argv[],
+enum command_ret_t cmd_process(int flag, int argc, char * const argv[],
 			       int *repeatable, unsigned long *ticks);
 
 void fixup_cmdtable(cmd_tbl_t *cmdtp, int size);
