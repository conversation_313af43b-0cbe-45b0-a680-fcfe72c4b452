From ea50d7f6921e2c3017258ce8fdfad632d82fbd87 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Mon, 8 Nov 2021 14:30:00 +0800
Subject: [PATCH] cmd: source: fix the error that the command source
 failed to execute

Signed-off-by: <PERSON> <<EMAIL>>
---
 cmd/source.c | 2 +-
 1 file changed, 1 insertion(+), 1 deletion(-)

--- a/cmd/source.c
+++ b/cmd/source.c
@@ -87,7 +87,7 @@ source (ulong addr, const char *fit_unam
 		 * past the zero-terminated sequence of image lengths to get
 		 * to the actual image data
 		 */
-		while (*data++ != IMAGE_PARAM_INVAL);
+		while (*data++);
 		break;
 #endif
 #if defined(CONFIG_FIT)
